<FindBugsFilter>
    <!--refer to https://spotbugs.readthedocs.io/en/latest/bugDescriptions.html-->
    <Match>
        <Bug pattern="CNT_ROUGH_CONSTANT_VALUE,
            AT_OPERATION_SEQUENCE_ON_CONCURRENT_ABSTRACTION,
            STCAL_STATIC_CALENDAR_INSTANCE,
            STCAL_STATIC_SIMPLE_DATE_FORMAT_INSTANCE,
            STCAL_INVOKE_ON_STATIC_CALENDAR_INSTANCE,
            STCAL_INVOKE_ON_STATIC_DATE_FORMAT_INSTANCE,
            NP_SYNC_AND_NULL_CHECK_FIELD,
            NP_OPTIONAL_RETURN_NULL,
            NP_NONNULL_FIELD_NOT_INITIALIZED_IN_CONSTRUCTOR,
            VR_UNRESOLVABLE_REFERENCE,
            IL_INFINITE_LOOP,
            IO_APPENDING_TO_OBJECT_OUTPUT_STREAM,
            IL_INFINITE_RECURSIVE_LOOP,
            IL_CONTAINER_ADDED_TO_ITSELF,
            VO_VOLATILE_REFERENCE_TO_ARRAY,
            VO_VOLATILE_INCREMENT,
            UI_INHERITANCE_UNSAFE_GETRESOURCE,
            HSC_HUGE_SHARED_STRING_CONSTANT,
            RpC_REPEATED_CONDITIONAL_TEST,
            NOISE_NULL_DEREFERENCE,
            NOISE_METHOD_CALL,
            NOISE_FIELD_REFERENCE,
            NOISE_OPERATION,
            DP_DO_INSIDE_DO_PRIVILEGED,
            DP_CREATE_CLASSLOADER_INSIDE_DO_PRIVILEGED,
            IMSE_DONT_CATCH_IMSE,
            FL_MATH_USING_FLOAT_PRECISION,
            CAA_COVARIANT_ARRAY_FIELD,
            CAA_COVARIANT_ARRAY_RETURN,
            CAA_COVARIANT_ARRAY_LOCAL,
            CAA_COVARIANT_ARRAY_ELEMENT_STORE,
            DE_MIGHT_DROP,
            DE_MIGHT_IGNORE,
            NM_FUTURE_KEYWORD_USED_AS_IDENTIFIER,
            NM_FUTURE_KEYWORD_USED_AS_MEMBER_IDENTIFIER,
            DMI_VACUOUS_CALL_TO_EASYMOCK_METHOD,
            DMI_FUTILE_ATTEMPT_TO_CHANGE_MAXPOOL_SIZE_OF_SCHEDULED_THREAD_POOL_EXECUTOR,
            DMI_BIGDECIMAL_CONSTRUCTED_FROM_DOUBLE,
            DMI_SCHEDULED_THREAD_POOL_EXECUTOR_WITH_ZERO_CORE_THREADS,
            DMI_UNSUPPORTED_METHOD,
            DMI_THREAD_PASSED_WHERE_RUNNABLE_EXPECTED,
            DMI_BLOCKING_METHODS_ON_URL,
            DMI_COLLECTION_OF_URLS,
            DMI_ANNOTATION_IS_NOT_VISIBLE_TO_REFLECTION,
            DM_RUN_FINALIZERS_ON_EXIT,
            DM_STRING_CTOR,
            DM_STRING_VOID_CTOR,
            DM_STRING_TOSTRING,
            DM_GC,
            DM_BOOLEAN_CTOR,
            DM_NUMBER_CTOR,
            DM_FP_NUMBER_CTOR,
            DM_CONVERT_CASE,
            DM_BOXED_PRIMITIVE_TOSTRING,
            DM_BOXED_PRIMITIVE_FOR_PARSING,
            DM_BOXED_PRIMITIVE_FOR_COMPARE,
            BX_UNBOXED_AND_COERCED_FOR_TERNARY_OPERATOR,
            BX_UNBOXING_IMMEDIATELY_REBOXED,
            BX_BOXING_IMMEDIATELY_UNBOXED,
            BX_BOXING_IMMEDIATELY_UNBOXED_TO_PERFORM_COERCION,
            DM_NEW_FOR_GETCLASS,
            DM_MONITOR_WAIT_ON_CONDITION,
            NP_ARGUMENT_MIGHT_BE_NULL,
            NP_EQUALS_SHOULD_HANDLE_NULL_ARGUMENT,
            NP_DEREFERENCE_OF_READLINE_VALUE,
            NP_IMMEDIATE_DEREFERENCE_OF_READLINE,
            RV_ABSOLUTE_VALUE_OF_RANDOM_INT,
            RV_ABSOLUTE_VALUE_OF_HASHCODE,
            RV_REM_OF_RANDOM_INT,
            RV_REM_OF_HASHCODE,
            RV_01_TO_INT,
            DM_INVALID_MIN_MAX,
            DM_NEXTINT_VIA_NEXTDOUBLE,
            DM_USELESS_THREAD,
            DC_DOUBLECHECK,
            DC_PARTIALLY_CONSTRUCTED,
            EQ_CHECK_FOR_OPERAND_NOT_COMPATIBLE_WITH_THIS,
            EQ_COMPARING_CLASS_NAMES,
            EQ_UNUSUAL,
            EQ_GETCLASS_AND_CLASS_CONSTANT,
            EQ_ALWAYS_TRUE,
            EQ_ALWAYS_FALSE,
            EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC,
            EQ_DOESNT_OVERRIDE_EQUALS,
            EQ_DONT_DEFINE_EQUALS_FOR_ENUM,
            EQ_SELF_USE_OBJECT,
            EQ_OTHER_USE_OBJECT,
            EQ_OTHER_NO_OBJECT,
            EQ_SELF_NO_OBJECT,
            CO_SELF_NO_OBJECT,
            CO_COMPARETO_RESULTS_MIN_VALUE,
            CO_COMPARETO_INCORRECT_FLOATING,
            RV_NEGATING_RESULT_OF_COMPARETO,
            ES_COMPARING_STRINGS_WITH_EQ,
            ES_COMPARING_PARAMETER_STRING_WITH_EQ,
            HE_SIGNATURE_DECLARES_HASHING_OF_UNHASHABLE_CLASS,
            HE_USE_OF_UNHASHABLE_CLASS,
            EQ_COMPARETO_USE_OBJECT_EQUALS,
            HE_HASHCODE_USE_OBJECT_EQUALS,
            HE_HASHCODE_NO_EQUALS,
            HE_EQUALS_USE_HASHCODE,
            HE_INHERITS_EQUALS_USE_HASHCODE,
            HE_EQUALS_NO_HASHCODE,
            EQ_ABSTRACT_SELF,
            CO_ABSTRACT_SELF,
            DL_SYNCHRONIZATION_ON_SHARED_CONSTANT,
            DL_SYNCHRONIZATION_ON_BOOLEAN,
            DL_SYNCHRONIZATION_ON_BOXED_PRIMITIVE,
            DL_SYNCHRONIZATION_ON_UNSHARED_BOXED_PRIMITIVE,
            WL_USING_GETCLASS_RATHER_THAN_CLASS_LITERAL,
            ESync_EMPTY_SYNC,
            IS2_INCONSISTENT_SYNC,
            NN_NAKED_NOTIFY,
            MS_EXPOSE_REP,
            EI_EXPOSE_STATIC_REP2,
            RU_INVOKE_RUN,
            SP_SPIN_ON_FIELD,
            NS_NON_SHORT_CIRCUIT,
            NS_DANGEROUS_NON_SHORT_CIRCUIT,
            TLW_TWO_LOCK_WAIT,
            UW_UNCOND_WAIT,
            UR_UNINIT_READ,
            UR_UNINIT_READ_CALLED_FROM_SUPER_CONSTRUCTOR,
            UG_SYNC_SET_UNSYNC_GET,
            IC_INIT_CIRCULARITY,
            IC_SUPERCLASS_USES_SUBCLASS_DURING_INITIALIZATION,
            SI_INSTANCE_BEFORE_FINALS_ASSIGNED,
            IT_NO_SUCH_ELEMENT,
            IS_FIELD_NOT_GUARDED,
            ML_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD,
            ML_SYNC_ON_UPDATED_FIELD,
            MS_OOI_PKGPROTECT,
            MS_FINAL_PKGPROTECT,
            MS_SHOULD_BE_FINAL,
            MS_SHOULD_BE_REFACTORED_TO_BE_FINAL,
            MS_PKGPROTECT,
            MS_MUTABLE_HASHTABLE,
            MS_MUTABLE_ARRAY,
            MS_MUTABLE_COLLECTION,
            MS_MUTABLE_COLLECTION_PKGPROTECT,
            MS_CANNOT_BE_FINAL,
            ME_MUTABLE_ENUM_FIELD,
            ME_ENUM_FIELD_SETTER,
            NM_METHOD_NAMING_CONVENTION,
            NM_FIELD_NAMING_CONVENTION,
            NM_SAME_SIMPLE_NAME_AS_INTERFACE,
            NM_SAME_SIMPLE_NAME_AS_SUPERCLASS,
            NM_CLASS_NAMING_CONVENTION,
            NM_VERY_CONFUSING,
            NM_VERY_CONFUSING_INTENTIONAL,
            NM_WRONG_PACKAGE,
            NM_WRONG_PACKAGE_INTENTIONAL,
            NM_METHOD_CONSTRUCTOR_CONFUSION,
            NM_LCASE_HASHCODE,
            NM_LCASE_TOSTRING,
            NM_BAD_EQUAL,
            IA_AMBIGUOUS_INVOCATION_OF_INHERITED_OR_OUTER_METHOD,
            NM_CLASS_NOT_EXCEPTION,
            RR_NOT_CHECKED,
            SR_NOT_CHECKED,
            WS_WRITEOBJECT_SYNC,
            RS_READOBJECT_SYNC,
            SC_START_IN_CTOR,
            SF_SWITCH_FALLTHROUGH,
            SF_SWITCH_NO_DEFAULT,
            SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH,
            SF_DEAD_STORE_DUE_TO_SWITCH_FALLTHROUGH_TO_THROW,
            SS_SHOULD_BE_STATIC,
            UUF_UNUSED_FIELD,
            URF_UNREAD_FIELD,
            QF_QUESTIONABLE_FOR_LOOP,
            NP_UNWRITTEN_FIELD,
            UWF_NULL_FIELD,
            UWF_UNWRITTEN_FIELD,
            SIC_THREADLOCAL_DEADLY_EMBRACE,
            SIC_INNER_SHOULD_BE_STATIC,
            SIC_INNER_SHOULD_BE_STATIC_NEEDS_THIS,
            SIC_INNER_SHOULD_BE_STATIC_ANON,
            WA_NOT_IN_LOOP,
            WA_AWAIT_NOT_IN_LOOP,
            NO_NOTIFY_NOT_NOTIFYALL,
            UC_USELESS_VOID_METHOD,
            UC_USELESS_CONDITION,
            UC_USELESS_CONDITION_TYPE,
            UC_USELESS_OBJECT,
            UC_USELESS_OBJECT_STACK,
            RANGE_ARRAY_INDEX,
            RANGE_ARRAY_OFFSET,
            RANGE_ARRAY_LENGTH,
            RANGE_STRING_INDEX,
            RV_RETURN_VALUE_IGNORED_INFERRED,
            RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT,
            RV_EXCEPTION_NOT_THROWN,
            RV_CHECK_COMPARETO_FOR_SPECIFIC_RETURN_VALUE,
            RV_CHECK_FOR_POSITIVE_INDEXOF,
            RV_DONT_JUST_NULL_CHECK_READLINE,
            NP_ALWAYS_NULL,
            NP_CLOSING_NULL,
            NP_STORE_INTO_NONNULL_FIELD,
            NP_ALWAYS_NULL_EXCEPTION,
            NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE,
            NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE,
            NP_NULL_ON_SOME_PATH_MIGHT_BE_INFEASIBLE,
            NP_NULL_ON_SOME_PATH,
            NP_NULL_ON_SOME_PATH_EXCEPTION,
            NP_NULL_PARAM_DEREF,
            NP_NULL_PARAM_DEREF_NONVIRTUAL,
            NP_NULL_PARAM_DEREF_ALL_TARGETS_DANGEROUS,
            NP_NONNULL_PARAM_VIOLATION,
            NP_NONNULL_RETURN_VIOLATION,
            NP_TOSTRING_COULD_RETURN_NULL,
            NP_LOAD_OF_KNOWN_NULL_VALUE,
            NP_GUARANTEED_DEREF,
            NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH,
            OS_OPEN_STREAM,
            OS_OPEN_STREAM_EXCEPTION_PATH,
            UCF_USELESS_CONTROL_FLOW,
            UCF_USELESS_CONTROL_FLOW_NEXT_LINE,
            DMI_ARGUMENTS_WRONG_ORDER,
            RCN_REDUNDANT_NULLCHECK_OF_NULL_VALUE,
            RCN_REDUNDANT_NULLCHECK_WOULD_HAVE_BEEN_A_NPE,
            RCN_REDUNDANT_COMPARISON_TWO_NULL_VALUES,
            RCN_REDUNDANT_COMPARISON_OF_NULL_AND_NONNULL_VALUE,
            UL_UNRELEASED_LOCK,
            UL_UNRELEASED_LOCK_EXCEPTION_PATH,
            RC_REF_COMPARISON_BAD_PRACTICE,
            RC_REF_COMPARISON_BAD_PRACTICE_BOOLEAN,
            RC_REF_COMPARISON,
            VA_PRIMITIVE_ARRAY_PASSED_TO_OBJECT_VARARG,
            VA_FORMAT_STRING_USES_NEWLINE,
            EC_UNRELATED_TYPES_USING_POINTER_EQUALITY,
            EC_UNRELATED_TYPES,
            EC_ARRAY_AND_NONARRAY,
            EC_NULL_ARG,
            EC_UNRELATED_INTERFACES,
            EC_UNRELATED_CLASS_AND_INTERFACE,
            MWN_MISMATCHED_WAIT,
            MWN_MISMATCHED_NOTIFY,
            SA_LOCAL_SELF_ASSIGNMENT,
            SA_LOCAL_SELF_ASSIGNMENT_INSTEAD_OF_FIELD,
            INT_BAD_COMPARISON_WITH_INT_VALUE,
            INT_BAD_COMPARISON_WITH_SIGNED_BYTE,
            INT_BAD_COMPARISON_WITH_NONNEGATIVE_VALUE,
            INT_BAD_REM_BY_1,
            INT_VACUOUS_COMPARISON,
            INT_VACUOUS_BIT_OPERATION,
            BIT_ADD_OF_SIGNED_BYTE,
            BIT_IOR_OF_SIGNED_BYTE,
            BIT_SIGNED_CHECK,
            BIT_SIGNED_CHECK_HIGH_BIT,
            BIT_AND,
            BIT_AND_ZZ,
            BIT_IOR,
            SA_FIELD_SELF_ASSIGNMENT,
            SA_LOCAL_DOUBLE_ASSIGNMENT,
            SA_FIELD_DOUBLE_ASSIGNMENT,
            SA_FIELD_SELF_COMPUTATION,
            SA_LOCAL_SELF_COMPUTATION,
            SA_FIELD_SELF_COMPARISON,
            SA_LOCAL_SELF_COMPARISON,
            LI_LAZY_INIT_STATIC,
            LI_LAZY_INIT_UPDATE_STATIC,
            JLM_JSR166_UTILCONCURRENT_MONITORENTER,
            JML_JSR166_CALLING_WAIT_RATHER_THAN_AWAIT,
            JLM_JSR166_LOCK_MONITORENTER,
            UPM_UNCALLED_PRIVATE_METHOD,
            UMAC_UNCALLABLE_METHOD_OF_ANONYMOUS_CLASS,
            ODR_OPEN_DATABASE_RESOURCE,
            ODR_OPEN_DATABASE_RESOURCE_EXCEPTION_PATH,
            SBSC_USE_STRINGBUFFER_CONCATENATION,
            IIL_ELEMENTS_GET_LENGTH_IN_LOOP,
            IIL_PREPARE_STATEMENT_IN_LOOP,
            IIL_PATTERN_COMPILE_IN_LOOP,
            IIL_PATTERN_COMPILE_IN_LOOP_INDIRECT,
            IIO_INEFFICIENT_INDEX_OF,
            IIO_INEFFICIENT_LAST_INDEX_OF,
            ITA_INEFFICIENT_TO_ARRAY,
            BOA_BADLY_OVERRIDDEN_ADAPTER,
            SIO_SUPERFLUOUS_INSTANCEOF,
            BAC_BAD_APPLET_CONSTRUCTOR,
            EC_INCOMPATIBLE_ARRAY_COMPARE,
            EC_BAD_ARRAY_COMPARE,
            STI_INTERRUPTED_ON_CURRENTTHREAD,
            STI_INTERRUPTED_ON_UNKNOWNTHREAD,
            DLS_DEAD_LOCAL_STORE_IN_RETURN,
            DLS_DEAD_LOCAL_INCREMENT_IN_RETURN,
            DLS_DEAD_STORE_OF_CLASS_LITERAL,
            DLS_DEAD_LOCAL_STORE,
            DLS_DEAD_LOCAL_STORE_SHADOWS_FIELD,
            DLS_DEAD_LOCAL_STORE_OF_NULL,
            IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN,
            MF_METHOD_MASKS_FIELD,
            MF_CLASS_MASKS_FIELD,
            WMI_WRONG_MAP_ITERATOR,
            ISC_INSTANTIATE_STATIC_CLASS,
            REC_CATCH_EXCEPTION,
            FE_FLOATING_POINT_EQUALITY,
            FE_TEST_IF_EQUAL_TO_NOT_A_NUMBER,
            UM_UNNECESSARY_MATH,
            CD_CIRCULAR_DEPENDENCY,
            RI_REDUNDANT_INTERFACES,
            PS_PUBLIC_SEMAPHORES,
            ICAST_INT_2_LONG_AS_INSTANT,
            ICAST_INTEGER_MULTIPLY_CAST_TO_LONG,
            ICAST_IDIV_CAST_TO_DOUBLE,
            ICAST_INT_CAST_TO_DOUBLE_PASSED_TO_CEIL,
            ICAST_INT_CAST_TO_FLOAT_PASSED_TO_ROUND,
            NP_NULL_INSTANCEOF,
            DMI_RANDOM_USED_ONLY_ONCE,
            DMI_LONG_BITS_TO_DOUBLE_INVOKED_ON_INT,
            BC_EQUALS_METHOD_SHOULD_WORK_FOR_ALL_OBJECTS,
            BC_BAD_CAST_TO_CONCRETE_COLLECTION,
            BC_UNCONFIRMED_CAST_OF_RETURN_VALUE,
            BC_IMPOSSIBLE_CAST,
            BC_IMPOSSIBLE_DOWNCAST,
            BC_IMPOSSIBLE_DOWNCAST_OF_TOARRAY,
            BC_IMPOSSIBLE_INSTANCEOF,
            BC_VACUOUS_INSTANCEOF,
            BC_BAD_CAST_TO_ABSTRACT_COLLECTION,
            RE_POSSIBLE_UNINTENDED_PATTERN,
            RE_BAD_SYNTAX_FOR_REGULAR_EXPRESSION,
            RE_CANT_USE_FILE_SEPARATOR_AS_REGULAR_EXPRESSION,
            DLS_OVERWRITTEN_INCREMENT,
            ICAST_BAD_SHIFT_AMOUNT,
            BSHIFT_WRONG_ADD_PRIORITY,
            IM_MULTIPLYING_RESULT_OF_IREM,
            IM_BAD_CHECK_FOR_ODD,
            IM_AVERAGE_COMPUTATION_COULD_OVERFLOW,
            DMI_INVOKING_HASHCODE_ON_ARRAY,
            DMI_INVOKING_TOSTRING_ON_ARRAY,
            DMI_INVOKING_TOSTRING_ON_ANONYMOUS_ARRAY,
            ICAST_QUESTIONABLE_UNSIGNED_RIGHT_SHIFT,
            DMI_HARDCODED_ABSOLUTE_FILENAME,
            DMI_BAD_MONTH,
            DMI_USELESS_SUBSTRING,
            DMI_CALLING_NEXT_FROM_HASNEXT,
            ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD,
            SWL_SLEEP_WITH_LOCK_HELD,
            DB_DUPLICATE_BRANCHES,
            DB_DUPLICATE_SWITCH_CLAUSES,
            IMA_INEFFICIENT_MEMBER_ACCESS,
            XFB_XML_FACTORY_BYPASS,
            USM_USELESS_SUBCLASS_METHOD,
            USM_USELESS_ABSTRACT_METHOD,
            CI_CONFUSED_INHERITANCE,
            QBA_QUESTIONABLE_BOOLEAN_ASSIGNMENT,
            GC_UNCHECKED_TYPE_IN_GENERIC_CALL,
            GC_UNRELATED_TYPES,
            PZ_DONT_REUSE_ENTRY_OBJECTS_IN_ITERATORS,
            DMI_ENTRY_SETS_MAY_REUSE_ENTRY_OBJECTS,
            DMI_USING_REMOVEALL_TO_CLEAR_COLLECTION,
            DMI_VACUOUS_SELF_COLLECTION_CALL,
            DMI_DOH,
            DMI_COLLECTIONS_SHOULD_NOT_CONTAIN_THEMSELVES,
            TQ_UNKNOWN_VALUE_USED_WHERE_ALWAYS_STRICTLY_REQUIRED,
            TQ_COMPARING_VALUES_WITH_INCOMPATIBLE_TYPE_QUALIFIERS,
            TQ_ALWAYS_VALUE_USED_WHERE_NEVER_REQUIRED,
            TQ_NEVER_VALUE_USED_WHERE_ALWAYS_REQUIRED,
            TQ_MAYBE_SOURCE_VALUE_REACHES_ALWAYS_SINK,
            TQ_MAYBE_SOURCE_VALUE_REACHES_NEVER_SINK,
            TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_NEVER_SINK,
            TQ_EXPLICIT_UNKNOWN_SOURCE_VALUE_REACHES_ALWAYS_SINK,
            OBL_UNSATISFIED_OBLIGATION_EXCEPTION_EDGE,
            OBL_UNSATISFIED_OBLIGATION,
            FB_UNEXPECTED_WARNING,
            FB_MISSING_EXPECTED_WARNING,
            RV_RETURN_VALUE_OF_PUTIFABSENT_IGNORED,
            LG_LOST_LOGGER_DUE_TO_WEAK_REFERENCE,
            NP_METHOD_RETURN_RELAXING_ANNOTATION,
            NP_METHOD_PARAMETER_TIGHTENS_ANNOTATION"/>
    </Match>

    <!--refer to http://fb-contrib.sourceforge.net/bugdescriptions.html-->
    <Match>
        <Bug pattern="ISB_INEFFICIENT_STRING_BUFFERING,
            ISB_EMPTY_STRING_APPENDING,
            ISB_TOSTRING_APPENDING,
            SCI_SYNCHRONIZED_COLLECTION_ITERATORS,
            CC_CYCLOMATIC_COMPLEXITY,
            LII_LIST_INDEXED_ITERATING,
            UCC_UNRELATED_COLLECTION_CONTENTS,
            DRE_DECLARED_RUNTIME_EXCEPTION,
            LSC_LITERAL_STRING_COMPARISON,
            PCOA_PARTIALLY_CONSTRUCTED_OBJECT_ACCESS,
            DLC_DUBIOUS_LIST_COLLECTION,
            FP_FINAL_PARAMETERS,
            MAC_MANUAL_ARRAY_COPY,
            FPL_FLOATING_POINT_LOOPS,
            NCMU_NON_COLLECTION_METHOD_USE,
            CAO_CONFUSING_AUTOBOXED_OVERLOADING,
            AFBR_ABNORMAL_FINALLY_BLOCK_RETURN,
            SMII_STATIC_METHOD_INSTANCE_INVOCATION,
            STS_SPURIOUS_THREAD_STATES,
            NAB_NEEDLESS_AUTOBOXING_CTOR,
            NAB_NEEDLESS_BOXING_STRING_CTOR,
            NAB_NEEDLESS_AUTOBOXING_VALUEOF,
            NAB_NEEDLESS_BOXING_PARSE,
            NAB_NEEDLESS_BOXING_VALUEOF,
            NAB_NEEDLESS_BOX_TO_UNBOX,
            NAB_NEEDLESS_BOX_TO_CAST,
            NAB_NEEDLESS_BOOLEAN_CONSTANT_CONVERSION,
            COM_COPIED_OVERRIDDEN_METHOD,
            COM_PARENT_DELEGATED_CALL,
            ABC_ARRAY_BASED_COLLECTIONS,
            ODN_ORPHANED_DOM_NODE,
            AOM_ABSTRACT_OVERRIDDEN_METHOD,
            BSB_BLOATED_SYNCHRONIZED_BLOCK,
            SCR_SLOPPY_CLASS_REFLECTION,
            AWCBR_ARRAY_WRAPPED_CALL_BY_REFERENCE,
            SG_SLUGGISH_GUI,
            NIR_NEEDLESS_INSTANCE_RETRIEVAL,
            DDC_DOUBLE_DATE_COMPARISON,
            SWCO_SUSPICIOUS_WAIT_ON_CONCURRENT_OBJECT,
            JVR_JDBC_VENDOR_RELIANCE,
            PMB_POSSIBLE_MEMORY_BLOAT,
            PMB_INSTANCE_BASED_THREAD_LOCAL,
            LSYC_LOCAL_SYNCHRONIZED_COLLECTION,
            FCBL_FIELD_COULD_BE_LOCAL,
            NRTL_NON_RECYCLEABLE_TAG_LIB,
            UEC_USE_ENUM_COLLECTIONS,
            NMCS_NEEDLESS_MEMBER_COLLECTION_SYNCHRONIZATION,
            SACM_STATIC_ARRAY_CREATED_IN_METHOD,
            UTA_USE_TO_ARRAY,
            LEST_LOST_EXCEPTION_STACK_TRACE,
            UCPM_USE_CHARACTER_PARAMETERIZED_METHOD,
            TR_TAIL_RECURSION,
            URV_INHERITED_METHOD_WITH_RELATED_TYPES,
            SCRV_SUSPICIOUS_COMPARATOR_RETURN_VALUES,
            SPP_NEGATIVE_BITSET_ITEM,
            SPP_INTERN_ON_CONSTANT,
            SPP_NO_CHAR_SB_CTOR,
            SPP_USE_MATH_CONSTANT,
            SPP_STUTTERED_ASSIGNMENT,
            SPP_USE_ISNAN,
            SPP_USE_BIGDECIMAL_STRING_CTOR,
            SPP_STRINGBUFFER_WITH_EMPTY_STRING,
            SPP_EQUALS_ON_ENUM,
            SPP_INVALID_BOOLEAN_NULL_CHECK,
            SPP_USE_CHARAT,
            SPP_USELESS_TERNARY,
            SPP_SUSPECT_STRING_TEST,
            SPP_USE_STRINGBUILDER_LENGTH,
            SPP_INVALID_CALENDAR_COMPARE,
            SPP_USE_ISEMPTY,
            SPP_USE_GETPROPERTY,
            SPP_USELESS_CASING,
            SPP_SERIALVER_SHOULD_BE_PRIVATE,
            SPP_NON_ARRAY_PARM,
            SPP_EMPTY_CASING,
            SPP_TEMPORARY_TRIM,
            SPP_STRINGBUILDER_IS_MUTABLE,
            SPP_USE_GET0,
            SPP_DOUBLE_APPENDED_LITERALS,
            SPP_NULL_BEFORE_INSTANCEOF,
            SPP_NON_USEFUL_TOSTRING,
            SPP_TOSTRING_ON_STRING,
            SPP_EQUALS_ON_STRING_BUILDER,
            SPP_CONVERSION_OF_STRING_LITERAL,
            SPP_STATIC_FORMAT_STRING,
            SPP_WRONG_COMMONS_TO_STRING_OBJECT,
            SPP_USE_ZERO_WITH_COMPARATOR,
            BAS_BLOATED_ASSIGNMENT_SCOPE,
            SCII_SPOILED_CHILD_INTERFACE_IMPLEMENTOR,
            DWI_DELETING_WHILE_ITERATING,
            DWI_MODIFYING_WHILE_ITERATING,
            USS_USE_STRING_SPLIT,
            SJVU_SUSPICIOUS_JDK_VERSION_USE,
            UAA_USE_ADD_ALL,
            MRC_METHOD_RETURNS_CONSTANT,
            MOM_MISLEADING_OVERLOAD_MODEL,
            EXS_EXCEPTION_SOFTENING_HAS_CHECKED,
            CFS_CONFUSING_FUNCTION_SEMANTICS,
            UTAO_JUNIT_ASSERTION_ODDITIES_ACTUAL_CONSTANT,
            UTAO_JUNIT_ASSERTION_ODDITIES_INEXACT_DOUBLE,
            UTAO_JUNIT_ASSERTION_ODDITIES_BOOLEAN_ASSERT,
            UTAO_JUNIT_ASSERTION_ODDITIES_IMPOSSIBLE_NULL,
            UTAO_JUNIT_ASSERTION_ODDITIES_ASSERT_USED,
            UTAO_JUNIT_ASSERTION_ODDITIES_USE_ASSERT_NULL,
            UTAO_JUNIT_ASSERTION_ODDITIES_USE_ASSERT_NOT_NULL,
            UTAO_JUNIT_ASSERTION_ODDITIES_USE_ASSERT_EQUALS,
            UTAO_JUNIT_ASSERTION_ODDITIES_USE_ASSERT_NOT_EQUALS,
            UTAO_JUNIT_ASSERTION_ODDITIES_NO_ASSERT,
            UTAO_JUNIT_ASSERTION_ODDITIES_USING_DEPRECATED,
            SCA_SUSPICIOUS_CLONE_ALGORITHM,
            WEM_OBSCURING_EXCEPTION,
            LO_LOGGER_LOST_EXCEPTION_STACK_TRACE,
            LO_SUSPECT_LOG_CLASS,
            LO_SUSPECT_LOG_PARAMETER,
            LO_INVALID_FORMATTING_ANCHOR,
            LO_INCORRECT_NUMBER_OF_ANCHOR_PARAMETERS,
            LO_EXCEPTION_WITH_LOGGER_PARMS,
            LO_APPENDED_STRING_IN_FORMAT_STRING,
            LO_INVALID_STRING_FORMAT_NOTATION,
            LO_EMBEDDED_SIMPLE_STRING_FORMAT_IN_FORMAT_STRING,
            LO_TOSTRING_PARAMETER,
            LO_NON_PRIVATE_STATIC_LOGGER,
            IICU_INCORRECT_INTERNAL_CLASS_USE,
            DSOC_DUBIOUS_SET_OF_COLLECTIONS,
            BED_BOGUS_EXCEPTION_DECLARATION,
            BED_HIERARCHICAL_EXCEPTION_DECLARATION,
            UNNC_UNNECESSARY_NEW_NULL_CHECK,
            DTEP_DEPRECATED_TYPESAFE_ENUM_PATTERN,
            SUA_SUSPICIOUS_UNINITIALIZED_ARRAY,
            ITU_INAPPROPRIATE_TOSTRING_USE,
            OC_OVERZEALOUS_CASTING,
            PDP_POORLY_DEFINED_PARAMETER,
            CVAA_CONTRAVARIANT_ARRAY_ASSIGNMENT,
            CVAA_CONTRAVARIANT_ELEMENT_ASSIGNMENT,
            NFF_NON_FUNCTIONAL_FIELD,
            SNG_SUSPICIOUS_NULL_FIELD_GUARD,
            SNG_SUSPICIOUS_NULL_LOCAL_GUARD,
            MDM_RUNTIME_EXIT_OR_HALT,
            MDM_RUNFINALIZATION,
            MDM_THREAD_PRIORITIES,
            MDM_WAIT_WITHOUT_TIMEOUT,
            MDM_SIGNAL_NOT_SIGNALALL,
            MDM_THREAD_FAIRNESS,
            MDM_LOCK_ISLOCKED,
            MDM_SETDEFAULTLOCALE,
            MDM_BIGDECIMAL_EQUALS,
            MDM_RANDOM_SEED,
            MDM_SECURERANDOM,
            ROOM_REFLECTION_ON_OBJECT_METHODS,
            IPU_IMPROPER_PROPERTIES_USE,
            IPU_IMPROPER_PROPERTIES_USE_SETPROPERTY,
            PCAIL_POSSIBLE_CONSTANT_ALLOCATION_IN_LOOP,
            WOC_WRITE_ONLY_COLLECTION_LOCAL,
            WOC_WRITE_ONLY_COLLECTION_FIELD,
            SGSU_SUSPICIOUS_GETTER_SETTER_USE,
            LGO_LINGERING_GRAPHICS_OBJECT,
            STB_STACKED_TRY_BLOCKS,
            CEBE_COMMONS_EQUALS_BUILDER_ISEQUALS,
            CHTH_COMMONS_HASHCODE_BUILDER_TOHASHCODE,
            CSBTS_COMMONS_STRING_BUILDER_TOSTRING,
            CCNE_COMPARE_CLASS_EQUALS_NAME,
            CAAL_CONFUSING_ARRAY_AS_LIST,
            PSC_PRESIZE_COLLECTIONS,
            PSC_SUBOPTIMAL_COLLECTION_SIZING,
            UMTP_UNBOUND_METHOD_TEMPLATE_PARAMETER,
            NPMC_NON_PRODUCTIVE_METHOD_CALL,
            AIOB_ARRAY_INDEX_OUT_OF_BOUNDS,
            AIOB_ARRAY_STORE_TO_NULL_REFERENCE,
            ICA_INVALID_CONSTANT_ARGUMENT,
            CNC_COLLECTION_NAMING_CONFUSION,
            PME_POOR_MANS_ENUM,
            UP_UNUSED_PARAMETER,
            MUC_MODIFYING_UNMODIFIABLE_COLLECTION,
            UJM_UNJITABLE_METHOD,
            HES_LOCAL_EXECUTOR_SERVICE,
            CTU_CONFLICTING_TIME_UNITS,
            CSI_CHAR_SET_ISSUES_USE_STANDARD_CHARSET,
            CSI_CHAR_SET_ISSUES_USE_STANDARD_CHARSET_NAME,
            CSI_CHAR_SET_ISSUES_UNKNOWN_ENCODING,
            CBC_CONTAINS_BASED_CONDITIONAL,
            SLS_SUSPICIOUS_LOOP_SEARCH,
            CRF_CONFLATING_RESOURCES_AND_FILES,
            IOI_DOUBLE_BUFFER_COPY,
            IOI_COPY_WITH_READER,
            IOI_USE_OF_FILE_STREAM_CONSTRUCTORS,
            CCI_CONCURRENT_COLLECTION_ISSUES_USE_PUT_IS_RACY,
            UTWR_USE_TRY_WITH_RESOURCES,
            SSCU_SUSPICIOUS_SHADED_CLASS_USE,
            USFW_UNSYNCHRONIZED_SINGLETON_FIELD_WRITES,
            OI_OPTIONAL_ISSUES_USES_DELAYED_EXECUTION,
            OI_OPTIONAL_ISSUES_CHECKING_REFERENCE,
            OI_OPTIONAL_ISSUES_PRIMITIVE_VARIANT_PREFERRED,
            OI_OPTIONAL_ISSUES_USES_ORELSEGET_WITH_NULL,
            UAC_UNNECESSARY_API_CONVERSION_DATE_TO_INSTANT,
            UAC_UNNECESSARY_API_CONVERSION_FILE_TO_PATH,
            RFI_SET_ACCESSIBLE,
            MUI_CONTAINSKEY_BEFORE_GET,
            MUI_GET_BEFORE_REMOVE,
            MUI_CALLING_SIZE_ON_SUBCONTAINER,
            MUI_NULL_CHECK_ON_MAP_SUBSET_ACCESSOR,
            MUI_USE_CONTAINSKEY,
            LUI_USE_SINGLETON_LIST,
            LUI_USE_COLLECTION_ADD,
            LUI_USE_GET0,
            FII_USE_METHOD_REFERENCE,
            FII_AVOID_CONTAINS_ON_COLLECTED_STREAM,
            FII_USE_ANY_MATCH,
            FII_USE_FIND_FIRST,
            FII_COMBINE_FILTERS,
            FII_AVOID_SIZE_ON_COLLECTED_STREAM,
            SUI_CONTAINS_BEFORE_ADD,
            SUI_CONTAINS_BEFORE_REMOVE"/>
    </Match>
</FindBugsFilter>
