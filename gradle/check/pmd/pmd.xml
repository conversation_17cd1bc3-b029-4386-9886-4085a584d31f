<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         name="pmd rules"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">
    <description>pmd rules</description>

    <rule ref="category/java/bestpractices.xml/AccessorClassGeneration"/>
    <rule ref="category/java/bestpractices.xml/AccessorMethodGeneration"/>
    <rule ref="category/java/bestpractices.xml/AvoidPrintStackTrace"/>
    <rule ref="category/java/bestpractices.xml/AvoidReassigningCatchVariables"/>
    <rule ref="category/java/bestpractices.xml/AvoidReassigningParameters"/>
    <rule ref="category/java/bestpractices.xml/AvoidReassigningLoopVariables"/>
    <rule ref="category/java/bestpractices.xml/ConstantsInInterface"/>
    <rule ref="category/java/bestpractices.xml/DefaultLabelNotLastInSwitchStmt"/>
    <rule ref="category/java/bestpractices.xml/DoubleBraceInitialization"/>
    <rule ref="category/java/bestpractices.xml/ForLoopCanBeForeach"/>
    <rule ref="category/java/bestpractices.xml/ForLoopVariableCount"/>
    <rule ref="category/java/bestpractices.xml/LooseCoupling"/>
    <rule ref="category/java/bestpractices.xml/MissingOverride"/>
    <rule ref="category/java/bestpractices.xml/OneDeclarationPerLine"/>
    <rule ref="category/java/bestpractices.xml/LiteralsFirstInComparisons"/>
    <rule ref="category/java/bestpractices.xml/PreserveStackTrace"/>
    <rule ref="category/java/bestpractices.xml/SystemPrintln"/>
    <rule ref="category/java/bestpractices.xml/UnusedAssignment"/>
    <rule ref="category/java/bestpractices.xml/UnusedFormalParameter"/>
    <rule ref="category/java/bestpractices.xml/UnusedLocalVariable"/>
    <rule ref="category/java/bestpractices.xml/UnusedPrivateField"/>
    <rule ref="category/java/bestpractices.xml/UnusedPrivateMethod"/>
    <rule ref="category/java/bestpractices.xml/UseAssertEqualsInsteadOfAssertTrue"/>
    <rule ref="category/java/bestpractices.xml/UseAssertNullInsteadOfAssertTrue"/>
    <rule ref="category/java/bestpractices.xml/UseAssertSameInsteadOfAssertTrue"/>
    <rule ref="category/java/bestpractices.xml/UseAssertTrueInsteadOfAssertEquals"/>
    <rule ref="category/java/bestpractices.xml/UseCollectionIsEmpty"/>
    <rule ref="category/java/bestpractices.xml/UseStandardCharsets"/>
    <rule ref="category/java/bestpractices.xml/UseTryWithResources"/>

    <rule ref="category/java/codestyle.xml/AvoidProtectedFieldInFinalClass"/>
    <rule ref="category/java/codestyle.xml/AvoidProtectedMethodInFinalClassNotExtending"/>
    <rule ref="category/java/codestyle.xml/BooleanGetMethodName"/>
    <rule ref="category/java/codestyle.xml/ForLoopShouldBeWhileLoop"/>
    <rule ref="category/java/codestyle.xml/IdenticalCatchBranches"/>
<!--    <rule ref="category/java/codestyle.xml/LinguisticNaming">-->
<!--        <properties>-->
<!--            <property name="ignoredAnnotations" value="org.junit.jupiter.api.Test"/>-->
<!--        </properties>-->
<!--    </rule>-->
    <rule ref="category/java/codestyle.xml/UnnecessaryAnnotationValueElement"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryCast"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryConstructor"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryFullyQualifiedName"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryImport"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryLocalBeforeReturn"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryModifier"/>
    <rule ref="category/java/codestyle.xml/UnnecessaryReturn"/>
    <rule ref="category/java/codestyle.xml/UseDiamondOperator"/>
    <rule ref="category/java/codestyle.xml/UselessParentheses"/>
    <rule ref="category/java/codestyle.xml/UselessQualifiedThis"/>
    <rule ref="category/java/codestyle.xml/UseShortArrayInitializer"/>

    <rule ref="category/java/design.xml/AbstractClassWithoutAnyMethod"/>
    <rule ref="category/java/design.xml/AvoidThrowingNewInstanceOfSameException"/>
    <rule ref="category/java/design.xml/AvoidThrowingNullPointerException"/>
    <rule ref="category/java/design.xml/AvoidUncheckedExceptionsInSignatures"/>
    <rule ref="category/java/design.xml/ClassWithOnlyPrivateConstructorsShouldBeFinal"/>
    <rule ref="category/java/design.xml/CollapsibleIfStatements"/>
    <rule ref="category/java/design.xml/DoNotExtendJavaLangError"/>
    <rule ref="category/java/design.xml/ExceptionAsFlowControl"/>
    <rule ref="category/java/design.xml/FinalFieldCouldBeStatic"/>
    <rule ref="category/java/design.xml/ImmutableField"/>
    <rule ref="category/java/design.xml/LogicInversion"/>
    <rule ref="category/java/design.xml/SimplifiedTernary"/>
    <rule ref="category/java/design.xml/SimplifyBooleanAssertion"/>
    <rule ref="category/java/design.xml/SimplifyBooleanExpressions"/>
    <rule ref="category/java/design.xml/SimplifyConditional"/>
    <rule ref="category/java/design.xml/SingularField"/>
    <rule ref="category/java/design.xml/SwitchDensity"/>
    <rule ref="category/java/design.xml/UselessOverridingMethod"/>

    <rule ref="category/java/errorprone.xml/AssignmentInOperand"/>
    <rule ref="category/java/errorprone.xml/AssignmentToNonFinalStatic"/>
    <rule ref="category/java/errorprone.xml/AvoidAssertAsIdentifier"/>
    <rule ref="category/java/errorprone.xml/AvoidBranchingStatementAsLastInLoop"/>
    <rule ref="category/java/errorprone.xml/AvoidCallingFinalize"/>
    <rule ref="category/java/errorprone.xml/AvoidCatchingNPE"/>
    <rule ref="category/java/errorprone.xml/AvoidDecimalLiteralsInBigDecimalConstructor"/>
    <rule ref="category/java/errorprone.xml/AvoidEnumAsIdentifier"/>
    <rule ref="category/java/errorprone.xml/AvoidInstanceofChecksInCatchClause"/>
    <rule ref="category/java/errorprone.xml/AvoidLosingExceptionInformation"/>
    <rule ref="category/java/errorprone.xml/AvoidMultipleUnaryOperators"/>
    <rule ref="category/java/errorprone.xml/AvoidUsingOctalValues"/>
    <rule ref="category/java/errorprone.xml/BadComparison"/>
    <rule ref="category/java/errorprone.xml/BrokenNullCheck"/>
    <rule ref="category/java/errorprone.xml/CheckSkipResult"/>
    <rule ref="category/java/errorprone.xml/ClassCastExceptionWithToArray"/>
    <rule ref="category/java/errorprone.xml/CompareObjectsWithEquals"/>
    <rule ref="category/java/errorprone.xml/ConstructorCallsOverridableMethod"/>
    <rule ref="category/java/errorprone.xml/DetachedTestCase"/>
    <rule ref="category/java/errorprone.xml/DoNotCallGarbageCollectionExplicitly"/>
    <rule ref="category/java/errorprone.xml/DoNotExtendJavaLangThrowable"/>
    <rule ref="category/java/errorprone.xml/DoNotThrowExceptionInFinally"/>
    <rule ref="category/java/errorprone.xml/DontImportSun"/>
    <rule ref="category/java/errorprone.xml/DontUseFloatTypeForLoopIndices"/>
    <rule ref="category/java/errorprone.xml/EmptyCatchBlock"/>
    <rule ref="category/java/errorprone.xml/EmptyFinallyBlock"/>
    <rule ref="category/java/errorprone.xml/EmptyIfStmt"/>
    <rule ref="category/java/errorprone.xml/EmptyInitializer"/>
    <rule ref="category/java/errorprone.xml/EmptyStatementBlock"/>
    <rule ref="category/java/errorprone.xml/EmptyStatementNotInLoop"/>
    <rule ref="category/java/errorprone.xml/EmptySwitchStatements"/>
    <rule ref="category/java/errorprone.xml/EmptySynchronizedBlock"/>
    <rule ref="category/java/errorprone.xml/EmptyTryBlock"/>
    <rule ref="category/java/errorprone.xml/EmptyWhileStmt"/>
    <rule ref="category/java/errorprone.xml/EqualsNull"/>
    <rule ref="category/java/errorprone.xml/IdempotentOperations"/>
    <rule ref="category/java/errorprone.xml/InstantiationToGetClass"/>
    <rule ref="category/java/errorprone.xml/InvalidLogMessageFormat"/>
    <rule ref="category/java/errorprone.xml/JumbledIncrementer"/>
    <rule ref="category/java/errorprone.xml/MisplacedNullCheck"/>
    <rule ref="category/java/errorprone.xml/MissingBreakInSwitch"/>
    <rule ref="category/java/errorprone.xml/NonStaticInitializer"/>
    <rule ref="category/java/errorprone.xml/OverrideBothEqualsAndHashcode"/>
    <rule ref="category/java/errorprone.xml/ReturnFromFinallyBlock"/>
    <rule ref="category/java/errorprone.xml/SingleMethodSingleton"/>
    <rule ref="category/java/errorprone.xml/SingletonClassReturningNewInstance"/>
    <rule ref="category/java/errorprone.xml/StringBufferInstantiationWithChar"/>
    <rule ref="category/java/errorprone.xml/SuspiciousEqualsMethodName"/>
    <rule ref="category/java/errorprone.xml/SuspiciousHashcodeMethodName"/>
    <rule ref="category/java/errorprone.xml/SuspiciousOctalEscape"/>
    <rule ref="category/java/errorprone.xml/TestClassWithoutTestCases"/>
    <rule ref="category/java/errorprone.xml/UnconditionalIfStatement"/>
    <rule ref="category/java/errorprone.xml/UnnecessaryBooleanAssertion"/>
    <rule ref="category/java/errorprone.xml/UnnecessaryCaseChange"/>
    <rule ref="category/java/errorprone.xml/UnnecessaryConversionTemporary"/>
    <rule ref="category/java/errorprone.xml/UnusedNullCheckInEquals"/>
    <rule ref="category/java/errorprone.xml/UseCorrectExceptionLogging"/>
    <rule ref="category/java/errorprone.xml/UseEqualsToCompareStrings"/>
    <rule ref="category/java/errorprone.xml/UselessOperationOnImmutable"/>
    <rule ref="category/java/errorprone.xml/UseLocaleWithCaseConversions"/>
    <rule ref="category/java/errorprone.xml/UseProperClassLoader"/>

    <rule ref="category/java/multithreading.xml/AvoidSynchronizedAtMethodLevel"/>
    <rule ref="category/java/multithreading.xml/AvoidThreadGroup"/>
    <rule ref="category/java/multithreading.xml/DontCallThreadRun"/>
    <rule ref="category/java/multithreading.xml/UnsynchronizedStaticFormatter"/>
    <rule ref="category/java/multithreading.xml/UseNotifyAllInsteadOfNotify"/>

    <rule ref="category/java/performance.xml/AddEmptyString"/>
    <rule ref="category/java/performance.xml/AppendCharacterWithChar"/>
    <rule ref="category/java/performance.xml/AvoidArrayLoops"/>
    <rule ref="category/java/performance.xml/AvoidCalendarDateCreation"/>
    <rule ref="category/java/performance.xml/AvoidFileStream"/>
    <rule ref="category/java/performance.xml/BigIntegerInstantiation"/>
    <rule ref="category/java/performance.xml/BooleanInstantiation"/>
    <rule ref="category/java/performance.xml/ByteInstantiation"/>
    <rule ref="category/java/performance.xml/ConsecutiveAppendsShouldReuse"/>
    <rule ref="category/java/performance.xml/ConsecutiveLiteralAppends"/>
    <rule ref="category/java/performance.xml/InefficientEmptyStringCheck"/>
    <rule ref="category/java/performance.xml/InefficientStringBuffering"/>
    <rule ref="category/java/performance.xml/InsufficientStringBufferDeclaration"/>
    <rule ref="category/java/performance.xml/IntegerInstantiation"/>
    <rule ref="category/java/performance.xml/LongInstantiation"/>
    <rule ref="category/java/performance.xml/OptimizableToArrayCall"/>
    <rule ref="category/java/performance.xml/ShortInstantiation"/>
    <rule ref="category/java/performance.xml/StringInstantiation"/>
    <rule ref="category/java/performance.xml/StringToString"/>
    <rule ref="category/java/performance.xml/TooFewBranchesForASwitchStatement"/>
    <rule ref="category/java/performance.xml/UnnecessaryWrapperObjectCreation"/>
    <rule ref="category/java/performance.xml/UseArraysAsList"/>
    <rule ref="category/java/performance.xml/UseIndexOfChar"/>
    <rule ref="category/java/performance.xml/UselessStringValueOf"/>
    <rule ref="category/java/performance.xml/UseStringBufferForStringAppends"/>
    <rule ref="category/java/performance.xml/UseStringBufferLength"/>
</ruleset>
