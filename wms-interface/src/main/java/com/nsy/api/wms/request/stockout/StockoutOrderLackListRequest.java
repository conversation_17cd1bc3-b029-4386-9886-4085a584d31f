package com.nsy.api.wms.request.stockout;

import com.nsy.api.wms.request.base.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@ApiModel(value = "StockoutOrderLackListRequest", description = "出库单缺货列表查询request")
public class StockoutOrderLackListRequest extends PageRequest {

    @ApiModelProperty(value = "出库单缺货Id", name = "stockoutOrderLackIds")
    private List<Integer> stockoutOrderLackIds;

    @ApiModelProperty(value = "仓库", name = "spaceId")
    private Integer spaceId;

    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    @ApiModelProperty(value = "波次号", name = "batchId")
    private Integer batchId;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "扫描任务id", name = "scanTaskId")
    private Integer scanTaskId;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "工作区域", name = "workspace")
    private String workspace;

    @ApiModelProperty(value = "工作区域多选", name = "workspaceList")
    private List<String> workspaceList;

    @ApiModelProperty(value = "缺货来源", name = "lackSource")
    private String lackSource;

    @ApiModelProperty(value = "扫描台", name = "scanType")
    private String scanType;

    @ApiModelProperty(value = "开始时间", name = "createStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createStartDate;

    @ApiModelProperty(value = "结束时间", name = "createEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createEndDate;

    public List<Integer> getStockoutOrderLackIds() {
        return stockoutOrderLackIds;
    }

    public void setStockoutOrderLackIds(List<Integer> stockoutOrderLackIds) {
        this.stockoutOrderLackIds = stockoutOrderLackIds;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getScanTaskId() {
        return scanTaskId;
    }

    public void setScanTaskId(Integer scanTaskId) {
        this.scanTaskId = scanTaskId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }

    public String getLackSource() {
        return lackSource;
    }

    public void setLackSource(String lackSource) {
        this.lackSource = lackSource;
    }

    public String getScanType() {
        return scanType;
    }

    public void setScanType(String scanType) {
        this.scanType = scanType;
    }

    public List<String> getWorkspaceList() {
        return workspaceList;
    }

    public void setWorkspaceList(List<String> workspaceList) {
        this.workspaceList = workspaceList;
    }

    public Date getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(Date createStartDate) {
        this.createStartDate = createStartDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }
}
