package com.nsy.api.wms.response.stockout;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @version 1.0
 * <AUTHOR>
 * @date 2022/8/23 10:34
 */
public class ProformaInvoiceItemResponse implements Serializable {

    private static final long serialVersionUID = -5612460874280640580L;

    private String customsDeclareEn;

    private String fabricTypeEn;

    private String spinType;

    private String hsCode;

    private String descriptionOfGoods;

    private Integer qty;

    private String customsDeclareUnit;

    private BigDecimal userPrice;

    public String getCustomsDeclareEn() {
        return customsDeclareEn;
    }

    public void setCustomsDeclareEn(String customsDeclareEn) {
        this.customsDeclareEn = customsDeclareEn;
    }

    public String getFabricTypeEn() {
        return fabricTypeEn;
    }

    public void setFabricTypeEn(String fabricTypeEn) {
        this.fabricTypeEn = fabricTypeEn;
    }

    public String getSpinType() {
        return spinType;
    }

    public void setSpinType(String spinType) {
        this.spinType = spinType;
    }

    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

    public String getDescriptionOfGoods() {
        return descriptionOfGoods;
    }

    public void setDescriptionOfGoods(String descriptionOfGoods) {
        this.descriptionOfGoods = descriptionOfGoods;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getCustomsDeclareUnit() {
        return customsDeclareUnit;
    }

    public void setCustomsDeclareUnit(String customsDeclareUnit) {
        this.customsDeclareUnit = customsDeclareUnit;
    }

    public BigDecimal getUserPrice() {
        return userPrice;
    }

    public void setUserPrice(BigDecimal userPrice) {
        this.userPrice = userPrice;
    }

}
