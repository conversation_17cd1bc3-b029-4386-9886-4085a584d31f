package com.nsy.api.wms.request.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-11-13 10:09
 */
@ApiModel(value = "StockinQaTaskBatchPageRequest", description = "质检批次分页参数")
public class StockinQaTaskBatchPageRequest extends PageRequest {

    @ApiModelProperty("批次id")
    private Integer batchId;
    
    @ApiModelProperty("任务类型")
    private String taskType;

    @ApiModelProperty("仓库ID")
    private Integer spaceId;

    @ApiModelProperty("商品ID")
    private Integer productId;

    @ApiModelProperty("spuList")
    private List<String> spuList;

    @ApiModelProperty("skuList")
    private List<String> skuList;

    @ApiModelProperty("二级分类ID")
    private Integer categoryId;

    @ApiModelProperty("批次状态")
    private String batchStatus;

    @ApiModelProperty("是否分配 0:否 1:是")
    private Integer isDistribution;

    @ApiModelProperty("负责人")
    private String taskOwner;

    @ApiModelProperty("用户ID")
    private Integer userId;

    @ApiModelProperty("质检分组Id")
    private Integer groupId;

    @ApiModelProperty("质检分组")
    private List<Integer> groupIdList;

    @ApiModelProperty("分组人员集合")
    private List<Integer> groupUserIdList;

    /**
     * 测量完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "批次创建开始时间", name = "createDate")
    private Date createDateStart;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "批次创建完成时间", name = "createDate")
    private Date createDateEnd;

    /**
     * 测量完成时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "批次分配开始时间", name = "distributionDateStart")
    private Date distributionDateStart;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "批次分配完成时间", name = "distributionDateDateEnd")
    private Date distributionDateDateEnd;


    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getBatchStatus() {
        return batchStatus;
    }

    public void setBatchStatus(String batchStatus) {
        this.batchStatus = batchStatus;
    }

    public Integer getIsDistribution() {
        return isDistribution;
    }

    public void setIsDistribution(Integer isDistribution) {
        this.isDistribution = isDistribution;
    }

    public String getTaskOwner() {
        return taskOwner;
    }

    public void setTaskOwner(String taskOwner) {
        this.taskOwner = taskOwner;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public Date getDistributionDateStart() {
        return distributionDateStart;
    }

    public void setDistributionDateStart(Date distributionDateStart) {
        this.distributionDateStart = distributionDateStart;
    }

    public Date getDistributionDateDateEnd() {
        return distributionDateDateEnd;
    }

    public void setDistributionDateDateEnd(Date distributionDateDateEnd) {
        this.distributionDateDateEnd = distributionDateDateEnd;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public List<String> getSpuList() {
        return spuList;
    }

    public void setSpuList(List<String> spuList) {
        this.spuList = spuList;
    }

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public List<Integer> getGroupUserIdList() {
        return groupUserIdList;
    }

    public void setGroupUserIdList(List<Integer> groupUserIdList) {
        this.groupUserIdList = groupUserIdList;
    }

    public List<Integer> getGroupIdList() {
        return groupIdList;
    }

    public void setGroupIdList(List<Integer> groupIdList) {
        this.groupIdList = groupIdList;
    }
} 