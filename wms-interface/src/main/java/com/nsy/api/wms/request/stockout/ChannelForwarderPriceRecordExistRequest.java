package com.nsy.api.wms.request.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-03-22 15:59:31
 */
@ApiModel(value = "ChannelForwarderPriceRecordExistRequest", description = "查询是否存在")
public class ChannelForwarderPriceRecordExistRequest {
    private String logisticsNo;

    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    @NotBlank
    private String logisticsCompany;

    /**
     * 日期
     */
    @ApiModelProperty("运价日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull
    private Date priceDate;

    private String countryCode;

    private String postCode;

    private BigDecimal predictWeight;

    private String destinationFulfillmentCenterId;

    private Integer shipmentSkuQty;

    private Integer targetCountryFreightId;

    private BigDecimal targetPrice;

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Integer getTargetCountryFreightId() {
        return targetCountryFreightId;
    }

    public void setTargetCountryFreightId(Integer targetCountryFreightId) {
        this.targetCountryFreightId = targetCountryFreightId;
    }

    public BigDecimal getTargetPrice() {
        return targetPrice;
    }

    public void setTargetPrice(BigDecimal targetPrice) {
        this.targetPrice = targetPrice;
    }

    public Integer getShipmentSkuQty() {
        return shipmentSkuQty;
    }

    public void setShipmentSkuQty(Integer shipmentSkuQty) {
        this.shipmentSkuQty = shipmentSkuQty;
    }

    public String getDestinationFulfillmentCenterId() {
        return destinationFulfillmentCenterId;
    }

    public void setDestinationFulfillmentCenterId(String destinationFulfillmentCenterId) {
        this.destinationFulfillmentCenterId = destinationFulfillmentCenterId;
    }

    public BigDecimal getPredictWeight() {
        return predictWeight;
    }

    public void setPredictWeight(BigDecimal predictWeight) {
        this.predictWeight = predictWeight;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Date getPriceDate() {
        return priceDate;
    }

    public void setPriceDate(Date priceDate) {
        this.priceDate = priceDate;
    }
}

