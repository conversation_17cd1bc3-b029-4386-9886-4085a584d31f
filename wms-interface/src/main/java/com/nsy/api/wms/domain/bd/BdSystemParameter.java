package com.nsy.api.wms.domain.bd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "BdSystemParameter", description = "系统参数配置")
public class BdSystemParameter implements Serializable {
    private static final long serialVersionUID = 4511855945504784255L;
    /**
     * 配置键
     */
    @ApiModelProperty(value = "配置键", name = "configKey")
    private String configKey;

    /**
     * 配置名
     */
    @ApiModelProperty(value = "配置名", name = "configName")
    private String configName;

    /**
     * 配置值
     */
    @ApiModelProperty(value = "配置值", name = "configValue")
    private String configValue;

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }
}
