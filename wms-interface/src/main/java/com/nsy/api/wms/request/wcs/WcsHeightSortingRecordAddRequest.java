package com.nsy.api.wms.request.wcs;

import java.math.BigDecimal;

/**
 * 测量高度分拣记录新增请求DTO
 * 用于接收客户端提交的测量高度分拣记录数据
 * 包含商品基础信息、尺寸重量数据、测量结果等信息
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class WcsHeightSortingRecordAddRequest {

    /**
     * 地区代码，用于多租户数据隔离
     * 示例值：GUANGZHOU、QUANZHOU、DONGGUAN等
     * 必填字段，用于确定数据归属
     */
    private String location;

    /**
     * 商品SKU编码，商品规格的唯一标识
     * 格式：通常为字母数字组合，如 "TEST-SKU-001"
     * 必填字段，用于关联商品基础信息
     */
    private String sku;

    /**
     * 商品条形码，用于扫描识别
     * 格式：支持多种条码格式，如EAN-13、Code128等
     * 可选字段，可能与SKU相同或不同
     */
    private String barcode;

    /**
     * 商品长度，单位：厘米(CM)
     * 范围：通常为正数，精度到小数点后2位
     * 商品包装后的实际长度尺寸
     */
    private BigDecimal length;

    /**
     * 商品宽度，单位：厘米(CM)
     * 范围：通常为正数，精度到小数点后2位
     * 商品包装后的实际宽度尺寸
     */
    private BigDecimal width;

    /**
     * 商品高度，单位：厘米(CM)
     * 范围：通常为正数，精度到小数点后2位
     * 商品包装后的实际高度尺寸，是重点测量对象
     */
    private BigDecimal height;

    /**
     * 商品重量，单位：千克(KG)
     * 范围：通常为正数，精度到小数点后3位
     * 商品包装后的实际重量
     */
    private BigDecimal weight;

    /**
     * 测量类型，标识本次测量的方式
     * 取值：AUTO(自动测量)、MANUAL(手动测量)、SYSTEM(系统录入)等
     * 用于区分不同的数据来源和测量精度
     */
    private String measureType;

    /**
     * 体积重，单位：千克(KG)
     * 范围：通常为正数，精度到小数点后3位
     * 根据商品尺寸按照标准公式计算的体积重量
     * 公式通常为：长×宽×高÷体积重系数
     */
    private BigDecimal volumeWeight;

    /**
     * FBA配送费，单位：元(RMB)
     * 范围：通常为正数，精度到小数点后2位
     * 亚马逊FBA(Fulfillment by Amazon)服务费用
     * 根据商品尺寸和重量计算得出
     */
    private BigDecimal fbaCost;

    /**
     * 测量高度，单位：厘米(CM)
     * 范围：通常为正数，精度到小数点后2位
     * WCS系统实际测量得到的商品高度
     * 用于与预设高度进行对比，判断是否符合预期
     */
    private BigDecimal measureHeight;

    /**
     * 测量体积重，单位：千克(KG)
     * 范围：通常为正数，精度到小数点后3位
     * 根据测量得到的实际尺寸重新计算的体积重量
     * 用于与预设体积重进行对比
     */
    private BigDecimal measureVolumeWeight;

    /**
     * 测量FBA配送费，单位：元(RMB)
     * 范围：通常为正数，精度到小数点后2位
     * 根据测量结果重新计算的FBA费用
     * 用于成本核算和费用对比
     */
    private BigDecimal measureFbaCost;

    /**
     * 分拣口状态，表示商品测量结果的最终判定
     * 取值：
     * - STANDARD: 达标，商品尺寸符合预期范围
     * - NO_STANDARD: 不达标，商品尺寸超出预期范围
     * - ERROR: 异常，测量过程中出现错误或数据异常
     * 用于后续的分拣处理和异常跟踪
     */
    private String sortingPort;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getMeasureType() {
        return measureType;
    }

    public void setMeasureType(String measureType) {
        this.measureType = measureType;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public BigDecimal getMeasureHeight() {
        return measureHeight;
    }

    public void setMeasureHeight(BigDecimal measureHeight) {
        this.measureHeight = measureHeight;
    }

    public BigDecimal getMeasureVolumeWeight() {
        return measureVolumeWeight;
    }

    public void setMeasureVolumeWeight(BigDecimal measureVolumeWeight) {
        this.measureVolumeWeight = measureVolumeWeight;
    }

    public BigDecimal getMeasureFbaCost() {
        return measureFbaCost;
    }

    public void setMeasureFbaCost(BigDecimal measureFbaCost) {
        this.measureFbaCost = measureFbaCost;
    }

    public String getSortingPort() {
        return sortingPort;
    }

    public void setSortingPort(String sortingPort) {
        this.sortingPort = sortingPort;
    }
} 