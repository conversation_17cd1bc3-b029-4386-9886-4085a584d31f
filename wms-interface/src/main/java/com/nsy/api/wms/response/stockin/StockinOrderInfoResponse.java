package com.nsy.api.wms.response.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockinOrderInfoResponse", description = "入库单信息response")
public class StockinOrderInfoResponse {

    @ApiModelProperty(value = "入库单id", name = "stockinOrderId")
    private Integer stockinOrderId;

    @ApiModelProperty(value = "入库单号", name = "stockinOrderNo")
    private String stockinOrderNo;

    @ApiModelProperty(value = "出库箱号", name = "supplierDeliveryBoxCode")
    private String supplierDeliveryBoxCode;

    @ApiModelProperty(value = "入库类型", name = "stockinType")
    private String stockinType;

    @ApiModelProperty(value = "仓库", name = "spaceName")
    private String spaceName;

    @ApiModelProperty(value = "入库类型描述", name = "stockinTypeStr")
    private String stockinTypeStr;

    @ApiModelProperty(value = "工厂Id", name = "supplierId")
    private Integer supplierId;
    @ApiModelProperty(value = "工厂", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "箱号", name = "boxIndex")
    private Integer boxIndex;

    @ApiModelProperty(value = "采购计划单", name = "purchasePlanNo")
    private String purchasePlanNo;

    @ApiModelProperty(value = "分公司采购计划单", name = "branchPurchasePlanNo")
    private String branchPurchasePlanNo;

    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    private String supplierDeliveryNo;

    @ApiModelProperty(value = "收货员", name = "operator")
    private String operator;

    @ApiModelProperty(value = "创建时间", name = "createDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @ApiModelProperty(value = "预入库总件数", name = "expectedQty")
    private Integer expectedQty;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "状态描述", name = "statusStr")
    private String statusStr;

    @ApiModelProperty(value = "预到货时间", name = "planArriveDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planArriveDate;

    @ApiModelProperty(value = "收货中时间", name = "receivingDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receivingDate;

    @ApiModelProperty(value = "待质检时间", name = "waitQaDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date waitQaDate;

    @ApiModelProperty(value = "质检中时间", name = "qaDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date qaDate;

    @ApiModelProperty(value = "待上架时间", name = "waitShelveDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date waitShelveDate;

    @ApiModelProperty(value = "上架中时间", name = "shelvingDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelvingDate;

    @ApiModelProperty(value = "上架完成时间", name = "shelveOperateEndDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelveOperateEndDate;

    @ApiModelProperty(value = "入库单完成时间", name = "shelveOperateEndDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completedDate;

    @ApiModelProperty(value = "上架完成时间", name = "completeShelvedDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeShelvedDate;

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getStockinOrderNo() {
        return stockinOrderNo;
    }

    public void setStockinOrderNo(String stockinOrderNo) {
        this.stockinOrderNo = stockinOrderNo;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public Date getCompletedDate() {
        return completedDate;
    }

    public void setCompletedDate(Date completedDate) {
        this.completedDate = completedDate;
    }

    public Integer getStockinOrderId() {
        return stockinOrderId;
    }

    public void setStockinOrderId(Integer stockinOrderId) {
        this.stockinOrderId = stockinOrderId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getStockinType() {
        return stockinType;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public Integer getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(Integer boxIndex) {
        this.boxIndex = boxIndex;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(Integer expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Date getPlanArriveDate() {
        return planArriveDate;
    }

    public void setPlanArriveDate(Date planArriveDate) {
        this.planArriveDate = planArriveDate;
    }

    public Date getReceivingDate() {
        return receivingDate;
    }

    public void setReceivingDate(Date receivingDate) {
        this.receivingDate = receivingDate;
    }

    public Date getWaitQaDate() {
        return waitQaDate;
    }

    public void setWaitQaDate(Date waitQaDate) {
        this.waitQaDate = waitQaDate;
    }

    public Date getQaDate() {
        return qaDate;
    }

    public void setQaDate(Date qaDate) {
        this.qaDate = qaDate;
    }

    public Date getShelvingDate() {
        return shelvingDate;
    }

    public void setShelvingDate(Date shelvingDate) {
        this.shelvingDate = shelvingDate;
    }

    public Date getWaitShelveDate() {
        return waitShelveDate;
    }

    public void setWaitShelveDate(Date waitShelveDate) {
        this.waitShelveDate = waitShelveDate;
    }

    public Date getShelveOperateEndDate() {
        return shelveOperateEndDate;
    }

    public void setShelveOperateEndDate(Date shelveOperateEndDate) {
        this.shelveOperateEndDate = shelveOperateEndDate;
    }

    public String getStockinTypeStr() {
        return stockinTypeStr;
    }

    public void setStockinTypeStr(String stockinTypeStr) {
        this.stockinTypeStr = stockinTypeStr;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public Date getCompleteShelvedDate() {
        return completeShelvedDate;
    }

    public void setCompleteShelvedDate(Date completeShelvedDate) {
        this.completeShelvedDate = completeShelvedDate;
    }

    public String getBranchPurchasePlanNo() {
        return branchPurchasePlanNo;
    }

    public void setBranchPurchasePlanNo(String branchPurchasePlanNo) {
        this.branchPurchasePlanNo = branchPurchasePlanNo;
    }
}
