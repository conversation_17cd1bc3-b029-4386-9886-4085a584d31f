package com.nsy.api.wms.domain.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "StockLendInfo", description = "存货借用列表")
public class StockLendInfoCount {


    @ApiModelProperty(value = "借出数量", name = "lendQty")
    private Integer lendQty;

    @ApiModelProperty(value = "申请借货数量", name = "applyLendQty")
    private Integer applyLendQty;

    @ApiModelProperty(value = "归还数量", name = "returnQty")
    private Integer returnQty;

    public Integer getLendQty() {
        return lendQty;
    }

    public void setLendQty(Integer lendQty) {
        this.lendQty = lendQty;
    }

    public Integer getApplyLendQty() {
        return applyLendQty;
    }

    public void setApplyLendQty(Integer applyLendQty) {
        this.applyLendQty = applyLendQty;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }
}
