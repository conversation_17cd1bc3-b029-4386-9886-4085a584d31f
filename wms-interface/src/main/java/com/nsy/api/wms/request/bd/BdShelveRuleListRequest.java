package com.nsy.api.wms.request.bd;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "BdShelveRuleListRequest", description = "上架规则列表查询request")
public class BdShelveRuleListRequest extends PageRequest implements Serializable {

    /** 上架规则编码 */
    @ApiModelProperty(value = "上架规则编码", name = "shelveRuleCode")
    private String shelveRuleCode;

    /** 上架规则名称 */
    @ApiModelProperty(value = "上架规则名称", name = "shelveRuleName")
    private String shelveRuleName;

    /** 仓库id */
    @ApiModelProperty(value = "仓库id", name = "spaceIds")
    private List<Integer> spaceIds;

    /** 上架规则级别,product,spec,category */
    @ApiModelProperty(value = "上架规则级别", name = "shelveRuleType")
    private String shelveRuleType;

    /** 上架规则名称 */
    @ApiModelProperty(value = "入库类型", name = "stockinType")
    private String stockinType;

    public String getStockinType() {
        return stockinType;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public String getShelveRuleCode() {
        return shelveRuleCode;
    }

    public void setShelveRuleCode(String shelveRuleCode) {
        this.shelveRuleCode = shelveRuleCode;
    }

    public String getShelveRuleName() {
        return shelveRuleName;
    }

    public void setShelveRuleName(String shelveRuleName) {
        this.shelveRuleName = shelveRuleName;
    }

    public List<Integer> getSpaceIds() {
        return spaceIds;
    }

    public void setSpaceIds(List<Integer> spaceIds) {
        this.spaceIds = spaceIds;
    }

    public String getShelveRuleType() {
        return shelveRuleType;
    }

    public void setShelveRuleType(String shelveRuleType) {
        this.shelveRuleType = shelveRuleType;
    }
}
