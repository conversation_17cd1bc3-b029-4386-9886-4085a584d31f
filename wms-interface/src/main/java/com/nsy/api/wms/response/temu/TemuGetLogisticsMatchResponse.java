package com.nsy.api.wms.response.temu;


import java.math.BigDecimal;
import java.util.List;

public class TemuGetLogisticsMatchResponse {
    private List<ResultItem> result;

    public List<ResultItem> getResult() {
        return result;
    }

    public void setResult(List<ResultItem> result) {
        this.result = result;
    }

    public static class ResultItem {
        //最大预估运费（单位 元）
        private BigDecimal maxChargeAmount;
        //预测 ID
        private Long predictId;
        //最大预估商家承担运费（单位元）
        private BigDecimal maxSupplierChargeAmount;
        //最小预估商家承担运费（单位元）
        private BigDecimal minSupplierChargeAmount;
        //最晚预约时间
        private Long latestAppointmentTime;
        //快递公司 Id
        private Long expressCompanyId;
        //最小预估运费（单位 元）
        private BigDecimal minChargeAmount;
        //快递公司名称
        private String expressCompanyName;

        public BigDecimal getMaxChargeAmount() {
            return maxChargeAmount;
        }

        public void setMaxChargeAmount(BigDecimal maxChargeAmount) {
            this.maxChargeAmount = maxChargeAmount;
        }

        public Long getPredictId() {
            return predictId;
        }

        public void setPredictId(Long predictId) {
            this.predictId = predictId;
        }

        public BigDecimal getMaxSupplierChargeAmount() {
            return maxSupplierChargeAmount;
        }

        public void setMaxSupplierChargeAmount(BigDecimal maxSupplierChargeAmount) {
            this.maxSupplierChargeAmount = maxSupplierChargeAmount;
        }

        public BigDecimal getMinSupplierChargeAmount() {
            return minSupplierChargeAmount;
        }

        public void setMinSupplierChargeAmount(BigDecimal minSupplierChargeAmount) {
            this.minSupplierChargeAmount = minSupplierChargeAmount;
        }

        public Long getLatestAppointmentTime() {
            return latestAppointmentTime;
        }

        public void setLatestAppointmentTime(Long latestAppointmentTime) {
            this.latestAppointmentTime = latestAppointmentTime;
        }

        public Long getExpressCompanyId() {
            return expressCompanyId;
        }

        public void setExpressCompanyId(Long expressCompanyId) {
            this.expressCompanyId = expressCompanyId;
        }

        public BigDecimal getMinChargeAmount() {
            return minChargeAmount;
        }

        public void setMinChargeAmount(BigDecimal minChargeAmount) {
            this.minChargeAmount = minChargeAmount;
        }

        public String getExpressCompanyName() {
            return expressCompanyName;
        }

        public void setExpressCompanyName(String expressCompanyName) {
            this.expressCompanyName = expressCompanyName;
        }
    }


}
