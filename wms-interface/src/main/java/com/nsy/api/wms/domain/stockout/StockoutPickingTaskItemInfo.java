package com.nsy.api.wms.domain.stockout;

import java.util.Date;

public class StockoutPickingTaskItemInfo {

    /** 商品Id */
    private Integer productId;

    /** 规格编码Id */
    private Integer specId;

    /** sku */
    private String sku;
    
    private String fnSku;

    /** 定制款sku */
    private String customerSku;

    /** 条形码 */
    private String barcode;

    /** 库区id */
    private Integer spaceAreaId;

    /** 库区名称 */
    private String spaceAreaName;

    /** 库位Id */
    private Integer positionId;

    /** 库位编码 */
    private String positionCode;

    /** 待拣数量 */
    private Integer expectedQty;

    private Integer sort;

    /**
     * 是否缺货 0--否  1--是
     */
    private Integer isLack;

    private Integer lackQty;

    /**
     * 加工完成数 = 合格数 + 次品数
     */
    private Integer processCompleteQty;

    private Integer processBadQty;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 开始时间
     */
    private Date operatorDate;

    public Integer getIsLack() {
        return isLack;
    }

    public void setIsLack(Integer isLack) {
        this.isLack = isLack;
    }

    public Integer getLackQty() {
        return lackQty;
    }

    public void setLackQty(Integer lackQty) {
        this.lackQty = lackQty;
    }

    public Integer getProcessCompleteQty() {
        return processCompleteQty;
    }

    public void setProcessCompleteQty(Integer processCompleteQty) {
        this.processCompleteQty = processCompleteQty;
    }

    public Integer getProcessBadQty() {
        return processBadQty;
    }

    public void setProcessBadQty(Integer processBadQty) {
        this.processBadQty = processBadQty;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperatorDate() {
        return operatorDate;
    }

    public void setOperatorDate(Date operatorDate) {
        this.operatorDate = operatorDate;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCustomerSku() {
        return customerSku;
    }

    public void setCustomerSku(String customerSku) {
        this.customerSku = customerSku;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(Integer expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Integer getSpaceAreaId() {
        return spaceAreaId;
    }

    public void setSpaceAreaId(Integer spaceAreaId) {
        this.spaceAreaId = spaceAreaId;
    }

    public String getFnSku() {
        return fnSku;
    }

    public void setFnSku(String fnSku) {
        this.fnSku = fnSku;
    }
}
