package com.nsy.api.wms.request.bd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

@ApiModel(value = "SystemPackageManageStatusRequest", description = "配置是否发布request")
public class SystemPackageManageStatusRequest extends BdIsDeletedRequest {

    @NotNull
    @ApiModelProperty(value = "id", name = "packageManageId", required = true)
    Integer packageManageId;

    public Integer getPackageManageId() {
        return packageManageId;
    }

    public void setPackageManageId(Integer packageManageId) {
        this.packageManageId = packageManageId;
    }
}
