package com.nsy.api.wms.request.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 装箱清单绑定客户发货信息请求
 */
@ApiModel(value = "StockoutShipmentBindCustomerShipmentRequest", description = "装箱清单绑定客户发货信息请求")
public class StockoutShipmentBindCustomerShipmentRequest {

    @ApiModelProperty(value = "装箱清单ID列表", name = "shipmentIds", required = true)
    @NotNull(message = "装箱清单ID列表不能为空")
    @Size(min = 1, message = "至少选择一个箱子")
    private List<Integer> shipmentIds;

    @ApiModelProperty(value = "FBA Shipment ID", name = "fbaShipmentId", required = true)
    @NotBlank(message = "FBA Shipment ID不能为空")
    private String fbaShipmentId;

    @ApiModelProperty(value = "亚马逊仓库ID", name = "destinationFulfillmentCenterId")
    private String destinationFulfillmentCenterId;

    @ApiModelProperty(value = "亚马逊引用ID", name = "amazonReferenceId")
    private String amazonReferenceId;

    public List<Integer> getShipmentIds() {
        return shipmentIds;
    }

    public void setShipmentIds(List<Integer> shipmentIds) {
        this.shipmentIds = shipmentIds;
    }

    public String getFbaShipmentId() {
        return fbaShipmentId;
    }

    public void setFbaShipmentId(String fbaShipmentId) {
        this.fbaShipmentId = fbaShipmentId;
    }

    public String getDestinationFulfillmentCenterId() {
        return destinationFulfillmentCenterId;
    }

    public void setDestinationFulfillmentCenterId(String destinationFulfillmentCenterId) {
        this.destinationFulfillmentCenterId = destinationFulfillmentCenterId;
    }

    public String getAmazonReferenceId() {
        return amazonReferenceId;
    }

    public void setAmazonReferenceId(String amazonReferenceId) {
        this.amazonReferenceId = amazonReferenceId;
    }
}
