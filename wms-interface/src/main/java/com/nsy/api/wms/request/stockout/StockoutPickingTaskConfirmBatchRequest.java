package com.nsy.api.wms.request.stockout;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutPickingTaskConfirmBatchRequest", description = "批量拣货确认request")
public class StockoutPickingTaskConfirmBatchRequest {

    @ApiModelProperty(value = "拣货任务id列表", name = "taskIdList")
    private List<Integer> taskIdList;

    public List<Integer> getTaskIdList() {
        return taskIdList;
    }

    public void setTaskIdList(List<Integer> taskIdList) {
        this.taskIdList = taskIdList;
    }
}
