package com.nsy.api.wms.enumeration;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/10 10:59
 */
public enum FlowDefinitionEnum {

    PRODUCT_TRY_ON("wsm_product_try_on", "商品试穿任务"),

    QA_ORDER("wms_qa_order", "质检单流程"),

    UNKNOWN("unknown", "未知");

    FlowDefinitionEnum(String processDefinitionKey, String processName) {
        this.processDefinitionKey = processDefinitionKey;
        this.processName = processName;
    }

    /**
     * 工作流的流程定义key
     */
    private final String processDefinitionKey;
    /**
     * 流程名称
     */
    private final String processName;

    /**
     * 获取该流程下的任务节点定义
     * 非虚拟任务节点的
     */
    public List<FlowTaskDefinitionEnum> flowTaskDefinitions() {
        return Arrays.stream(FlowTaskDefinitionEnum.values())
                .filter(flowTaskDefinitionEnum -> flowTaskDefinitionEnum.getFlowDefinition() == this)
                .collect(Collectors.toList());
    }

    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    public String getProcessName() {
        return processName;
    }


}

