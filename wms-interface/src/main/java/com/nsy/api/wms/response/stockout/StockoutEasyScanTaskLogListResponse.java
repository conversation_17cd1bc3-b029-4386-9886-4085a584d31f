package com.nsy.api.wms.response.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "StockoutEasyScanTaskLogListResponse", description = "简易复核任务日志列表response")
public class StockoutEasyScanTaskLogListResponse {

    @ApiModelProperty(value = "日志id", name = "taskId")
    private Integer scanTaskLogId;

    @ApiModelProperty(value = "事件类型", name = "logType")
    private String logType;

    @ApiModelProperty(value = "描述", name = "content")
    private String content;

    @ApiModelProperty(value = "操作人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "操作时间", name = "createDate")
    private Date createDate;

    @ApiModelProperty(value = "操作ip", name = "ipAddress")
    private String ipAddress;

    public Integer getScanTaskLogId() {
        return scanTaskLogId;
    }

    public void setScanTaskLogId(Integer scanTaskLogId) {
        this.scanTaskLogId = scanTaskLogId;
    }

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
