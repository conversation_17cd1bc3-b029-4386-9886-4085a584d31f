package com.nsy.api.wms.domain.bd;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class BdPlatformRule {

    private Integer platformRuleId;
    /**
     *规则编码
     */
    @ApiModelProperty(value = "规则编码", name = "platformRuleCode")
    private String platformRuleCode;

    /**
     *规则名称
     */
    @ApiModelProperty(value = "规则名称", name = "platformRuleName")
    private String platformRuleName;

    /**
     * 预约类型
     */
    @ApiModelProperty(value = "预约类型", name = "type")
    private String type;

    @ApiModelProperty(value = "预约类型", name = "typeStr")
    private String typeStr;

    /**
     * 入库优先级
     */
    @ApiModelProperty(value = "入库优先级", name = "stockinOrder")
    private String stockinOrder;

    @ApiModelProperty(value = "入库优先级", name = "stockinOrderStr")
    private String stockinOrderStr;

    /**
     * 出库优先级
     */
    @ApiModelProperty(value = "出库优先级", name = "stockoutOrder")
    private String stockoutOrder;

    @ApiModelProperty(value = "出库优先级", name = "stockoutOrderStr")
    private String stockoutOrderStr;

    /**
     * 入库月台审核
     */
    @ApiModelProperty(value = "入库月台审核", name = "stockinAuditRule")
    private String stockinAuditRule;

    @ApiModelProperty(value = "入库月台审核", name = "stockinAuditRuleStr")
    private String stockinAuditRuleStr;

    /**
     * 出库月台审核
     */
    @ApiModelProperty(value = "出库月台审核", name = "stockoutAuditRule")
    private String stockoutAuditRule;

    @ApiModelProperty(value = "出库月台审核", name = "stockoutAuditRuleStr")
    private String stockoutAuditRuleStr;

    /**
     * 0-启用，1-停用
     */
    @ApiModelProperty(value = "0-启用，1-停用", name = "isDeleted")
    private Integer isDeleted;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    private Date createDate;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者", name = "createBy")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateDate")
    private Date updateDate;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者", name = "updateBy")
    private String updateBy;

    public Integer getPlatformRuleId() {
        return platformRuleId;
    }

    public void setPlatformRuleId(Integer platformRuleId) {
        this.platformRuleId = platformRuleId;
    }

    public String getPlatformRuleCode() {
        return platformRuleCode;
    }

    public void setPlatformRuleCode(String platformRuleCode) {
        this.platformRuleCode = platformRuleCode;
    }

    public String getPlatformRuleName() {
        return platformRuleName;
    }

    public void setPlatformRuleName(String platformRuleName) {
        this.platformRuleName = platformRuleName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStockinOrder() {
        return stockinOrder;
    }

    public void setStockinOrder(String stockinOrder) {
        this.stockinOrder = stockinOrder;
    }

    public String getStockoutOrder() {
        return stockoutOrder;
    }

    public void setStockoutOrder(String stockoutOrder) {
        this.stockoutOrder = stockoutOrder;
    }

    public String getStockinAuditRule() {
        return stockinAuditRule;
    }

    public void setStockinAuditRule(String stockinAuditRule) {
        this.stockinAuditRule = stockinAuditRule;
    }

    public String getStockoutAuditRule() {
        return stockoutAuditRule;
    }

    public void setStockoutAuditRule(String stockoutAuditRule) {
        this.stockoutAuditRule = stockoutAuditRule;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTypeStr() {
        return typeStr;
    }

    public void setTypeStr(String typeStr) {
        this.typeStr = typeStr;
    }

    public String getStockinOrderStr() {
        return stockinOrderStr;
    }

    public void setStockinOrderStr(String stockinOrderStr) {
        this.stockinOrderStr = stockinOrderStr;
    }

    public String getStockoutOrderStr() {
        return stockoutOrderStr;
    }

    public void setStockoutOrderStr(String stockoutOrderStr) {
        this.stockoutOrderStr = stockoutOrderStr;
    }

    public String getStockinAuditRuleStr() {
        return stockinAuditRuleStr;
    }

    public void setStockinAuditRuleStr(String stockinAuditRuleStr) {
        this.stockinAuditRuleStr = stockinAuditRuleStr;
    }

    public String getStockoutAuditRuleStr() {
        return stockoutAuditRuleStr;
    }

    public void setStockoutAuditRuleStr(String stockoutAuditRuleStr) {
        this.stockoutAuditRuleStr = stockoutAuditRuleStr;
    }
}
