package com.nsy.api.wms.request.stockin;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@ApiModel(value = "StockinSupplierDeliveryOrderQuickDirectShelveRequest", description = "工厂出库单市场上架 Request")
public class StockinSupplierDeliveryOrderQuickDirectShelveRequest {

    @ApiModelProperty(value = "库位编码", name = "positionCode")
    @NotEmpty
    private String positionCode;

    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    @NotEmpty
    private String supplierDeliveryNo;

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }
}
