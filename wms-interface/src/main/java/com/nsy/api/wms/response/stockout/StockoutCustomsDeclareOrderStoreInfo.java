package com.nsy.api.wms.response.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 9:18
 */
@ApiModel(value = "StockoutCustomsDeclareOrderRecommendResponse", description = "报关推荐方案-仓库信息")
public class StockoutCustomsDeclareOrderStoreInfo {

    @ApiModelProperty(value = "仓库名称", name = "storeName")
    private String amazonSpaceName;

    @ApiModelProperty(value = "订单信息", name = "orderInfoList")
    private List<StockoutCustomsDeclareOrderInfo> orderInfoList;

    public String getAmazonSpaceName() {
        return amazonSpaceName;
    }

    public void setAmazonSpaceName(String amazonSpaceName) {
        this.amazonSpaceName = amazonSpaceName;
    }

    public List<StockoutCustomsDeclareOrderInfo> getOrderInfoList() {
        return orderInfoList;
    }

    public void setOrderInfoList(List<StockoutCustomsDeclareOrderInfo> orderInfoList) {
        this.orderInfoList = orderInfoList;
    }
}
