package com.nsy.api.wms.domain.stockout;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
@ApiModel(value = "StockoutOrderPackMappingInfo", description = "packSku组成")
public class StockoutOrderPackMappingInfo {

    @ApiModelProperty(value = "id", name = "id")
    private Integer id;

    /**
     * 地区
     */
    @ApiModelProperty(value = "location", name = "location")
    private String location;

    /**
     * 出库单id
     */
    @ApiModelProperty(value = "出库单号id", name = "stockoutOrderId")
    private Integer stockoutOrderId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;

    /**
     * 订单明细号
     */
    @ApiModelProperty(value = "订单明细", name = "orderItemNo")
    private String orderItemNo;

    /**
     * 源pack sku
     */
    @ApiModelProperty(value = "源pack sku", name = "originSku")
    private String originSku;

    /**
     * 源pack seller barcode
     */
    private String packSellerBarcode;

    /**
     * 源pack seller sku
     */
    private String packSellerSku;

    /**
     * 源pack sku 数量
     */
    @ApiModelProperty(value = "源pack sku 数量", name = "originQty")
    private Integer originQty;

    /**
     * 对应ERP sku
     */
    @ApiModelProperty(value = "对应ERP sku", name = "mappingSku")
    private String mappingSku;

    /**
     * 对应ERP sku 数量
     */
    @ApiModelProperty(value = "对应ERP sku 数量", name = "mappingQty")
    private Integer mappingQty;

    /**
     * 对应ERP 订单明细 数量
     */
    @ApiModelProperty(value = "对应ERP 订单明细 数量", name = "orderRealQty")
    private Integer orderRealQty;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String updateBy;

    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String createBy;

    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private Integer version;

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockoutOrderId() {
        return stockoutOrderId;
    }

    public void setStockoutOrderId(Integer stockoutOrderId) {
        this.stockoutOrderId = stockoutOrderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderItemNo() {
        return orderItemNo;
    }

    public void setOrderItemNo(String orderItemNo) {
        this.orderItemNo = orderItemNo;
    }

    public String getOriginSku() {
        return originSku;
    }

    public void setOriginSku(String originSku) {
        this.originSku = originSku;
    }

    public Integer getOriginQty() {
        return originQty;
    }

    public void setOriginQty(Integer originQty) {
        this.originQty = originQty;
    }

    public String getMappingSku() {
        return mappingSku;
    }

    public void setMappingSku(String mappingSku) {
        this.mappingSku = mappingSku;
    }

    public Integer getMappingQty() {
        return mappingQty;
    }

    public void setMappingQty(Integer mappingQty) {
        this.mappingQty = mappingQty;
    }

    public Integer getOrderRealQty() {
        return orderRealQty;
    }

    public void setOrderRealQty(Integer orderRealQty) {
        this.orderRealQty = orderRealQty;
    }

    public String getPackSellerBarcode() {
        return packSellerBarcode;
    }

    public void setPackSellerBarcode(String packSellerBarcode) {
        this.packSellerBarcode = packSellerBarcode;
    }

    public String getPackSellerSku() {
        return packSellerSku;
    }

    public void setPackSellerSku(String packSellerSku) {
        this.packSellerSku = packSellerSku;
    }
}
