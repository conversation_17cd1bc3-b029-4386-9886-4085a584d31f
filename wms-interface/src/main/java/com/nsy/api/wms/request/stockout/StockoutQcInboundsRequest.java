package com.nsy.api.wms.request.stockout;


import com.nsy.api.wms.domain.stockout.StockoutQcInboundsItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "StockoutQcInboundsRequest", description = "出库单质检报告")
public class StockoutQcInboundsRequest {

    @ApiModelProperty(value = "质检明细", name = "qcInboundsItemList")
    private List<StockoutQcInboundsItem> qcInboundsItemList;


    public List<StockoutQcInboundsItem> getQcInboundsItemList() {
        return qcInboundsItemList;
    }

    public void setQcInboundsItemList(List<StockoutQcInboundsItem> qcInboundsItemList) {
        this.qcInboundsItemList = qcInboundsItemList;
    }
}
