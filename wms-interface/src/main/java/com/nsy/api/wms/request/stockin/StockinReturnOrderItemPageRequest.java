package com.nsy.api.wms.request.stockin;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2022/03/17
 */
@ApiModel(value = "StockinReturnOrderItemPageRequest", description = "退货单明细列表request")
public class StockinReturnOrderItemPageRequest extends PageRequest implements Serializable {
    @ApiModelProperty(value = "退货单id", name = "returnTaskId")
    private Integer returnTaskId;
    @ApiModelProperty(value = "采购单号", name = "purchasePlanNo")
    private String purchasePlanNo;
    @ApiModelProperty(value = "工厂出库单号", name = "supplierDeliveryNo")
    private String supplierDeliveryNo;
    @ApiModelProperty(value = "退货明细id集合", name = "returnTaskItemIdList")
    private List<Integer> returnTaskItemIdList;

    public Integer getReturnTaskId() {
        return returnTaskId;
    }

    public void setReturnTaskId(Integer returnTaskId) {
        this.returnTaskId = returnTaskId;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public List<Integer> getReturnTaskItemIdList() {
        return returnTaskItemIdList;
    }

    public void setReturnTaskItemIdList(List<Integer> returnTaskItemIdList) {
        this.returnTaskItemIdList = returnTaskItemIdList;
    }
}
