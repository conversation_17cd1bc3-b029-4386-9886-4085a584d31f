package com.nsy.api.wms.request.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "StockoutOrderAreaQueryRequest", description = "出库单区域查询request")
public class StockoutOrderAreaQueryRequest {

    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo", required = true)
    @NotBlank(message = "出库单号不能为空")
    private String stockoutOrderNo;

    @ApiModelProperty(value = "规格编码", name = "sku", required = true)
    @NotBlank(message = "规格编码不能为空")
    private String sku;

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }
} 