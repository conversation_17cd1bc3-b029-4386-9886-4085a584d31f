package com.nsy.api.wms.response.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "ShelveSplitTaskBindingPickingBoxResponse", description = "上架分拣任务收货箱绑定response")
public class ShelveSplitTaskBindingReceiveBoxResponse {
    @ApiModelProperty(value = "上架分拣库区", name = "spaceAreaName")
    private String spaceAreaName;
    @ApiModelProperty(value = "分拣口", name = "outlet")
    private Integer outlet;
    @ApiModelProperty(value = "上架分拣库区对应上架分拣明细id数组", name = "itemIdList")
    private List<Integer> itemIdList;
    @ApiModelProperty(value = "收货箱号", name = "internalBoxCode")
    private String internalBoxCode;

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public Integer getOutlet() {
        return outlet;
    }

    public void setOutlet(Integer outlet) {
        this.outlet = outlet;
    }

    public List<Integer> getItemIdList() {
        return itemIdList;
    }

    public void setItemIdList(List<Integer> itemIdList) {
        this.itemIdList = itemIdList;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }
}
