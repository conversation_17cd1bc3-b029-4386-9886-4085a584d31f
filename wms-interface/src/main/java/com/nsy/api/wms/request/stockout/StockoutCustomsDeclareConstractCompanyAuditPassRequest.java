package com.nsy.api.wms.request.stockout;


import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutCustomsDeclareConstractCompanyAuditRequest", description = "关单合同公司审核通过request")
public class StockoutCustomsDeclareConstractCompanyAuditPassRequest {

    private List<Integer> declareContractIdList;

    public List<Integer> getDeclareContractIdList() {
        return declareContractIdList;
    }

    public void setDeclareContractIdList(List<Integer> declareContractIdList) {
        this.declareContractIdList = declareContractIdList;
    }
}
