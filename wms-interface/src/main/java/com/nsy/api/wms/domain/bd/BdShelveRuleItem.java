package com.nsy.api.wms.domain.bd;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "BdShelveRuleItem", description = "上架规则明细response")
public class BdShelveRuleItem {

    /**
     *
     */
    @ApiModelProperty(value = "上架规则明细id", name = "shelveRuleItemId")
    private Integer shelveRuleItemId;

    @ApiModelProperty(value = "地区", name = "location")
    private String location;

    /**
     * 上架规则id
     */
    @ApiModelProperty(value = "上架规则id", name = "shelveRuleId")
    private Integer shelveRuleId;

    /**
     * 规则明细类型
     */
    @ApiModelProperty(value = "规则明细类型", name = "shelveRuleItemType")
    private String shelveRuleItemType;

    @ApiModelProperty(value = "规则明细类型描述", name = "shelveRuleItemTypeStr")
    private String shelveRuleItemTypeStr;

    /**
     * 库区id
     */
    @ApiModelProperty(value = "库区id", name = "spaceAreaId")
    private Integer spaceAreaId;

    @ApiModelProperty(value = "目标库区", name = "spaceAreaName")
    private String spaceAreaName;
    /**
     * 区域id
     */
    @ApiModelProperty(value = "区域id", name = "areaId")
    private Integer areaId;

    @ApiModelProperty(value = "目标区域", name = "areaName")
    private String areaName;

    /**
     * 库位
     */
    @ApiModelProperty(value = "库位id", name = "positionId")
    private Integer positionId;

    /**
     * 库位
     */
    @ApiModelProperty(value = "库位编码", name = "positionCode")
    private String positionCode;

    /**
     * 库位范围从
     */
    @ApiModelProperty(value = "库位范围从", name = "positionStart")
    private String positionStart;

    /**
     * 库位范围至
     */
    @ApiModelProperty(value = "库位范围至", name = "positionEnd")
    private String positionEnd;

    /**
     * 库位空间限制;件数,重量,体积,可多种
     */
    @ApiModelProperty(value = "用于列表展示:库位空间限制;件数,重量,体积,可多种", name = "limitType")
    private String limitType;

    @ApiModelProperty(value = "用于编辑:库位空间限制;件数,重量,体积,可多种", name = "limitTypes")
    private List<String> limitTypes;

    /**
     * 品质属性包含
     */
    @ApiModelProperty(value = "品质属性包含", name = "qualityInclude")
    private String qualityInclude;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", name = "sort")
    private Integer sort;

    /**
     * 是否已删除 1-是，0-否
     */
    @ApiModelProperty(value = "是否已删除 1-是，0-否", name = "isDeleted")
    private Integer isDeleted;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 更新者
     */
    private String updateBy;

    public Integer getShelveRuleItemId() {
        return shelveRuleItemId;
    }

    public void setShelveRuleItemId(Integer shelveRuleItemId) {
        this.shelveRuleItemId = shelveRuleItemId;
    }

    public String getShelveRuleItemTypeStr() {
        return shelveRuleItemTypeStr;
    }

    public void setShelveRuleItemTypeStr(String shelveRuleItemTypeStr) {
        this.shelveRuleItemTypeStr = shelveRuleItemTypeStr;
    }

    public String getLocation() {
        return this.location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getShelveRuleId() {
        return this.shelveRuleId;
    }

    public void setShelveRuleId(Integer shelveRuleId) {
        this.shelveRuleId = shelveRuleId;
    }

    public String getShelveRuleItemType() {
        return shelveRuleItemType;
    }

    public void setShelveRuleItemType(String shelveRuleItemType) {
        this.shelveRuleItemType = shelveRuleItemType;
    }

    public Integer getSpaceAreaId() {
        return this.spaceAreaId;
    }

    public void setSpaceAreaId(Integer spaceAreaId) {
        this.spaceAreaId = spaceAreaId;
    }

    public Integer getPositionId() {
        return this.positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public String getPositionStart() {
        return this.positionStart;
    }

    public void setPositionStart(String positionStart) {
        this.positionStart = positionStart;
    }

    public String getPositionEnd() {
        return this.positionEnd;
    }

    public void setPositionEnd(String positionEnd) {
        this.positionEnd = positionEnd;
    }

    public String getLimitType() {
        return this.limitType;
    }

    public void setLimitType(String limitType) {
        this.limitType = limitType;
    }

    public List<String> getLimitTypes() {
        return limitTypes;
    }

    public void setLimitTypes(List<String> limitTypes) {
        this.limitTypes = limitTypes;
    }

    public String getQualityInclude() {
        return this.qualityInclude;
    }

    public void setQualityInclude(String qualityInclude) {
        this.qualityInclude = qualityInclude;
    }

    public Integer getSort() {
        return this.sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
}
