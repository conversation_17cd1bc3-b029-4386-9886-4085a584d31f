package com.nsy.api.wms.domain.logistics.documents;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("地址信息")
public class AddressInfo {
    @ApiModelProperty(value = "公司名称", name = "companyName")
    private String companyName;
    @ApiModelProperty(value = "收人姓名", name = "name")
    private String name;
    @ApiModelProperty(value = "所在国家编码", name = "countryCode")
    private String countryCode;
    @ApiModelProperty(value = "所在国家名称", name = "countryName")
    private String countryName;
    @ApiModelProperty(value = "所在州/省", name = "stateOrProvinceCode")
    private String stateOrProvinceCode;
    @ApiModelProperty(value = "所在城市", name = "city")
    private String city;
    @ApiModelProperty(value = "所在地邮政编码", name = "zipCode")
    private String zipCode;
    @ApiModelProperty(value = "详细地址", name = "street")
    private String street;
    @ApiModelProperty(value = "电话号码", name = "phone")
    private String phone;
    @ApiModelProperty(value = "手机号码", name = "mobile")
    private String mobile;
    @ApiModelProperty(value = "邮箱", name = "email")
    private String email;
    @ApiModelProperty(value = "税号", name = "taxNumber")
    private String taxNumber;

    @ApiModelProperty(value = "默认发货账号:1--是 0--否", name = "defaultSenderAccount")
    private Integer defaultSenderAccount;

    public Integer getDefaultSenderAccount() {
        return defaultSenderAccount;
    }

    public void setDefaultSenderAccount(Integer defaultSenderAccount) {
        this.defaultSenderAccount = defaultSenderAccount;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getStateOrProvinceCode() {
        return stateOrProvinceCode;
    }

    public void setStateOrProvinceCode(String stateOrProvinceCode) {
        this.stateOrProvinceCode = stateOrProvinceCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getStreet() {
        return street;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
