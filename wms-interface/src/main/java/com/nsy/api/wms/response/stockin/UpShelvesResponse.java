package com.nsy.api.wms.response.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "UpShelvesResponse", description = "正常上架response")
public class UpShelvesResponse {

    @ApiModelProperty(value = "是否走异常上架,1是", name = "isExceptionShelve")
    private Integer isExceptionShelve;

    @ApiModelProperty(value = "库存", name = "allStock")
    private Integer allStock;

    @ApiModelProperty(value = "上架返回信息", name = "message")
    private String message;

    @ApiModelProperty(value = "内部箱状态", name = "internalBoxStatus")
    private String internalBoxStatus;

    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "内部箱号（多个）", name = "internalBoxCodeList")
    private List<String> internalBoxCodeList;

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public List<String> getInternalBoxCodeList() {
        return internalBoxCodeList;
    }

    public void setInternalBoxCodeList(List<String> internalBoxCodeList) {
        this.internalBoxCodeList = internalBoxCodeList;
    }

    public Integer getIsExceptionShelve() {
        return isExceptionShelve;
    }

    public void setIsExceptionShelve(Integer isExceptionShelve) {
        this.isExceptionShelve = isExceptionShelve;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getAllStock() {
        return allStock;
    }

    public void setAllStock(Integer allStock) {
        this.allStock = allStock;
    }

    public String getInternalBoxStatus() {
        return internalBoxStatus;
    }

    public void setInternalBoxStatus(String internalBoxStatus) {
        this.internalBoxStatus = internalBoxStatus;
    }
}
