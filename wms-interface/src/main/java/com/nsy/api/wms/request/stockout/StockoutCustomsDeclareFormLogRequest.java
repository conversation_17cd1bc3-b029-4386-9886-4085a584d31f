package com.nsy.api.wms.request.stockout;


import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutCustomsDeclareFormLogRequest", description = "关单log查询request")
public class StockoutCustomsDeclareFormLogRequest extends PageRequest implements Serializable {

    @ApiModelProperty("关单id")
    private Integer declareFormId;

    public Integer getDeclareFormId() {
        return declareFormId;
    }

    public void setDeclareFormId(Integer declareFormId) {
        this.declareFormId = declareFormId;
    }
}
