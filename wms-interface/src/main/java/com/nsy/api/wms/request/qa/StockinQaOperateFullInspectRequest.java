package com.nsy.api.wms.request.qa;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-06 9:56
 */
public class StockinQaOperateFullInspectRequest {
    @ApiModelProperty("质检单ID")
    @NotNull
    private Integer stockinQaOrderId;

    @ApiModelProperty("全检件数")
    @NotNull
    private Integer fullInspectCount;

    @ApiModelProperty("质检不合格件数")
    @NotNull
    private Integer unqualifiedCount;
    /**
     * 退货数量
     */
    @ApiModelProperty("退货数量")
    @NotNull
    private Integer returnCount;


    @ApiModelProperty("附件地址")
    private String attachmentUrl;

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("不合格的问题描述")
    private String unqualifiedQuestion;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    @ApiModelProperty("不合格原因")
    private String unqualifiedReason;

    /**
     * 不合格的问题描述
     */
    @ApiModelProperty("次要不合格的问题描述")
    private String unqualifiedQuestionSecondary;

    /**
     * 质检不合格的原因
     */
    @ApiModelProperty("次要不合格原因归类")
    private String unqualifiedCategorySecondary;

    @ApiModelProperty("次要不合格原因")
    private String unqualifiedReasonSecondary;
    /**
     * 备注
     */
    @ApiModelProperty("全检备注")
    private String remark;

    /**
     * 当前步骤检测图片
     */
    @ApiModelProperty("当前步骤检测图片")
    public List<String> imageList;

    public Integer getStockinQaOrderId() {
        return stockinQaOrderId;
    }

    public void setStockinQaOrderId(Integer stockinQaOrderId) {
        this.stockinQaOrderId = stockinQaOrderId;
    }

    public Integer getFullInspectCount() {
        return fullInspectCount;
    }

    public void setFullInspectCount(Integer fullInspectCount) {
        this.fullInspectCount = fullInspectCount;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getUnqualifiedQuestionSecondary() {
        return unqualifiedQuestionSecondary;
    }

    public void setUnqualifiedQuestionSecondary(String unqualifiedQuestionSecondary) {
        this.unqualifiedQuestionSecondary = unqualifiedQuestionSecondary;
    }

    public String getUnqualifiedCategorySecondary() {
        return unqualifiedCategorySecondary;
    }

    public void setUnqualifiedCategorySecondary(String unqualifiedCategorySecondary) {
        this.unqualifiedCategorySecondary = unqualifiedCategorySecondary;
    }

    public String getUnqualifiedReasonSecondary() {
        return unqualifiedReasonSecondary;
    }

    public void setUnqualifiedReasonSecondary(String unqualifiedReasonSecondary) {
        this.unqualifiedReasonSecondary = unqualifiedReasonSecondary;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<String> getImageList() {
        return imageList;
    }

    public void setImageList(List<String> imageList) {
        this.imageList = imageList;
    }
}
