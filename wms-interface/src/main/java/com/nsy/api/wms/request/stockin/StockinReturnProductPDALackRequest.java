package com.nsy.api.wms.request.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@ApiModel(value = "StockinReturnProductPDALackRequest", description = "pda缺货request")
public class StockinReturnProductPDALackRequest implements Serializable {
    private static final long serialVersionUID = -6531410961097918095L;

    @ApiModelProperty(value = "退货任务详情主键", required = true)
    @Size(min = 1, message = "退货任务详情主键不能为空")
    private List<Integer> taskItemIdList;


    @ApiModelProperty("真实退货数（扫描数）")
    private int actualReturnQty;

    public List<Integer> getTaskItemIdList() {
        return taskItemIdList;
    }

    public void setTaskItemIdList(List<Integer> taskItemIdList) {
        this.taskItemIdList = taskItemIdList;
    }

    public int getActualReturnQty() {
        return actualReturnQty;
    }

    public void setActualReturnQty(int actualReturnQty) {
        this.actualReturnQty = actualReturnQty;
    }
}
