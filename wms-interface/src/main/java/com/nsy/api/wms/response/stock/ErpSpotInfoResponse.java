package com.nsy.api.wms.response.stock;

import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

public class ErpSpotInfoResponse {

    @JsonProperty("PlatformScheduleList")
    private List<ErpSpotInfoResponse.StockPlatformScheduleSupplier> platformScheduleSupplierList;

    @JsonProperty("SupplierDeliveryNoList")
    private List<String> supplierDeliveryNoList;

    @JsonProperty("LogisticsNo")
    private String logisticsNo;

    public List<StockPlatformScheduleSupplier> getPlatformScheduleSupplierList() {
        return platformScheduleSupplierList;
    }

    public void setPlatformScheduleSupplierList(List<StockPlatformScheduleSupplier> platformScheduleSupplierList) {
        this.platformScheduleSupplierList = platformScheduleSupplierList;
    }

    public List<String> getSupplierDeliveryNoList() {
        return supplierDeliveryNoList;
    }

    public void setSupplierDeliveryNoList(List<String> supplierDeliveryNoList) {
        this.supplierDeliveryNoList = supplierDeliveryNoList;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public static class StockPlatformScheduleSupplier {

        @JsonProperty("SupplierDeliveryNo")
        private String supplierDeliveryNo;

        @JsonProperty("StockinType")
        private StockinTypeEnum stockinType;

        @JsonProperty("SupplierId")
        private Integer supplierId;

        @JsonProperty("SupplierName")
        private String supplierName;

        @JsonProperty("Location")
        private String location;

        @JsonProperty("PurchaseUserName")
        private String purchaseUserName;

        @JsonProperty("PurchaseUserRealName")
        private String purchaseUserRealName;

        @JsonProperty("PlanBoxNum")
        private int planBoxNum;

        @DateTimeFormat(pattern = "yyyy-MM-dd")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @JsonProperty("PlanArriveDate")
        private Date planArriveDate;

        @JsonProperty("TruckLicense")
        private String truckLicense;

        @JsonProperty("TruckType")
        private String truckType;

        @JsonProperty("Driver")
        private String driver;

        @JsonProperty("ItemList")
        private List<ErpSpotInfoResponse.Item> item;

        public String getLocation() {
            return location;
        }

        public StockinTypeEnum getStockinType() {
            return stockinType;
        }

        public void setStockinType(StockinTypeEnum stockinType) {
            this.stockinType = stockinType;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getSupplierDeliveryNo() {
            return supplierDeliveryNo;
        }

        public void setSupplierDeliveryNo(String supplierDeliveryNo) {
            this.supplierDeliveryNo = supplierDeliveryNo;
        }

        public Integer getSupplierId() {
            return supplierId;
        }

        public void setSupplierId(Integer supplierId) {
            this.supplierId = supplierId;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public String getPurchaseUserName() {
            return purchaseUserName;
        }

        public void setPurchaseUserName(String purchaseUserName) {
            this.purchaseUserName = purchaseUserName;
        }

        public String getPurchaseUserRealName() {
            return purchaseUserRealName;
        }

        public void setPurchaseUserRealName(String purchaseUserRealName) {
            this.purchaseUserRealName = purchaseUserRealName;
        }

        public int getPlanBoxNum() {
            return planBoxNum;
        }

        public void setPlanBoxNum(int planBoxNum) {
            this.planBoxNum = planBoxNum;
        }

        public Date getPlanArriveDate() {
            return planArriveDate;
        }

        public void setPlanArriveDate(Date planArriveDate) {
            this.planArriveDate = planArriveDate;
        }

        public String getTruckLicense() {
            return truckLicense;
        }

        public void setTruckLicense(String truckLicense) {
            this.truckLicense = truckLicense;
        }

        public String getTruckType() {
            return truckType;
        }

        public void setTruckType(String truckType) {
            this.truckType = truckType;
        }

        public String getDriver() {
            return driver;
        }

        public void setDriver(String driver) {
            this.driver = driver;
        }

        public List<ErpSpotInfoResponse.Item> getItem() {
            return item;
        }

        public void setItem(List<ErpSpotInfoResponse.Item> item) {
            this.item = item;
        }
    }

    public static class Item {

        @JsonProperty("SupplierDeliveryBoxCode")
        private String supplierDeliveryBoxCode;

        @JsonProperty("SupplierDeliveryBarcode")
        private String supplierDeliveryBarcode;

        @JsonProperty("SpaceId")
        private Integer spaceId;

        @JsonProperty("BoxIndex")
        private Integer boxIndex;

        @JsonProperty("PurchasePlanNo")
        private String purchasePlanNo;

        @JsonProperty("OrderNo")
        private String orderNo;

        @JsonProperty("PurchaseApplyInfo")
        private String purchaseApplyInfo;

        @JsonProperty("LabelAttributeNames")
        private String labelAttributeNames;

        /** 采购申请类型，1:缺货订单申请，2:定制申请，3:FBA发货申请，4:采购申请,5:正常采购,6:退货返工,7:开发申请,8:分公司申请,9:现货补单,10:市场补单 */
        @JsonProperty("PurchasingApplyType")
        private Integer purchasingApplyType;

        @JsonProperty("BatchCode")
        private String batchCode;

        @JsonProperty("Sku")
        private String sku;

        @JsonProperty("Barcode")
        private String barcode;

        @JsonProperty("Qty")
        private Integer qty;

        public String getSupplierDeliveryBoxCode() {
            return supplierDeliveryBoxCode;
        }

        public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
            this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
        }

        public String getLabelAttributeNames() {
            return labelAttributeNames;
        }

        public void setLabelAttributeNames(String labelAttributeNames) {
            this.labelAttributeNames = labelAttributeNames;
        }

        public Integer getPurchasingApplyType() {
            return purchasingApplyType;
        }

        public void setPurchasingApplyType(Integer purchasingApplyType) {
            this.purchasingApplyType = purchasingApplyType;
        }

        public Integer getSpaceId() {
            return spaceId;
        }

        public void setSpaceId(Integer spaceId) {
            this.spaceId = spaceId;
        }

        public Integer getBoxIndex() {
            return boxIndex;
        }

        public void setBoxIndex(Integer boxIndex) {
            this.boxIndex = boxIndex;
        }

        public String getPurchasePlanNo() {
            return purchasePlanNo;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getPurchaseApplyInfo() {
            return purchaseApplyInfo;
        }

        public void setPurchaseApplyInfo(String purchaseApplyInfo) {
            this.purchaseApplyInfo = purchaseApplyInfo;
        }

        public void setPurchasePlanNo(String purchasePlanNo) {
            this.purchasePlanNo = purchasePlanNo;
        }


        public String getBatchCode() {
            return batchCode;
        }

        public void setBatchCode(String batchCode) {
            this.batchCode = batchCode;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getBarcode() {
            return barcode;
        }

        public void setBarcode(String barcode) {
            this.barcode = barcode;
        }

        public Integer getQty() {
            return qty;
        }

        public void setQty(Integer qty) {
            this.qty = qty;
        }

        public String getSupplierDeliveryBarcode() {
            return supplierDeliveryBarcode;
        }

        public void setSupplierDeliveryBarcode(String supplierDeliveryBarcode) {
            this.supplierDeliveryBarcode = supplierDeliveryBarcode;
        }
    }
}
