package com.nsy.api.wms.response.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("退货任务扫描库位返回")
public class StockinReturnProductTaskScanPositionResponse {

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplierName;
    /**
     * 退货性质
     */
    @ApiModelProperty("退货性质")
    private String returnNature;
    /**
     * 扫描数
     */
    @ApiModelProperty("扫描数")
    private Integer qty;

    @ApiModelProperty("明细列表")
    List<StockinReturnProductTaskScanPositionItemResponse> itemList;

    @ApiModelProperty("是否启用")
    private Boolean isUse = Boolean.TRUE;

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getReturnNature() {
        return returnNature;
    }

    public void setReturnNature(String returnNature) {
        this.returnNature = returnNature;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public List<StockinReturnProductTaskScanPositionItemResponse> getItemList() {
        return itemList;
    }

    public void setItemList(List<StockinReturnProductTaskScanPositionItemResponse> itemList) {
        this.itemList = itemList;
    }

    public Boolean getUse() {
        return isUse;
    }

    public void setUse(Boolean use) {
        isUse = use;
    }
}
