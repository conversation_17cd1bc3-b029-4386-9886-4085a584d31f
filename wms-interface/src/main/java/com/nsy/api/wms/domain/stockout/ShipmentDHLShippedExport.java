package com.nsy.api.wms.domain.stockout;

// DHL发货数据
// @HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)
// @ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER, wrapped = true)
public class ShipmentDHLShippedExport {
//    @NsyExcelProperty(value = "订单编号")
    private String orderNo;
//    @NsyExcelProperty(value = "快递单号")
    private String logisticsNO;
//    @NsyExcelProperty(value = "物流公司")
    private String logisticsCompany;
//    @NsyExcelProperty(value = "国家")
    private String countryCode;
//    @NsyExcelProperty(value = "订单状态")
    private String orderStatus;
//    @NsyExcelProperty(value = "发货日期")
    private String deliveryDate;
//    @NsyExcelProperty(value = "订单详情备注")
    private String orderDetailRemark;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLogisticsNO() {
        return logisticsNO;
    }

    public void setLogisticsNO(String logisticsNO) {
        this.logisticsNO = logisticsNO;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getOrderDetailRemark() {
        return orderDetailRemark;
    }

    public void setOrderDetailRemark(String orderDetailRemark) {
        this.orderDetailRemark = orderDetailRemark;
    }
}
