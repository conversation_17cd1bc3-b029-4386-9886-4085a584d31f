package com.nsy.api.wms.request.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(value = "StockoutShipmentAddRequest", description = "装箱清单新增")
public class StockoutShipmentAddRequest {

    @ApiModelProperty(value = "包装类型", name = "packingType")
    private String packingType;

    @ApiModelProperty(value = "箱子编号", name = "shipmentBoxCode")
    private String shipmentBoxCode;

    @ApiModelProperty(value = "箱子序号(第几箱)", name = "boxIndex")
    private Integer boxIndex;

    @ApiModelProperty(value = "是否空箱", name = "isEmpty")
    private Integer isEmpty;

    @ApiModelProperty(value = "规格", name = "boxSize")
    private String boxSize;

    @ApiModelProperty(value = "重量(千克)", name = "weight")
    private BigDecimal weight;

    @ApiModelProperty(value = "体积重(千克)", name = "volumeWeight")
    private BigDecimal volumeWeight;

    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;

    @ApiModelProperty(value = "打印模式(按箱打印/按单打印)", name = "printType")
    private String printType;

    @ApiModelProperty(value = "请求的页面", name = "pageIn")
    private String pageIn;

    @ApiModelProperty(value = "是否自动打印", name = "autoPrint")
    private Boolean autoPrint = Boolean.FALSE;

    public Integer getIsEmpty() {
        return isEmpty;
    }

    public void setIsEmpty(Integer isEmpty) {
        this.isEmpty = isEmpty;
    }

    public Boolean getAutoPrint() {
        return autoPrint;
    }

    public void setAutoPrint(Boolean autoPrint) {
        this.autoPrint = autoPrint;
    }

    public String getPageIn() {
        return pageIn;
    }

    public void setPageIn(String pageIn) {
        this.pageIn = pageIn;
    }

    public String getPrintType() {
        return printType;
    }

    public void setPrintType(String printType) {
        this.printType = printType;
    }

    public String getPackingType() {
        return packingType;
    }

    public void setPackingType(String packingType) {
        this.packingType = packingType;
    }

    public String getShipmentBoxCode() {
        return shipmentBoxCode;
    }

    public void setShipmentBoxCode(String shipmentBoxCode) {
        this.shipmentBoxCode = shipmentBoxCode;
    }

    public Integer getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(Integer boxIndex) {
        this.boxIndex = boxIndex;
    }

    public String getBoxSize() {
        return boxSize;
    }

    public void setBoxSize(String boxSize) {
        this.boxSize = boxSize;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
