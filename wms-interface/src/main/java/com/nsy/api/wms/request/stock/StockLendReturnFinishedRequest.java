package com.nsy.api.wms.request.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;


@ApiModel(value = "StockLendReturnFinishedRequest", description = "归还完成登记")
public class StockLendReturnFinishedRequest {
    @ApiModelProperty(value = "归还单ID", name = "returnId")
    Integer returnId;

    @ApiModelProperty(value = "明细列表", name = "itemList")
    List<StockLendReturnFinishedItemRequest> itemList = new ArrayList<>();

    public Integer getReturnId() {
        return returnId;
    }

    public void setReturnId(Integer returnId) {
        this.returnId = returnId;
    }


    public List<StockLendReturnFinishedItemRequest> getItemList() {
        return itemList;
    }

    public void setItemList(List<StockLendReturnFinishedItemRequest> itemList) {
        this.itemList = itemList;
    }
}
