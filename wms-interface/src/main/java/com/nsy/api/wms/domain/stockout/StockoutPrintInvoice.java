package com.nsy.api.wms.domain.stockout;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class StockoutPrintInvoice {

    @JsonProperty("ShipperCompanyName")
    private String shipperCompanyName;

    @JsonProperty("ShipperPersonName")
    private String shipperPersonName;

    @JsonProperty("ShipperStreetLines1")
    private String shipperStreetLines1;

    @JsonProperty("ShipperStreetLines2")
    private String shipperStreetLines2;

    @JsonProperty("ShipperCountryCode")
    private String shipperCountryCode;

    @JsonProperty("ShipperPostalCode")
    private String shipperPostalCode;

    @JsonProperty("ShipperStateOrProvinceCode")
    private String shipperStateOrProvinceCode;

    @JsonProperty("ShipperCity")
    private String shipperCity;

    @JsonProperty("ShipperPhoneNumber")
    private String shipperPhoneNumber;

    @JsonProperty("ServiceType")
    private String serviceType;

    @JsonProperty("PackagingType")
    private String packagingType;

    @JsonProperty("PackageCount")
    private String packageCount;

    @JsonProperty("tid")
    private String orderNo;

    @JsonProperty("TotalWeightValue")
    private String totalWeightValue;

    @JsonProperty("ShipmentAccountNumber")
    private String shipmentAccountNumber;

    @JsonProperty("RecipientCompanyName")
    private String recipientCompanyName;

    @JsonProperty("RecipientPersonName")
    private String recipientPersonName;

    @JsonProperty("RecipientStreetLines1")
    private String recipientStreetLines1;

    @JsonProperty("RecipientStreetLines2")
    private String recipientStreetLines2;

    @JsonProperty("RecipientCountryCode")
    private String recipientCountryCode;

    @JsonProperty("RecipientPostalCode")
    private String recipientPostalCode;

    @JsonProperty("RecipientCity")
    private String recipientCity;

    @JsonProperty("RecipientEmail")
    private String recipientEmail;

    @JsonProperty("RecipientPhoneNumber")
    private String recipientPhoneNumber;

    @JsonProperty("RecipientMobileNumber")
    private String recipientMobileNumber;

    @JsonProperty("RecipientStateOrProvinceCode")
    private String recipientStateOrProvinceCode;

    @JsonProperty("CustomsValueAmount")
    private String customsValueAmount;

    @JsonProperty("FreightCharges")
    private Integer freightCharges;

    @JsonProperty("CustomerReference")
    private String customerReference;

    @JsonProperty("PackageDimensions")
    private String packageDimensions;

    @JsonProperty("Commodities")
    private List<StockoutPrintInvoiceItem> commodities;

    public String getShipperCompanyName() {
        return shipperCompanyName;
    }

    public void setShipperCompanyName(String shipperCompanyName) {
        this.shipperCompanyName = shipperCompanyName;
    }

    public String getShipperPersonName() {
        return shipperPersonName;
    }

    public void setShipperPersonName(String shipperPersonName) {
        this.shipperPersonName = shipperPersonName;
    }

    public String getShipperStreetLines1() {
        return shipperStreetLines1;
    }

    public void setShipperStreetLines1(String shipperStreetLines1) {
        this.shipperStreetLines1 = shipperStreetLines1;
    }

    public String getShipperStreetLines2() {
        return shipperStreetLines2;
    }

    public void setShipperStreetLines2(String shipperStreetLines2) {
        this.shipperStreetLines2 = shipperStreetLines2;
    }

    public String getShipperCountryCode() {
        return shipperCountryCode;
    }

    public void setShipperCountryCode(String shipperCountryCode) {
        this.shipperCountryCode = shipperCountryCode;
    }

    public String getShipperPostalCode() {
        return shipperPostalCode;
    }

    public void setShipperPostalCode(String shipperPostalCode) {
        this.shipperPostalCode = shipperPostalCode;
    }

    public String getShipperStateOrProvinceCode() {
        return shipperStateOrProvinceCode;
    }

    public void setShipperStateOrProvinceCode(String shipperStateOrProvinceCode) {
        this.shipperStateOrProvinceCode = shipperStateOrProvinceCode;
    }

    public String getShipperCity() {
        return shipperCity;
    }

    public void setShipperCity(String shipperCity) {
        this.shipperCity = shipperCity;
    }

    public String getShipperPhoneNumber() {
        return shipperPhoneNumber;
    }

    public void setShipperPhoneNumber(String shipperPhoneNumber) {
        this.shipperPhoneNumber = shipperPhoneNumber;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getPackagingType() {
        return packagingType;
    }

    public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
    }

    public String getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(String packageCount) {
        this.packageCount = packageCount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getTotalWeightValue() {
        return totalWeightValue;
    }

    public void setTotalWeightValue(String totalWeightValue) {
        this.totalWeightValue = totalWeightValue;
    }

    public String getShipmentAccountNumber() {
        return shipmentAccountNumber;
    }

    public void setShipmentAccountNumber(String shipmentAccountNumber) {
        this.shipmentAccountNumber = shipmentAccountNumber;
    }

    public String getRecipientCompanyName() {
        return recipientCompanyName;
    }

    public void setRecipientCompanyName(String recipientCompanyName) {
        this.recipientCompanyName = recipientCompanyName;
    }

    public String getRecipientPersonName() {
        return recipientPersonName;
    }

    public void setRecipientPersonName(String recipientPersonName) {
        this.recipientPersonName = recipientPersonName;
    }

    public String getRecipientStreetLines1() {
        return recipientStreetLines1;
    }

    public void setRecipientStreetLines1(String recipientStreetLines1) {
        this.recipientStreetLines1 = recipientStreetLines1;
    }

    public String getRecipientStreetLines2() {
        return recipientStreetLines2;
    }

    public void setRecipientStreetLines2(String recipientStreetLines2) {
        this.recipientStreetLines2 = recipientStreetLines2;
    }

    public String getRecipientCountryCode() {
        return recipientCountryCode;
    }

    public void setRecipientCountryCode(String recipientCountryCode) {
        this.recipientCountryCode = recipientCountryCode;
    }

    public String getRecipientPostalCode() {
        return recipientPostalCode;
    }

    public void setRecipientPostalCode(String recipientPostalCode) {
        this.recipientPostalCode = recipientPostalCode;
    }

    public String getRecipientCity() {
        return recipientCity;
    }

    public void setRecipientCity(String recipientCity) {
        this.recipientCity = recipientCity;
    }

    public String getRecipientEmail() {
        return recipientEmail;
    }

    public void setRecipientEmail(String recipientEmail) {
        this.recipientEmail = recipientEmail;
    }

    public String getRecipientPhoneNumber() {
        return recipientPhoneNumber;
    }

    public void setRecipientPhoneNumber(String recipientPhoneNumber) {
        this.recipientPhoneNumber = recipientPhoneNumber;
    }

    public String getRecipientMobileNumber() {
        return recipientMobileNumber;
    }

    public void setRecipientMobileNumber(String recipientMobileNumber) {
        this.recipientMobileNumber = recipientMobileNumber;
    }

    public String getRecipientStateOrProvinceCode() {
        return recipientStateOrProvinceCode;
    }

    public void setRecipientStateOrProvinceCode(String recipientStateOrProvinceCode) {
        this.recipientStateOrProvinceCode = recipientStateOrProvinceCode;
    }

    public String getCustomsValueAmount() {
        return customsValueAmount;
    }

    public void setCustomsValueAmount(String customsValueAmount) {
        this.customsValueAmount = customsValueAmount;
    }

    public Integer getFreightCharges() {
        return freightCharges;
    }

    public void setFreightCharges(Integer freightCharges) {
        this.freightCharges = freightCharges;
    }

    public String getCustomerReference() {
        return customerReference;
    }

    public void setCustomerReference(String customerReference) {
        this.customerReference = customerReference;
    }

    public String getPackageDimensions() {
        return packageDimensions;
    }

    public void setPackageDimensions(String packageDimensions) {
        this.packageDimensions = packageDimensions;
    }

    public List<StockoutPrintInvoiceItem> getCommodities() {
        return commodities;
    }

    public void setCommodities(List<StockoutPrintInvoiceItem> commodities) {
        this.commodities = commodities;
    }
}
