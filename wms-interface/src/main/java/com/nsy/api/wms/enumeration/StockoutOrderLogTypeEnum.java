package com.nsy.api.wms.enumeration;

import java.util.Arrays;

public enum StockoutOrderLogTypeEnum {
    STOCKOUT_ORDER_GENERATE("出库单生成"),
    READY_WAVE_GENERATED("出库单待生成波次"),
    READY_PICK("生成波次"),
    PICKING("拣货中"),
    READY_OUTBOUND("拣货完成"),
    PACKING_COMFIRM("装箱确认"),
    OUTBOUNDING("开始扫描"),
    SCAN_RETRY("复核重新扫描"),
    SCAN_FINISH("复核完成"),
    BATCH_SCAN("波次扫描"),
    CHANGE_FIND_GOODS_BY_DOC("改以单找货"),
    CONFIRM_LACK("确定缺货"),
    MERGE_STOCKOUT("合并出库单"),
    SECONDARY_SORTING("二次分拣"),
    WCS_SORTING("窄带分拣"),
    WCS_EXCEPTION_SORTING("窄带异常分拣"),
    SORTING_WITHDRAWAL("分拣撤货"),
    COMPLETE_SCAN("完成扫描"),
    SPELLING_ORDER_SUCCESS("拼单成功"),
    READY_DELIVERY("扫描完成"),
    SCAN_ORDER_FINISH("复核任务结束"),
    DELIVERED("已发货"),
    CANCEL("已取消"),
    DOCUMENTS_CONFIRM("单证确定"),
    DELIVERY_PACKAGE("发货打包"),
    DELIVERY_DELETE_PACKAGE("删除包裹"),
    DELIVERY_ADD_PACKAGE("放入包裹"),
    CUSTOMER_PICK_CONFIRM("自提确定"),
    DELIVERY_CONFIRM("发货确定"),
    DELIVERY_CONFIRM_BOX("箱子发货"),
    CHANGE_LOGISTICS("修改物流"),
    GET_LABEL("获取面单"),
    CHANGE_STATUS("状态变更"),
    CHANGE_STATUS_FAIL("状态变更失败"),
    CHANGE_PICKING_TYPE("拣货模式修改"),
    CHANGE_WORK_AREA("更换工作区域"),
    SHIPMENT_WITHDRAWAL("装箱撤货"),
    FORCED_CANCEL("强制取消"),
    SHIPPING_NOTICE("发货通知"),
    NOTICE_LACK("通知缺货"),
    PUSH_OVERSEA("推送海外仓"),
    STOP_OVERSEA("海外仓拦截"),
    OVERSEA_GET_LOGISTICS("海外仓获取配送物流单号"),
    OVERSEA_PUSH_RESULT("推送海外仓"),
    OVERSEA_SHIP_RESULT("海外仓发货"),
    CLEAR_LOGISTICS_NO("清除物流单号"),
    GET_LOGISTICS_FAIL_RECORD("获取物流单号失败"),
    SHIPMENT_CHANGE_LOGISTICS_NO("装箱清单修改物流单号"),
    STOCKOUT_ORDER_CHANGE_LOGISTICS_NO("出库单修改物流单号"),
    SYNC_IOSS("同步erp设置ioss"),
    CREATE_LACK_PICKING_TASK("生成缺货拣货任务"),
    RE_PRINT_TRANSPARENCY_CODE("重新打印T标"),
    PRE_MATCH_TRANSPARENCY_CODE("获取T标"),
    PDD_GET_LABEL("拼多多获取条码"),
    SHEIN_GET_LABEL("Shein获取条码"),
    PDD_CREATE_SHIPORDER("拼多多创建发货单"),
    SHEIN_CREATE_SHIPORDER("shein创建发货单"),
    PDD_SYNC_INFO("拼多多同步信息"),
    STOCK_PREMATCH("库存分配"),
    PDD_PRINT_BOXMARKINFO("拼多多打印箱唛"),
    LACK_PICKING_CANCEL("取消缺货拣货"),
    CANCEL_PREMATCH("取消预配"),
    CANCEL_BATCH("取消预配"),
    SYNC_MEMO("设置备注"),
    OTTO_CREATE_ORDER("otto创建订单"),
    TIKTOK_BUILD_INFO("TK补充信息获取"),
    FBA_LABEL_GET("申请FBA箱贴"),
    CREATE_REPLENISH_ORDER("生成FBA补货单"),
    CANCEL_REPLENISH_ORDER("取消FBA补货单"),
    BIND_CUSTOMER_SHIPMENT("绑定客户Shipment信息"),
    ADD_CANCEL_QTY("增加出库单明细取消数");

    String type;

    StockoutOrderLogTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public static String getNameBy(String logType) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(logType))
                .findAny()
                .map(StockoutOrderLogTypeEnum::getType)
                .orElse(READY_PICK.getType());
    }

}
