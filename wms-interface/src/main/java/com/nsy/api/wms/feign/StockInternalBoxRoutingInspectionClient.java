package com.nsy.api.wms.feign;

import com.nsy.api.wms.request.stock.StockInternalBoxCodeRequest;
import com.nsy.api.wms.response.external.FeignClientResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = "api-wms", contextId = "StockInternalBoxRoutingInspection")
public interface StockInternalBoxRoutingInspectionClient {

    @ApiOperation("根据箱条形码，查询巡检信息")
    @RequestMapping(value = "routing-inspection/routing-inspection-list", method = RequestMethod.POST)
    FeignClientResponse getRoutingInspectionList(@RequestBody StockInternalBoxCodeRequest request);

    @ApiOperation("根据箱条形码，修改内部箱状态")
    @RequestMapping(value = "routing-inspection/internal-box", method = RequestMethod.POST)
    FeignClientResponse changeInternalBoxStatus(@RequestBody StockInternalBoxCodeRequest request);
}
