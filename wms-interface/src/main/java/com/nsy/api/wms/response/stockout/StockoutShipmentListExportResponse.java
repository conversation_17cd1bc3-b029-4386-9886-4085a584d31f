package com.nsy.api.wms.response.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "StockoutShipmentListExportResponse", description = "装箱清单查询导出对象")
public class StockoutShipmentListExportResponse {
    @ApiModelProperty(value = "箱子编号", name = "shipmentBoxCode")
    private String shipmentBoxCode;
    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;
    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;
    @ApiModelProperty(value = "波次号", name = "batchId")
    private String batchId;
    @ApiModelProperty(value = "第几箱", name = "boxIndex")
    private Integer boxIndex;
    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    @ApiModelProperty(value = "重量(千克)", name = "weight")
    private BigDecimal weight;
    @ApiModelProperty(value = "体积重(千克)", name = "volumeWeight")
    private BigDecimal volumeWeight;
    @ApiModelProperty(value = "规格", name = "boxSize")
    private String boxSize;
    @ApiModelProperty(value = "箱内总数", name = "boxSkuAmount")
    private Integer boxSkuAmount;
    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;
    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;
    @ApiModelProperty(value = "货代渠道", name = "forwarderChannel")
    private String forwarderChannel;
    @ApiModelProperty(value = "转运物流单号", name = "transferLogisticsNo")
    private String transferLogisticsNo;
    @ApiModelProperty(value = "仓库", name = "spaceName")
    private String spaceName;
    @ApiModelProperty(value = "工作区域", name = "workspace")
    private String workspace;
    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;
    @ApiModelProperty(value = "运单来源", name = "sourceCn")
    private String sourceCn;
    @ApiModelProperty(value = "打印状态", name = "isPrintCn")
    private String isPrintCn;
    @ApiModelProperty(value = "是否上传发票", name = "uploadInvoice")
    private String uploadInvoice;
    @ApiModelProperty(value = "是否上传发联", name = "uploadFalian")
    private String uploadFalian;
    @ApiModelProperty(value = "发货时间", name = "deliveryDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryDate;
    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    @ApiModelProperty("创建人")
    private String createBy;
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
    @ApiModelProperty("修改人")
    private String updateBy;
    @ApiModelProperty("收货人")
    private String receiverName;
    @ApiModelProperty("拣货人")
    private String pickingName;

    public String getShipmentBoxCode() {
        return shipmentBoxCode;
    }

    public void setShipmentBoxCode(String shipmentBoxCode) {
        this.shipmentBoxCode = shipmentBoxCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Integer getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(Integer boxIndex) {
        this.boxIndex = boxIndex;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public String getBoxSize() {
        return boxSize;
    }

    public void setBoxSize(String boxSize) {
        this.boxSize = boxSize;
    }

    public Integer getBoxSkuAmount() {
        return boxSkuAmount;
    }

    public void setBoxSkuAmount(Integer boxSkuAmount) {
        this.boxSkuAmount = boxSkuAmount;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getForwarderChannel() {
        return forwarderChannel;
    }

    public void setForwarderChannel(String forwarderChannel) {
        this.forwarderChannel = forwarderChannel;
    }

    public String getTransferLogisticsNo() {
        return transferLogisticsNo;
    }

    public void setTransferLogisticsNo(String transferLogisticsNo) {
        this.transferLogisticsNo = transferLogisticsNo;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSourceCn() {
        return sourceCn;
    }

    public void setSourceCn(String sourceCn) {
        this.sourceCn = sourceCn;
    }

    public String getIsPrintCn() {
        return isPrintCn;
    }

    public void setIsPrintCn(String isPrintCn) {
        this.isPrintCn = isPrintCn;
    }

    public String getUploadInvoice() {
        return uploadInvoice;
    }

    public void setUploadInvoice(String uploadInvoice) {
        this.uploadInvoice = uploadInvoice;
    }

    public String getUploadFalian() {
        return uploadFalian;
    }

    public void setUploadFalian(String uploadFalian) {
        this.uploadFalian = uploadFalian;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getPickingName() {
        return pickingName;
    }

    public void setPickingName(String pickingName) {
        this.pickingName = pickingName;
    }
}
