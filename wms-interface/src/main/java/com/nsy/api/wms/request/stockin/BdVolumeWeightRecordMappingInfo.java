package com.nsy.api.wms.request.stockin;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-04 10:22
 */
public class BdVolumeWeightRecordMappingInfo {

    @ApiModelProperty(value = "选择的记录id", name = "id")
    private Integer id;

    @ApiModelProperty(value = "计费费用", name = "fbaCost")
    private BigDecimal fbaCost;

    @ApiModelProperty(value = "高度测量结果", name = "sortingPort")
    private String sortingPort;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public String getSortingPort() {
        return sortingPort;
    }

    public void setSortingPort(String sortingPort) {
        this.sortingPort = sortingPort;
    }
}
