package com.nsy.api.wms.response.stockout;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "StockoutGenerateBatchHotResponse", description = "爆款波次生成Response")
public class StockoutGenerateBatchHotResponse {

    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "待生成波次", name = "readyGeneratedBatchQty")
    private Integer readyGeneratedBatchQty;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "待生成波次出库单id", name = "readyGeneratedBatchId")
    private List<Integer> readyGeneratedBatchId;

    @ApiModelProperty(hidden = true)
    private String stockoutOrderIdStr;

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Integer getReadyGeneratedBatchQty() {
        return readyGeneratedBatchQty;
    }

    public void setReadyGeneratedBatchQty(Integer readyGeneratedBatchQty) {
        this.readyGeneratedBatchQty = readyGeneratedBatchQty;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getStockoutOrderIdStr() {
        return stockoutOrderIdStr;
    }

    public void setStockoutOrderIdStr(String stockoutOrderIdStr) {
        this.stockoutOrderIdStr = stockoutOrderIdStr;
    }

    public List<Integer> getReadyGeneratedBatchId() {
        return readyGeneratedBatchId;
    }

    public void setReadyGeneratedBatchId(List<Integer> readyGeneratedBatchId) {
        this.readyGeneratedBatchId = readyGeneratedBatchId;
    }
}
