package com.nsy.api.wms.response.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel("PDA退货任务信息实体")
public class StockinReturnProductPDATaskInfoResponse {

    @ApiModelProperty("多个任务返回")
    private List<StockinReturnProductMultipleTaskInfo> multipleTaskInfoList;
    @ApiModelProperty("单个任务返回")
    private StockinReturnProductSingleTaskInfo singleTaskInfo;

    public List<StockinReturnProductMultipleTaskInfo> getMultipleTaskInfoList() {
        return multipleTaskInfoList;
    }

    public void setMultipleTaskInfoList(List<StockinReturnProductMultipleTaskInfo> multipleTaskInfoList) {
        this.multipleTaskInfoList = multipleTaskInfoList;
    }

    public StockinReturnProductSingleTaskInfo getSingleTaskInfo() {
        return singleTaskInfo;
    }

    public void setSingleTaskInfo(StockinReturnProductSingleTaskInfo singleTaskInfo) {
        this.singleTaskInfo = singleTaskInfo;
    }
}
