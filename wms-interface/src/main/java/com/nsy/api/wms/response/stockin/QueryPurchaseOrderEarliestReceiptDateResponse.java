package com.nsy.api.wms.response.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 采购订单最新发货日期
 *
 * <AUTHOR>
 */
@ApiModel(value = "QueryPurchaseOrderEarliestReceiptDateResponse", description = "采购单最早收货日期")
public class QueryPurchaseOrderEarliestReceiptDateResponse implements Serializable {

    private static final long serialVersionUID = 3664111044899721966L;
    @ApiModelProperty("采购计划单号")
    public String purchasePlanNo;

    @ApiModelProperty("SKU")
    public String sku;

    @ApiModelProperty("采购单最早收货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date earliestReceiptDate;


    @ApiModelProperty("采购单最最晚收货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public Date latestReceiptDate;

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public Date getEarliestReceiptDate() {
        return earliestReceiptDate;
    }

    public void setEarliestReceiptDate(Date earliestReceiptDate) {
        this.earliestReceiptDate = earliestReceiptDate;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Date getLatestReceiptDate() {
        return latestReceiptDate;
    }

    public void setLatestReceiptDate(Date latestReceiptDate) {
        this.latestReceiptDate = latestReceiptDate;
    }
}
