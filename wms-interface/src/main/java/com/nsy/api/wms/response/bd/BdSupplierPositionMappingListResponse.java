package com.nsy.api.wms.response.bd;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "BdSupplierPositionMappingListResponse", description = "供应商退货库位列表详情")
public class BdSupplierPositionMappingListResponse {

    @ApiModelProperty(value = "主键标识", name = "id")
    private Integer mappingId;

    @ApiModelProperty(value = "供应商Id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "采购员", name = "buyerName")
    private String buyerName;

    @ApiModelProperty(value = "库区", name = "spaceAreaName")
    private String spaceAreaName;

    @ApiModelProperty(value = "库位编号", name = "positionCode")
    private String positionCode;

    @ApiModelProperty(value = "0 启用，1 禁用", name = "status")
    private Boolean status;

    @ApiModelProperty(value = "退货数", name = "returnQty")
    private Integer returnQty;

    @ApiModelProperty(value = "创建人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "创建时间", name = "createDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "退货性质", name = "returnNature")
    private String returnNature;

    @ApiModelProperty(value = "仓库名称", name = "spaceName")
    private String spaceName;

    @ApiModelProperty(value = "退货性质(英文)", name = "returnNatureEn")
    private String returnNatureEn;

    @ApiModelProperty(value = "退货明细最早创建时间", name = "earliestProductCreateDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date earliestProductCreateDate;

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }


    public String getReturnNature() {
        return returnNature;
    }

    public void setReturnNature(String returnNature) {
        this.returnNature = returnNature;
    }

    public String getReturnNatureEn() {
        return returnNatureEn;
    }

    public void setReturnNatureEn(String returnNatureEn) {
        this.returnNatureEn = returnNatureEn;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Date getEarliestProductCreateDate() {
        return earliestProductCreateDate;
    }

    public void setEarliestProductCreateDate(Date earliestProductCreateDate) {
        this.earliestProductCreateDate = earliestProductCreateDate;
    }
}

