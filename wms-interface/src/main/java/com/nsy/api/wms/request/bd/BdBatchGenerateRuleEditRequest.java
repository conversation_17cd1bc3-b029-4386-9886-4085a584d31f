package com.nsy.api.wms.request.bd;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "BdBatchGenerateRuleEditRequest", description = "波次生成规则新增request")
public class BdBatchGenerateRuleEditRequest {


    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id", name = "spaceId", required = true)
    private Integer spaceId;

    @ApiModelProperty(value = "工作区", name = "workspace", required = true)
    private String workspace;

    /**
     * 出库类型
     */
    @ApiModelProperty(value = "出库类型", name = "stockoutType", required = true)
    private String stockoutType;

    /**
     * 描述
     */
    @ApiModelProperty(value = "备注", name = "description")
    private String description;

    /**
     * 状态 1-停用，0-启用
     */
    @ApiModelProperty(value = "是否停用 1-停用，0-启用", name = "isDeleted", required = true)
    private Integer isDeleted;


    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getStockoutType() {
        return stockoutType;
    }

    public void setStockoutType(String stockoutType) {
        this.stockoutType = stockoutType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }
}
