package com.nsy.api.wms.response.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutBatchOrderResponse", description = "波次出库单查询Response")
public class StockoutBatchOrderResponse {


    /**
     * 批次id
     */
    @ApiModelProperty(value = "波次Id", name = "batchId")
    private Integer batchId;

    @ApiModelProperty(value = "出库订单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;
    /**
     * 是否缺货
     */
    @ApiModelProperty(value = "是否缺货", name = "isLack")
    private Integer isLack;

    @ApiModelProperty(value = "SKU总数量", name = "skuQty")
    private Integer skuQty;

    @ApiModelProperty(value = "扫描数量", name = "scanQty")
    private Integer scanQty;

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getIsLack() {
        return isLack;
    }

    public void setIsLack(Integer isLack) {
        this.isLack = isLack;
    }

    public Integer getSkuQty() {
        return skuQty;
    }

    public void setSkuQty(Integer skuQty) {
        this.skuQty = skuQty;
    }

    public Integer getScanQty() {
        return scanQty;
    }

    public void setScanQty(Integer scanQty) {
        this.scanQty = scanQty;
    }
}
