package com.nsy.api.wms.domain.qc;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class QcRecordExport {

    private String sku;

    private String internalBoxCode;

    private String internalBoxType;

    private Integer qty;

    private Integer qualifiedCount;

    private Integer unqualifiedCount;

    private String createBy;

    private String createDate;

    private String unqualifiedCategory;
    private String unqualifiedReason;
    private String unqualifiedReasonInfo;
    private String businessType;
    private String storeName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lendReturnDate;

    private String returnOrderNo;
    private String stockLendReturnCode;
    private String stockLendCode;

    public String getReturnOrderNo() {
        return returnOrderNo;
    }

    public void setReturnOrderNo(String returnOrderNo) {
        this.returnOrderNo = returnOrderNo;
    }

    public String getStockLendReturnCode() {
        return stockLendReturnCode;
    }

    public void setStockLendReturnCode(String stockLendReturnCode) {
        this.stockLendReturnCode = stockLendReturnCode;
    }

    public String getStockLendCode() {
        return stockLendCode;
    }

    public void setStockLendCode(String stockLendCode) {
        this.stockLendCode = stockLendCode;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getUnqualifiedReasonInfo() {
        return unqualifiedReasonInfo;
    }

    public void setUnqualifiedReasonInfo(String unqualifiedReasonInfo) {
        this.unqualifiedReasonInfo = unqualifiedReasonInfo;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Date getLendReturnDate() {
        return lendReturnDate;
    }

    public void setLendReturnDate(Date lendReturnDate) {
        this.lendReturnDate = lendReturnDate;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getInternalBoxType() {
        return internalBoxType;
    }

    public void setInternalBoxType(String internalBoxType) {
        this.internalBoxType = internalBoxType;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public Integer getQualifiedCount() {
        return qualifiedCount;
    }

    public void setQualifiedCount(Integer qualifiedCount) {
        this.qualifiedCount = qualifiedCount;
    }

    public Integer getUnqualifiedCount() {
        return unqualifiedCount;
    }

    public void setUnqualifiedCount(Integer unqualifiedCount) {
        this.unqualifiedCount = unqualifiedCount;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }
}
