package com.nsy.api.wms.response.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "StockoutReplaceSkuResponse", description = "换码标注返回")
public class StockoutReplaceSkuResponse {

    @ApiModelProperty(value = "是否展示", name = "showAlert")
    private Boolean showAlert;

    @ApiModelProperty(value = "换码", name = "replaceSku")
    private String replaceSku;


    public String getReplaceSku() {
        return replaceSku;
    }

    public void setReplaceSku(String replaceSku) {
        this.replaceSku = replaceSku;
    }

    public Boolean getShowAlert() {
        return showAlert;
    }

    public void setShowAlert(Boolean showAlert) {
        this.showAlert = showAlert;
    }
}
