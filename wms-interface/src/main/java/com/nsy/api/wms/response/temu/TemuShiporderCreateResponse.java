package com.nsy.api.wms.response.temu;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * temu 创建发货单返回
 */
public class TemuShiporderCreateResponse {
    @JsonProperty("result")
    private Result result;


    public Result getResult() {
        return this.result;
    }

    public static class Result {
        @JsonProperty("cancelUrgencyTypeSubPurchaseOrderSnList")
        private List<String> cancelUrgencyTypeSubPurchaseOrderSnList;
        @JsonProperty("isUrgencyType")
        private Boolean isUrgencyType;
        @JsonProperty("cancelUrgencyType")
        private Boolean cancelUrgencyType;

        public void setCancelUrgencyTypeSubPurchaseOrderSnList(List<String> cancelUrgencyTypeSubPurchaseOrderSnList) {
            this.cancelUrgencyTypeSubPurchaseOrderSnList = cancelUrgencyTypeSubPurchaseOrderSnList;
        }

        public Boolean getUrgencyType() {
            return isUrgencyType;
        }

        public void setUrgencyType(Boolean urgencyType) {
            isUrgencyType = urgencyType;
        }

        public void setCancelUrgencyType(Boolean cancelUrgencyType) {
            this.cancelUrgencyType = cancelUrgencyType;
        }

        public List<String> getCancelUrgencyTypeSubPurchaseOrderSnList() {
            return this.cancelUrgencyTypeSubPurchaseOrderSnList;
        }

        public Boolean getIsUrgencyType() {
            return this.isUrgencyType;
        }

        public Boolean getCancelUrgencyType() {
            return this.cancelUrgencyType;
        }
    }
}
