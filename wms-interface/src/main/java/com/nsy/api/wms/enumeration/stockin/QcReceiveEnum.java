package com.nsy.api.wms.enumeration.stockin;

import java.util.Arrays;

/**
 * 入库质检优先级
 */
public enum QcReceiveEnum {
    STOCKIN_QC_COMBINE("边入库边质检"),
    STOCKIN_QC("先入库后质检"),
    QC_STOCKIN("先质检后入库");
    QcReceiveEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }

    public static String getDesc(String qcReceive) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(qcReceive))
                .findAny()
                .map(QcReceiveEnum::getName)
                .orElse(STOCKIN_QC_COMBINE.getName());
    }
}
