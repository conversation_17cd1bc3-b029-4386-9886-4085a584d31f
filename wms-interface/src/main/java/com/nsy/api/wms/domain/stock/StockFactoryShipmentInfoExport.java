package com.nsy.api.wms.domain.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class StockFactoryShipmentInfoExport {
    // 工厂出库单号
    private String supplierDeliveryNo;

    // 供应商名称
    private String supplierName;

    // 仓库
    private String spaceName;

    // 采购员真实姓名
    private String purchaseUserRealName;

    // 采购模式中文展示
    private String purchaseModelStr;

    // 工厂发货时间
    private String deliveryDate;

    // 预计到货时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planArriveDate;

    // 发货总箱数
    private Integer planBoxNum;

    // 发货总件数
    private Integer shipmentQty;

    // 收货总件数
    private Integer receiveQty;

    // 需退货件数
    private Integer returnQty;

    // 上架件数
    private Integer shelvedQty;

    // 预约状态中文展示
    private String appointmentStatusStr;

    // 入库状态展示
    private String stockinStatusStr;

    // 月台名称
    private String platformName;

    // 物流公司
    private String logisticsCompany;

    // 物流单号
    private String logisticsNo;

    // 预约入库时间
    private String appointmentStockinDate;

    // 到仓时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditDate;

    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    //直发备注
    private String remarks;

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public Date getPlanArriveDate() {
        return planArriveDate;
    }

    public void setPlanArriveDate(Date planArriveDate) {
        this.planArriveDate = planArriveDate;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getPurchaseUserRealName() {
        return purchaseUserRealName;
    }

    public void setPurchaseUserRealName(String purchaseUserRealName) {
        this.purchaseUserRealName = purchaseUserRealName;
    }

    public String getPurchaseModelStr() {
        return purchaseModelStr;
    }

    public void setPurchaseModelStr(String purchaseModelStr) {
        this.purchaseModelStr = purchaseModelStr;
    }

    public String getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Integer getPlanBoxNum() {
        return planBoxNum;
    }

    public void setPlanBoxNum(Integer planBoxNum) {
        this.planBoxNum = planBoxNum;
    }

    public Integer getShipmentQty() {
        return shipmentQty;
    }

    public void setShipmentQty(Integer shipmentQty) {
        this.shipmentQty = shipmentQty;
    }

    public Integer getReceiveQty() {
        return receiveQty;
    }

    public void setReceiveQty(Integer receiveQty) {
        this.receiveQty = receiveQty;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public Integer getShelvedQty() {
        return shelvedQty;
    }

    public void setShelvedQty(Integer shelvedQty) {
        this.shelvedQty = shelvedQty;
    }

    public String getAppointmentStatusStr() {
        return appointmentStatusStr;
    }

    public void setAppointmentStatusStr(String appointmentStatusStr) {
        this.appointmentStatusStr = appointmentStatusStr;
    }

    public String getStockinStatusStr() {
        return stockinStatusStr;
    }

    public void setStockinStatusStr(String stockinStatusStr) {
        this.stockinStatusStr = stockinStatusStr;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getAppointmentStockinDate() {
        return appointmentStockinDate;
    }

    public void setAppointmentStockinDate(String appointmentStockinDate) {
        this.appointmentStockinDate = appointmentStockinDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
