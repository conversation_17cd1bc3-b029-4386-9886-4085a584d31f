package com.nsy.api.wms.request.qa;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-02-24 18:23
 */
public class BdQaInspectPageRequest extends PageRequest {

    @ApiModelProperty("规则名")
    private String ruleName;

    @ApiModelProperty("启用/禁用")
    private Integer isDeleted;

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
