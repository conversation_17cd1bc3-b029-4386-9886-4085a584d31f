package com.nsy.api.wms.request.stockout;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import java.util.List;

public class PrintKjpBoxRequest {

    @Size(min = 1, message = "idList不能为空")
    private List<Integer> idList;

    private Integer printNum;

    @ApiModelProperty("是否展示订单号 0：否 1：是")
    private Integer isShowOrder;

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }

    public Integer getPrintNum() {
        return printNum;
    }

    public void setPrintNum(Integer printNum) {
        this.printNum = printNum;
    }

    public Integer getIsShowOrder() {
        return isShowOrder;
    }

    public void setIsShowOrder(Integer isShowOrder) {
        this.isShowOrder = isShowOrder;
    }
}
