package com.nsy.api.wms.domain.stockout;

public class FaireBoxCodePrint {


    /**
     * 箱号
     */
    private String boxCode;
    private String boxCodeIndex;

    /**
     * 出库单号
     */
    private String orderNos;

    private String receiver;

    private String receiverAddress;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 收件人省份
     */
    private String province;

    /**
     * 收货城市
     */
    private String city;
    /**
     * 收货地址区县
     */
    private String area;
    /**
     * 邮编
     */
    private String postalCode;

    public String getBoxCode() {
        return boxCode;
    }

    public void setBoxCode(String boxCode) {
        this.boxCode = boxCode;
    }

    public String getBoxCodeIndex() {
        return boxCodeIndex;
    }

    public void setBoxCodeIndex(String boxCodeIndex) {
        this.boxCodeIndex = boxCodeIndex;
    }

    public String getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(String orderNos) {
        this.orderNos = orderNos;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }
}
