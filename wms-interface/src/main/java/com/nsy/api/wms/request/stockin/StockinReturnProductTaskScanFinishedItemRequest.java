package com.nsy.api.wms.request.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "StockinReturnProductTaskScanFinishedItemRequest", description = "采购退货退货完成明细request")
public class StockinReturnProductTaskScanFinishedItemRequest {

    @ApiModelProperty(value = "returnProductId", name = "returnProductId")
    private Integer returnProductId;

    @ApiModelProperty(value = "sku", name = "sku")
    private String sku;

    @ApiModelProperty(value = "数量", name = "qty")
    private Integer qty;

    /**
     * 原区域
     */
    private Integer sourceAreaId;

    //包装方式
    private String packingMethod = "";

    //版本号
    private String versionNo = "";

    public Integer getReturnProductId() {
        return returnProductId;
    }

    public void setReturnProductId(Integer returnProductId) {
        this.returnProductId = returnProductId;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSourceAreaId() {
        return sourceAreaId;
    }

    public void setSourceAreaId(Integer sourceAreaId) {
        this.sourceAreaId = sourceAreaId;
    }

    public String getPackingMethod() {
        return packingMethod;
    }

    public void setPackingMethod(String packingMethod) {
        this.packingMethod = packingMethod;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }
}
