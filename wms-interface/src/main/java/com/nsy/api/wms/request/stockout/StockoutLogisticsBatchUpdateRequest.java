package com.nsy.api.wms.request.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023-03-27 09:50:22
 */
@ApiModel(value = "StockoutLogisticsBatchUpdateRequest", description = "更新request")
public class StockoutLogisticsBatchUpdateRequest {
    @ApiModelProperty("id")
    private Integer id;


    /**
     * 实际单价
     */
    @ApiModelProperty("实际单价")
    private BigDecimal realUnitPrice;



    /**
     * 实际总价
     */
    @ApiModelProperty("实际总价")
    private BigDecimal realPrice;


    /**
     * 实际重量
     */
    @ApiModelProperty("实际重量")
    private BigDecimal realWeight;


    /**
     * 税费
     */
    @ApiModelProperty("税费")
    private BigDecimal realTax;

    /**
     * 报关费用
     */
    @ApiModelProperty("报关费用")
    private BigDecimal realDeclarePrice;

    /**
     * 附件地址
     */
    @ApiModelProperty("附件地址")
    private String memoDocumentUrl;

    @ApiModelProperty("备注")
    private String memo;

    public String getMemoDocumentUrl() {
        return memoDocumentUrl;
    }

    public void setMemoDocumentUrl(String memoDocumentUrl) {
        this.memoDocumentUrl = memoDocumentUrl;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public BigDecimal getRealUnitPrice() {
        return realUnitPrice;
    }

    public void setRealUnitPrice(BigDecimal realUnitPrice) {
        this.realUnitPrice = realUnitPrice;
    }

    public BigDecimal getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    public BigDecimal getRealWeight() {
        return realWeight;
    }

    public void setRealWeight(BigDecimal realWeight) {
        this.realWeight = realWeight;
    }

    public BigDecimal getRealTax() {
        return realTax;
    }

    public void setRealTax(BigDecimal realTax) {
        this.realTax = realTax;
    }

    public BigDecimal getRealDeclarePrice() {
        return realDeclarePrice;
    }

    public void setRealDeclarePrice(BigDecimal realDeclarePrice) {
        this.realDeclarePrice = realDeclarePrice;
    }
}

