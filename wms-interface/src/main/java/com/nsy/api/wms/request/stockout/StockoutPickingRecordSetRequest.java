package com.nsy.api.wms.request.stockout;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutPickingRecordSetRequest", description = "拣货任务SKU信息录入request")
public class StockoutPickingRecordSetRequest {

    @ApiModelProperty(value = "拣货任务明细主键id", name = "taskItemId")
    private Integer taskItemId;

    @ApiModelProperty(value = "内部箱号", name = "batchCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "扫描数量", name = "qty")
    private Integer qty;

    public StockoutPickingRecordSetRequest() {
    }

    public StockoutPickingRecordSetRequest(Integer taskItemId, String internalBoxCode, Integer qty) {
        this.taskItemId = taskItemId;
        this.internalBoxCode = internalBoxCode;
        this.qty = qty;
    }

    public Integer getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(Integer taskItemId) {
        this.taskItemId = taskItemId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }
}
