package com.nsy.api.wms.response.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * HXD
 * 装箱打印/出库单打印返回
 * 2021/10/22
 **/
@ApiModel(value = "StockoutOrderLabelResponse", description = "物流面单")
public class StockoutOrderLabelResponse {

    @ApiModelProperty("id")
    private Integer labelId;

    /**
     * 地区
     */
    @ApiModelProperty("地区")
    private String location;

    /**
     * 物流单号
     */
    @ApiModelProperty("物流单号")
    private String logisticsNo;

    /**
     * 出库单Id
     */
    @ApiModelProperty("出库单Id")
    private Integer stockoutOrderId;

    /**
     * 面单链接
     */
    @ApiModelProperty("面单链接")
    private String labelUrl;

    /**
     * 面单内容
     */
    @ApiModelProperty("面单内容")
    private String printContent;

    /**
     * 面单来源
     */
    @ApiModelProperty("面单来源")
    private String source;

    /**
     * 是否打印(1为是0为否)
     */
    @ApiModelProperty("是否打印(1为是0为否)")
    private Integer isPrint;

    @ApiModelProperty("箱子编号")
    private List<String> shipmentBoxCode;

    public List<String> getShipmentBoxCode() {
        return shipmentBoxCode;
    }

    public void setShipmentBoxCode(List<String> shipmentBoxCode) {
        this.shipmentBoxCode = shipmentBoxCode;
    }

    public Integer getLabelId() {
        return labelId;
    }

    public void setLabelId(Integer labelId) {
        this.labelId = labelId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Integer getStockoutOrderId() {
        return stockoutOrderId;
    }

    public void setStockoutOrderId(Integer stockoutOrderId) {
        this.stockoutOrderId = stockoutOrderId;
    }

    public String getLabelUrl() {
        return labelUrl;
    }

    public void setLabelUrl(String labelUrl) {
        this.labelUrl = labelUrl;
    }

    public String getPrintContent() {
        return printContent;
    }

    public void setPrintContent(String printContent) {
        this.printContent = printContent;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getIsPrint() {
        return isPrint;
    }

    public void setIsPrint(Integer isPrint) {
        this.isPrint = isPrint;
    }
}
