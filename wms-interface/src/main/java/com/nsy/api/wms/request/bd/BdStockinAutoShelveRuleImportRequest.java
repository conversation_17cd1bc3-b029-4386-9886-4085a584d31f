package com.nsy.api.wms.request.bd;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-04-28 16:18
 */
public class BdStockinAutoShelveRuleImportRequest {
    @ApiModelProperty("仓库id")
    private Integer spaceId;
    @ApiModelProperty(value = "仓库名称", required = true)
    @NotBlank(message = "仓库名称不能为空")
    private String spaceName;

    @ApiModelProperty("内部箱号")
    private String internalBoxCode;

    @ApiModelProperty("内部类型")
    private String internalBoxType;

    @ApiModelProperty(value = "目标库位", required = true)
    @NotBlank(message = "目标库位不能为空")
    private String positionCode;


    private String errorMsg;

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getInternalBoxType() {
        return internalBoxType;
    }

    public void setInternalBoxType(String internalBoxType) {
        this.internalBoxType = internalBoxType;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }
} 
