package com.nsy.api.wms.request.bd;

import com.nsy.api.wms.domain.bd.BdSystemParameter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "BdSystemParameterRequest", description = "系统参数配置request")
public class BdSystemParameterRequest implements Serializable {
    private static final long serialVersionUID = 4511855945504784255L;
    /**
     * 配置
     */
    @ApiModelProperty(value = "配置键", name = "configList")
    List<BdSystemParameter> configList;

    public List<BdSystemParameter> getConfigList() {
        return configList;
    }

    public void setConfigList(List<BdSystemParameter> configList) {
        this.configList = configList;
    }
}
