package com.nsy.api.wms.request.stockout;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ApiModel(value = "StockoutPackageBagAddRequest", description = "发货包裹新增request")
public class StockoutPackageBagAddRequest {
    private Integer bagId;

    @ApiModelProperty(value = "发货编号", name = "bagCode")
    private String bagCode;

    @ApiModelProperty(value = "箱号", name = "bagIndex")
    private int bagIndex;

    @ApiModelProperty(value = "地区", name = "bagIndex")
    private String location;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "包裹数量", name = "packageQty")
    private int packageQty;

    @ApiModelProperty(value = "重量", name = "weight")
    private BigDecimal weight;

    @ApiModelProperty(value = "报关方式", name = "customsDeclareType")
    private String customsDeclareType;

    @ApiModelProperty(value = "物流渠道", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private List<String> logisticsNoList;


    @ApiModelProperty(value = "打包人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "更新时间", name = "updateDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    @ApiModelProperty(value = "更新人", name = "updateBy")
    private String updateBy;

    public Integer getBagId() {
        return bagId;
    }

    public void setBagId(Integer bagId) {
        this.bagId = bagId;
    }

    public String getBagCode() {
        return bagCode;
    }

    public void setBagCode(String bagCode) {
        this.bagCode = bagCode;
    }

    public int getBagIndex() {
        return bagIndex;
    }

    public void setBagIndex(int bagIndex) {
        this.bagIndex = bagIndex;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getPackageQty() {
        return packageQty;
    }

    public void setPackageQty(int packageQty) {
        this.packageQty = packageQty;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getCustomsDeclareType() {
        return customsDeclareType;
    }

    public void setCustomsDeclareType(String customsDeclareType) {
        this.customsDeclareType = customsDeclareType;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public List<String> getLogisticsNo() {
        return logisticsNoList;
    }

    public void setLogisticsNo(List<String> logisticsNo) {
        this.logisticsNoList = logisticsNo;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
