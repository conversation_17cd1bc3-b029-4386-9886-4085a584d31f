package com.nsy.api.wms.enumeration.stock;

import org.apache.commons.lang3.StringUtils;

public enum StockPlatformScheduleLogTypeEnum {

    GENERATE("月台生成"),
    GENERATE_STOCKIN_TASK("生成入库任务"),
    START_RECEIVING("开始收货"),
    ALL_RECEIVED("全部收货完成"),
    ALL_COMPLETED("入库单全部完成");

    String logType;

    StockPlatformScheduleLogTypeEnum(String logType) {
        this.logType = logType;
    }

    public String getLogType() {
        return logType;
    }

    public static String getByName(String name) {
        for (StockPlatformScheduleLogTypeEnum s : StockPlatformScheduleLogTypeEnum.values()) {
            if (StringUtils.equals(s.name(), name)) {
                return s.getLogType();
            }
        }
        return StringUtils.EMPTY;
    }
}
