package com.nsy.api.wms.response.stockin;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/30 18:30
 */
public class StockinSupplierDeliveryEfficientStatusDTO {

    private String supplierDeliveryNo;

    private String status;

    /**
     * 月台审核时间
     */
    private Date auditDate;

    /**
     * 最晚收货时间
     */
    private Date receivedEndDate;

    /**
     * 开始质检时间
     */
    private Date completeQcStartDate;

    /**
     * 最晚质检时间
     */
    private Date completeQcEndDate;

    /**
     * 开始上架时间
     */
    private Date completeShelvedStartDate;

    /**
     * 最晚上架时间
     */
    private Date completeShelvedEndDate;

    /**
     * 上架完成时间
     */
    private Date completeDate;

    /**
     * 核对完成时间
     */
    private Date checkedDate;

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public Date getCompleteQcStartDate() {
        return completeQcStartDate;
    }

    public void setCompleteQcStartDate(Date completeQcStartDate) {
        this.completeQcStartDate = completeQcStartDate;
    }

    public Date getCompleteQcEndDate() {
        return completeQcEndDate;
    }

    public void setCompleteQcEndDate(Date completeQcEndDate) {
        this.completeQcEndDate = completeQcEndDate;
    }

    public Date getCompleteShelvedStartDate() {
        return completeShelvedStartDate;
    }

    public void setCompleteShelvedStartDate(Date completeShelvedStartDate) {
        this.completeShelvedStartDate = completeShelvedStartDate;
    }

    public Date getCompleteShelvedEndDate() {
        return completeShelvedEndDate;
    }

    public void setCompleteShelvedEndDate(Date completeShelvedEndDate) {
        this.completeShelvedEndDate = completeShelvedEndDate;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }

    public Date getReceivedEndDate() {
        return receivedEndDate;
    }

    public void setReceivedEndDate(Date receivedEndDate) {
        this.receivedEndDate = receivedEndDate;
    }

    public Date getCheckedDate() {
        return checkedDate;
    }

    public void setCheckedDate(Date checkedDate) {
        this.checkedDate = checkedDate;
    }
}
