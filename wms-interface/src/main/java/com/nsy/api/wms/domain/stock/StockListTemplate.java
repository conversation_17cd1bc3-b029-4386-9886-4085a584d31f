package com.nsy.api.wms.domain.stock;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

public class StockListTemplate {

    @ApiModelProperty(value = "库存Id", name = "stockId")
    private Integer stockId;

    @ApiModelProperty(value = "商品编码", name = "spu")
    private String spu;

    @ApiModelProperty(value = "商品名称", name = "productName")
    private String productName;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "仓库名称", name = "spaceName")
    private String spaceName;

    @ApiModelProperty(value = "库区名称", name = "spaceAreaName")
    private String spaceAreaName;

    @ApiModelProperty(value = "库位编码", name = "positionCode")
    private String positionCode;

    @ApiModelProperty(value = "内部箱编码", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "库存", name = "stock")
    private Integer stock;

    @ApiModelProperty(value = "预配数", name = "preQty")
    private Integer preQty;

    @ApiModelProperty(value = "库容总重量（g）", name = "maximumWeight")
    private BigDecimal maximumWeight;

    @ApiModelProperty(value = "库容总体积（cm3）", name = "maximumVolume")
    private BigDecimal maximumVolume;

    @ApiModelProperty(value = "备注", name = "description")
    private String description;

    public Integer getStockId() {
        return stockId;
    }

    public void setStockId(Integer stockId) {
        this.stockId = stockId;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Integer getPreQty() {
        return preQty;
    }

    public void setPreQty(Integer preQty) {
        this.preQty = preQty;
    }

    public BigDecimal getMaximumWeight() {
        return maximumWeight;
    }

    public void setMaximumWeight(BigDecimal maximumWeight) {
        this.maximumWeight = maximumWeight;
    }

    public BigDecimal getMaximumVolume() {
        return maximumVolume;
    }

    public void setMaximumVolume(BigDecimal maximumVolume) {
        this.maximumVolume = maximumVolume;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
