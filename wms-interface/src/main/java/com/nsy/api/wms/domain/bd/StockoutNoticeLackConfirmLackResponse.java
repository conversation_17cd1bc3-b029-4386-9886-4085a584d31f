package com.nsy.api.wms.domain.bd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "StockoutNoticeLackConfirmLackResponse", description = "通知缺货 - 确认缺货")
public class StockoutNoticeLackConfirmLackResponse {

    @ApiModelProperty(value = "sku列表", name = "skuList")
    private List<String> skuList;

    @ApiModelProperty(value = "出库单号列表", name = "stockoutOrderNoList")
    private List<String> stockoutOrderNoList;

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }


    public List<String> getStockoutOrderNoList() {
        return stockoutOrderNoList;
    }

    public void setStockoutOrderNoList(List<String> stockoutOrderNoList) {
        this.stockoutOrderNoList = stockoutOrderNoList;
    }
}
