package com.nsy.api.wms.domain.bd;


import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
public class BdChangeLog {

    private Integer id;

    /** 地区 */
    private String location;

    /** 事件类型 */
    private String type;

    /** 日志消息 */
    private String content;

    /** 变更表 */
    private String changeTable;

    /** 日志明细 */
    private String detail;

    /** 版本号 */
    private Integer version;

    /** 创建时间 */
    private Date createDate;

    /** 创建者 */
    private String createBy;

    /** 更新时间 */
    private Date updateDate;

    /** 更新者 */
    private String updateBy;


    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public String getChangeTable() {
        return this.changeTable;
    }

    public void setChangeTable(String changeTable) {
        this.changeTable = changeTable;
    }
    public String getDetail() {
        return this.detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
}
