package com.nsy.api.wms.request.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "StockLendExportRequest", description = "借用单导出请求request")
public class StockLendExportRequest implements Serializable {

    @ApiModelProperty(value = "借用单ID列表", name = "idList")
    private List<Integer> idList = new ArrayList<>();

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }
}
