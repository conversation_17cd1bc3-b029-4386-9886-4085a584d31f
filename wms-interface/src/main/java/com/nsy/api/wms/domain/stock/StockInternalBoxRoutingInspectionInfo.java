package com.nsy.api.wms.domain.stock;

import java.util.Date;

public class StockInternalBoxRoutingInspectionInfo {

    private Integer boxDetailId;

    private Integer supplierId;

    private String supplierNum;

    private String supplierName;

    private String purchaseNumber;

    private String receiveOrder;

    private String stockinOrderNo;

    private String sku;

    private String boxBarcode;

    private Integer productInboundsCount;

    private Date inboundsDate;

    private String status;

    private String skuBarcode;

    private String colorSku;

    private String productSku;

    private String workmanshipVersion;

    private Integer isNew;

    private Integer purchasingApplyType;

    private String purchasingApplyTypeStr;

    public Integer getPurchasingApplyType() {
        return purchasingApplyType;
    }

    public void setPurchasingApplyType(Integer purchasingApplyType) {
        this.purchasingApplyType = purchasingApplyType;
    }

    public String getPurchasingApplyTypeStr() {
        return purchasingApplyTypeStr;
    }

    public void setPurchasingApplyTypeStr(String purchasingApplyTypeStr) {
        this.purchasingApplyTypeStr = purchasingApplyTypeStr;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    public String getWorkmanshipVersion() {
        return workmanshipVersion;
    }

    public void setWorkmanshipVersion(String workmanshipVersion) {
        this.workmanshipVersion = workmanshipVersion;
    }

    public Integer getBoxDetailId() {
        return boxDetailId;
    }

    public void setBoxDetailId(Integer boxDetailId) {
        this.boxDetailId = boxDetailId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierNum() {
        return supplierNum;
    }

    public void setSupplierNum(String supplierNum) {
        this.supplierNum = supplierNum;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getPurchaseNumber() {
        return purchaseNumber;
    }

    public void setPurchaseNumber(String purchaseNumber) {
        this.purchaseNumber = purchaseNumber;
    }

    public String getReceiveOrder() {
        return receiveOrder;
    }

    public void setReceiveOrder(String receiveOrder) {
        this.receiveOrder = receiveOrder;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBoxBarcode() {
        return boxBarcode;
    }

    public void setBoxBarcode(String boxBarcode) {
        this.boxBarcode = boxBarcode;
    }

    public Integer getProductInboundsCount() {
        return productInboundsCount;
    }

    public void setProductInboundsCount(Integer productInboundsCount) {
        this.productInboundsCount = productInboundsCount;
    }

    public Date getInboundsDate() {
        return inboundsDate;
    }

    public void setInboundsDate(Date inboundsDate) {
        this.inboundsDate = inboundsDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSkuBarcode() {
        return skuBarcode;
    }

    public void setSkuBarcode(String skuBarcode) {
        this.skuBarcode = skuBarcode;
    }

    public String getColorSku() {
        return colorSku;
    }

    public void setColorSku(String colorSku) {
        this.colorSku = colorSku;
    }

    public String getProductSku() {
        return productSku;
    }

    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }

    public String getStockinOrderNo() {
        return stockinOrderNo;
    }

    public void setStockinOrderNo(String stockinOrderNo) {
        this.stockinOrderNo = stockinOrderNo;
    }
}
