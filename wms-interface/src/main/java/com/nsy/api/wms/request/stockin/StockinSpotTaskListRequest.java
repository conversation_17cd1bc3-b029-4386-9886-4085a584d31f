package com.nsy.api.wms.request.stockin;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "StockinSpotTaskListRequest", description = "现货入库任务列表查询request")
public class StockinSpotTaskListRequest extends PageRequest {

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "现货计划单号", name = "purchasePlanNo")
    private String purchasePlanNo;

    public StockinSpotTaskListRequest(String logisticsNo, String purchasePlanNo) {
        this.logisticsNo = logisticsNo;
        this.purchasePlanNo = purchasePlanNo;
    }

    public StockinSpotTaskListRequest() {
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }
}
