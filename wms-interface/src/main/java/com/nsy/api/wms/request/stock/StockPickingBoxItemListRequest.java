package com.nsy.api.wms.request.stock;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockPickingBoxItemListRequest", description = "拣货箱明细列表request")
public class StockPickingBoxItemListRequest extends PageRequest implements Serializable {

    @ApiModelProperty(value = "波次号", name = "batchId")
    private Integer batchId;

    @ApiModelProperty(value = "拣货箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "状态：EMPTY空箱,PACKING装箱中,WAIT_QC待质检,QC_PROCESSING质检中,WAIT_SHELVE待上架,SHELVING上架中,SHELVED已上架,RETURNING退货中", name = "status")
    private String status;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "创建开始时间", name = "createStartDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createStartDate;

    @ApiModelProperty(value = "创建结束时间", name = "createEndDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createEndDate;

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(String createStartDate) {
        this.createStartDate = createStartDate;
    }

    public String getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(String createEndDate) {
        this.createEndDate = createEndDate;
    }
}
