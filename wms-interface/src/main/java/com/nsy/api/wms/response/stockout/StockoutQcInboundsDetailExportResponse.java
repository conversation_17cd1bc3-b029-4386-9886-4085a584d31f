package com.nsy.api.wms.response.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/17 9:25
 */
public class StockoutQcInboundsDetailExportResponse {

    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    @ApiModelProperty(value = "质检开始时间", name = "qcStartDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date qcStartDate;

    @ApiModelProperty(value = "质检结束时间", name = "qcEndDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date qcEndDate;

    @ApiModelProperty(value = "质检人", name = "qcUserName")
    private String qcUserName;

    @ApiModelProperty(value = "商品规格", name = "sku")
    private String sku;

    @ApiModelProperty(value = "条形码", name = "barcode")
    private String barcode;

    @ApiModelProperty(value = "订单数量", name = "orderQty")
    private Integer orderQty;

    @ApiModelProperty(value = "合格总数", name = "qualifiedQty")
    private Integer qualifiedQty;

    @ApiModelProperty(value = "次品总数", name = "defectivesQty")
    private Integer defectivesQty;

    @ApiModelProperty(value = "补货数", name = "replenishmentQty")
    private Integer replenishmentQty;

    @ApiModelProperty(value = "次品原因归类", name = "unqualifiedCategory")
    private String unqualifiedCategory;

    @ApiModelProperty(value = "次品原因", name = "unqualifiedReason")
    private String unqualifiedReason;

    @ApiModelProperty(value = "次品原因描述", name = "unqualifiedQuestion")
    private String unqualifiedQuestion;

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public Date getQcStartDate() {
        return qcStartDate;
    }

    public void setQcStartDate(Date qcStartDate) {
        this.qcStartDate = qcStartDate;
    }

    public Date getQcEndDate() {
        return qcEndDate;
    }

    public void setQcEndDate(Date qcEndDate) {
        this.qcEndDate = qcEndDate;
    }

    public String getQcUserName() {
        return qcUserName;
    }

    public void setQcUserName(String qcUserName) {
        this.qcUserName = qcUserName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public Integer getOrderQty() {
        return orderQty;
    }

    public void setOrderQty(Integer orderQty) {
        this.orderQty = orderQty;
    }

    public Integer getQualifiedQty() {
        return qualifiedQty;
    }

    public void setQualifiedQty(Integer qualifiedQty) {
        this.qualifiedQty = qualifiedQty;
    }

    public Integer getDefectivesQty() {
        return defectivesQty;
    }

    public void setDefectivesQty(Integer defectivesQty) {
        this.defectivesQty = defectivesQty;
    }

    public Integer getReplenishmentQty() {
        return replenishmentQty;
    }

    public void setReplenishmentQty(Integer replenishmentQty) {
        this.replenishmentQty = replenishmentQty;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getUnqualifiedQuestion() {
        return unqualifiedQuestion;
    }

    public void setUnqualifiedQuestion(String unqualifiedQuestion) {
        this.unqualifiedQuestion = unqualifiedQuestion;
    }
}
