package com.nsy.api.wms.domain.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

@ApiModel(value = "StockTransferTaskRequest", description = "库内调拨任务request")
public class StockTransferTaskRequest {

    @ApiModelProperty(value = "任务id（多选）", name = "taskIdList")
    private List<Integer> taskIdList;

    public List<Integer> getTaskIdList() {
        return taskIdList;
    }

    public void setTaskIdList(List<Integer> taskIdList) {
        this.taskIdList = taskIdList;
    }
}
