package com.nsy.api.wms.enumeration.qa;

import java.util.Arrays;

/**
 * 描述：稽查结果
 *
 * <AUTHOR>
 * @date 2025/3/12 11:17
 */
public enum StockinQaOrderInspectResultEnum {
    PUT_ON("合格上架"),
    SOME_RETURN("部分退货"),
    BATCH_RETURN("批量退货"),
    BACK_QA("退回重新质检");

    String status;

    StockinQaOrderInspectResultEnum(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public static String getDesc(String name) {
        return Arrays.stream(values())
            .filter(s -> s.name().equals(name))
            .findAny()
            .map(StockinQaOrderInspectResultEnum::getStatus)
            .orElse(null);
    }
}
