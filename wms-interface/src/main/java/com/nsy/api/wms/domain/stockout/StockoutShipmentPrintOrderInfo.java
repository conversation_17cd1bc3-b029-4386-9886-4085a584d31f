package com.nsy.api.wms.domain.stockout;

import java.util.List;

/**
 * 装箱清单-打印订单信息
 */
public class StockoutShipmentPrintOrderInfo {

    private String stockoutOrderNo;

    private String printDate;

    private String to;

    private String receiverMobile;

    private String receiverAddress;

    private String receiverZip;

    private Integer skuQty;

    private Integer totalQty;

    private String description;

    private List<ShipmentBoxSkuInfo> boxSkuInfoList;

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getPrintDate() {
        return printDate;
    }

    public void setPrintDate(String printDate) {
        this.printDate = printDate;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getReceiverZip() {
        return receiverZip;
    }

    public void setReceiverZip(String receiverZip) {
        this.receiverZip = receiverZip;
    }

    public Integer getSkuQty() {
        return skuQty;
    }

    public void setSkuQty(Integer skuQty) {
        this.skuQty = skuQty;
    }

    public Integer getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(Integer totalQty) {
        this.totalQty = totalQty;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<ShipmentBoxSkuInfo> getBoxSkuInfoList() {
        return boxSkuInfoList;
    }

    public void setBoxSkuInfoList(List<ShipmentBoxSkuInfo> boxSkuInfoList) {
        this.boxSkuInfoList = boxSkuInfoList;
    }
}
