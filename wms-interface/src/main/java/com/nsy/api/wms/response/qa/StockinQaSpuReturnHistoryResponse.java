
package com.nsy.api.wms.response.qa;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/11/28 15:26
 */
@ApiModel(value = "StockinQaSpuReturnHistoryResponse", description = "历史同款退货信息")
public class StockinQaSpuReturnHistoryResponse {

    @ApiModelProperty("sku")
    private String sku;

    @ApiModelProperty("不合格原因归类")
    private String unqualifiedCategory;

    /**
     * 不合格原因
     */
    @ApiModelProperty("不合格原因")
    private String unqualifiedReason;

    @ApiModelProperty("退货次数")
    private Integer returnCount;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public Integer getReturnCount() {
        return returnCount;
    }

    public void setReturnCount(Integer returnCount) {
        this.returnCount = returnCount;
    }
}
