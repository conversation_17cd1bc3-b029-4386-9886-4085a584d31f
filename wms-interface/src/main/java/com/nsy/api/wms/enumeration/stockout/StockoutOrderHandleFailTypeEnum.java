package com.nsy.api.wms.enumeration.stockout;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/5/23 16:48
 */
public enum StockoutOrderHandleFailTypeEnum {

    LOGISTICS_GET_FAIL("物流面单获取失败"),
    TRANSPARENCY_CODE_OCCUPY_FAIL("tcode占用失败");

    String name;

    StockoutOrderHandleFailTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getNameBy(String type) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(type))
                .findAny()
                .map(StockoutOrderHandleFailTypeEnum::getName)
                .orElse(null);
    }
}