package com.nsy.api.wms.domain.stockin;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description:
 * @author: caish
 * @time: 2021/7/12 16:55
 */
@ApiModel(value = "DeliverySkuCheckView", description = "sku扫描信息")
public class ReceiveSkuView {

    @ApiModelProperty(value = "状态", name = "status")
    private String status;
    /**
     * sku
     */
    @ApiModelProperty(value = "商品sku", name = "sku")
    private String sku;

    @ApiModelProperty(value = "规格编码主键", name = "specId")
    private Integer specId;

    @ApiModelProperty(value = "工厂出厂数量", name = "deliveryQty")
    private Integer deliveryQty;

    @ApiModelProperty(value = "接受数量", name = "receiveQty")
    private Integer receiveQty;

    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "标签属性：S、A、新品首单、改版首单、正常、AMZN新", name = "labelAttributeNames")
    private String labelAttributeNames;

    @ApiModelProperty(value = "强推款标签", name = "stronglyRecommendLabel")
    private String stronglyRecommendLabel;

    @ApiModelProperty(value = "二创", name = "secondCreationLabel")
    private String secondCreationLabel;

    @ApiModelProperty(value = "包装方式", name = "packageName")
    private String packageName;

    //是否引流款
    private Boolean isLeadGeneration;

    @ApiModelProperty(value = "是否先进先出", name = "isFifo")
    private Boolean isFifo;

    @ApiModelProperty(value = "是否绿色通道", name = "isGreenChannel")
    private Boolean isGreenChannel = Boolean.FALSE;

    public String getStronglyRecommendLabel() {
        return stronglyRecommendLabel;
    }

    public void setStronglyRecommendLabel(String stronglyRecommendLabel) {
        this.stronglyRecommendLabel = stronglyRecommendLabel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLabelAttributeNames() {
        return labelAttributeNames;
    }

    public void setLabelAttributeNames(String labelAttributeNames) {
        this.labelAttributeNames = labelAttributeNames;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public Integer getDeliveryQty() {
        return deliveryQty;
    }

    public void setDeliveryQty(Integer deliveryQty) {
        this.deliveryQty = deliveryQty;
    }

    public Integer getReceiveQty() {
        return receiveQty;
    }

    public void setReceiveQty(Integer receiveQty) {
        this.receiveQty = receiveQty;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Boolean getIsLeadGeneration() {
        return isLeadGeneration;
    }

    public void setIsLeadGeneration(Boolean isLeadGeneration) {
        this.isLeadGeneration = isLeadGeneration;
    }

    public Boolean getIsFifo() {
        return isFifo;
    }

    public void setIsFifo(Boolean isFifo) {
        this.isFifo = isFifo;
    }

    public String getSecondCreationLabel() {
        return secondCreationLabel;
    }

    public void setSecondCreationLabel(String secondCreationLabel) {
        this.secondCreationLabel = secondCreationLabel;
    }

    public Boolean getIsGreenChannel() {
        return isGreenChannel;
    }

    public void setIsGreenChannel(Boolean isGreenChannel) {
        this.isGreenChannel = isGreenChannel;
    }
}
