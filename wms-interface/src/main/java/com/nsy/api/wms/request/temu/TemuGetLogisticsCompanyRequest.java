package com.nsy.api.wms.request.temu;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TemuGetLogisticsCompanyRequest {
    private TemuAuth temuAuth;

    @JsonProperty("partner")
    private String partner;
    @JsonProperty("dataDigest")
    private String dataDigest;

    public TemuAuth getTemuAuth() {
        return temuAuth;
    }

    public void setTemuAuth(TemuAuth temuAuth) {
        this.temuAuth = temuAuth;
    }

    public String getPartner() {
        return partner;
    }

    public void setPartner(String partner) {
        this.partner = partner;
    }

    public String getDataDigest() {
        return dataDigest;
    }

    public void setDataDigest(String dataDigest) {
        this.dataDigest = dataDigest;
    }
}

