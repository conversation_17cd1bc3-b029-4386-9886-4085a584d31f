package com.nsy.api.wms.response.tiktokshop;

/**
 * <AUTHOR>
 * @date 2024/6/26 18:24
 */
public class TikTokShopOrderSkuDetail {
    /**
     * 平台SKU编码
     */
    private String platformSkuCode;

    /**
     * 平台SKU商品条码
     */
    private String barcode;

    /**
     * 商家SKU货号
     */
    private String externalSkuCode;

    /**
     * SKC关键属性一属性英文名
     */
    private String secondKeyAttributeNameEn;

    /**
     * SKC关键属性一属性中文名
     */
    private String secondKeyAttributeNameZh;

    /**
     * SKC关键属性一属性英文名
     */
    private String secondKeyAttributeValueEn;

    /**
     * SKC关键属性一属性中文值
     */
    private String secondKeyAttributeValueZh;

    /**
     * SKU订单下单数量
     */
    private Integer stockupQuantity;

    /**
     * SKU送货数量
     */
    private Integer deliveredQuantity;

    /**
     * SKU收货数量
     */
    private Integer receivedQuantity;


    /**
     * SKU入库上架数量
     */
    private Integer inboundQuantity;


    /**
     * SKU质检退货数
     */
    private Integer returnedQuantity;

    public String getPlatformSkuCode() {
        return platformSkuCode;
    }

    public void setPlatformSkuCode(String platformSkuCode) {
        this.platformSkuCode = platformSkuCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getExternalSkuCode() {
        return externalSkuCode;
    }

    public void setExternalSkuCode(String externalSkuCode) {
        this.externalSkuCode = externalSkuCode;
    }

    public String getSecondKeyAttributeNameEn() {
        return secondKeyAttributeNameEn;
    }

    public void setSecondKeyAttributeNameEn(String secondKeyAttributeNameEn) {
        this.secondKeyAttributeNameEn = secondKeyAttributeNameEn;
    }

    public String getSecondKeyAttributeNameZh() {
        return secondKeyAttributeNameZh;
    }

    public void setSecondKeyAttributeNameZh(String secondKeyAttributeNameZh) {
        this.secondKeyAttributeNameZh = secondKeyAttributeNameZh;
    }

    public String getSecondKeyAttributeValueEn() {
        return secondKeyAttributeValueEn;
    }

    public void setSecondKeyAttributeValueEn(String secondKeyAttributeValueEn) {
        this.secondKeyAttributeValueEn = secondKeyAttributeValueEn;
    }

    public String getSecondKeyAttributeValueZh() {
        return secondKeyAttributeValueZh;
    }

    public void setSecondKeyAttributeValueZh(String secondKeyAttributeValueZh) {
        this.secondKeyAttributeValueZh = secondKeyAttributeValueZh;
    }

    public Integer getStockupQuantity() {
        return stockupQuantity;
    }

    public void setStockupQuantity(Integer stockupQuantity) {
        this.stockupQuantity = stockupQuantity;
    }

    public Integer getDeliveredQuantity() {
        return deliveredQuantity;
    }

    public void setDeliveredQuantity(Integer deliveredQuantity) {
        this.deliveredQuantity = deliveredQuantity;
    }

    public Integer getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(Integer receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    public Integer getInboundQuantity() {
        return inboundQuantity;
    }

    public void setInboundQuantity(Integer inboundQuantity) {
        this.inboundQuantity = inboundQuantity;
    }

    public Integer getReturnedQuantity() {
        return returnedQuantity;
    }

    public void setReturnedQuantity(Integer returnedQuantity) {
        this.returnedQuantity = returnedQuantity;
    }
}
