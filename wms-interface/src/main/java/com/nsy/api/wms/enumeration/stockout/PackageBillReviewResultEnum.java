package com.nsy.api.wms.enumeration.stockout;

import java.util.Arrays;
import java.util.Objects;

public enum PackageBillReviewResultEnum {

    // 核对结果(1核对通过，0核对不通过)
    PASS("核对通过", 1), REJECT("核对不通过", 0);

    private final String desc;

    private final Integer value;

    PackageBillReviewResultEnum(String desc, Integer value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getValue() {
        return value;
    }

    public static PackageBillReviewResultEnum resolveByValue(Integer value) {
        return Arrays.stream(PackageBillReviewResultEnum.values()).filter(e -> Objects.equals(e.getValue(), value)).findFirst().orElse(null);
    }
}
