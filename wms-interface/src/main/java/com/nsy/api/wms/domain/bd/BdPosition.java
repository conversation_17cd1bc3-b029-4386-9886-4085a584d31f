package com.nsy.api.wms.domain.bd;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class BdPosition {

    /**
     * 库位id
     */
    @ApiModelProperty(value = "库位id", name = "positionId")
    private Integer positionId;

    /**
     * 地区
     */
    @ApiModelProperty(value = "地区", name = "location")
    private String location;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码", name = "positionCode")
    private String positionCode;

    /**
     * 库位名
     */
    @ApiModelProperty(value = "库位名", name = "positionName")
    private String positionName;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id", name = "spaceId")
    private Integer spaceId;

    /**
     * 仓库名
     */
    @ApiModelProperty(value = "仓库名", name = "spaceName")
    private String spaceName;

    /**
     * 库区id
     */
    @ApiModelProperty(value = "库区id", name = "spaceAreaId")
    private Integer spaceAreaId;

    /**
     * 库区名称
     */
    @ApiModelProperty(value = "库区名称", name = "spaceAreaName")
    private String spaceAreaName;

    /**
     * 区域id
     */
    @ApiModelProperty(value = "区域id", name = "areaId")
    private Integer areaId;

    /**
     * 区域名
     */
    @ApiModelProperty(value = "区域名", name = "areaName")
    private String areaName;

    /**
     * 库位类型
     */
    @ApiModelProperty(value = "库位类型", name = "positionType")
    private String positionType;

    @ApiModelProperty(value = "库位类型", name = "positionTypeStr")
    private String positionTypeStr;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", name = "sort")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "description")
    private String description;

    /**
     * 长
     */
    @ApiModelProperty(value = "长", name = "length")
    private BigDecimal length;

    /**
     * 宽
     */
    @ApiModelProperty(value = "宽", name = "width")
    private BigDecimal width;

    /**
     * 高
     */
    @ApiModelProperty(value = "高", name = "height")
    private BigDecimal height;

    /**
     * 库容限制
     */
    @ApiModelProperty(value = "库容限制", name = "maximumVolume")
    private BigDecimal maximumVolume;

    /**
     * 库位限重
     */
    @ApiModelProperty(value = "库位限重", name = "maximumWeight")
    private BigDecimal maximumWeight;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用", name = "isDeleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者", name = "createBy")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", name = "updateDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者", name = "updateBy")
    private String updateBy;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "库区排序", name = "spaceAreaSort")
    private Integer spaceAreaSort;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份", name = "year")
    private Integer year;

    /**
     * 季度
     */
    @ApiModelProperty(value = "季度", name = "quarter")
    private Integer quarter;

    /**
     * 最小库存
     */
    @ApiModelProperty(value = "最小库存", name = "minStock")
    private Integer minStock;

    @ApiModelProperty(value = "部门", name = "businessType")
    private String businessType;

    /**
     * 店铺id
     */
    @ApiModelProperty(value = "店铺id", name = "storeId")
    private Integer storeId;

    /**
     * 店铺名
     */
    @ApiModelProperty(value = "店铺名", name = "storeName")
    private String storeName;

    @ApiModelProperty(value = "品牌名", name = "brandName")
    private String brandName;

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public String getPositionTypeStr() {
        return positionTypeStr;
    }

    public void setPositionTypeStr(String positionTypeStr) {
        this.positionTypeStr = positionTypeStr;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getSpaceAreaId() {
        return spaceAreaId;
    }

    public void setSpaceAreaId(Integer spaceAreaId) {
        this.spaceAreaId = spaceAreaId;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getPositionType() {
        return positionType;
    }

    public void setPositionType(String positionType) {
        this.positionType = positionType;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getMaximumVolume() {
        return maximumVolume;
    }

    public void setMaximumVolume(BigDecimal maximumVolume) {
        this.maximumVolume = maximumVolume;
    }

    public BigDecimal getMaximumWeight() {
        return maximumWeight;
    }

    public void setMaximumWeight(BigDecimal maximumWeight) {
        this.maximumWeight = maximumWeight;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Integer getSpaceAreaSort() {
        return spaceAreaSort;
    }

    public void setSpaceAreaSort(Integer spaceAreaSort) {
        this.spaceAreaSort = spaceAreaSort;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getQuarter() {
        return quarter;
    }

    public void setQuarter(Integer quarter) {
        this.quarter = quarter;
    }

    public Integer getMinStock() {
        return minStock;
    }

    public void setMinStock(Integer minStock) {
        this.minStock = minStock;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }
}
