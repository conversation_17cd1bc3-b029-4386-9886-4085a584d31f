package com.nsy.api.wms.request.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(value = "StockPDATakeStockTaskSubmitRequest", description = "盘点任务提交request")
public class StockPDATakeStockTaskSubmitRequest {

    @ApiModelProperty(value = "任务明细id", name = "id")
    private Integer id;

    @ApiModelProperty(value = "任务id", name = "taskId")
    private Integer taskId;

    @NotBlank
    @ApiModelProperty(value = "库位编码|内部箱码", name = "targetCode", required = true)
    private String targetCode;

    @NotBlank
    @ApiModelProperty(value = "规格编码", name = "sku", required = true)
    private String sku;

    @NotNull
    @ApiModelProperty(value = "库存", name = "stock", required = true)
    private Integer stock;

    @NotNull
    @ApiModelProperty(value = "是否清除库存", name = "clearStock", required = true)
    private Boolean clearStock;

    /**
     * 盘点原因
     */
    @ApiModelProperty(value = "盘点原因", name = "operateReason", required = true)
    private String operateReason;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Boolean getClearStock() {
        return clearStock;
    }

    public void setClearStock(Boolean clearStock) {
        this.clearStock = clearStock;
    }

    public String getTargetCode() {
        return targetCode;
    }

    public void setTargetCode(String targetCode) {
        this.targetCode = targetCode;
    }

    public String getOperateReason() {
        return operateReason;
    }

    public void setOperateReason(String operateReason) {
        this.operateReason = operateReason;
    }
}
