package com.nsy.api.wms.enumeration;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/10/10 10:59
 */
public enum FlowTaskDefinitionEnum {
    // ---- 试穿任务 start----
    TRY_ON_INITIATE("TRY_ON_INITIATE", "发起试穿任务", FlowDefinitionEnum.PRODUCT_TRY_ON),

    // ---- 试穿任务 end----

    // ---- 质检单 start----
    QA_AUDIT("qa_audit", "初审", FlowDefinitionEnum.QA_ORDER),
    QA_TWICE_AUDIT("qa_twice_audit", "复审", FlowDefinitionEnum.QA_ORDER),
    // ---- 质检单 end----
    UNKNOWN(StringUtils.EMPTY, "未知", null);


    /**
     * 工作流任务定义key
     */
    private final String taskDefinitionKey;
    /**
     * 工作流任务名称
     */
    private final String taskName;
    /**
     * 所属流程定义
     */
    private final FlowDefinitionEnum flowDefinition;

    FlowTaskDefinitionEnum(String taskDefinitionKey, String taskName, FlowDefinitionEnum flowDefinition) {
        this.taskDefinitionKey = taskDefinitionKey;
        this.taskName = taskName;
        this.flowDefinition = flowDefinition;
    }

    public static FlowTaskDefinitionEnum getInstance(String definitionEnumName) {
        return Arrays.stream(values()).filter(definitionEnum -> definitionEnum.name().equals(definitionEnumName))
                .findFirst().orElse(UNKNOWN);
    }

    public static FlowTaskDefinitionEnum getInstanceByDefinitionKey(String taskDefinitionKey) {
        if (StringUtils.isBlank(taskDefinitionKey)) {
            return UNKNOWN;
        }
        String key = taskDefinitionKey;
        if (key.contains(Constants.PENDING_PICK_KEY_POSTFIX)) { //待领取后缀的，去除转成对应的任务定义
            key = key.replace(Constants.PENDING_PICK_KEY_POSTFIX, StringUtils.EMPTY);
        }
        String actualKey = key;
        return Arrays.stream(values()).filter(definitionEnum -> definitionEnum.getTaskDefinitionKey().equals(actualKey))
                .findFirst().orElse(UNKNOWN);
    }

    public String getTaskDefinitionKey() {
        return taskDefinitionKey;
    }

    public String getTaskName() {
        return taskName;
    }

    public FlowDefinitionEnum getFlowDefinition() {
        return flowDefinition;
    }

    public String getProcessDefinitionKey() {
        return flowDefinition.getProcessDefinitionKey();
    }


    public static class Constants {
        /**
         * 待领取 之类的 任务定义key 后缀，
         * 目前前端会在 实际的任务定义后 拼上这个后缀，表明要获取这个任务的待领取的信息
         */
        public static final String PENDING_PICK_KEY_POSTFIX = "#PICK";
    }
}
