package com.nsy.api.wms.request.stockin;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description:
 * @author: chenz<PERSON><PERSON>
 */
@ApiModel(value = "StockinReturnProductPdaScanSkuRequest", description = "采购退货扫描")
public class StockinReturnProductPdaScanSkuRequest {

    @ApiModelProperty(value = "barcode", name = "barcode")
    private String barcode;

    @ApiModelProperty(value = "库位码", name = "positionCode")
    private String positionCode;

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }
}
