package com.nsy.api.wms.enumeration.stockout;

/**
 * 出口退税资料类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/26
 */
public enum ExportDrawbackInformationTypeEnum {
    //出口发票
    EXPORT_INVOICE(1, "出口发票"),
    //进项发票
    INPUT_INVOICE(2, "进项发票"),
    //采购合同
    PURCHASE_CONTRACT(3, "采购合同"),
    //采购物流发票
    PURCHASE_LOGISTICS_INVOICE(4, "采购物流发票"),
    //出口国内段物流发票
    EXPORT_NATIONAL_LOGISTICS_INVOICE(5, "出口国内段物流发票"),
    //出口国内段物流发票
    EXPORT_INTERNATIONAL_LOGISTICS_INVOICE(6, "出口国内段物流发票"),
    //采购物流合同
    PURCHASE_LOGISTICS_CONTRACT(7, "采购物流合同"),
    //出口国内段物流合同
    EXPORT_NATIONAL_LOGISTICS_CONTRACT(8, "出口国内段物流合同"),
    //出口国外段物流合同
    EXPORT_INTERNATIONAL_LOGISTICS_CONTRACT(9, "出口国外段物流合同"),
    //海关报关单
    CUSTOMS_DECLARATION(10, "海关报关单"),
    //放行通知书
    RELEASE_NOTICE(11, "放行通知书"),
    //提单
    BILL_OF_LANDING(12, "提单"),
    // 关单新的报关单据
    NEW_DOCUMENT(13, "报关单据(新)"),
    // 报关单据
    DOCUMENT(14, "报关单据");

    private Integer code;

    private String name;

    ExportDrawbackInformationTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
