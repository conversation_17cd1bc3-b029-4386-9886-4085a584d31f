package com.nsy.api.wms.response.stockin;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description:
 * @author: caish
 * @time: 2021/7/12 16:55
 */
@ApiModel(value = "StockinReturnProductPdaScanSkuResponse", description = "采购退货扫描")
public class StockinReturnProductPdaScanSkuResponse {

    /**
     * sku
     */
    @ApiModelProperty(value = "sku", name = "sku")
    private String sku;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    /**
     * 状态名
     */
    @ApiModelProperty(value = "状态名", name = "status")
    private String statusName;

    /**
     * 颜色
     */
    @ApiModelProperty("颜色")
    private String color;

    /**
     * 尺码
     */
    @ApiModelProperty("尺码")
    private String size;

    /**
     * 条形码
     */
    @ApiModelProperty("条形码")
    private String barcode;

    /**
     * sku原图地址
     */
    @ApiModelProperty("sku原图地址")
    private String imageUrl;

    /**
     * 缩略图
     */
    @ApiModelProperty(value = "缩略图地址", name = "thumbnailImageUrl")
    private String thumbnailImageUrl;

    /**
     * 预览图地址
     */
    @ApiModelProperty(value = "预览图地址", name = "previewImageUrl")
    private String previewImageUrl;

    /**
     * 库位编码
     */
    @ApiModelProperty("库位编码")
    private String positionCode;


    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getThumbnailImageUrl() {
        return thumbnailImageUrl;
    }

    public void setThumbnailImageUrl(String thumbnailImageUrl) {
        this.thumbnailImageUrl = thumbnailImageUrl;
    }

    public String getPreviewImageUrl() {
        return previewImageUrl;
    }

    public void setPreviewImageUrl(String previewImageUrl) {
        this.previewImageUrl = previewImageUrl;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }
}
