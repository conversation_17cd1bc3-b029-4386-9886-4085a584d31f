package com.nsy.api.wms.request.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@ApiModel(value = "StockinReturnProductTaskSaveRequest", description = "退货保存表单实体")
public class StockinReturnProductTaskSaveRequest implements Serializable {
    private static final long serialVersionUID = 1146532138828922L;

    /**
     * 退货库位信息主键列表
     */
    @ApiModelProperty(value = "退货库位信息主键列表", required = true)
    @NotEmpty(message = "退货库位信息主键列表不能为空")
    private List<Integer> ids;
    public List<Integer> getIds() {
        return ids;
    }
    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

}
