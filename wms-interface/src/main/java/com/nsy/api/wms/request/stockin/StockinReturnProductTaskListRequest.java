package com.nsy.api.wms.request.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "StockinReturnProductTaskRequest", description = "退货任务分页表单实体")
public class StockinReturnProductTaskListRequest extends PageRequest implements Serializable {
    private static final long serialVersionUID = 111232132322222L;

    @ApiModelProperty("退货任务ID")
    private Integer returnProductTaskId;

    @ApiModelProperty("仓库id")
    private Integer spaceId;

    @ApiModelProperty("供应商id")
    private Integer supplierId;

    @ApiModelProperty("already_generate: 已生成,returning: 退货中,return_success: 退货完成")
    @Length(max = 50, message = "编码长度不能超过50")
    private String status;

    @ApiModelProperty("库位编码")
    @Length(max = 50, message = "编码长度不能超过50")
    private String positionCode;

    @ApiModelProperty("规格编码")
    private String sku;

    @ApiModelProperty("退货性质")
    private String returnNature;

    @ApiModelProperty("操作人")
    @Length(max = 50, message = "编码长度不能超过50")
    private String operator;

    @ApiModelProperty("退货开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateStartDate;

    @ApiModelProperty("退货结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateEndDate;

    @ApiModelProperty(value = "选择中的多个退货任务的id", name = "returnProductTaskIdList")
    private List<Integer> returnProductTaskIdList;

    @ApiModelProperty("发货开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryStartDate;

    @ApiModelProperty("发货结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryEndDate;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("采购单号")
    private String purchasePlanNo;

    @ApiModelProperty("物流公司")
    private String logisticsCompany;

    @ApiModelProperty(value = "超过5天未退货完成", name = "unfinishedInFiveDays")
    private Boolean unfinishedInFiveDays;

    @ApiModelProperty(value = "运费承担方", name = "freightCarrier")
    private Integer freightCarrier;

    @ApiModelProperty(value = "处理方式", name = "handleMethod")
    private Integer handleMethod;

    private String location;

    @ApiModelProperty(value = "返工状态", name = "reworkStatus")
    private String reworkStatus;

    public String getReworkStatus() {
        return reworkStatus;
    }

    public void setReworkStatus(String reworkStatus) {
        this.reworkStatus = reworkStatus;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getReturnProductTaskId() {
        return returnProductTaskId;
    }

    public void setReturnProductTaskId(Integer returnProductTaskId) {
        this.returnProductTaskId = returnProductTaskId;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateStartDate() {
        return operateStartDate;
    }

    public void setOperateStartDate(Date operateStartDate) {
        this.operateStartDate = operateStartDate;
    }

    public Date getOperateEndDate() {
        return operateEndDate;
    }

    public void setOperateEndDate(Date operateEndDate) {
        this.operateEndDate = operateEndDate;
    }

    public String getReturnNature() {
        return returnNature;
    }

    public void setReturnNature(String returnNature) {
        this.returnNature = returnNature;
    }

    public List<Integer> getReturnProductTaskIdList() {
        return returnProductTaskIdList;
    }

    public void setReturnProductTaskIdList(List<Integer> returnProductTaskIdList) {
        this.returnProductTaskIdList = returnProductTaskIdList;
    }

    public Date getDeliveryStartDate() {
        return deliveryStartDate;
    }

    public void setDeliveryStartDate(Date deliveryStartDate) {
        this.deliveryStartDate = deliveryStartDate;
    }

    public Date getDeliveryEndDate() {
        return deliveryEndDate;
    }

    public void setDeliveryEndDate(Date deliveryEndDate) {
        this.deliveryEndDate = deliveryEndDate;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Boolean getUnfinishedInFiveDays() {
        return unfinishedInFiveDays;
    }

    public void setUnfinishedInFiveDays(Boolean unfinishedInFiveDays) {
        this.unfinishedInFiveDays = unfinishedInFiveDays;
    }

    public Integer getFreightCarrier() {
        return freightCarrier;
    }

    public void setFreightCarrier(Integer freightCarrier) {
        this.freightCarrier = freightCarrier;
    }

    public Integer getHandleMethod() {
        return handleMethod;
    }

    public void setHandleMethod(Integer handleMethod) {
        this.handleMethod = handleMethod;
    }
}
