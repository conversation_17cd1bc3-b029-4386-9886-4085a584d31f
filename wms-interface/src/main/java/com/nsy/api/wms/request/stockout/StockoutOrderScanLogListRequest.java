package com.nsy.api.wms.request.stockout;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "StockoutOrderScanLogListRequest", description = "复核任务日志request")
public class StockoutOrderScanLogListRequest extends PageRequest {

    @ApiModelProperty(value = "出库单编号", name = "StockoutOrderNo")
    private String stockoutOrderNo;

    @ApiModelProperty(value = "事件类型", name = "logType")
    private String logType;

    @ApiModelProperty(value = "描述", name = "content")
    private String content;

    @ApiModelProperty(value = "操作人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "任务id", name = "taskId")
    private Integer taskId;

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }
}
