package com.nsy.api.wms.response.stockout;

import com.nsy.api.wms.domain.stockout.PackShipmentTransferSku;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * HXD
 * 2021/8/24
 **/

public class StockoutPackShipmentItemSkuInfoResponse {
    @ApiModelProperty(value = "sku装箱集合", name = "skuList")
    private List<PackShipmentTransferSku> skuList;

    private Integer boxIndex;

    public List<PackShipmentTransferSku> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<PackShipmentTransferSku> skuList) {
        this.skuList = skuList;
    }

    public Integer getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(Integer boxIndex) {
        this.boxIndex = boxIndex;
    }
}
