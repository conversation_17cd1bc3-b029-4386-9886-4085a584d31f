package com.nsy.api.wms.enumeration.bd;

import com.google.common.base.Strings;
import org.apache.commons.lang3.EnumUtils;

/**
 * 供应商类型 数据字典
 */
public enum ScmSupplierTypeEnum {
    /**
     * 面料供应商
     */
    FABRIC_SUPPLIER("面料供应商", "SYM"),

    /**
     * 二次工艺供应商
     */
    SECONDARY_PROCESS_SUPPLIER("二次工艺供应商", "SYE"),

    /**
     * 成衣供应商
     */
    CLOTHING_SUPPLIER("成衣供应商", "SYC"),

    /**
     * 现货供应商
     */
    STOCK_SUPPLIER("现货供应商", "SYX"),

    /**
     * 虚拟供应商
     */
    VIRTUAL_SUPPLIER("虚拟供应商", "SYXN"),

    /**
     * 市场供应商
     */
    MARKET_SUPPLIER("市场供应商", "SYS"),

    /**
     * ODM
     */
    ODM("ODM", "SYODM"),
    /**
     * OEM
     */
    OEM("OEM", "SYOEM"),

    UNKNOWN("UNKNOWN", "UNKNOWN");

    private final String desc;
    /**
     * 供应商编码前缀
     */
    private final String prefixCode;

    ScmSupplierTypeEnum(String desc, String prefixCode) {
        this.desc = desc;
        this.prefixCode = prefixCode;
    }

    public String getDesc() {
        return desc;
    }

    public String getPrefixCode() {
        return prefixCode;
    }

    public static String getDescByName(String name) {
        if (Strings.isNullOrEmpty(name)) {
            return "";
        }
        if (EnumUtils.isValidEnum(SupplierTypeEnum.class, name)) {
            return SupplierTypeEnum.valueOf(name).getDesc();
        }
        return "";
    }

    public static ScmSupplierTypeEnum getInstance(String supplierTypeName) {
        if (EnumUtils.isValidEnum(SupplierTypeEnum.class, supplierTypeName)) {
            return ScmSupplierTypeEnum.valueOf(supplierTypeName);
        }
        return UNKNOWN;
    }
}
