package com.nsy.api.wms.request.stockout;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @description:
 * @author: caish
 * @time: 2021/8/31 18:26
 */
@ApiModel(value = "StockoutPickingQuickConfirmRequest", description = "拣货任务快捷拣货确认request")
public class StockoutPickingQuickConfirmRequest {

    @ApiModelProperty(value = "拣货任务id", name = "taskId")
    private Integer taskId;

    @ApiModelProperty(value = "内部箱号", name = "batchCode")
    private String internalBoxCode;

    private Boolean isConfirm = Boolean.FALSE;

    public StockoutPickingQuickConfirmRequest() {
    }

    public StockoutPickingQuickConfirmRequest(Integer taskId, String internalBoxCode, Boolean isConfirm) {
        this.taskId = taskId;
        this.internalBoxCode = internalBoxCode;
        this.isConfirm = isConfirm;
    }

    public Boolean getIsConfirm() {
        return isConfirm;
    }

    public void setIsConfirm(Boolean isConfirm) {
        this.isConfirm = isConfirm;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }
}
