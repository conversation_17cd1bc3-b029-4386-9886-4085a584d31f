package com.nsy.api.wms.domain.stockout;

/**
 * 扫描结果合计
 */
public class StockoutBatchSplitTaskScanResult {
    // 出库单件数
    private Integer totalBatchQty;
    // 拣货数
    private Integer totalExpectedQty;
    // 扫描数
    private Integer totalScanQty;
    // 缺货数
    private Integer totalLackQty;
    // 撤货数
    private Integer totalCancelQty;

    public Integer getTotalBatchQty() {
        return totalBatchQty;
    }

    public void setTotalBatchQty(Integer totalBatchQty) {
        this.totalBatchQty = totalBatchQty;
    }

    public Integer getTotalExpectedQty() {
        return totalExpectedQty;
    }

    public void setTotalExpectedQty(Integer totalExpectedQty) {
        this.totalExpectedQty = totalExpectedQty;
    }

    public Integer getTotalScanQty() {
        return totalScanQty;
    }

    public void setTotalScanQty(Integer totalScanQty) {
        this.totalScanQty = totalScanQty;
    }

    public Integer getTotalLackQty() {
        return totalLackQty;
    }

    public void setTotalLackQty(Integer totalLackQty) {
        this.totalLackQty = totalLackQty;
    }

    public Integer getTotalCancelQty() {
        return totalCancelQty;
    }

    public void setTotalCancelQty(Integer totalCancelQty) {
        this.totalCancelQty = totalCancelQty;
    }
}
