package com.nsy.api.wms.request.stock;

import com.nsy.api.wms.request.base.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@ApiModel(value = "StockTransferRecordListRequest", description = "调拨列表查询request")
public class StockTransferRecordListRequest extends PageRequest {

    @ApiModelProperty(value = "任务ID", name = "id")
    private Integer id;

    @ApiModelProperty(value = "区域ID", name = "areaId")
    private Integer areaId;

    @ApiModelProperty(value = "调出库位/内部箱", name = "transferOutCode")
    private String transferOutCode;

    @ApiModelProperty(value = "调入库位/内部箱", name = "transferInCode")
    private String transferInCode;

    @ApiModelProperty(value = "调拨日期-开始", name = "transferStartDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transferStartDate;

    @ApiModelProperty(value = "调拨日期-结束", name = "transferEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transferEndDate;

    @ApiModelProperty(value = "sku", name = "sku")
    private String sku;

    @ApiModelProperty(value = "操作人", name = "createBy")
    private String createBy;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getTransferOutCode() {
        return transferOutCode;
    }

    public void setTransferOutCode(String transferOutCode) {
        this.transferOutCode = transferOutCode;
    }

    public String getTransferInCode() {
        return transferInCode;
    }

    public void setTransferInCode(String transferInCode) {
        this.transferInCode = transferInCode;
    }

    public Date getTransferStartDate() {
        return transferStartDate;
    }

    public void setTransferStartDate(Date transferStartDate) {
        this.transferStartDate = transferStartDate;
    }

    public Date getTransferEndDate() {
        return transferEndDate;
    }

    public void setTransferEndDate(Date transferEndDate) {
        this.transferEndDate = transferEndDate;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}
