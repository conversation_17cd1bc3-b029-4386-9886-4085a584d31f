package com.nsy.api.wms.enumeration.stockout;

import java.util.Arrays;

public enum StockoutOrderBusinessTypeEnum {

    B2B("B2B"),
    B2C("B2C"),
    INDEPENDENT_STATION("独立站"),
    DOMESTIC("内贸部"),
    DOKOTOO("dokotoo");

    String name;

    StockoutOrderBusinessTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String getNameBy(String workspace) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(workspace))
                .findAny()
                .map(StockoutOrderBusinessTypeEnum::getName)
                .orElse(B2B.getName());
    }
}
