package com.nsy.api.wms.domain.stockin;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockinOrderItemSet", description = "入库任务SKU信息录入")
public class StockinOrderItemSet {

    @ApiModelProperty(value = "入库任务明细id", name = "taskItemId")
    private Integer taskItemId;

    @ApiModelProperty(value = "装箱明细", name = "stockinOrderTaskItemDetailList")
    private List<StockinOrderTaskItemDetail> stockinOrderTaskItemDetailList;

    public Integer getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(Integer taskItemId) {
        this.taskItemId = taskItemId;
    }

    public List<StockinOrderTaskItemDetail> getStockinOrderTaskItemDetailList() {
        return stockinOrderTaskItemDetailList;
    }

    public void setStockinOrderTaskItemDetailList(List<StockinOrderTaskItemDetail> stockinOrderTaskItemDetailList) {
        this.stockinOrderTaskItemDetailList = stockinOrderTaskItemDetailList;
    }
}
