package com.nsy.api.wms.enumeration.stockin;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-18 9:54
 */
public enum StockinMeasureTypeEnum {

    VOLUME_WEIGHT("体积重"),
    HEIGHT("高度");

    StockinMeasureTypeEnum(String name) {
        this.name = name;
    }

    private final String name;

    public String getName() {
        return name;
    }

    public static String getDesc(String qcReceive) {
        return Arrays.stream(values())
                .filter(s -> s.name().equals(qcReceive))
                .findAny()
                .map(StockinMeasureTypeEnum::getName)
                .orElse(null);
    }
}
