package com.nsy.api.wms.response.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class StockinReceiptItemListInfo {
    @ApiModelProperty(value = "入库单id", name = "stockinOrderId")
    private Integer stockinOrderId;

    @ApiModelProperty(value = "真实入库单id", name = "realStockinOrderId")
    private Integer realStockinOrderId;

    /**
     * 类型 1：分公司下单，需生成泉州接收单
     */
    @ApiModelProperty(value = "类型", name = "orderType")
    private Integer orderType;

    @ApiModelProperty(value = "入库单类型", name = "stockinType")
    private String stockinType;

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "入库单号", name = "stockinOrderNo")
    private String stockinOrderNo;

    @ApiModelProperty(value = "入库任务id", name = "taskId")
    private Integer taskId;

    @ApiModelProperty(value = "月台id", name = "platformScheduleId")
    private Integer platformScheduleId;

    @ApiModelProperty(value = "供应商id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商名称", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "仓库", name = "spaceName")
    private String spaceName;

    @ApiModelProperty(value = "出库箱码", name = "supplierDeliveryBoxCode")
    private String supplierDeliveryBoxCode;

    @ApiModelProperty(value = "采购单号", name = "purchasePlanNo")
    private String purchasePlanNo;

    @ApiModelProperty(value = "采购员", name = "purchaseUserRealName")
    private String purchaseUserRealName;

    @ApiModelProperty("发货日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryDate;

    @ApiModelProperty(value = "收货数", name = "receiveQty")
    private int receiveQty;

    @ApiModelProperty(value = "让步接收数", name = "concessionsQty")
    private int concessionsQty;

    @ApiModelProperty(value = "已退货数", name = "returnedQty")
    private int returnedQty;

    @ApiModelProperty(value = "正常上架件数", name = "shelvedQty")
    private int shelvedQty;

    @ApiModelProperty(value = "明细状态", name = "status")
    private String status;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "商品编码", name = "spu")
    private String spu;

    @ApiModelProperty(value = "备注", name = "description")
    private String description;

    private String supplierDeliveryNo;

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    public int getReturnedQty() {
        return returnedQty;
    }

    public void setReturnedQty(int returnedQty) {
        this.returnedQty = returnedQty;
    }

    public int getShelvedQty() {
        return shelvedQty;
    }

    public void setShelvedQty(int shelvedQty) {
        this.shelvedQty = shelvedQty;
    }

    public Integer getStockinOrderId() {
        return stockinOrderId;
    }

    public void setStockinOrderId(Integer stockinOrderId) {
        this.stockinOrderId = stockinOrderId;
    }

    public Integer getRealStockinOrderId() {
        return realStockinOrderId;
    }

    public void setRealStockinOrderId(Integer realStockinOrderId) {
        this.realStockinOrderId = realStockinOrderId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getStockinType() {
        return stockinType;
    }

    public int getConcessionsQty() {
        return concessionsQty;
    }

    public void setConcessionsQty(int concessionsQty) {
        this.concessionsQty = concessionsQty;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getStockinOrderNo() {
        return stockinOrderNo;
    }

    public void setStockinOrderNo(String stockinOrderNo) {
        this.stockinOrderNo = stockinOrderNo;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public Integer getPlatformScheduleId() {
        return platformScheduleId;
    }

    public void setPlatformScheduleId(Integer platformScheduleId) {
        this.platformScheduleId = platformScheduleId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getPurchaseUserRealName() {
        return purchaseUserRealName;
    }

    public void setPurchaseUserRealName(String purchaseUserRealName) {
        this.purchaseUserRealName = purchaseUserRealName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public int getReceiveQty() {
        return receiveQty;
    }

    public void setReceiveQty(int receiveQty) {
        this.receiveQty = receiveQty;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }
}
