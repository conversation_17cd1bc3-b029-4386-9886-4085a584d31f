package com.nsy.api.wms.enumeration.stockout;

public enum StockoutWalmartFirstPartyOrderEnum {
    WAIT_STOCKIN("待入库"),
    HAS_STOCKINED("已入库"),
    WAIT_DELIVERY("待发货"),
    HAS_DELIVERYED("已发货");

    String name;


    StockoutWalmartFirstPartyOrderEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static StockoutWalmartFirstPartyOrderEnum of(String name) {
        for (StockoutWalmartFirstPartyOrderEnum item : StockoutWalmartFirstPartyOrderEnum.values()) {
            if (item.name().equals(name)) {
                return item;
            }
        }
        return null;
    }


}
