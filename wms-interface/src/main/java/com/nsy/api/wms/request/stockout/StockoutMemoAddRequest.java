package com.nsy.api.wms.request.stockout;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "StockoutMemoAddRequest", description = "出库单备注新增request")
public class StockoutMemoAddRequest {

    @ApiModelProperty(value = "订单编号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "仓库备注", name = "orderMemo")
    private String orderMemo;

    @ApiModelProperty(value = "业务备注", name = "sellerMemo")
    private String sellerMemo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderMemo() {
        return orderMemo;
    }

    public void setOrderMemo(String orderMemo) {
        this.orderMemo = orderMemo;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }
}
