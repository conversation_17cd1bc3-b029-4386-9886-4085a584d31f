package com.nsy.api.wms.request.stockin;

import java.util.List;

/**
 *
 * <AUTHOR>
 * 2022-03-15
 */
public class SpecSupplierPurchasePriceRequest {
    private Integer erpSpecId;
    private Integer supplierId;
    private List<Integer> supplierIdList;
    private Boolean isExtend;
    private String sku;

    public Boolean getExtend() {
        return isExtend;
    }

    public void setExtend(Boolean extend) {
        isExtend = extend;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getErpSpecId() {
        return erpSpecId;
    }

    public void setErpSpecId(Integer erpSpecId) {
        this.erpSpecId = erpSpecId;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public Boolean getIsExtend() {
        return isExtend;
    }

    public void setIsExtend(Boolean isExtend) {
        this.isExtend = isExtend;
    }

    public List<Integer> getSupplierIdList() {
        return supplierIdList;
    }

    public void setSupplierIdList(List<Integer> supplierIdList) {
        this.supplierIdList = supplierIdList;
    }
}
