package com.nsy.api.wms.request.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "StockLendCantReturnRequest", description = "无法归还Request")
public class StockLendCantReturnRequest {
    @ApiModelProperty(value = "借用id", name = "lendId", required = true)
    private Integer lendId;

    @ApiModelProperty(value = "说明", name = "remark")
    private String remark;

    public Integer getLendId() {
        return lendId;
    }

    public void setLendId(Integer lendId) {
        this.lendId = lendId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
