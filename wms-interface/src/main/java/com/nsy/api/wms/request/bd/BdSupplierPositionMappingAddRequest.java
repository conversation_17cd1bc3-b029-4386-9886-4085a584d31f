package com.nsy.api.wms.request.bd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "BdSupplierPositionMappingAddRequest", description = "退货库位新增request")
public class BdSupplierPositionMappingAddRequest {

    @ApiModelProperty(value = "供应商Id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "供应商名", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "库位编码", name = "positionCode")
    private String positionCode;

    @ApiModelProperty(value = "退货性质", name = "returnNature")
    private String returnNature;

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getReturnNature() {
        return returnNature;
    }

    public void setReturnNature(String returnNature) {
        this.returnNature = returnNature;
    }
}
