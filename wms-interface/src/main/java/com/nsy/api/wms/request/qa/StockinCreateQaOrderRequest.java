package com.nsy.api.wms.request.qa;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2024/11/15 10:50
 */
public class StockinCreateQaOrderRequest {

    @ApiModelProperty("商品条形码")
    private String barcode;

    @ApiModelProperty(value = "内部箱号", name = "internalBoxCode")
    private String internalBoxCode;

    @ApiModelProperty(value = "忽略提示 0 否1是", name = "ignoreNotice")
    private Integer ignoreNotice = 0;

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getIgnoreNotice() {
        return ignoreNotice;
    }

    public void setIgnoreNotice(Integer ignoreNotice) {
        this.ignoreNotice = ignoreNotice;
    }
}

