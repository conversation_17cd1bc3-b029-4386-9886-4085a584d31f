package com.nsy.api.wms.response.stockout;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 装箱清单
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "StockoutShipmentRequest", description = "装箱清单request")
public class StockoutShipmentRequest {

    @ApiModelProperty(value = "箱子编号", name = "shipmentBoxCodeList")
    private List<String> shipmentBoxCodeList;

    public List<String> getShipmentBoxCodeList() {
        return shipmentBoxCodeList;
    }

    public void setShipmentBoxCodeList(List<String> shipmentBoxCodeList) {
        this.shipmentBoxCodeList = shipmentBoxCodeList;
    }
}
