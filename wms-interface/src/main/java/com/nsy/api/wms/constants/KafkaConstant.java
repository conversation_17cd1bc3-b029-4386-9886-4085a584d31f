package com.nsy.api.wms.constants;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class KafkaConstant {
    //仓库
    public static final String BUSINESS_MARK_WMS_STOCK_IN_SHELVE = "商品入库-上架";

    public static final String TOPIC_NAME_STOCKIN_SHELVE_TASK = "stock-shelve-task-topic";
    // 回退订单
    public static final String BACK_TRADE_TOPIC = "back_trade_topic";
    public static final String BACK_TRADE_BUSINESS_MARK = "回退订单同步";
    //拣货单完成同步
    public static final String FINISH_PARTIAL_PICK_TOPIC = "finish_partial_pick_topic";
    public static final String FINISH_PARTIAL_PICK_BUSINESS_MARK = "拣货单完成同步";
    //加工完成处理同步
    public static final String SYNC_PROCESS_OPERATE_TOPIC = "sync_process_operate_topic";
    public static final String SYNC_PROCESS_OPERATE_BUSINESS_MARK = "加工完成处理";
    //加工撤货箱调拨同步
/*    public static final String SYNC_PROCESS_WITHDRAWAL_BOX_TOPIC = "sync_process_withdrawal_box_topic";
    public static final String SYNC_PROCESS_WITHDRAWAL_BUSINESS_MARK = "加工撤货箱调拨";*/
    //装箱清单同步
    public static final String SYNC_PICKING_BOX_TOPIC = "sync_picking_box_topic";
    public static final String SYNC_PICKING_BOX_BUSINESS_MARK = "装箱清单同步";
    //装箱清单发货同步
    public static final String SYNC_PICKING_BOX_SHIPPED_TOPIC = "sync_picking_box_shipped_topic";
    public static final String SYNC_PICKING_BOX_SHIPPED_BUSINESS_MARK = "装箱清单发货同步";

    //装箱清单发货同步tms
    public static final String SYNC_SHIPMENT_SHIPPED_TOPIC = "sync_shipment_shipped_topic";
    public static final String SYNC_SHIPMENT_SHIPPED_BUSINESS_MARK = "装箱清单发货同步Tms";

    //次品调拨出库
    public static final String ADD_TRANSFER_OUT_BY_LOSS_QTY_TOPIC = "add_transfer_out_by_loss_qty_topic";
    public static final String ADD_TRANSFER_OUT_BY_LOSS_QTY_MARK = "次品调拨出库";

    //推送退货登记到订单系统
    public static final String PUSH_OMS_BY_RETURN_PRODUCT_TOPIC = "push_oms_by_return_product_topic";
    public static final String PUSH_OMS_BY_RETURN_PRODUCT_MARK = "推送退货登记到订单系统";

    public static final String BUSINESS_EXTERNAK_API_SYNC = "外部接口异步调用";

    public static final String EXTERNAK_API_SYNC = "externak-api-sync-topic";

    public static final String LOG_SYNC_TO_ES_TOPIC = "log-sync-to-es-topic";
    public static final String LOG_SYNC_TO_ES_JOB_NAME = "日志增量同步es";

    public static final String WMS_SEND_DINGDING_MESSAGE_TOPIC = "send-dingding-message-topic";
    public static final String WMS_SEND_DINGDING_MESSAGE_NAME = "钉钉通知job";

    public static final String ERP_SYNC_STOCK_TOPIC = "sync-stock-to-wms-topic";
    public static final String ERP_SYNC_STOCK_TOPIC_NAME = "库存同步job";

    public static final String ERP_SYNC_INVENTORY_TOPIC = "sync-inventory-to-wms-topic";
    public static final String ERP_SYNC_INVENTORY_TOPIC_NAME = "盘点同步job";

    // 表监听topic
    public static final String NSY_WMS_STOCKIN_RETURN_PRODUCT = "nsy_wms_stockin_return_product";


    public static final String NSY_PRODUCT_PRODUCT = "nsy_product_product";

    public static final String NSY_PRODUCT_PRODUCT_CATEGORY = "nsy_product_bd_product_category";

    public static final String NSY_PRODUCT_PRODUCT_STYLE = "nsy_product_product_style";

    public static final String NSY_PRODUCT_PRODUCT_LABEL = "nsy_product_product_label";

    public static final String NSY_PRODUCT_PRODUCT_SKC_LABEL = "nsy_product_product_skc_label";

    public static final String NSY_PRODUCT_PRODUCT_IMAGE = "nsy_product_product_image";

    public static final String NSY_PRODUCT_PRODUCT_SPEC = "nsy_product_product_spec";

    public static final String EXTERNAL_PURCHASE_ORDER_STOCK_IN_TOPIC = "external_purchase_order_stock_in_topic";
    public static final String EXTERNAL_PURCHASE_ORDER_STOCK_IN_NAME = "沃尔玛PO已入库推送工厂";

    public static final String NSY_PRODUCT_PRODUCT_COLOR_INFO = "nsy_product_product_color_info";

    public static final String NSY_PRODUCT_PRODUCT_SPEC_IMAGE = "nsy_product_product_spec_image";

    public static final String NSY_PRODUCT_PRODUCT_AMAZON_MARKETING = "nsy_product_product_amazon_marketing";

    public static final String NSY_SCM_SUPPLIER = "nsy_scm_supplier";

    public static final String NSY_LOGISTICS_COMPANY = "nsy_tms_tms_logistics_company";

    public static final String SYNC_BOX_TO_AMAZON_TOPIC = "sync-box-to-amazon-topic";
    public static final String SYNC_BOX_TO_AMAZON_TOPIC_NAME = "同步箱子到亚马逊";

    public static final String SYNC_AMAZON_BOX_COMPLETE = "sync_amazon_box_complete";
    public static final String SYNC_AMAZON_BOX_COMPLETE_NAME = "同步亚马逊申请箱贴";

    public static final String SYNC_OMS_BOX_COMPLETE = "sync_oms_box_complete";
    public static final String SYNC_OMS_BOX_COMPLETE_NAME = "同步OMS申请箱贴";

    public static final String SYNC_OMS_ORDER_SHIP_TOPIC = "sync_oms_order_ship_topic";
    public static final String SYNC_OMS_ORDER_SHIP_TOPIC_NAME = "同步OMS订单发货";

    public static final String SYNC_AMAZON_BOX_SHIP_TOPIC = "sync_amazon_box_ship_topic";
    public static final String SYNC_AMAZON_BOX_SHIP_TOPIC_NAME = "同步亚马逊箱子发货";

    public static final String SYNC_STA_SHIPMENT_AND_BOX_RELATIONSHIP_TOPIC = "sync_sta_shipment_and_box_relationship_topic";

    public static final String SYNC_OMS_SHIPMENT_AND_BOX_RELATIONSHIP_TOPIC = "sync_oms_shipment_and_box_relationship_topic";

    //拣货任务更新波次单、波次出库单、出库单状态
    public static final String STOCKOUT_PICKING_TASK_UPDATE_BATCH_TOPIC = "stockout-picking-task-update-batch-topic";
    public static final String STOCKOUT_PICKING_TASK_UPDATE_BATCH_TOPIC_NAME = "拣货任务开始更新波次单状态";

    //拣货任务完成更新波次单
    public static final String STOCKOUT_BATCH_PICKING_TASK_COMPLETE_TOPIC = "stockout_batch_picking_task_complete_topic";
    public static final String STOCKOUT_BATCH_PICKING_TASK_COMPLETE_TOPIC_NAME = "拣货任务完成更新波次单";

    //拣货任务完成更新分拣任务
    public static final String STOCKOUT_BATCH_PICKING_TASK_COMPLETE_SPLIT_TOPIC = "stockout_batch_picking_task_complete_split_topic";
    public static final String STOCKOUT_BATCH_PICKING_TASK_COMPLETE_SPLIT_TOPIC_NAME = "拣货任务完成更新分拣任务";

    public static final String SYNC_STOCKOUT_PICKING_TASK_COMPLETE_TOPIC = "sync_stockout_picking_task_complete_topic";
    public static final String SYNC_STOCKOUT_PICKING_TASK_COMPLETE_TOPIC_NAME = "异步完成拣货任务";

    public static final String WMS_STOCK_OUT_ORDER_SHIPMENTS_NAME = "销售出库单发货推送";

    public static final String STOCKOUT_SCAN_SHIPMENT_TOPIC = "stockout-scan-shipment-topic";
    public static final String STOCKOUT_SCAN_SHIPMENT_TOPIC_NAME = "复核扫描装箱记录";

    public static final String TRANSPARENCY_CODE_APPLY_SYNC_TOPIC = "transparency-code-apply-sync-topic";
    public static final String TRANSPARENCY_CODE_APPLY_SYNC_TOPIC_NAME = "透明计划码申请完成通知";

    public static final String WMS_STOCK_IN_ORDER_SHELVED_NAME = "采购入库单上架推送";

    public static final String WMS_STOCK_OUT_ORDER_IN_TRANSIT_NAME = "采购退货单运输中推送";

    public static final String WMS_OTHER_STOCK_IN_ORDER_NAME = "其他入库单推送";

    public static final String WMS_OTHER_STOCK_OUT_ORDER_NAME = "其他出库单推送";

    public static final String WMS_STOCK_DIRECT_TRANSFER_ORDER_NAME = "直接调拨单推送";

    public static final String WMS_DISTR_TRANSFER_IN_NAME = "分布式调入单推送";
    public static final String WMS_DISTR_TRANSFER_OUT_NAME = "分布式调出单推送";
    public static final String WMS_STOCK_TRANSFER_ORDER_NAME = "直接调拨单推送";

    /**
     * 泉州仓WMS出库单发货topic
     */
    public static final String WMS_STOCK_OUT_ORDER_SHIPMENTS_TOPIC = "wms_stock_out_order_shipments_topic";

    //窄带分拣完成
    public static final String AUTO_MACHINE_SORT_TOPIC = "auto_machine_sort_topic";
    public static final String AUTO_MACHINE_SORT_MARK = "窄带分拣完成";

    //报关合同同步
    public static final String CUSTOMS_DECLARE_CONTRACT_SYNC_TOPIC = "customs_declare_contract_sync_topic";
    public static final String CUSTOMS_DECLARE_CONTRACT_SYNC_MARK = "报关关单同步";

    // 出库单取消  触发 复核任务/简易复核 等
    public static final String STOCKOUT_ORDER_CANCELLED_TODO_TOPIC = "stockout_order_cancelled_todo_topic";
    public static final String STOCKOUT_ORDER_CANCELLED_TODO_TOPIC_MARK = "出库单取消";


    //打印dhl
    public static final String STOCKOUT_PRINT_DHL_TOPIC = "stockout_print_dhl_topic";
    public static final String STOCKOUT_PRINT_DHL_BUSINESS_MARK = "打印dhl";

    //打包完成更新库存
    public static final String SYNC_STOCKOUT_PACKAGE_COMPLETE_TOPIC = "sync_stockout_package_complete_topic";
    public static final String SYNC_STOCKOUT_PACKAGE_COMPLETE_TOPIC_NAME = "打包完成更新库存";
    //打印fedex
    public static final String STOCKOUT_PRINT_FEDEX_TOPIC = "stockout_print_fedex_topic";
    public static final String STOCKOUT_PRINT_FEDEX_BUSINESS_MARK = "打印fedex";
    //打印ups
    public static final String STOCKOUT_PRINT_UPS_TOPIC = "stockout_print_ups_topic";
    public static final String STOCKOUT_PRINT_UPS_BUSINESS_MARK = "打印ups";

    //同步质检系统质检任务
    public static final String SYNC_INBOUNDS_REQUIRED_CHECK_TOPIC = "sync_inbounds_required_check_topic";
    public static final String SYNC_INBOUNDS_REQUIRED_CHECK_TOPIC_NAME = "同步质检系统质检任务";
    //异步自提发货
    public static final String STOCKOUT_SHIPMENT_SELF_SHIPPED = "stockout_shipment_self_shipped";
    public static final String STOCKOUT_SHIPMENT_SELF_SHIPPED_MARK = "异步自提发货";

    //打印fedex
    public static final String STOCKOUT_CHANNEL_PRINT_TOPIC = "stockout_channel_print_topic";
    public static final String STOCKOUT_CHANNEL_PRINT_MARK = "货代获取并打印面单";
    //打印ups
    public static final String STOCKOUT_UPLOAD_FALIAN_INVOICE_TOPIC = "stockout_upload_falian_invoice_topic";
    public static final String STOCKOUT_UPLOAD_FALIAN_INVOICE_MARK = "上传ups发票";

    // faire批次发货
    public static final String STOCKOUT_FAIRE_BATCH_SHIPPED_TOPIC = "stockout_faire_batch_shipped_topic";
    public static final String STOCKOUT_FAIRE_BATCH_SHIPPED_TOPIC_MARK = "faire批次发货";

    // 创建FBA物流波次
    public static final String FBA_LOGISTICS_BATCH_CREATE_TOPIC = "fba_logistics_batch_create_topic";
    public static final String FBA_LOGISTICS_BATCH_CREATE_TOPIC_NAME = "创建FBA物流波次";

    //打包完成更新库存
    public static final String SYNC_STOCKOUT_PICK_TASK_QUICK_CONFIRM_TOPIC = "sync_stockout_pick_task_quick_confirm_topic";
    public static final String SYNC_STOCKOUT_PICK_TASK_QUICK_CONFIRM_TOPIC_NAME = "快捷拣货";

    //补货单创建消息发送
    public static final String SYNC_REPLENISH_ORDER_CREATE_TOPIC = "sync_replenish_order_create_topic";
    public static final String SYNC_REPLENISH_ORDER_CREATE_TOPIC_NAME = "补货单创建消息发送";

    //推送补货单消息到运营系统
    public static final String PUSH_REPLENISH_ORDER_TOPIC = "push_replenish_order_topic";
    public static final String PUSH_REPLENISH_ORDER_TOPIC_NAME = "推送补货单消息到运营系统";

    //入库任务生成
    public static final String STOCKIN_ORDER_TASK_CREATE_TOPIC = "stockin_order_task_create_topic";
    public static final String STOCKIN_ORDER_TASK_CREATE_TOPIC_NAME = "入库任务生成";

    //上架回填入库单明细
    public static final String STOCKIN_SHELVED_UPDATE_ORDER_TOPIC = "stockin_shelved_update_order_topic";
    public static final String STOCKIN_SHELVED_UPDATE_ORDER_TOPIC_NAME = "上架回填入库单明细";

    // 出库单推送
    public static final String STOCKOUT_ORDER_PUSH_TOPIC = "stockout_order_push_topic";
    public static final String STOCKOUT_ORDER_PUSH_TOPIC_MARK = "出库单推送";

    // 出库单预配
    public static final String STOCKOUT_ORDER_PREMATCH_TOPIC = "stockout_order_prematch_topic";
    public static final String STOCKOUT_ORDER_PREMATCH_TOPIC_MARK = "出库单预配";

    // 海外仓订单创建
    public static final String OVERSEAS_WAREHOUSE_ORDER_CREATE_TOPIC = "overseas_warehouse_order_create_topic";
    public static final String OVERSEAS_WAREHOUSE_ORDER_CREATE_TOPIC_MARK = "海外仓订单创建";

    // 拣货异常触发盘点
    public static final String STOCKOUT_PICKING_EXCEPTION_TAKE_TASK_TOPIC = "stockout_picking_exception_take_task_topic";
    public static final String STOCKOUT_PICKING_EXCEPTION_TAKE_TASK_TOPIC_NAME = "拣货异常触发盘点";

    // 采购退货，异步反馈退货数量
    public static final String STOCKIN_RETURN_PRODUCT_ASYNC_TOPIC = "stockin_return_product_async_topic";
    public static final String STOCKIN_RETURN_PRODUCT_ASYNC_TOPIC_NAME = "采购退货，异步反馈退货数量";

    //增值任务创建消息发送
    public static final String SYNC_VAS_TASK_CREATE_TOPIC = "sync_vas_task_create_topic";
    public static final String SYNC_VAS_TASK_CREATE_TOPIC_NAME = "增值任务创建消息发送";

    //复核通知缺货清理拣货箱
    public static final String STOCKOUT_ORDER_SCAN_NOTICE_LACK_CLEAR_BOX = "stockout_order_scan_notice_lack_clear_box_topic";
    public static final String STOCKOUT_ORDER_SCAN_NOTICE_LACK_CLEAR_BOX_TOPIC_NAME = "复核完成清理拣货箱";

    //领星新增仓库同步
    public static final String WMS_LINGXING_SYNC_SPACE_NAME = "领星新增仓库同步";

    //领星新增库位同步
    public static final String WMS_LINGXING_SYNC_POSITION_NAME = "领星新增库位同步";

    //领星新增入库单同步
    public static final String WMS_LINGXING_CREATE_STOCKIN_ORDER_NAME = "领星新增入库单同步";

    //领星称重同步
    public static final String WMS_LINGXING_PRODUCT_INFO_TOPIC_NAME = "领星称重/量高同步";

    //小包称重数据核对
    public static final String WMS_STOCKOUT_WEIGHT_CHECK_TOPIC = "wms_stockout_weight_check_topic";
    public static final String WMS_STOCKOUT_WEIGHT_CHECK_TOPIC_NAME = "小包称重数据核对";

    public static final String STOCK_UPDATE_TOPIC = "stock_update_topic";
    public static final String STOCK_UPDATE_TOPIC_NAME = "异步库存更新topic";

    //入库单推送etl消息
    public static final String STOCKIN_ORDER_PUSH_ETL_ASYNC_TOPIC = "stockin_order_push_etl_async_topic";
    public static final String STOCKIN_ORDER_PUSH_ETL_ASYNC_TOPIC_NAME = "入库单推送etl消息";

    //工作台待办处理
    public static final String BUSINESS_TASK_TOPIC = "business_task_topic";
    public static final String BUSINESS_TASK_TOPIC_NAME = "工作台待办topic";

    // 出库单自动发货操作
    public static final String AUTO_SHIP_TOPIC = "auto_ship_topic";
    public static final String AUTO_SHIP_TOPIC_MARK = "自动发货";

    // 波次自动分拣
    public static final String BATCH_QUICK_SPLIT = "batch_quick_split";
    public static final String BATCH_QUICK_SPLIT_MARK = "波次快速分拣";

    // 月台同步Erp
    public static final String STOCK_PLATFORM_SCHEDULE_TOPIC = "stock_platform_schedule_topic_topic";
    public static final String STOCK_PLATFORM_SCHEDULE_TOPIC_NAME = "月台同步Erp";

    // 月台同步Erp
    public static final String PRODUCT_TRY_ON_TASK_TOPIC = "product_try_on_task_topic";
    public static final String PRODUCT_TRY_ON_TASK_TOPIC_NAME = "商品试穿任务topic";

    // 工厂直发
    public static final String FACTORY_DIRECT_SHIPPING_TOPIC = "factory_direct_shipping_topic";

    // 上架、调拨、盘点同步商通
    public static final String STOCK_SYNC_TO_ERP_TOPIC = "stock_sync_to_erp_topic";
    public static final String STOCK_SYNC_TO_ERP_TOPIC_NAME = "库存同步ERP";

    //AEO操作
    public static final String AEO_OPERATE_TOPIC = "aeo_operate_topic";
    public static final String AEO_OPERATE_MARK = "AEO操作";

    // 更新质检任务
    public static final String STOCK_IN_QA_TASK_TOPIC = "stock_in_qa_task_topic";
    public static final String STOCK_IN_QA_TASK_TOPIC_NAME = "同步质检系统质检任务";

    // 质检单审核
    public static final String STOCK_IN_QA_ORDER_AUDIT_TOPIC = "stock_in_qa_order_audit_topic";
    public static final String STOCK_IN_QA_ORDER_AUDIT_TOPIC_NAME = "质检单审核";

    // 同步巡检到旧系统
    public static final String STOCK_IN_QA_ROUTING_INSPECT_SYNC_TOPIC = "stock_in_qa_routing_inspect_sync_topic";
    public static final String STOCK_IN_QA_ROUTING_INSPECT_SYNC_TOPIC_NAME = "巡检任务同步";


    // 同步质检到旧系统
    public static final String STOCK_IN_QA_ORDER_SYNC_TOPIC = "stock_in_qa_order_sync_topic";
    public static final String STOCK_IN_QA_ORDER_SYNC_TOPIC_NAME = "质检单同步旧系统";

    //更新申报要素
    public static final String UPDATE_ELEMENT_TOPIC = "update_element_topic";
    public static final String UPDATE_ELEMENT_MARK = "更新申报要素";


    //关单自动生成
    public static final String DECLARE_CONTRACT_AUTO_GENERATE_TOPIC = "declare_contract_auto_generate_topic";
    public static final String DECLARE_CONTRACT_AUTO_GENERATE_MARK = "关单自动生成";

    // 出库单自动发货操作
    public static final String SCAN_COMPLETE_UPDATE_FBA_LABEL_TOPIC = "scan_complete_update_fba_label_topic";
    public static final String SCAN_COMPLETE_UPDATE_FBA_LABEL_TOPIC_MARK = "复核完成更新箱贴状态";

    //关单获取
    public static final String DECLARE_FORM_FETCH_TOPIC = "declare_form_fetch_topic";
    public static final String DECLARE_FORM_FETCH_MARK = "关单获取";

    //报关合同审核通过
    public static final String DECLARE_CONTRACT_AUDIT_PASS_TOPIC = "declare_contract_audit_pass";
    public static final String DECLARE_CONTRACT_AUDIT_PASS_MARK = "报关合同审核通过";

}
