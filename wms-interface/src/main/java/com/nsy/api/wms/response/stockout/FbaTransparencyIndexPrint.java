package com.nsy.api.wms.response.stockout;


import io.swagger.annotations.ApiModel;

/**
 * 装箱清单
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "FbaTransparencyIndexPrint", description = "fba T标打印")
public class FbaTransparencyIndexPrint {

    private String sellerBarcode;

    private String sellerTitle;

    private String sellerSku;

    private String brandName;

    private String sellerText = "Made In China";

    private String transparencyCodeValue;

    private Integer barcodeIndex;

    public String getSellerBarcode() {
        return sellerBarcode;
    }

    public void setSellerBarcode(String sellerBarcode) {
        this.sellerBarcode = sellerBarcode;
    }

    public String getSellerTitle() {
        return sellerTitle;
    }

    public void setSellerTitle(String sellerTitle) {
        this.sellerTitle = sellerTitle;
    }

    public String getSellerSku() {
        return sellerSku;
    }

    public void setSellerSku(String sellerSku) {
        this.sellerSku = sellerSku;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSellerText() {
        return sellerText;
    }

    public void setSellerText(String sellerText) {
        this.sellerText = sellerText;
    }

    public String getTransparencyCodeValue() {
        return transparencyCodeValue;
    }

    public void setTransparencyCodeValue(String transparencyCodeValue) {
        this.transparencyCodeValue = transparencyCodeValue;
    }

    public Integer getBarcodeIndex() {
        return barcodeIndex;
    }

    public void setBarcodeIndex(Integer barcodeIndex) {
        this.barcodeIndex = barcodeIndex;
    }
}
