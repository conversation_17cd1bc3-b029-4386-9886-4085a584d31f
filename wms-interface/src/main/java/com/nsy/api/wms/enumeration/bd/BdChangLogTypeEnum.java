package com.nsy.api.wms.enumeration.bd;

public enum BdChangLogTypeEnum {
    SPACE("仓库"),
    PLATFORM("月台"),
    PLATFORM_RULE("月台预约规则"),
    AREA("区域"),
    SPACE_AREA("库区"),
    POSITION("库位"),
    STOCKIN_RULE("入库规则"),
    SHELVE_RULE("上架规则"),
    SHELVE_RULE_ITEM("上架规则明细"),
    SHELVE_RULE_GROUP("上架规则组"),
    SHELVE_RULE_GROUP_ITEM("上架规则组明细"),
    SUPPLIER_POSITION_MAPPING("供应商库位映射"),
    MATERIAL("物料"),
    BATCH_GENERATE_RULE("波次生成规则"),
    BATCH_GENERATE_RULE_ITEM("波次生成规则明细"),
    PICKING_TYPE_RULE("拣货模式规则"),
    COMPANY("公司"),
    QA_RULE("质检规则");

    String changeLogType;

    BdChangLogTypeEnum(String changeLogType) {
        this.changeLogType = changeLogType;
    }

    public String getChangeLogType() {
        return changeLogType;
    }
}
