package com.nsy.api.wms.response.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel(value = "StockoutOrderScanLogListResponse", description = "复核任务日志列表")
public class StockoutOrderScanLogListResponse {
    @ApiModelProperty(value = "事件类型", name = "logType")
    private String logType;

    @ApiModelProperty(value = "描述", name = "content")
    private String content;

    @ApiModelProperty(value = "操作人", name = "createBy")
    private String createBy;

    @ApiModelProperty(value = "操作时间", name = "createDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "操作ip", name = "ipAddress")
    private String ipAddress;

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}
