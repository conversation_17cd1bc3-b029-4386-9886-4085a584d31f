package com.nsy.api.wms.response.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-17 17:47
 */
public class StockinHeightRecordMappingResponse {
    @ApiModelProperty("主键ID")
    private Integer id;

    @ApiModelProperty("商品编码")
    private String sku;

    @ApiModelProperty("条形码")
    private String barcode;

    @ApiModelProperty("长度")
    private BigDecimal length;

    @ApiModelProperty("宽度")
    private BigDecimal width;

    @ApiModelProperty("高度")
    private BigDecimal height;

    @ApiModelProperty("重量")
    private BigDecimal weight;

    @ApiModelProperty("高度测量类型")
    private String heightMeasureType;

    @ApiModelProperty("高度测量类型 - 中文")
    private String heightMeasureTypeStr;
    @ApiModelProperty("体积重")
    private BigDecimal volumeWeight;

    @ApiModelProperty("FBA费用")
    private BigDecimal fbaCost;

    @ApiModelProperty("测量高度")
    private BigDecimal measureHeight;

    @ApiModelProperty("测量体积重")
    private BigDecimal measureVolumeWeight;

    @ApiModelProperty("测量FBA费用")
    private BigDecimal measureFbaCost;

    @ApiModelProperty("分拣口")
    private String sortingPort;

    @ApiModelProperty("达标/不达标 -- 中文")
    private String sortingPortStr;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty("是否超标准 0:否 1:是")
    private Integer isOverStandard;

    @ApiModelProperty("是否合格 0:否 1:是")
    private Integer isQualified;

    @ApiModelProperty("创建人")
    private String createBy;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getHeightMeasureType() {
        return heightMeasureType;
    }

    public void setHeightMeasureType(String heightMeasureType) {
        this.heightMeasureType = heightMeasureType;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public BigDecimal getMeasureHeight() {
        return measureHeight;
    }

    public void setMeasureHeight(BigDecimal measureHeight) {
        this.measureHeight = measureHeight;
    }

    public BigDecimal getMeasureVolumeWeight() {
        return measureVolumeWeight;
    }

    public void setMeasureVolumeWeight(BigDecimal measureVolumeWeight) {
        this.measureVolumeWeight = measureVolumeWeight;
    }

    public BigDecimal getMeasureFbaCost() {
        return measureFbaCost;
    }

    public void setMeasureFbaCost(BigDecimal measureFbaCost) {
        this.measureFbaCost = measureFbaCost;
    }

    public String getSortingPort() {
        return sortingPort;
    }

    public void setSortingPort(String sortingPort) {
        this.sortingPort = sortingPort;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getIsOverStandard() {
        return isOverStandard;
    }

    public void setIsOverStandard(Integer isOverStandard) {
        this.isOverStandard = isOverStandard;
    }

    public Integer getIsQualified() {
        return isQualified;
    }

    public void setIsQualified(Integer isQualified) {
        this.isQualified = isQualified;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getHeightMeasureTypeStr() {
        return heightMeasureTypeStr;
    }

    public void setHeightMeasureTypeStr(String heightMeasureTypeStr) {
        this.heightMeasureTypeStr = heightMeasureTypeStr;
    }

    public String getSortingPortStr() {
        return sortingPortStr;
    }

    public void setSortingPortStr(String sortingPortStr) {
        this.sortingPortStr = sortingPortStr;
    }
}
