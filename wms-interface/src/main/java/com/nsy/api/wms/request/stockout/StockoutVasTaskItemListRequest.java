package com.nsy.api.wms.request.stockout;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel(value = "StockoutVasTaskItemListRequest", description = "增值任务明细列表request")
public class StockoutVasTaskItemListRequest extends PageRequest {

    @ApiModelProperty(value = "任务id", name = "taskId")
    private Integer taskId;
    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }
}
