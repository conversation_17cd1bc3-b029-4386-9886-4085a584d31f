package com.nsy.api.wms.request.bd;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/11/27 17:21
 */
public class SupplierReturnAddressInfoSaveRequest {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("店铺id")
    @NotNull(message = "店铺信息不能为空")
    private Integer supplierId;

    @ApiModelProperty("店铺名")
    private String supplierName;

    /**
     * 收件人姓名
     */
    @ApiModelProperty("收件人姓名")
    private String recipient;
    /**
     * 收件人电话
     */
    @ApiModelProperty("收件人电话")
    private String recipientTel;
    /**
     * 省
     */
    @ApiModelProperty("省")
    private String province;
    /**
     * 市
     */
    @ApiModelProperty("市")
    private String city;
    /**
     * 区
     */
    @ApiModelProperty("区")
    private String district;
    /**
     * 收件人详细地址
     */
    @ApiModelProperty("收件人详细地址")
    private String address;

    @ApiModelProperty("物流方式")
    private String logisticsType;

    @ApiModelProperty(value = "运费承担方", name = "freightCarrier")
    private Integer freightCarrier;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public String getRecipientTel() {
        return recipientTel;
    }

    public void setRecipientTel(String recipientTel) {
        this.recipientTel = recipientTel;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public Integer getFreightCarrier() {
        return freightCarrier;
    }

    public void setFreightCarrier(Integer freightCarrier) {
        this.freightCarrier = freightCarrier;
    }
}
