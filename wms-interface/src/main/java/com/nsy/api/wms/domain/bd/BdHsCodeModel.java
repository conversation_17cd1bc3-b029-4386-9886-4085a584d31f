package com.nsy.api.wms.domain.bd;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 海关编码
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "BdHsCodeModel", description = "海关编码对象")
public class BdHsCodeModel {

    /**
     * 海关编码
     */
    @ApiModelProperty(value = "海关编码", name = "hsCode")
    private String hsCode;

    /**
     * 海关编码描述
     */
    @ApiModelProperty(value = "海关编码描述", name = "hsCodeCn")
    private String hsCodeCn;

    /**
     * 单位编码，多个以 / 间隔
     */
    @ApiModelProperty(value = "单位编码，多个以 / 间隔", name = "unit")
    private String unit;

    /**
     * 单位编码名称，多个以 / 间隔
     */
    @ApiModelProperty(value = "单位编码名称，多个以 / 间隔", name = "unitCn")
    private String unitCn;


    public String getHsCode() {
        return this.hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

    public String getHsCodeCn() {
        return this.hsCodeCn;
    }

    public void setHsCodeCn(String hsCodeCn) {
        this.hsCodeCn = hsCodeCn;
    }

    public String getUnit() {
        return this.unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitCn() {
        return this.unitCn;
    }

    public void setUnitCn(String unitCn) {
        this.unitCn = unitCn;
    }

}
