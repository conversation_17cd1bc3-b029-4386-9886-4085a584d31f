package com.nsy.api.wms.enumeration.overseas;

import org.apache.logging.log4j.util.Strings;

import java.util.Arrays;
import java.util.Objects;

/**
 * 海外仓订单状态枚举
 *
 * <AUTHOR>
 * @since 1.0
 */
public enum OverseasWarehouseOrderStatusEnum {
    PUSH_ERROR("推送异常"),
    PENDING_SHIP("待发货"),
    SHIPPED("已发货"),
    SIGNED("已签收");

    private final String name;

    OverseasWarehouseOrderStatusEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static String of(String status) {
        if (Strings.isBlank(status)) return null;
        OverseasWarehouseOrderStatusEnum resultEnum = Arrays.stream(values())
                .filter(instance -> status.equals(instance.name()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(resultEnum)) return null;

        return resultEnum.getName();
    }
}
