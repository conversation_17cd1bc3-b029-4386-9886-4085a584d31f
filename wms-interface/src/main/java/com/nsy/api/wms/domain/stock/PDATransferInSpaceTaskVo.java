package com.nsy.api.wms.domain.stock;

import java.util.Date;

public class PDATransferInSpaceTaskVo {

    private Integer transferTaskId;

    /**
     * 地区
     */
    private String location;

    /**
     * 仓库id
     */
    private Integer spaceId;

    /**
     * 仓库
     */
    private String spaceName;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 调出库位数
     */
    private Integer transferOutPositionQty;

    /**
     * 调入库位数
     */
    private Integer transferInPositionQty;

    /**
     * 调拨sku件数
     */
    private Integer transferSkuQty;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 开始时间
     */
    private Date operateStartDate;

    /**
     * 结束时间
     */
    private Date operateEndDate;

    private String transferBoxCode;

    public Integer getTransferTaskId() {
        return transferTaskId;
    }

    public void setTransferTaskId(Integer transferTaskId) {
        this.transferTaskId = transferTaskId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTransferOutPositionQty() {
        return transferOutPositionQty;
    }

    public void setTransferOutPositionQty(Integer transferOutPositionQty) {
        this.transferOutPositionQty = transferOutPositionQty;
    }

    public Integer getTransferInPositionQty() {
        return transferInPositionQty;
    }

    public void setTransferInPositionQty(Integer transferInPositionQty) {
        this.transferInPositionQty = transferInPositionQty;
    }

    public Integer getTransferSkuQty() {
        return transferSkuQty;
    }

    public void setTransferSkuQty(Integer transferSkuQty) {
        this.transferSkuQty = transferSkuQty;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateStartDate() {
        return operateStartDate;
    }

    public void setOperateStartDate(Date operateStartDate) {
        this.operateStartDate = operateStartDate;
    }

    public Date getOperateEndDate() {
        return operateEndDate;
    }

    public void setOperateEndDate(Date operateEndDate) {
        this.operateEndDate = operateEndDate;
    }

    public String getTransferBoxCode() {
        return transferBoxCode;
    }

    public void setTransferBoxCode(String transferBoxCode) {
        this.transferBoxCode = transferBoxCode;
    }
}
