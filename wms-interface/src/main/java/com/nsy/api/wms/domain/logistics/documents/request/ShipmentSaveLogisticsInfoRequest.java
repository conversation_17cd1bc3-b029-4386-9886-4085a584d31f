package com.nsy.api.wms.domain.logistics.documents.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;
@ApiModel(value = "ShipmentSaveLogisticsInfoRequest", description = "装箱清单回填物流单号request")
public class ShipmentSaveLogisticsInfoRequest {

    @ApiModelProperty(value = "选中的装箱清单id,用来回填物流单号", name = "shipmentIdList")
    private List<Integer> shipmentIdList;

    @ApiModelProperty(value = "物流公司", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "是否重新打印", name = "rePrint")
    private Boolean rePrint;

    @ApiModelProperty(value = "包裹数", name = "packageCount")
    private Integer packageCount;

    @ApiModelProperty(value = "包裹总重量", name = "weight")
    private BigDecimal weight;

    @ApiModelProperty(value = "箱规", name = "boxSize")
    private String boxSize;

    public List<Integer> getShipmentIdList() {
        return shipmentIdList;
    }

    public void setShipmentIdList(List<Integer> shipmentIdList) {
        this.shipmentIdList = shipmentIdList;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Boolean getRePrint() {
        return rePrint;
    }

    public void setRePrint(Boolean rePrint) {
        this.rePrint = rePrint;
    }

    public Integer getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(Integer packageCount) {
        this.packageCount = packageCount;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public String getBoxSize() {
        return boxSize;
    }

    public void setBoxSize(String boxSize) {
        this.boxSize = boxSize;
    }
}
