package com.nsy.api.wms.request.stockin;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel("退货任务请求实体")
public class StockinReturnProductTaskIdsRequest {
    private static final long serialVersionUID = 11465321388418922L;

    /**
     * 退货任务主键列表
     */
    @ApiModelProperty(value = "退货任务主键列表", required = true)
    @NotEmpty(message = "退货任务主键列表不能为空")
    private List<Integer> ids;
    public List<Integer> getIds() {
        return ids;
    }
    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }
}
