package com.nsy.api.wms.enumeration.stock;

public enum StockTransferStatusEnum {

    WAIT_OUTBOUND("待出库"),
    OUTBOUNDING("出库中"),
    OUTBOUNDED("已出库"),
    CANCELLED("已取消"),
    WART_INBOUND("待入库"),
    INBOUNDING("入库中"),
    INBOUNDED("已入库");

    String status;

    StockTransferStatusEnum(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
