package com.nsy.api.wms.enumeration.stockout;

public enum StockoutBatchSplitTaskTypeEnum {
    BEGIN_SORT("开始分拣"),
    EDIT_SCAN_QTY("修改扫描数"),
    CANCEL_SCAN("撤销扫描"),
    SCAN_WITHDRAWAL("扫描撤货"),
    WITHDRAWAL("撤货"),
    NOTICE_LACK("通知缺货"),
    LACK_SORT("缺货拣货"),
    LACK_SORT_TASK("缺货拣货任务"),
    CONFIRM_LACK("确定缺货"),
    SORT_BATCH_STOCKOUT_ORDER("分拣波次/出库单"),
    CREATE_SHIPMENT("生成装箱"),
    SUSPEND_SORT("暂停分拣"),
    SORTED("分拣完成"),
    CHANGE_SORT_STATUS("修改分拣任务状态"),
    EXCEPTION_SORT("异常分拣");

    String name;

    StockoutBatchSplitTaskTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
