package com.nsy.api.wms.response.stockout;

import com.nsy.api.wms.domain.stockout.ShipmentShipDate;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

/**
 * HXD
 * 2021/8/24
 **/

public class StockoutShipmentShipDateResponse {
    @ApiModelProperty(value = "sku装箱集合", name = "shipmentActualDeliveryDateList")
    private List<ShipmentShipDate> shipmentActualDeliveryDateList = new ArrayList<>();

    public List<ShipmentShipDate> getShipmentActualDeliveryDateList() {
        return shipmentActualDeliveryDateList;
    }

    public void setShipmentActualDeliveryDateList(List<ShipmentShipDate> shipmentActualDeliveryDateList) {
        this.shipmentActualDeliveryDateList = shipmentActualDeliveryDateList;
    }
}
