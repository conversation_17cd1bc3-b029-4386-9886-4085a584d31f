package com.nsy.api.wms.request.bd;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/7/30 16:05
 */
public class BdBoxSpecificationsSaveRequest {


    /**
     * 装箱别名
     */
    private String boxAlias;

    /**
     * 长
     */
    private BigDecimal length;
    /**
     * 宽
     */
    private BigDecimal width;
    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 体积重
     */
    private BigDecimal volumeWeight;

    public String getBoxAlias() {
        return boxAlias;
    }

    public void setBoxAlias(String boxAlias) {
        this.boxAlias = boxAlias;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(BigDecimal volumeWeight) {
        this.volumeWeight = volumeWeight;
    }
}
