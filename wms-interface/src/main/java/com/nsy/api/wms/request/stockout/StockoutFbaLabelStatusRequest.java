package com.nsy.api.wms.request.stockout;

import io.swagger.annotations.ApiModel;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "StockoutFbaLabelStatusRequest", description = "更新Fba箱贴状态")
public class StockoutFbaLabelStatusRequest {

    @NotBlank
    private String orderNo;

    // (待申请INIT，申请中APPLYING，申请完成COMPLETE, 申请异常EXCEPTION)
    @NotBlank
    private String labelStatus;

    private Boolean syncAmazon = Boolean.FALSE;

    // 失败/异常 原因，长度200
    private String exceptionMsg;

    public String getExceptionMsg() {
        return exceptionMsg;
    }

    public void setExceptionMsg(String exceptionMsg) {
        this.exceptionMsg = exceptionMsg;
    }

    public Boolean getSyncAmazon() {
        return syncAmazon;
    }

    public void setSyncAmazon(Boolean syncAmazon) {
        this.syncAmazon = syncAmazon;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLabelStatus() {
        return labelStatus;
    }

    public void setLabelStatus(String labelStatus) {
        this.labelStatus = labelStatus;
    }
}
