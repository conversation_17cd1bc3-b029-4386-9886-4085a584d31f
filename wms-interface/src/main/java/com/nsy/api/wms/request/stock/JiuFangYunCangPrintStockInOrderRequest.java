package com.nsy.api.wms.request.stock;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-27 11:24
 */
public class JiuFangYunCangPrintStockInOrderRequest {
    /**
     * location
     */
    private String location;

    /**
     * logisticsCompany
     */
    private String logisticsCompany;

    private String platformReferenceNo;

    /**
     * 打印文件类型 list清单, box箱唛, box-qr热敏纸(100x100)(二维码)箱唛,carton自定义箱唛
     */
    private String pdfType;

    public String getPlatformReferenceNo() {
        return platformReferenceNo;
    }

    public void setPlatformReferenceNo(String platformReferenceNo) {
        this.platformReferenceNo = platformReferenceNo;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getPdfType() {
        return pdfType;
    }

    public void setPdfType(String pdfType) {
        this.pdfType = pdfType;
    }
}

