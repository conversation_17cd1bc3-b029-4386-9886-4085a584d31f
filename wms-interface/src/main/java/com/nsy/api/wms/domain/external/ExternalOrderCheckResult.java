package com.nsy.api.wms.domain.external;

import io.swagger.annotations.ApiModelProperty;

public class ExternalOrderCheckResult {
    @ApiModelProperty(value = "id", name = "checkResultId")
    private Integer checkResultId;

    @ApiModelProperty(value = "接口地址", name = "apiUrl")
    private String apiUrl;

    @ApiModelProperty(value = "请求json", name = "requestContent")
    private String requestContent;

    @ApiModelProperty(value = "响应内容", name = "responseContent")
    private String responseContent;

    public Integer getCheckResultId() {
        return checkResultId;
    }

    public void setCheckResultId(Integer checkResultId) {
        this.checkResultId = checkResultId;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getRequestContent() {
        return requestContent;
    }

    public void setRequestContent(String requestContent) {
        this.requestContent = requestContent;
    }

    public String getResponseContent() {
        return responseContent;
    }

    public void setResponseContent(String responseContent) {
        this.responseContent = responseContent;
    }
}
