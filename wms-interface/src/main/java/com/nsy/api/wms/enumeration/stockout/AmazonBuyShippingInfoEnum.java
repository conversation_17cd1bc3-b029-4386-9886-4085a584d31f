package com.nsy.api.wms.enumeration.stockout;

/**
 * 购买配送
 */
public enum AmazonBuyShippingInfoEnum {
    NONE("None", "未设置"),
    ENABLE("Enable", "已设置"),
    DISABLE("Disable", "取消设置");

    String value;

    String desc;

    AmazonBuyShippingInfoEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
