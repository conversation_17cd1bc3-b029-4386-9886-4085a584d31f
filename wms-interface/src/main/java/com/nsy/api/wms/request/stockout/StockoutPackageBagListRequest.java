package com.nsy.api.wms.request.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import java.util.List;


@ApiModel(value = "StockoutPackageBagListRequest", description = "发货包裹列表查询request")
public class StockoutPackageBagListRequest extends PageRequest {

    @ApiModelProperty(hidden = true)
    private List<Integer> bagIds;

    @ApiModelProperty(value = "发货编号", name = "bagCode")
    private String bagCode;

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "物流渠道(公司)", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "报关方式", name = "customsDeclareType")
    private String customsDeclareType;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "状态", name = "status")
    private List<String> statusList;

    @ApiModelProperty(value = "箱号", name = "bagIndex")
    private int bagIndex;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "打包时间-开始", name = "createDateStartDate")
    private Date createDateStartDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "打包时间-结束", name = "createDateEndDate")
    private Date createDateEndDate;

    public List<Integer> getBagIds() {
        return bagIds;
    }

    public void setBagIds(List<Integer> bagIds) {
        this.bagIds = bagIds;
    }

    public String getBagCode() {
        return bagCode;
    }

    public void setBagCode(String bagCode) {
        this.bagCode = bagCode;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getCustomsDeclareType() {
        return customsDeclareType;
    }

    public void setCustomsDeclareType(String customsDeclareType) {
        this.customsDeclareType = customsDeclareType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateDateStartDate() {
        return createDateStartDate;
    }

    public void setCreateDateStartDate(Date createDateStartDate) {
        this.createDateStartDate = createDateStartDate;
    }

    public Date getCreateDateEndDate() {
        return createDateEndDate;
    }

    public void setCreateDateEndDate(Date createDateEndDate) {
        this.createDateEndDate = createDateEndDate;
    }

    public int getBagIndex() {
        return bagIndex;
    }

    public void setBagIndex(int bagIndex) {
        this.bagIndex = bagIndex;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }
}
