package com.nsy.api.wms.request.overseas;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 海外仓订单包裹时间更新请求
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel("海外仓订单包裹时间更新请求")
public class OverseasWarehouseOrderPackageTimeUpdateRequest {

    @ApiModelProperty(value = "物流单号", required = true)
    @NotBlank(message = "物流单号不能为空")
    private String logisticsNo;

    @ApiModelProperty("包裹提取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date packagePickupTime;

    @ApiModelProperty("包裹签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date packageSignedTime;

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Date getPackagePickupTime() {
        return packagePickupTime;
    }

    public void setPackagePickupTime(Date packagePickupTime) {
        this.packagePickupTime = packagePickupTime;
    }

    public Date getPackageSignedTime() {
        return packageSignedTime;
    }

    public void setPackageSignedTime(Date packageSignedTime) {
        this.packageSignedTime = packageSignedTime;
    }
}
