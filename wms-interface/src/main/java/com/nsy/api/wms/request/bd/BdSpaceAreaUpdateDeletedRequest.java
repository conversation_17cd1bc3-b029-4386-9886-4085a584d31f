package com.nsy.api.wms.request.bd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "BdSpaceAreaUpdateDeletedRequest", description = "库区启用禁用request")
public class BdSpaceAreaUpdateDeletedRequest {

    @ApiModelProperty(value = "启用、禁用", name = "deleted", required = true)
    private Boolean deleted;

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
