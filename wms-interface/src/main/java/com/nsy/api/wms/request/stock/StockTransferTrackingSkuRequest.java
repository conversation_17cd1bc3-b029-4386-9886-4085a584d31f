package com.nsy.api.wms.request.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 查询sku仓库在途库存请求类
 */
@ApiModel(value = "StockTransferTrackingSkuRequest", description = "查询sku仓库在途库存请求")
public class StockTransferTrackingSkuRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "SKU信息列表", required = true)
    @NotEmpty(message = "SKU信息列表不能为空")
    @Valid
    private List<SkuInfo> skuInfoList;

    public List<SkuInfo> getSkuInfoList() {
        return skuInfoList;
    }

    public void setSkuInfoList(List<SkuInfo> skuInfoList) {
        this.skuInfoList = skuInfoList;
    }

    @ApiModel(value = "SkuInfo", description = "SKU信息")
    public static class SkuInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "SKU编码", required = true)
        @NotEmpty(message = "SKU编码不能为空")
        private String sku;

        @ApiModelProperty(value = "ERP仓库ID列表", required = false)
        // 允许为空，为空时查询所有仓库
        private List<Integer> erpSpaceIdList;

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public List<Integer> getErpSpaceIdList() {
            return erpSpaceIdList;
        }

        public void setErpSpaceIdList(List<Integer> erpSpaceIdList) {
            this.erpSpaceIdList = erpSpaceIdList;
        }
    }
}
