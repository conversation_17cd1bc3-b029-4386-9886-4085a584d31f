package com.nsy.api.wms.request.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "PrintTemplateContentAddRequest", description = "面单模板代码新增Request")
public class PrintTemplateContentAddRequest {

    @ApiModelProperty(value = "模板内容", name = "content")
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
