package com.nsy.api.wms.request.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "AutoMachineSortListRequest", description = "窄带分拣列表request")
public class AutoMachineSortListRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = -6572702088702704971L;
    @ApiModelProperty(value = "波次号", name = "batchId")
    private Integer batchId;

    @ApiModelProperty(value = "波次类型,1:合并波次 0:正常波次", name = "isMergeBatch")
    private Integer isMergeBatch;

    @ApiModelProperty(value = "工作区域", name = "workspace")
    private String workspace;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "状态数组", name = "statusList")
    private List<String> statusList;

    @ApiModelProperty(value = "仓库", name = "spaceIdList")
    private List<Integer> spaceIdList;

    @ApiModelProperty(value = "分拣日期开始", name = "createStartDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createStartDate;

    @ApiModelProperty(value = "分拣日期结束", name = "createEndDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createEndDate;

    @ApiModelProperty(value = "复核日期开始", name = "operateStartDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateStartDate;

    @ApiModelProperty(value = "复核日期结束", name = "operateEndDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateEndDate;

    @ApiModelProperty(value = "是否异常分拣", name = "isException")
    private Boolean isException;

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public Integer getIsMergeBatch() {
        return isMergeBatch;
    }

    public void setIsMergeBatch(Integer isMergeBatch) {
        this.isMergeBatch = isMergeBatch;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public List<Integer> getSpaceIdList() {
        return spaceIdList;
    }

    public void setSpaceIdList(List<Integer> spaceIdList) {
        this.spaceIdList = spaceIdList;
    }

    public Date getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(Date createStartDate) {
        this.createStartDate = createStartDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }

    public Date getOperateStartDate() {
        return operateStartDate;
    }

    public void setOperateStartDate(Date operateStartDate) {
        this.operateStartDate = operateStartDate;
    }

    public Date getOperateEndDate() {
        return operateEndDate;
    }

    public void setOperateEndDate(Date operateEndDate) {
        this.operateEndDate = operateEndDate;
    }

    public Boolean getIsException() {
        return isException;
    }

    public void setIsException(Boolean isException) {
        this.isException = isException;
    }
}
