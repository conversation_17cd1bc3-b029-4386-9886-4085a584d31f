package com.nsy.api.wms.enumeration.common;

/**
 * 开关枚举
 */
public enum OpenEnum {
    ENABLE(1, "启用"),
    DISABLE(0, "停用");

    private Integer code;
    private String value;

    OpenEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValueByCode(Integer code) {
        String result = "";
        if (code == null) {
            return result;
        }
        for (OpenEnum statusEnum: OpenEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.value;
            }
        }
        return result;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
