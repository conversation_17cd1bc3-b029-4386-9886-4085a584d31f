package com.nsy.api.wms.request.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ApiModel(value = "StockoutOrderListRequest", description = "出库单列表查询request")
public class StockoutOrderListRequest extends PageRequest {

    private List<Integer> idList;

    @ApiModelProperty(value = "erp拣货单id", name = "erpPickId")
    private Integer erpPickId;

    @ApiModelProperty(value = "出库单Id", name = "stockoutOrderIds")
    private List<Integer> stockoutOrderIds = new ArrayList<>();

    @ApiModelProperty(value = "出库单号", name = "stockoutOrderNo")
    private String stockoutOrderNo;

    @ApiModelProperty(value = "订单号", name = "orderNo")
    private String orderNo;

    @ApiModelProperty(value = "批量订单号", name = "orderNos")
    private List<String> orderNos = new ArrayList<>();

    @ApiModelProperty(value = "批量出库单号", name = "stockoutOrderNoList")
    private List<String> stockoutOrderNoList;

    @ApiModelProperty(value = "批量物流单号", name = "logisticsNoList")
    private List<String> logisticsNoList;

    @ApiModelProperty(value = "补货单号", name = "replenishOrder")
    private String replenishOrder;

    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;

    @ApiModelProperty(value = "收货人姓名", name = "receiverName")
    private String receiverName;

    @ApiModelProperty(value = "国家代码", name = "countryCode")
    private String countryCode;

    @ApiModelProperty(value = "拣货模式（多选）", name = "pickingTypes")
    private List<String> pickingTypes;

    @ApiModelProperty(value = "波次号", name = "batchId")
    private Integer batchId;

    @ApiModelProperty(value = "仓库（多选）", name = "spaceIds")
    private List<Integer> spaceIds;

    @ApiModelProperty(value = "物流公司（多选）", name = "logisticsCompanys")
    private List<String> logisticsCompanys;

    @ApiModelProperty(value = "工作区域", name = "workspace")
    private List<String> workspace;

    @ApiModelProperty(value = "预发货日期-开始", name = "exceptStartDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exceptStartDate;

    @ApiModelProperty(value = "预发货日期-结束", name = "exceptEndDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exceptEndDate;

    @ApiModelProperty(value = "创建日期开始", name = "createStartDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createStartDate;

    @ApiModelProperty(value = "创建日期结束", name = "createEndDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createEndDate;

    @ApiModelProperty(value = "是否急单", name = "urgent")
    private Boolean urgent;

    @ApiModelProperty(value = "是否合单", name = "merge")
    private Boolean merge;

    @ApiModelProperty(value = "是否PACK", name = "pack")
    private Boolean pack;

    @ApiModelProperty(value = "是否加工", name = "needProcess")
    private Boolean needProcess;

    private Integer isProcess;

    @ApiModelProperty(value = "是否缺货", name = "lack")
    private Boolean lack;

    @ApiModelProperty(value = "预估重量-低", name = "estimateWeightLow")
    private BigDecimal estimateWeightLow;

    @ApiModelProperty(value = "预估重量-高", name = "estimateWeightHigh")
    private BigDecimal estimateWeightHigh;

    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;

    @ApiModelProperty(value = "店铺id List", name = "storeIdList")
    private List<Integer> storeIdList;

    @ApiModelProperty(value = "规格编码", name = "sku")
    private String sku;

    @ApiModelProperty(value = "规格编码列表", name = "skuList")
    private List<String> skuList = new ArrayList<>();

    @ApiModelProperty(value = "通知发货", name = "notifyShipStatus")
    private String notifyShipStatus;

    @ApiModelProperty(value = "部门（多选）", name = "businessTypes")
    private List<String> businessTypes;

    @ApiModelProperty(value = "出库类型数组", name = "stockoutTypes")
    private List<String> stockoutTypes;

    @ApiModelProperty(value = "出库状态", name = "status")
    private String status;

    @ApiModelProperty(value = "待分拣数", name = "expectedQty")
    private Integer expectedQty;

    @ApiModelProperty(value = "出库状态（多选）", name = "statusList")
    private List<String> statusList;

    @ApiModelProperty(value = "平台名称", name = "platformName")
    private String platformName;

    @ApiModelProperty(value = "区域名称", name = "areaNameList")
    private List<String> areaNameList;

    @ApiModelProperty(value = "标签id列表", name = "tagIdList")
    private List<Integer> tagIdList;

    @ApiModelProperty(value = "增值服务集合", name = "vasTypeList")
    private String vasType;

    @ApiModelProperty(value = "是否增值", name = "isAddVas")
    private Boolean isAddVas;

    @ApiModelProperty(value = "库区ids", hidden = true)
    private List<Integer> spaceAreaIds;

    @ApiModelProperty(value = "库区名称", name = "spaceAreaNameList")
    private List<String> spaceAreaNameList;


    @ApiModelProperty(value = "是否透明计划", name = "isTransparency")
    private Boolean isTransparency;

    public String getReplenishOrder() {
        return replenishOrder;
    }

    public void setReplenishOrder(String replenishOrder) {
        this.replenishOrder = replenishOrder;
    }

    public Boolean getIsTransparency() {
        return isTransparency;
    }

    public void setIsTransparency(Boolean transparency) {
        isTransparency = transparency;
    }

    public List<Integer> getSpaceAreaIds() {
        return spaceAreaIds;
    }

    public void setSpaceAreaIds(List<Integer> spaceAreaIds) {
        this.spaceAreaIds = spaceAreaIds;
    }

    public Integer getErpPickId() {
        return erpPickId;
    }

    public void setErpPickId(Integer erpPickId) {
        this.erpPickId = erpPickId;
    }

    public List<String> getOrderNos() {
        return orderNos;
    }

    public void setOrderNos(List<String> orderNos) {
        this.orderNos = orderNos;
    }

    public List<Integer> getStockoutOrderIds() {
        return stockoutOrderIds;
    }

    public void setStockoutOrderIds(List<Integer> stockoutOrderIds) {
        this.stockoutOrderIds = stockoutOrderIds;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public List<String> getWorkspace() {
        return workspace;
    }

    public void setWorkspace(List<String> workspace) {
        this.workspace = workspace;
    }

    public Date getExceptStartDate() {
        return exceptStartDate;
    }

    public void setExceptStartDate(Date exceptStartDate) {
        this.exceptStartDate = exceptStartDate;
    }

    public Date getExceptEndDate() {
        return exceptEndDate;
    }

    public void setExceptEndDate(Date exceptEndDate) {
        this.exceptEndDate = exceptEndDate;
    }

    public Boolean getUrgent() {
        return urgent;
    }

    public void setUrgent(Boolean urgent) {
        this.urgent = urgent;
    }

    public Boolean getMerge() {
        return merge;
    }

    public void setMerge(Boolean merge) {
        this.merge = merge;
    }

    public Boolean getNeedProcess() {
        return needProcess;
    }

    public void setNeedProcess(Boolean needProcess) {
        this.needProcess = needProcess;
    }

    public Integer getIsProcess() {
        return isProcess;
    }

    public void setIsProcess(Integer isProcess) {
        this.isProcess = isProcess;
    }

    public Boolean getLack() {
        return lack;
    }

    public void setLack(Boolean lack) {
        this.lack = lack;
    }

    public BigDecimal getEstimateWeightLow() {
        return estimateWeightLow;
    }

    public void setEstimateWeightLow(BigDecimal estimateWeightLow) {
        this.estimateWeightLow = estimateWeightLow;
    }

    public BigDecimal getEstimateWeightHigh() {
        return estimateWeightHigh;
    }

    public void setEstimateWeightHigh(BigDecimal estimateWeightHigh) {
        this.estimateWeightHigh = estimateWeightHigh;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public List<Integer> getStoreIdList() {
        return storeIdList;
    }

    public void setStoreIdList(List<Integer> storeIdList) {
        this.storeIdList = storeIdList;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getNotifyShipStatus() {
        return notifyShipStatus;
    }

    public void setNotifyShipStatus(String notifyShipStatus) {
        this.notifyShipStatus = notifyShipStatus;
    }

    public List<String> getStockoutTypes() {
        return stockoutTypes;
    }

    public void setStockoutTypes(List<String> stockoutTypes) {
        this.stockoutTypes = stockoutTypes;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<String> getPickingTypes() {
        return pickingTypes;
    }

    public void setPickingTypes(List<String> pickingTypes) {
        this.pickingTypes = pickingTypes;
    }

    public List<Integer> getSpaceIds() {
        return spaceIds;
    }

    public void setSpaceIds(List<Integer> spaceIds) {
        this.spaceIds = spaceIds;
    }

    public List<String> getLogisticsCompanys() {
        return logisticsCompanys;
    }

    public void setLogisticsCompanys(List<String> logisticsCompanys) {
        this.logisticsCompanys = logisticsCompanys;
    }

    public List<String> getBusinessTypes() {
        return businessTypes;
    }

    public void setBusinessTypes(List<String> businessTypes) {
        this.businessTypes = businessTypes;
    }

    public List<String> getStatusList() {
        return statusList;
    }

    public void setStatusList(List<String> statusList) {
        this.statusList = statusList;
    }

    public Date getCreateStartDate() {
        return createStartDate;
    }

    public void setCreateStartDate(Date createStartDate) {
        this.createStartDate = createStartDate;
    }

    public Date getCreateEndDate() {
        return createEndDate;
    }

    public void setCreateEndDate(Date createEndDate) {
        this.createEndDate = createEndDate;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Integer getExpectedQty() {
        return expectedQty;
    }

    public void setExpectedQty(Integer expectedQty) {
        this.expectedQty = expectedQty;
    }

    public Boolean getPack() {
        return pack;
    }

    public void setPack(Boolean pack) {
        this.pack = pack;
    }

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public List<Integer> getTagIdList() {
        return tagIdList;
    }

    public void setTagIdList(List<Integer> tagIdList) {
        this.tagIdList = tagIdList;
    }

    public List<String> getAreaNameList() {
        return areaNameList;
    }

    public void setAreaNameList(List<String> areaNameList) {
        this.areaNameList = areaNameList;
    }

    public List<String> getStockoutOrderNoList() {
        return stockoutOrderNoList;
    }

    public void setStockoutOrderNoList(List<String> stockoutOrderNoList) {
        this.stockoutOrderNoList = stockoutOrderNoList;
    }

    public List<String> getLogisticsNoList() {
        return logisticsNoList;
    }

    public void setLogisticsNoList(List<String> logisticsNoList) {
        this.logisticsNoList = logisticsNoList;
    }

    public String getVasType() {
        return vasType;
    }

    public void setVasType(String vasType) {
        this.vasType = vasType;
    }

    public Boolean getIsAddVas() {
        return isAddVas;
    }

    public void setIsAddVas(Boolean isAddVas) {
        this.isAddVas = isAddVas;
    }

    public List<String> getSpaceAreaNameList() {
        return spaceAreaNameList;
    }

    public void setSpaceAreaNameList(List<String> spaceAreaNameList) {
        this.spaceAreaNameList = spaceAreaNameList;
    }

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }
}
