package com.nsy.api.wms.request.stock;

import io.swagger.annotations.ApiModel;

import java.util.List;

@ApiModel(value = "ConfirmDifferenceRequest", description = "差异核对单-确认差异 request")
public class ConfirmDifferenceRequest {
    private List<ConfirmDifferenceParam> list;

    public List<ConfirmDifferenceParam> getList() {
        return list;
    }

    public void setList(List<ConfirmDifferenceParam> list) {
        this.list = list;
    }

    public static class ConfirmDifferenceParam {
        private Integer checkOrderItemId;
        private Integer confirmQty;

        public Integer getCheckOrderItemId() {
            return checkOrderItemId;
        }

        public void setCheckOrderItemId(Integer checkOrderItemId) {
            this.checkOrderItemId = checkOrderItemId;
        }

        public Integer getConfirmQty() {
            return confirmQty;
        }

        public void setConfirmQty(Integer confirmQty) {
            this.confirmQty = confirmQty;
        }
    }
}
