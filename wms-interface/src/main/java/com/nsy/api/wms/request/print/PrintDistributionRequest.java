package com.nsy.api.wms.request.print;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("打印分发请求")
public class PrintDistributionRequest {
    
    /**
     * 通过模板名称 或者 模板ID 来确定模板
     */
    @ApiModelProperty("模板内容名称")
    private String templateContentName;

    @ApiModelProperty("模板ID")
    private Integer templateId;
    
    @ApiModelProperty("打印参数")
    private String requestParams;

    private String location;

    private String typeService;

    public String getTypeService() {
        return typeService;
    }

    public void setTypeService(String typeService) {
        this.typeService = typeService;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getTemplateContentName() {
        return templateContentName;
    }

    public void setTemplateContentName(String templateContentName) {
        this.templateContentName = templateContentName;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }
    
} 
