package com.nsy.api.wms.domain.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "SkuStockInfo", description = "sku库存信息")
public class SkuStockInfo {
    @ApiModelProperty(value = "sku", name = "sku")
    private String sku;
    @ApiModelProperty(value = "库存", name = "stock")
    private Integer stock;
    @ApiModelProperty(value = "已分配库存", name = "preMatchQty")
    private Integer preMatchQty;
    @ApiModelProperty(value = "stockId", name = "stockId")
    private Integer stockId;

    public Integer getStockId() {
        return stockId;
    }

    public void setStockId(Integer stockId) {
        this.stockId = stockId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Integer getPreMatchQty() {
        return preMatchQty;
    }

    public void setPreMatchQty(Integer preMatchQty) {
        this.preMatchQty = preMatchQty;
    }
}
