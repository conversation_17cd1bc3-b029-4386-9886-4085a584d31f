package com.nsy.api.wms.domain.logistics.documents;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;


public class UpsOtherChannelInfo {

    private Integer shipmentId;

    @ApiModelProperty(value = "商品数量", name = "qty")
    private Integer qty;

    @ApiModelProperty(value = "国家", name = "country")
    private String country;

    @ApiModelProperty(value = "商品描述(分类英文名称(MADE IN CHINA)英文成分)", name = "description")
    private String description;

    @ApiModelProperty(value = "海关编码", name = "hsCode")
    private String hsCode;

    @ApiModelProperty(value = "单价", name = "unitPrice")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "报关中文品名", name = "customsDeclareCn")
    private String customsDeclareCn;

    @ApiModelProperty(value = "报关英文品名", name = "customsDeclareEn")
    private String customsDeclareEn;

    @ApiModelProperty(value = "英文成分名称", name = "fabricTypeEn")
    private String fabricTypeEn;

    @ApiModelProperty(value = "中文成分名称", name = "fabricTypeCn")
    private String fabricTypeCn;

    @ApiModelProperty(value = "商品单件重量(g)", name = "weight")
    private BigDecimal weight;


    @ApiModelProperty(value = "商品单件实际重量(g)", name = "actualWeight")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "分类英文名称", name = "categoryName")
    private String categoryEn;

    @ApiModelProperty(value = "分类Id", name = "categoryId")
    private String categoryId;

    @ApiModelProperty(value = "报关单位", name = "declareUnit")
    private String declareUnit;

    @ApiModelProperty(value = "单位", name = "unit")
    private String unit;

    @ApiModelProperty(value = "商品总价格", name = "goodsPrice")
    private Double goodsPrice;


    private String stockoutOrderNo;

    private String orderNo;

    private String name;
    /*-------------------------详细发票多出信息----------------*/
    @ApiModelProperty(value = "sku", name = "sku")
    private String sku;
    @ApiModelProperty(value = "颜色-尺码", name = "colorSize")
    private String colorSize;

    // 随机取一个spu
    @ApiModelProperty(value = "spu", name = "spu")
    private String spu;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getActualWeight() {
        return actualWeight;
    }

    public Integer getShipmentId() {
        return shipmentId;
    }

    public void setShipmentId(Integer shipmentId) {
        this.shipmentId = shipmentId;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getCustomsDeclareCn() {
        return customsDeclareCn;
    }

    public void setCustomsDeclareCn(String customsDeclareCn) {
        this.customsDeclareCn = customsDeclareCn;
    }

    public String getCustomsDeclareEn() {
        return customsDeclareEn;
    }

    public void setCustomsDeclareEn(String customsDeclareEn) {
        this.customsDeclareEn = customsDeclareEn;
    }

    public String getFabricTypeEn() {
        return fabricTypeEn;
    }

    public void setFabricTypeEn(String fabricTypeEn) {
        this.fabricTypeEn = fabricTypeEn;
    }

    public String getFabricTypeCn() {
        return fabricTypeCn;
    }

    public void setFabricTypeCn(String fabricTypeCn) {
        this.fabricTypeCn = fabricTypeCn;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public void setActualWeight(BigDecimal actualWeight) {
        this.actualWeight = actualWeight;
    }

    public String getCategoryEn() {
        return categoryEn;
    }

    public void setCategoryEn(String categoryEn) {
        this.categoryEn = categoryEn;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getDeclareUnit() {
        return declareUnit;
    }

    public void setDeclareUnit(String declareUnit) {
        this.declareUnit = declareUnit;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(Double goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getColorSize() {
        return colorSize;
    }

    public void setColorSize(String colorSize) {
        this.colorSize = colorSize;
    }

    public String getSpu() {
        return spu;
    }

    public void setSpu(String spu) {
        this.spu = spu;
    }
}
