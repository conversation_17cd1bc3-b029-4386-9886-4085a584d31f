package com.nsy.api.wms.domain.qc;

/**
 * User: Emily
 * Date: 2018/8/3
 */
public class ProductAmazonMarketing {
    private Integer id;

    /**
     * 商品id
     */
    private Integer productId;
    private Integer erpProductId;
    private String productName;
    private String productSku;
    private String size;
    /**
     * 类目
     */
    private String departmentName;

    /**
     * 成分
     */
    private String fabricType;

    /**
     * 款式
     */
    private String styleName;

    /**
     * 版型
     */
    private String fitType;

    /**
     * 闭合方式
     */
    private String closureType;

    /**
     * 产品关键词
     */
    private String itemType;

    /**
     * 弹性
     */
    private String elasticity;

    /**
     * 产品描述
     */
    private String description;

    /**
     * 模特id
     */
    private Integer modelId;

    /**
     * 描述1
     */
    private String bulletPoint1;

    /**
     * 描述2
     */
    private String bulletPoint2;

    /**
     * 描述3
     */
    private String bulletPoint3;

    /**
     * 描述4
     */
    private String bulletPoint4;
    /**
     * 描述5
     */
    private String bulletPoint5;

    /**
     * 描述6
     */
    private String bulletPoint6;

    private String englishName;

    /**
     * 品牌名
     */
    private String brand;

    /**
     * 搜索关键字
     */
    private String searchTerm;

    private Integer amazonCategoryId;

    /**
     * 顶级父类Id
     */
    private Integer topAmazonCategoryId;

    private String amazonCategoryName;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getFabricType() {
        return fabricType;
    }

    public void setFabricType(String fabricType) {
        this.fabricType = fabricType;
    }

    public String getStyleName() {
        return styleName;
    }

    public void setStyleName(String styleName) {
        this.styleName = styleName;
    }

    public String getFitType() {
        return fitType;
    }

    public void setFitType(String fitType) {
        this.fitType = fitType;
    }

    public String getClosureType() {
        return closureType;
    }

    public void setClosureType(String closureType) {
        this.closureType = closureType;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getProductSku() {
        return productSku;
    }

    public void setProductSku(String productSku) {
        this.productSku = productSku;
    }

    public String getElasticity() {
        return elasticity;
    }

    public void setElasticity(String elasticity) {
        this.elasticity = elasticity;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getModelId() {
        return modelId;
    }

    public void setModelId(Integer modelId) {
        this.modelId = modelId;
    }

    public String getBulletPoint1() {
        return bulletPoint1;
    }

    public void setBulletPoint1(String bulletPoint1) {
        this.bulletPoint1 = bulletPoint1;
    }

    public String getBulletPoint2() {
        return bulletPoint2;
    }

    public void setBulletPoint2(String bulletPoint2) {
        this.bulletPoint2 = bulletPoint2;
    }

    public String getBulletPoint3() {
        return bulletPoint3;
    }

    public void setBulletPoint3(String bulletPoint3) {
        this.bulletPoint3 = bulletPoint3;
    }

    public String getBulletPoint4() {
        return bulletPoint4;
    }

    public void setBulletPoint4(String bulletPoint4) {
        this.bulletPoint4 = bulletPoint4;
    }

    public String getBulletPoint5() {
        return bulletPoint5;
    }

    public void setBulletPoint5(String bulletPoint5) {
        this.bulletPoint5 = bulletPoint5;
    }

    public String getBulletPoint6() {
        return bulletPoint6;
    }

    public void setBulletPoint6(String bulletPoint6) {
        this.bulletPoint6 = bulletPoint6;
    }

    public String getEnglishName() {
        return englishName;
    }

    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getSearchTerm() {
        return searchTerm;
    }

    public void setSearchTerm(String searchTerm) {
        this.searchTerm = searchTerm;
    }

    public Integer getAmazonCategoryId() {
        return amazonCategoryId;
    }

    public void setAmazonCategoryId(Integer amazonCategoryId) {
        this.amazonCategoryId = amazonCategoryId;
    }

    public Integer getTopAmazonCategoryId() {
        return topAmazonCategoryId;
    }

    public void setTopAmazonCategoryId(Integer topAmazonCategoryId) {
        this.topAmazonCategoryId = topAmazonCategoryId;
    }

    public String getAmazonCategoryName() {
        return amazonCategoryName;
    }

    public void setAmazonCategoryName(String amazonCategoryName) {
        this.amazonCategoryName = amazonCategoryName;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Integer getErpProductId() {
        return erpProductId;
    }

    public void setErpProductId(Integer erpProductId) {
        this.erpProductId = erpProductId;
    }
}
