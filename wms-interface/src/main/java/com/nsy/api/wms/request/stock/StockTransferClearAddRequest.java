package com.nsy.api.wms.request.stock;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@ApiModel(value = "StockTransferClearAddRequest", description = "新增清仓计划request")
public class StockTransferClearAddRequest extends PageRequest implements Serializable {


    @ApiModelProperty(value = "仓库id", name = "spaceId")
    private Integer spaceId;

    @ApiModelProperty(value = "规格id", name = "specIdList")
    private List<Integer> specIdList;

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public List<Integer> getSpecIdList() {
        return specIdList;
    }

    public void setSpecIdList(List<Integer> specIdList) {
        this.specIdList = specIdList;
    }
}
