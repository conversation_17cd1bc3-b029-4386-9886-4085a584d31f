package com.nsy.api.wms.response.stockin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import java.util.List;

@ApiModel("PDA更新退货完成请求实体类")
public class StockinReturnProductPDATaskFinishRequest {

    @ApiModelProperty(value = "退货任务主键", required = true)
    @Size(min = 1, message = "退货任务主键不能为空")
    private List<Integer> taskIdList;

    @ApiModelProperty("退货方式")
    private String returnMethod;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("任务没有的sku,库位上有")
    private List<StockinReturnProductPDATaskFinishItem> itemList;
    public List<Integer> getTaskIdList() {
        return taskIdList;
    }

    public void setTaskIdList(List<Integer> taskIdList) {
        this.taskIdList = taskIdList;
    }

    public String getReturnMethod() {
        return returnMethod;
    }

    public void setReturnMethod(String returnMethod) {
        this.returnMethod = returnMethod;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public List<StockinReturnProductPDATaskFinishItem> getItemList() {
        return itemList;
    }

    public void setItemList(List<StockinReturnProductPDATaskFinishItem> itemList) {
        this.itemList = itemList;
    }
}
