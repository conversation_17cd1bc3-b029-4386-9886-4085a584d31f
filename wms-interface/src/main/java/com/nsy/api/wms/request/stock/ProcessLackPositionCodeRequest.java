package com.nsy.api.wms.request.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "ProcessLackPositionCodeRequest", description = "加工查询缺货拣货库位request")
public class ProcessLackPositionCodeRequest {

    @ApiModelProperty(value = "taskId", name = "taskId")
    private Integer taskId;


    @ApiModelProperty(value = "操作人", name = "operator")
    private String operator;

    @ApiModelProperty(value = "胚款", name = "baseSku")
    private String baseSku;

    @ApiModelProperty(value = "地区", name = "location")
    private String location;

    public String getBaseSku() {
        return baseSku;
    }

    public void setBaseSku(String baseSku) {
        this.baseSku = baseSku;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
