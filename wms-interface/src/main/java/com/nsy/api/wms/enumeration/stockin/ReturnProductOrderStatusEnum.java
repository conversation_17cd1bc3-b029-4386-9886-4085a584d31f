package com.nsy.api.wms.enumeration.stockin;

import org.apache.commons.lang3.StringUtils;

/**
 * 退货单状态
 */
public enum ReturnProductOrderStatusEnum {
    WAIT_REGISTER("WAIT_REGISTER", "待登记"),
    REGISTERING("REGISTERING", "登记中"),
    WAIT_CONFIRM("WAIT_CONFIRM", "待业务确认"),
    CONFIRMED("CONFIRMED", "待入库登记"),
    ALREADY_REGISTER("ALREADY_REGISTER", "已登记");

    private String code;
    private String value;

    ReturnProductOrderStatusEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValueByCode(String code) {
        String result = "";
        if (StringUtils.isBlank(code)) {
            return result;
        }
        for (ReturnProductOrderStatusEnum statusEnum: ReturnProductOrderStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.value;
            }
        }
        return result;
    }
    public String getCode() {
        return code;
    }
    public String getValue() {
        return value;
    }
}
