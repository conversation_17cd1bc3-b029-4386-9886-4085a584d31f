package com.nsy.api.wms.response.stockout;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel(value = "StockoutCustomsDeclareConstractPageResponse", description = "合同分页返回Response")
public class StockoutCustomsDeclareConstractPageResponse {

    @ApiModelProperty(value = "ID", name = "declareContractId")
    private Integer declareContractId;

    @ApiModelProperty(value = "合同编号", name = "declareContractNo")
    private String declareContractNo;

    @ApiModelProperty(value = "签署链接", name = "signUrl")
    private String signUrl;

    @ApiModelProperty(value = "公司签署链接", name = "companySignUrl")
    private String companySignUrl;

    @ApiModelProperty(value = "预览连接", name = "previewUrl")
    private String previewUrl;

    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    @ApiModelProperty(value = "状态中文", name = "statusCn")
    private String statusCn;

    @ApiModelProperty(value = "需方ID", name = "companyId")
    private Integer companyId;

    @ApiModelProperty(value = "需方名称", name = "companyName")
    private String companyName;

    @ApiModelProperty(value = "供方id", name = "supplierId")
    private Integer supplierId;

    @ApiModelProperty(value = "供方名称", name = "supplierName")
    private String supplierName;

    @ApiModelProperty(value = "数量", name = "qty")
    private Integer qty;

    @ApiModelProperty(value = "进项金额", name = "inputPrice")
    private BigDecimal inputPrice;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "审核时间", name = "auditDate")
    private Date auditDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "工厂签署时间", name = "supplierSignDate")
    private Date supplierSignDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "公司签署时间", name = "companySignDate")
    private Date companySignDate;

    @ApiModelProperty(value = "附件地址", name = "attachmentUrl")
    private String attachmentUrl;

    @ApiModelProperty(value = "是否工厂签署合同", name = "hasSupplierSignContract")
    private Boolean hasSupplierSignContract;

    @ApiModelProperty(value = "是否公司签署合同", name = "hasCompanySignContract")
    private Boolean hasCompanySignContract;

    //是否确认合同
    @ApiModelProperty(value = "是否确认合同", name = "hasConfirmContract")
    private Boolean hasConfirmContract;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间", name = "createDate")
    private Date createDate;

    //合同地址
    private String contractUrl;

    //合同流程id
    private String signFlowId;

    //合同上传e签宝状态
    private Boolean fileUploadStatus;

    //Excel合同文件预览链接
    @ApiModelProperty(value = "Excel合同文件预览链接", name = "excelPreviewUrl")
    private String excelPreviewUrl;

    public Integer getDeclareContractId() {
        return declareContractId;
    }

    public void setDeclareContractId(Integer declareContractId) {
        this.declareContractId = declareContractId;
    }

    public String getDeclareContractNo() {
        return declareContractNo;
    }

    public void setDeclareContractNo(String declareContractNo) {
        this.declareContractNo = declareContractNo;
    }

    public String getSignUrl() {
        return signUrl;
    }

    public void setSignUrl(String signUrl) {
        this.signUrl = signUrl;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public BigDecimal getInputPrice() {
        return inputPrice;
    }

    public void setInputPrice(BigDecimal inputPrice) {
        this.inputPrice = inputPrice;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public Date getSupplierSignDate() {
        return supplierSignDate;
    }

    public void setSupplierSignDate(Date supplierSignDate) {
        this.supplierSignDate = supplierSignDate;
    }

    public Date getCompanySignDate() {
        return companySignDate;
    }

    public void setCompanySignDate(Date companySignDate) {
        this.companySignDate = companySignDate;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public Boolean getHasSupplierSignContract() {
        return hasSupplierSignContract;
    }

    public void setHasSupplierSignContract(Boolean hasSupplierSignContract) {
        this.hasSupplierSignContract = hasSupplierSignContract;
    }

    public Boolean getHasCompanySignContract() {
        return hasCompanySignContract;
    }

    public void setHasCompanySignContract(Boolean hasCompanySignContract) {
        this.hasCompanySignContract = hasCompanySignContract;
    }

    public String getStatusCn() {
        return statusCn;
    }

    public void setStatusCn(String statusCn) {
        this.statusCn = statusCn;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCompanySignUrl() {
        return companySignUrl;
    }

    public void setCompanySignUrl(String companySignUrl) {
        this.companySignUrl = companySignUrl;
    }

    public Boolean getHasConfirmContract() {
        return hasConfirmContract;
    }

    public void setHasConfirmContract(Boolean hasConfirmContract) {
        this.hasConfirmContract = hasConfirmContract;
    }

    public String getContractUrl() {
        return contractUrl;
    }

    public void setContractUrl(String contractUrl) {
        this.contractUrl = contractUrl;
    }

    public String getSignFlowId() {
        return signFlowId;
    }

    public void setSignFlowId(String signFlowId) {
        this.signFlowId = signFlowId;
    }

    public Boolean getFileUploadStatus() {
        return fileUploadStatus;
    }

    public void setFileUploadStatus(Boolean fileUploadStatus) {
        this.fileUploadStatus = fileUploadStatus;
    }

    public String getExcelPreviewUrl() {
        return excelPreviewUrl;
    }

    public void setExcelPreviewUrl(String excelPreviewUrl) {
        this.excelPreviewUrl = excelPreviewUrl;
    }
}

