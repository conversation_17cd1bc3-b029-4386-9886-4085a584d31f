package com.nsy.api.wms.enumeration.stock;

/**
 * 借用库存变动日志类型
 */
public enum StockLendStockChangeLogTypeEnum {
    STOCKOUT_ORDER_SHIPMENT("出库单出库"),
    CANT_RETURN("无法归还"),
    OVERDUE_CANCELLED("超期取消"),
    LEND_CODE_RIGIST("单号登记"),
    BUSINESS_RIGIST("部门登记"),
    AUTO_DISTRIBUTE("自动分配"),
    DISTRIBUTE("手动分配");

    StockLendStockChangeLogTypeEnum(String name) {
        this.name = name;
    }

    private String name;

    public String getName() {
        return name;
    }

}
