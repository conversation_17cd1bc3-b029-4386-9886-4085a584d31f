package com.nsy.api.wms.feign.common;

import com.nsy.api.wms.request.common.IdToNameForScmSupplierTaxRequest;
import com.nsy.api.wms.response.common.IdToNameForScmSupplierTaxResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "api-wms", contextId = "wms-IdToNameFeignClient")
@Api(tags = "wms - id转name 控制类")
public interface IdToNameFeignClient {
    @ApiOperation(value = "供应商税务信息", notes = "供应商税务信息")
    @PostMapping("/id-to-name/for-scm-supplier-tax")
    IdToNameForScmSupplierTaxResponse getForScmSupplierTax(@RequestBody IdToNameForScmSupplierTaxRequest request);
}
