package com.nsy.api.wms.request.stock;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

@ApiModel(value = "StockTakeStockPlanSpaceAreaRequest", description = "盘点计划库区列表")
public class StockTakeStockPlanSpaceAreaRequest {
    /**
     * 库区id
     */
    @ApiModelProperty("库区id")
    private Integer spaceAreaId;
    /**
     * 库区名称
     */
    @ApiModelProperty("库区名称")
    @Length(max = 50, message = "编码长度不能超过50")
    private String spaceAreaName;

    public Integer getSpaceAreaId() {
        return spaceAreaId;
    }

    public void setSpaceAreaId(Integer spaceAreaId) {
        this.spaceAreaId = spaceAreaId;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }
}
