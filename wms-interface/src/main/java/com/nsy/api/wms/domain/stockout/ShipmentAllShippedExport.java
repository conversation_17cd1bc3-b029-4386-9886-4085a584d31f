package com.nsy.api.wms.domain.stockout;


import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

// 导出发货信息
// @HeadStyle(horizontalAlignment = HorizontalAlignment.CENTER)
// @ContentStyle(horizontalAlignment = HorizontalAlignment.CENTER, wrapped = true)
public class ShipmentAllShippedExport {
//    @NsyExcelProperty(value = "订单号")
    private String orderNo;
//    @NsyExcelProperty(value = "sku编号")
    private String sku;
//    @NsyExcelProperty(value = "颜色/尺码")
    private String colorAndSize;

    private String shipmentIdConcat;

    private String color;

    private String size;
//    @NsyExcelProperty(value = "时颖条形码")
    private String barcode;
//    @NsyExcelProperty(value = "客户Sku")
    private String storeSku;
//    @NsyExcelProperty(value = "客户条形码")
    private String storeBarcode;
//    @NsyExcelProperty(value = "数量")
    private String qty;
//    @NsyExcelProperty(value = "单价")
    private String invoicePrice;
//    @NsyExcelProperty(value = "收货人名字")
    private String receiveName;
//    @NsyExcelProperty(value = "箱号")
    private String boxIndex;
//    @NsyExcelProperty(value = "发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryDate;
//    @NsyExcelProperty(value = "快递公司")
    private String logisticsCompany;
//    @NsyExcelProperty(value = "快递单号")
    private String logisticsNo;
//    @NsyExcelProperty(value = "重量")
    private String weight;
//    @NsyExcelProperty(value = "体积重")
    private String volumeWeight;
//    @NsyExcelProperty(value = "规格")
    private String boxSize;
//    @NsyExcelProperty(value = "卖家备注")
    private String sellerRemark;
//    @NsyExcelProperty(value = "店铺名称")
    private String storeName;

    private String receiverInfo;

    private String transferLogisticsNo;

    //    @NsyExcelProperty(value = "海运/空运")
    private String shippingType;

    private String forwarderChannel;

    public String getShipmentIdConcat() {
        return shipmentIdConcat;
    }

    public void setShipmentIdConcat(String shipmentIdConcat) {
        this.shipmentIdConcat = shipmentIdConcat;
    }

    public String getForwarderChannel() {
        return forwarderChannel;
    }

    public void setForwarderChannel(String forwarderChannel) {
        this.forwarderChannel = forwarderChannel;
    }

    public String getTransferLogisticsNo() {
        return transferLogisticsNo;
    }

    public void setTransferLogisticsNo(String transferLogisticsNo) {
        this.transferLogisticsNo = transferLogisticsNo;
    }

    public String getShippingType() {
        return shippingType;
    }

    public void setShippingType(String shippingType) {
        this.shippingType = shippingType;
    }

    public String getReceiverInfo() {
        return receiverInfo;
    }

    public void setReceiverInfo(String receiverInfo) {
        this.receiverInfo = receiverInfo;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getColorAndSize() {
        return colorAndSize;
    }

    public void setColorAndSize(String colorAndSize) {
        this.colorAndSize = colorAndSize;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getStoreSku() {
        return storeSku;
    }

    public void setStoreSku(String storeSku) {
        this.storeSku = storeSku;
    }

    public String getStoreBarcode() {
        return storeBarcode;
    }

    public void setStoreBarcode(String storeBarcode) {
        this.storeBarcode = storeBarcode;
    }

    public String getQty() {
        return qty;
    }

    public void setQty(String qty) {
        this.qty = qty;
    }

    public String getInvoicePrice() {
        return invoicePrice;
    }

    public void setInvoicePrice(String invoicePrice) {
        this.invoicePrice = invoicePrice;
    }

    public String getReceiveName() {
        return receiveName;
    }

    public void setReceiveName(String receiveName) {
        this.receiveName = receiveName;
    }

    public String getBoxIndex() {
        return boxIndex;
    }

    public void setBoxIndex(String boxIndex) {
        this.boxIndex = boxIndex;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getVolumeWeight() {
        return volumeWeight;
    }

    public void setVolumeWeight(String volumeWeight) {
        this.volumeWeight = volumeWeight;
    }

    public String getBoxSize() {
        return boxSize;
    }

    public void setBoxSize(String boxSize) {
        this.boxSize = boxSize;
    }

    public String getSellerRemark() {
        return sellerRemark;
    }

    public void setSellerRemark(String sellerRemark) {
        this.sellerRemark = sellerRemark;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
}
