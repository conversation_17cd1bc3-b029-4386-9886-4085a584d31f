package com.nsy.wms.mq.consumer;

import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.stockin.StockinOrderTaskService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.mq.consumer.base.CommonConsumer;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:11
 */
@Component
public class StockInOrderTaskGenerateConsumer extends CommonConsumer<LocationWrapperMessage> {

    @Autowired
    StockinOrderTaskService stockinOrderTaskService;

    @KafkaListener(topics = KafkaConstant.STOCKIN_ORDER_TASK_CREATE_TOPIC)
    public void newConsumer(ConsumerRecord<?, ?> record) {
        QMessage<LocationWrapperMessage> receiveMessage = JsonMapper.fromJson(record.value().toString(), QMessage.class);
        processMessage(receiveMessage);
    }

    @Override
    protected void doProcessMessage(QMessage<LocationWrapperMessage> message) {
        LocationWrapperMessage messageContent = JsonMapper.fromJson(JsonMapper.toJson(message.getMessageContent()), LocationWrapperMessage.class);
        TenantContext.setTenant(messageContent.getLocation());
        StockPlatformScheduleEntity scheduleEntity = JsonMapper.fromJson(JsonMapper.toJson(messageContent.getContent()), StockPlatformScheduleEntity.class);
        stockinOrderTaskService.generateStockinOrderTask(scheduleEntity, messageContent.getOperator());
    }

}