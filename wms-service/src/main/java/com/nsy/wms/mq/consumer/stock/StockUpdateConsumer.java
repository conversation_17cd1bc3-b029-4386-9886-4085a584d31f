package com.nsy.wms.mq.consumer.stock;

import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.wms.business.domain.bo.mq.StockUpdateMessage;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.mq.consumer.base.CommonConsumer;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: 库存表更新异步消费 （单线程顺序消费）
 * 可能消费较慢，适用于库存变动并发大、耗时长、时效要求不高的情况
 * 若对库存变动时效要求高的情况，请不要使用该方式
 * @author: caishaohui
 * @time: 2024/1/4 10:07
 */
@Component
public class StockUpdateConsumer extends CommonConsumer<StockUpdateMessage> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockUpdateConsumer.class);

    @Autowired
    StockService stockService;


    @KafkaListener(topics = KafkaConstant.STOCK_UPDATE_TOPIC)
    public void insertScanDetailConsumer(ConsumerRecord<?, ?> record) {
        QMessage<StockUpdateMessage> receiveMessage = JsonMapper.fromJson(record.value().toString(), QMessage.class);
        LOGGER.info("StockUpdateConsumer receive message: {} ", record.value());
        processMessage(receiveMessage);
    }

    @Override
    protected void doProcessMessage(QMessage<StockUpdateMessage> receiveMessage) {
        StockUpdateMessage messageContent = JsonMapper.fromJson(JsonMapper.toJson(receiveMessage.getMessageContent()), StockUpdateMessage.class);
        TenantContext.setTenant(messageContent.getLocation());
        LoginInfoService.setName(messageContent.getOperator());
        List<StockUpdateRequest> updateRequests = messageContent.getContent();
        stockService.updateStockBatch(updateRequests);
        LOGGER.info("StockUpdateConsumer done: {} ", receiveMessage.getMessageId());
    }

}
