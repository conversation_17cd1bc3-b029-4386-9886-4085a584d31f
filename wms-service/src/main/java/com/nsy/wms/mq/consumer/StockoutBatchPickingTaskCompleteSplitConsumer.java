package com.nsy.wms.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.stockout.StockoutBatchSplitTaskService;
import com.nsy.wms.business.service.stockout.StockoutPickingTaskItemService;
import com.nsy.wms.business.service.stockout.StockoutPickingTaskService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.mq.consumer.base.CommonConsumer;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 波次下拣货任务完成分拣任务
 */
@Component
public class StockoutBatchPickingTaskCompleteSplitConsumer extends CommonConsumer<LocationWrapperMessage> {


    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutBatchPickingTaskCompleteSplitConsumer.class);

    @Autowired
    StockoutBatchSplitTaskService stockoutBatchSplitTaskService;
    @Autowired
    StockoutPickingTaskService stockoutPickingTaskService;
    @Autowired
    StockoutPickingTaskItemService stockoutPickingTaskItemService;

    @KafkaListener(topics = KafkaConstant.STOCKOUT_BATCH_PICKING_TASK_COMPLETE_SPLIT_TOPIC)
    public void newConsumer(ConsumerRecord<?, ?> record) {
        QMessage<LocationWrapperMessage> receiveMessage = JsonMapper.fromJson(record.value().toString(), QMessage.class);
        LOGGER.info("StockoutBatchPickingTaskCompleteSplitConsumer receive message: {}", record.value());
        processMessage(receiveMessage);
    }

    @Override
    protected void doProcessMessage(QMessage<LocationWrapperMessage> receiveMessage) {
        LocationWrapperMessage messageContent = JsonMapper.fromJson(JsonMapper.toJson(receiveMessage.getMessageContent()), LocationWrapperMessage.class);
        TenantContext.setTenant(messageContent.getLocation());
        Integer batchId = (Integer) messageContent.getContent();

        List<StockoutPickingTaskEntity> list = stockoutPickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .select(StockoutPickingTaskEntity::getTaskId)
                .eq(StockoutPickingTaskEntity::getBatchId, batchId)
                .notIn(StockoutPickingTaskEntity::getTaskType, CollUtil.newArrayList(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name(),
                        StockoutPickingTaskTypeEnum.REPLENISHMENT_PICKING.name())));
        List<StockoutPickingTaskItemEntity> pickingTaskItemEntityList = stockoutPickingTaskItemService.findAllByTaskIdIn(list.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList()));
        stockoutBatchSplitTaskService.pickingComplete(batchId, pickingTaskItemEntityList);
    }
}
