package com.nsy.wms.mq.consumer;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.enumeration.overseas.OverseasWarehouseOrderStatusEnum;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOverseasWarehouseOrderService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.mq.consumer.base.CommonConsumer;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOverseasWarehouseOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * 海外仓订单创建消费者
 *
 * <AUTHOR>
 * @since 1.0
 */
@Component
public class OverseasWarehouseOrderCreateConsumer extends CommonConsumer<LocationWrapperMessage> {

    private static final Logger LOGGER = LoggerFactory.getLogger(OverseasWarehouseOrderCreateConsumer.class);

    @Autowired
    StockoutOrderService stockoutOrderService;

    @Autowired
    StockoutOverseasWarehouseOrderService overseasWarehouseOrderService;

    @Autowired
    BdSpaceService bdSpaceService;

    @KafkaListener(topics = KafkaConstant.OVERSEAS_WAREHOUSE_ORDER_CREATE_TOPIC)
    public void createOverseasWarehouseOrderConsumer(ConsumerRecord<?, ?> record) {
        QMessage<LocationWrapperMessage> receiveMessage = JsonMapper.fromJson(record.value().toString(), QMessage.class);
        LOGGER.info("OverseasWarehouseOrderCreateConsumer receive message: {}", record.value());
        processMessage(receiveMessage);
    }

    @Override
    protected void doProcessMessage(QMessage<LocationWrapperMessage> receiveMessage) {
        try {
            LocationWrapperMessage messageContent = JsonMapper.fromJson(JsonMapper.toJson(receiveMessage.getMessageContent()), LocationWrapperMessage.class);
            TenantContext.setTenant(messageContent.getLocation());
            LoginInfoService.setName("OverseasWarehouseOrderCreateConsumer");
            // 获取出库单ID
            Integer stockoutOrderId = (Integer) messageContent.getContent();
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(stockoutOrderId);
            if (stockoutOrderEntity == null) {
                throw new BusinessServiceException(String.format("当前出库单： %s，不存在", stockoutOrderId));
            }
            LOGGER.info("开始创建海外仓订单，出库单号: {}, 拣货单ID: {}", stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderEntity.getErpPickId());
            // 创建海外仓订单
            createOverseasWarehouseOrder(stockoutOrderEntity);
        } catch (RuntimeException e) {
            LOGGER.error("OverseasWarehouseOrderCreateConsumer exception message: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建海外仓订单
     *
     * @param stockoutOrderEntity 出库单实体
     */
    private void createOverseasWarehouseOrder(StockoutOrderEntity stockoutOrderEntity) {
        try {
            // 检查是否已经存在海外仓订单
            StockoutOverseasWarehouseOrderEntity existingOrder = overseasWarehouseOrderService.findByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            if (existingOrder != null) {
                LOGGER.info("海外仓订单已存在，出库单号: {}", stockoutOrderEntity.getStockoutOrderNo());
                return;
            }

            // 创建海外仓订单实体
            StockoutOverseasWarehouseOrderEntity overseasWarehouseOrderEntity = new StockoutOverseasWarehouseOrderEntity();
            // 设置基本信息
            overseasWarehouseOrderEntity.setLocation(stockoutOrderEntity.getLocation());
            overseasWarehouseOrderEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            overseasWarehouseOrderEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            overseasWarehouseOrderEntity.setStoreId(stockoutOrderEntity.getStoreId());
            overseasWarehouseOrderEntity.setStoreName(stockoutOrderEntity.getStoreName());
            overseasWarehouseOrderEntity.setPlatformName(stockoutOrderEntity.getPlatformName());
            overseasWarehouseOrderEntity.setLogisticsCompany(stockoutOrderEntity.getLogisticsCompany());
            
            // 设置时间信息
            overseasWarehouseOrderEntity.setOrderTime(stockoutOrderEntity.getReadyDate());
            overseasWarehouseOrderEntity.setStockoutCreateTime(stockoutOrderEntity.getCreateDate());
            
            // 设置海外仓信息（从出库单的仓库信息获取）
            overseasWarehouseOrderEntity.setOverseasSpaceId(stockoutOrderEntity.getSpaceId());
            // 查询仓库名称
            if (stockoutOrderEntity.getSpaceId() != null) {
                BdSpaceEntity spaceEntity = bdSpaceService.getById(stockoutOrderEntity.getSpaceId());
                if (spaceEntity != null) {
                    overseasWarehouseOrderEntity.setOverseasSpaceName(spaceEntity.getSpaceName());
                }
            }
            
            // 设置初始状态为推送异常
            overseasWarehouseOrderEntity.setStatus(OverseasWarehouseOrderStatusEnum.PUSH_ERROR.name());
            
            // 保存海外仓订单
            overseasWarehouseOrderService.create(overseasWarehouseOrderEntity);
            
            LOGGER.info("海外仓订单创建成功，出库单号: {}, 海外仓订单ID: {}", 
                stockoutOrderEntity.getStockoutOrderNo(), overseasWarehouseOrderEntity.getId());
        } catch (Exception e) {
            LOGGER.error("创建海外仓订单失败，出库单号: {}, 错误信息: {}", 
                stockoutOrderEntity.getStockoutOrderNo(), e.getMessage(), e);
            throw e;
        }
    }
}
