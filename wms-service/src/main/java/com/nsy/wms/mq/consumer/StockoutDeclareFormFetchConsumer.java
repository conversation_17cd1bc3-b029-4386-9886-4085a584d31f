package com.nsy.wms.mq.consumer;

import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.external.request.DeclareFormFetchRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormFetchService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.mq.consumer.base.AsyncProcessFlowConsumer;
import com.nsy.wms.mq.producer.QMessageInfoEntity;
import com.nsy.wms.mq.producer.QMessageInfoService;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
public class StockoutDeclareFormFetchConsumer extends AsyncProcessFlowConsumer<String, Void> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutDeclareFormFetchConsumer.class);

    @Autowired
    StockoutCustomsDeclareFormFetchService formFetchService;
    @Autowired
    QMessageInfoService qMessageInfoService;
    @Autowired
    ObjectMapper objectMapper;

    @KafkaListener(topics = KafkaConstant.DECLARE_FORM_FETCH_TOPIC)
    public void listenerConsumer(ConsumerRecord<?, String> record) throws JsonProcessingException {
        QMessage<String> receiveMessage = objectMapper.readValue(record.value(), new StockoutDeclareFormFetchConsumer.ReceiveMessageTypeReference());
        LOGGER.info("StockoutDeclareFormFetchConsumer receive message: {}", record.value());
        SpringUtil.getBean(StockoutDeclareFormFetchConsumer.class).processMessage(receiveMessage);
    }

    @Override
    protected Void doProcessMessage(QMessage<String> message) {
        QMessageInfoEntity qMessageInfo = qMessageInfoService.getById(message.getMessageContent());
        LocationWrapperMessage<DeclareFormFetchRequest> messageContent;
        try {
            messageContent = objectMapper.readValue(qMessageInfo.getMessageContent(), new StockoutDeclareFormFetchConsumer.LocationWrapperTypeReference());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
        LoginInfoService.setName(messageContent.getOperator());
        TenantContext.setTenant(messageContent.getLocation());
        formFetchService.fetchData(messageContent.getContent());
        LoginInfoService.removeName();
        return null;
    }

    private static final class ReceiveMessageTypeReference extends TypeReference<QMessage<String>> {
    }

    private static final class LocationWrapperTypeReference extends TypeReference<LocationWrapperMessage<DeclareFormFetchRequest>> {
    }

}
