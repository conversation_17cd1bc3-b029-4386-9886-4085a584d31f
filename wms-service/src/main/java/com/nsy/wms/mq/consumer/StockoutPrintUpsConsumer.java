package com.nsy.wms.mq.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.logistics.documents.request.UpsRequestInfo;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.logistics.documents.LogisticsDocumentsService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.mq.consumer.base.AsyncProcessFlowConsumer;
import com.nsy.wms.mq.producer.QMessageInfoEntity;
import com.nsy.wms.mq.producer.QMessageInfoService;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@Component
public class StockoutPrintUpsConsumer extends AsyncProcessFlowConsumer<Integer, BaseGetLogisticsNoResponse> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutPrintUpsConsumer.class);

    @Autowired
    LogisticsDocumentsService logisticsDocumentsService;
    @Autowired
    QMessageInfoService qMessageInfoService;
    @Autowired
    private ObjectMapper objectMapper;

    @KafkaListener(topics = KafkaConstant.STOCKOUT_PRINT_UPS_TOPIC)
    public void listenerConsumer(ConsumerRecord<?, String> record) throws JsonProcessingException {
        QMessage<Integer> receiveMessage = objectMapper.readValue(record.value(), new StockoutPrintUpsConsumer.ReceiveMessageTypeReference());
        LOGGER.info("StockoutPrintUpsConsumer receive message: {}", record.value());
        processMessage(receiveMessage);
    }

    @Override
    protected BaseGetLogisticsNoResponse doProcessMessage(QMessage<Integer> message) {
        QMessageInfoEntity qMessageInfo = qMessageInfoService.getById(message.getMessageContent());
        LocationWrapperMessage<UpsRequestInfo> messageContent;
        try {
            messageContent = objectMapper.readValue(qMessageInfo.getMessageContent(), new StockoutPrintUpsConsumer.LocationWrapperTypeReference());
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }

        TenantContext.setTenant(messageContent.getLocation());
        LoginInfoService.setName(messageContent.getOperator());
        BaseGetLogisticsNoResponse response = logisticsDocumentsService.printUps(messageContent.getContent());
        LoginInfoService.removeName();
        return response;
    }


    private static final class ReceiveMessageTypeReference extends TypeReference<QMessage<Integer>> {
    }

    private static final class LocationWrapperTypeReference extends TypeReference<LocationWrapperMessage<UpsRequestInfo>> {
    }

}
