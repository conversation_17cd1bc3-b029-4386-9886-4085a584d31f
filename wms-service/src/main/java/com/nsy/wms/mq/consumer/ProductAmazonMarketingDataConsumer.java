package com.nsy.wms.mq.consumer;

import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.product.NsyProductProductAmazonMarketing;
import com.nsy.api.wms.domain.product.NsyProductProduct;
import com.nsy.wms.business.domain.dto.product.NsyProductProductAmazonMarketingMessage;
import com.nsy.wms.mq.TableListenerMessage;
import com.nsy.wms.mq.consumer.base.TableListenerConsumer;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoSyncService;
import com.nsy.wms.utils.JsonMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class ProductAmazonMarketingDataConsumer extends TableListenerConsumer<NsyProductProductAmazonMarketing> {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductAmazonMarketingDataConsumer.class);

    @Autowired
    ProductInfoSyncService productInfoSyncService;

    @KafkaListener(topics = KafkaConstant.NSY_PRODUCT_PRODUCT_AMAZON_MARKETING)
    public void newProductConsumer(ConsumerRecord<?, ?> record) {
        TableListenerMessage<NsyProductProductAmazonMarketing> receiveMessage = JsonMapper.fromJson(record.value().toString(), NsyProductProductAmazonMarketingMessage.class);
        LOGGER.info("ProductSyncDataConsumer receive message: {}", record.value());
        if (CollectionUtils.isEmpty(receiveMessage.getData()))
            return;
        processMessage(receiveMessage, receiveMessage.getData().get(0).getProductId().toString());
    }

    @Override
    protected void doProcessMessage(TableListenerMessage<NsyProductProductAmazonMarketing> receiveMessage) {
        LoginInfoService.setName("ProductSyncDataConsumer");
        receiveMessage.getData().forEach(info -> {
            NsyProductProduct nsyProductProductMessage = new NsyProductProduct();
            NsyProductProductAmazonMarketing message = JsonMapper.fromJson(JsonMapper.toJson(info), NsyProductProductAmazonMarketing.class);
            nsyProductProductMessage.setId(message.getProductId());
            productInfoSyncService.syncProductInfo(nsyProductProductMessage);
        });
        LoginInfoService.removeName();
    }

    @Override
    public String getTableName() {
        return KafkaConstant.NSY_PRODUCT_PRODUCT_AMAZON_MARKETING;
    }

    @Override
    public Class getBeanClass() {
        return NsyProductProductAmazonMarketingMessage.class;
    }
}
