package com.nsy.wms.config;

import com.dangdang.ddframe.job.api.ElasticJob;
import com.dangdang.ddframe.job.config.JobCoreConfiguration;
import com.dangdang.ddframe.job.config.JobTypeConfiguration;
import com.dangdang.ddframe.job.config.dataflow.DataflowJobConfiguration;
import com.dangdang.ddframe.job.config.simple.SimpleJobConfiguration;
import com.dangdang.ddframe.job.lite.api.JobScheduler;
import com.dangdang.ddframe.job.lite.config.LiteJobConfiguration;
import com.dangdang.ddframe.job.lite.spring.api.SpringJobScheduler;
import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperRegistryCenter;
import com.nsy.api.core.apicore.util.JSONBinder;
import com.nsy.api.core.apicore.util.StringHelper;
import com.nsy.wms.elasticjob.definition.ElasticJobDefinition;
import com.nsy.wms.elasticjob.definition.ElasticJobDefinitionList;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.CollectionUtils;

import javax.inject.Inject;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;


@Configuration
@ConditionalOnExpression("'${elastic-job.registry-center-server-list}'.length() > 0")
public class ElasticJobConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(ElasticJobConfig.class);

    @Inject
    Environment env;

    @Inject
    private ZookeeperRegistryCenter zookeeperRegistryCenter;


    @Inject
    private ApplicationContext context;

    /**
     * elastic job config
     */

    @Bean
    public List<JobScheduler> elasticJobList(ElasticJobDefinitionList elasticJobDefinitionList) throws ClassNotFoundException {
        List<JobScheduler> jobSchedulerList = new ArrayList<>();
        if (elasticJobDefinitionList != null && !CollectionUtils.isEmpty(elasticJobDefinitionList.getJobs())) {
            for (ElasticJobDefinition elasticJobDefinition : elasticJobDefinitionList.getJobs()) {
                String cron = elasticJobDefinition.getCron();
                int shardingTotalCount = elasticJobDefinition.getShardingTotalCount();
                String shardingItemParameters = elasticJobDefinition.getShardingItemParameters();
                String jobParameters = elasticJobDefinition.getJobParameters();
                ElasticJob elasticJob = (ElasticJob) context.getBean(Class.forName(elasticJobDefinition.getType()));
                JobScheduler jobScheduler = new SpringJobScheduler(elasticJob,
                    zookeeperRegistryCenter,
                    getJobConfig(elasticJobDefinition.getElasticJobType(),
                        elasticJobDefinition.getJobName(),
                        elasticJobDefinition.getStreamingProcess(),
                        (Class<? extends ElasticJob>) Class.forName(elasticJobDefinition.getType()),
                        cron,
                        shardingTotalCount,
                        shardingItemParameters,
                        jobParameters));
                jobScheduler.init();
                LOGGER.info("register job:{}", JsonMapper.toJson(elasticJobDefinition));
                jobSchedulerList.add(jobScheduler);
            }
        }
        return jobSchedulerList;
    }


    private LiteJobConfiguration getJobConfig(String elasticJobType,
                                              String jobName,
                                              Boolean streamingProcessing,
                                              final Class<? extends ElasticJob> jobClass,
                                              final String cron,
                                              final int shardingTotalCount,
                                              final String shardingItemParameters,
                                              final String jobParameters) {
        JobCoreConfiguration jobCoreConfiguration = JobCoreConfiguration.newBuilder(jobName, cron, shardingTotalCount)
            .shardingItemParameters(shardingItemParameters)
            .jobParameter(jobParameters)
            .build();
        JobTypeConfiguration jobTypeConfiguration = null;
        if (ElasticJobDefinition.SIMPLE_JOB.equals(elasticJobType)) {
            jobTypeConfiguration = new SimpleJobConfiguration(jobCoreConfiguration, jobClass.getCanonicalName());
        } else if (ElasticJobDefinition.DATAFLOW_JOB.equals(elasticJobType)) {
            jobTypeConfiguration = new DataflowJobConfiguration(jobCoreConfiguration, jobClass.getCanonicalName(), streamingProcessing);
        }
        return LiteJobConfiguration.newBuilder(jobTypeConfiguration).overwrite(true).build();
    }


    @Bean
    public ElasticJobDefinitionList elasticJobDefinitionList() throws IOException {
        ElasticJobDefinitionList elasticJobDefinitionList = new ElasticJobDefinitionList();
        String path = getClass().getProtectionDomain().getCodeSource().getLocation().getPath();
        String jobFile = env.getProperty("scheduler.elastic.job.config", "elastic-jobs.json");
        if (path.contains("BOOT-INF")) {
            // Run with JAR file
            return JSONBinder.fromJSON(ElasticJobDefinitionList.class, StringHelper.inputStream2String(Thread.currentThread().getContextClassLoader().getResourceAsStream(jobFile)));
        }
        // Run with IDE
        try {
            Resource resource = new ClassPathResource(jobFile);
            final URL url = resource.getURL();
            LOGGER.info("uri ={} ", url.toURI().getRawPath());
            final File app = new File(url.toURI());
            return JSONBinder.fromJSON(ElasticJobDefinitionList.class, StringHelper.file2String(app));
        } catch (URISyntaxException ex) {
            LOGGER.error("can not find the Uri." + ex.getMessage(), ex);
        } catch (IOException ex) {
            LOGGER.error("can not find the file." + ex.getMessage(), ex);
        }
        return elasticJobDefinitionList;
    }
}
