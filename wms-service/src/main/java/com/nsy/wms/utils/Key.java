package com.nsy.wms.utils;

import java.util.Arrays;
import java.util.Objects;

/**
 * created by jun.
 **/
public class Key {

    private static final String SEPARATOR = "###";

    private final String[] keys;

    public Key(String[] keys) {
        this.keys = keys;
    }

    public static Key of(String... keys) {
        return new Key(keys);
    }

    @Override
    public int hashCode() {
        return Objects.hash((Object[]) this.keys);
    }

    @Override
    public boolean equals(Object obj) {
        if (Objects.isNull(obj)) {
            return false;
        }
        if (this == obj) {
            return true;
        }
        if (obj instanceof Key) {
            Key other = (Key) obj;
            return Arrays.equals(this.keys, other.keys);
        }
        return false;
    }

    @Override
    public String toString() {
        return String.join(SEPARATOR, keys);
    }
}
