package com.nsy.wms.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class KafkaUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaUtils.class);

    /**
     * 根据JOB 参数， 获取每个JOB分片所订阅的Topic分区信息
     *
     * @param topic
     * @param topicPartitionNum topic 分区数
     * @param shadingTotalCount job 分片总数
     * @param shadingItem       job 当前分片
     * @return collection
     */
    public static Collection<TopicPartition> partitions(String topic, int topicPartitionNum, int shadingTotalCount, int shadingItem) {
        if (topicPartitionNum < shadingTotalCount) {
            LOGGER.warn("建议JOB分片总数小于等于分区数，job分片数:{}, 分区数:{}", shadingTotalCount, topicPartitionNum);
        }
        if (StringUtils.isEmpty(topic)) {
            return Collections.emptyList();
        }
        List<TopicPartition> topicPartitions = new ArrayList<>();
        if (topicPartitionNum <= 0) {
            TopicPartition topicPartition = new TopicPartition(topic, 0);
            topicPartitions.add(topicPartition);
        } else {
            for (int i = 0; i < topicPartitionNum; i++) {
                if (i % shadingTotalCount == shadingItem) {
                    topicPartitions.add(new TopicPartition(topic, i));
                }
            }
        }
        return topicPartitions;
    }


    public static Properties consumerProperties(String bootstrapServers, String groupId) {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        properties.setProperty(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, String.valueOf(true));
        properties.setProperty(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "1000");
        properties.setProperty(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, "6000");
        properties.setProperty(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, "3000");
        properties.setProperty(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, "3000");
        properties.setProperty(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, "500");
        properties.setProperty(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, String.valueOf(50));
        properties.setProperty(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        properties.setProperty(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        properties.setProperty(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        return properties;
    }

}
