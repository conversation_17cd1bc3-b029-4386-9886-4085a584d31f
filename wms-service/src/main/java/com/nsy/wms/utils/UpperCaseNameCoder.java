package com.nsy.wms.utils;

import com.thoughtworks.xstream.io.naming.NameCoder;

/**
 * HXD
 * 2021/9/8
 **/

public class UpperCaseNameCoder implements NameCoder {

    @Override
    public String encodeNode(String name) {
        return "xml".equalsIgnoreCase(name) ? "xml" : XmlUtils.upperCaseFirstLetter(name);
    }

    @Override
    public String encodeAttribute(String name) {
        return "xml".equalsIgnoreCase(name) ? "xml" : XmlUtils.upperCaseFirstLetter(name);
    }

    @Override
    public String decodeNode(String nodeName) {
        return null;
    }

    @Override
    public String decodeAttribute(String attributeName) {
        return null;
    }

}
