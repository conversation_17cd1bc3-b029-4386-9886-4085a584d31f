package com.nsy.wms.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 货币工具类
 */
public class CurrencyUtils {
    /**
     * 人民币转美元
     *
     * @param cny
     * @param exchangeRate
     * @param scale
     * @return
     */
    public static BigDecimal cnyToUsd(BigDecimal cny, BigDecimal exchangeRate, int scale) {
        return cny.divide(exchangeRate, scale, RoundingMode.HALF_UP);
    }

    /**
     * 美元转人民币
     *
     * @param usd
     * @param exchangeRate
     * @return
     */
    public static BigDecimal usdToCny(BigDecimal usd, BigDecimal exchangeRate) {
        return usd.multiply(exchangeRate);
    }
}
