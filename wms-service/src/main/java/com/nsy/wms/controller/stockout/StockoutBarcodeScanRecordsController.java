package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.request.stockout.StockoutBarcodeScanRecordsPageRequest;
import com.nsy.api.wms.request.stockout.StockoutBarcodeScanRecordsSaveRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutBarcodeScanRecordsPageResponse;
import com.nsy.wms.business.service.stockout.StockoutBarcodeScanRecordsService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/11/27 11:00
 */
@RestController
public class StockoutBarcodeScanRecordsController extends BaseController {

    @Autowired
    private StockoutBarcodeScanRecordsService stockoutBarcodeScanRecordsService;

    @ApiOperation(value = "查询列表", notes = "查询列表", produces = "application/json")
    @RequestMapping(value = "/stockout-barcode-scan-records/page", method = RequestMethod.POST)
    public PageResponse<StockoutBarcodeScanRecordsPageResponse> searchPage(@RequestBody StockoutBarcodeScanRecordsPageRequest request) {
        return stockoutBarcodeScanRecordsService.searchPage(request);
    }

    @ApiOperation(value = "保存扫描记录", notes = "保存扫描记录", produces = "application/json")
    @RequestMapping(value = "/stockout-barcode-scan-records/save", method = RequestMethod.POST)
    public void saveRecords(@RequestBody StockoutBarcodeScanRecordsSaveRequest request) {
        stockoutBarcodeScanRecordsService.saveRecords(request);
    }
}
