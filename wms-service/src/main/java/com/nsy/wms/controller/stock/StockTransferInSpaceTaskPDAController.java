package com.nsy.wms.controller.stock;

import com.nsy.api.wms.request.stock.PDATransferInSpaceTaskTempConfirmRequest;
import com.nsy.api.wms.request.stock.PDATransferInSpaceTaskTempGetRequest;
import com.nsy.api.wms.response.stock.PDATransferInSpaceTaskTempGetResponse;
import com.nsy.wms.controller.BaseController;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.request.stock.PDATransferInSpaceTaskConfirmRequest;
import com.nsy.api.wms.request.stock.PDATransferInSpaceTaskItemListRequest;
import com.nsy.api.wms.request.stock.PDATransferInSpaceTaskListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.PDATransferInSpaceTaskItemListResponse;
import com.nsy.api.wms.response.stock.PDATransferInSpaceTaskListResponse;
import com.nsy.api.wms.response.stock.PDATransferInSpaceTaskScanSkuResponse;
import com.nsy.wms.business.service.stock.StockTransferInSpaceTaskPDAService;
import com.nsy.wms.business.service.stock.StockTransferInSpaceTempTaskPDAService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@Api(tags = "PDA理库调拨接口")
public class StockTransferInSpaceTaskPDAController extends BaseController {

    @Autowired
    StockTransferInSpaceTaskPDAService pdaService;
    @Autowired
    StockTransferInSpaceTempTaskPDAService tempTaskPDAService;


    @ApiOperation(value = "库位调拨列表（调出列表：待调出、调出中；调入列表：待调入、调入中）", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<PDATransferInSpaceTaskListResponse> getList(@RequestBody PDATransferInSpaceTaskListRequest request) {
        return pdaService.getListByRequest(request);
    }

    @ApiOperation(value = "库位调拨明细列表", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task-item/list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<PDATransferInSpaceTaskItemListResponse> getItemList(@RequestBody PDATransferInSpaceTaskItemListRequest request) {
        return pdaService.getItemListByRequest(request);
    }

    @ApiOperation(value = "扫描库位", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/position/{taskId}/{positionCode}", method = RequestMethod.GET)
    public void scanPositionCode(@PathVariable Integer taskId, @Valid @PathVariable String positionCode) {
        pdaService.scanPositionCode(taskId, positionCode);
    }

    @ApiOperation(value = "扫描调拨箱条码", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/internal-box/{taskId}/{internalBoxCode}/{inOrOut}", method = RequestMethod.GET)
    public void scanInternalBoxCode(@PathVariable Integer taskId, @Valid @PathVariable String internalBoxCode, @PathVariable Integer inOrOut) {
        pdaService.scanInternalBoxCode(taskId, internalBoxCode, inOrOut);
    }

    @ApiOperation(value = "扫描商品条码", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/sku/{taskId}/{barcode}/{internalBoxCode}/{inOrOut}", method = RequestMethod.GET)
    @ResponseBody
    public PDATransferInSpaceTaskScanSkuResponse scanBarcode(@PathVariable Integer taskId, @Valid @PathVariable String barcode, @Valid @PathVariable String internalBoxCode, @PathVariable Integer inOrOut) {
        return pdaService.scanBarcode(taskId, barcode, internalBoxCode, inOrOut);
    }

    @ApiOperation(value = "确认调出", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/confirm-out", method = RequestMethod.POST)
    public void confirmOut(@RequestBody PDATransferInSpaceTaskConfirmRequest request) {
        pdaService.confirmHandle(request, 1);
    }


    @ApiOperation(value = "确认调入", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/confirm-in", method = RequestMethod.POST)
    public void confirmIn(@RequestBody PDATransferInSpaceTaskConfirmRequest request) {
        pdaService.confirmHandle(request, 0);
    }


    @ApiOperation(value = "调出数量调整", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/adjust", method = RequestMethod.POST)
    public void adjustHandle(@RequestBody PDATransferInSpaceTaskConfirmRequest request) {
        pdaService.adjustHandle(request);
    }

    @ApiOperation(value = "强制完成调拨任务", notes = "强制完成调拨任务", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/force-done/{taskId}", method = RequestMethod.PUT)
    public void forceComplete(@PathVariable Integer taskId) {
        pdaService.forceComplete(taskId);
    }

    @ApiOperation(value = "库区名称下拉数据源", notes = "库区名称下拉数据源", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-in-space-task/bd-space-area/name-select/{taskId}", method = RequestMethod.GET)
    @ResponseBody
    public List<SelectIntegerModel> getSpaceAreaNameSelect(@PathVariable Integer taskId) {
        return pdaService.getSpaceAreaNameSelect(taskId);
    }

    /* 临时调拨 start */

    @ApiOperation(value = "临时调拨-获取未分配库存数", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-temp/un-prematch-qty", method = RequestMethod.POST)
    @ResponseBody
    public PDATransferInSpaceTaskTempGetResponse getUnPrematchQty(@RequestBody PDATransferInSpaceTaskTempGetRequest request) {
        return tempTaskPDAService.getUnPrematchQty(request);
    }

    @ApiOperation(value = "临时调拨-确认调出", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-temp/confirm-out", method = RequestMethod.POST)
    @ResponseBody
    public Integer tempConfirmOut(@RequestBody PDATransferInSpaceTaskTempConfirmRequest request) {
        return tempTaskPDAService.tempConfirmOut(request);
    }

    @ApiOperation(value = "临时调拨-完成调拨", produces = "application/json")
    @RequestMapping(value = "/pda/stock-transfer-temp/complete-out/{taskId}", method = RequestMethod.POST)
    public void completeScan(@PathVariable Integer taskId) {
        tempTaskPDAService.tempCompleteOut(taskId);
    }


    /* 临时调拨 end */

}
