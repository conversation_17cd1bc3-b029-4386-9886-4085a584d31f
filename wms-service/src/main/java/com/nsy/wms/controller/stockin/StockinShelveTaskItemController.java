package com.nsy.wms.controller.stockin;

import com.nsy.api.wms.feign.StockinShelveTaskItemClient;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockin.QueryPurchaseOrderInfoRequest;
import com.nsy.api.wms.request.stockin.StockInShelveTaskLatestPutInfoRequest;
import com.nsy.api.wms.request.stockin.StockinShelveTaskItemListRequest;
import com.nsy.api.wms.request.stockin.StockinShelveTaskItemUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockin.QueryAllWaitShelveInfoResponse;
import com.nsy.api.wms.response.stockin.QueryPurchaseOrderLatestPutDateResponse;
import com.nsy.api.wms.response.stockin.ShelveTaskItemListResponse;
import com.nsy.api.wms.response.stockin.ShelveTaskItemResponse;
import com.nsy.api.wms.response.stockin.StockInShelveTaskLatestPutInfoResponse;
import com.nsy.wms.business.service.stockin.StockinShelveTaskItemService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;
import javax.validation.Valid;
import java.util.List;

/**
 * Controller层
 *
 * <AUTHOR>
 * @since 1.0
 */
@Api(tags = "上架任务明细相关接口")
@RestController
public class StockinShelveTaskItemController extends BaseController implements StockinShelveTaskItemClient {

    @Inject
    private StockinShelveTaskItemService stockinShelveTaskItemService;

    @ApiOperation(value = "修改上架任务明细", produces = "application/json")
    @RequestMapping(value = "/shelve-task-item/{shelveTaskItemId}", method = RequestMethod.PUT)
    public void updateStockinShelveTaskItem(@PathVariable("shelveTaskItemId") Integer shelveTaskItemId,
                                            @RequestBody StockinShelveTaskItemUpdateRequest request) {
        stockinShelveTaskItemService.updateShelveTaskItem(shelveTaskItemId, request);
    }

    @ApiOperation(value = "查询上架任务明细列表", produces = "application/json")
    @RequestMapping(value = "/shelve-task-item/list", method = RequestMethod.POST)
    public PageResponse<ShelveTaskItemListResponse> getStockinShelveTaskItemList(@RequestBody StockinShelveTaskItemListRequest request) {
        return stockinShelveTaskItemService.getStockinShelveTaskItemList(request);
    }

    // 根据上架任务id查任务及明细基础信息
    @RequestMapping(value = "/shelve-task-item-info/{shelveTaskId}", method = RequestMethod.GET)
    @ApiOperation(value = "根据上架任务id查询上架任务明细列表", produces = "application/json", notes = "shelveTaskId")
    @ApiImplicitParams({@ApiImplicitParam(name = "shelveTaskId", value = "上架任务id", dataType = "Integer", paramType = "path")})
    public ShelveTaskItemResponse getStockinShelveTaskItemInfo(@Valid @PathVariable Integer shelveTaskId) {
        return stockinShelveTaskItemService.getStockinShelveTaskItem(shelveTaskId);
    }

    // 打印上架清单
    @RequestMapping(value = "/shelve-task-item/listing-print", method = RequestMethod.POST)
    @ApiOperation(value = "打印上架清单", produces = "application/json")
    public PrintListResponse printDetail(@Valid @RequestBody IdListRequest idList) {
        return stockinShelveTaskItemService.printDetail(idList);
    }

    @Override
    public List<QueryPurchaseOrderLatestPutDateResponse> queryLatestLatestPutByPurchasePlanNo(@Valid @RequestBody QueryPurchaseOrderInfoRequest request) {
        return stockinShelveTaskItemService.queryLatestLatestPutByPurchasePlanNo(request);
    }

    @Override
    public List<QueryAllWaitShelveInfoResponse> queryAllWaitShelveInfo() {
        return stockinShelveTaskItemService.queryAllWaitUpShelvesInfo();
    }

    @Override
    public List<StockInShelveTaskLatestPutInfoResponse> queryLatestPutInfoByPurchasePlanNoAndSku(@Valid @RequestBody List<StockInShelveTaskLatestPutInfoRequest> request) {
        return stockinShelveTaskItemService.queryLatestPutInfoByPurchasePlanNoAndSku(request);
    }
}
