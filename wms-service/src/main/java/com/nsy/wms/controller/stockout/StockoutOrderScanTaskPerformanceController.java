package com.nsy.wms.controller.stockout;

import com.nsy.wms.business.service.stockout.StockoutOrderScanTaskPerformanceService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 复核任务贴码、封箱记录(StockoutOrderScanTaskPerformance)接口
 *
 * <AUTHOR>
 * @since 2023-03-10 10:47:41
 */
@Api(tags = "复核任务贴码、封箱记录(StockoutOrderScanTaskPerformance)相关接口")
@RestController
public class StockoutOrderScanTaskPerformanceController extends BaseController {

    @Resource
    private StockoutOrderScanTaskPerformanceService stockoutOrderScanTaskPerformanceService;

    /**
     * 扫描箱号
     */
    @ApiOperation("扫描箱号")
    @GetMapping("/stockout-order-scan-task-performance/start-scan")
    public void startScan(@RequestParam @ApiParam(name = "type", value = "类型(贴条码PASTE_BARCODE、PACK_BOX封箱打包)", required = true) String type,
                    @ApiParam(name = "boxCode", value = "箱号", required = true) String boxCode) {
        this.stockoutOrderScanTaskPerformanceService.startScan(type, boxCode);
    }


}

