package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.domain.bd.StockoutNoticeLackPickingTaskResponse;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutQcInboundsDetailPageRequest;
import com.nsy.api.wms.request.stockout.StockoutQcInboundsItemRequest;
import com.nsy.api.wms.request.stockout.StockoutQcInboundsLogRequest;
import com.nsy.api.wms.request.stockout.StockoutQcInboundsPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutQcInboundsDetailInfo;
import com.nsy.api.wms.response.stockout.StockoutQcInboundsDetailResponse;
import com.nsy.api.wms.response.stockout.StockoutQcInboundsLogResponse;
import com.nsy.api.wms.response.stockout.StockoutQcInboundsPageResponse;
import com.nsy.api.wms.response.stockout.StockoutQcInboundsResponse;
import com.nsy.wms.business.service.stockout.StockoutQcInboundsAttachService;
import com.nsy.wms.business.service.stockout.StockoutQcInboundsItemService;
import com.nsy.wms.business.service.stockout.StockoutQcInboundsLogService;
import com.nsy.wms.business.service.stockout.StockoutQcInboundsService;
import com.nsy.wms.repository.entity.stockout.StockoutQcInboundsAttachEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @description:
 * @author: caishaohui
 * @time: 2023/10/16 15:55
 */
@Api(tags = "出库质检相关接口")
@RestController
@RequestMapping("/stockout-qc")
public class StockoutQcInboundsController {

    @Autowired
    private StockoutQcInboundsService stockoutQcInboundsService;

    @Autowired
    private StockoutQcInboundsItemService stockoutQcInboundsItemService;

    @Autowired
    private StockoutQcInboundsAttachService attachService;

    @Autowired
    private StockoutQcInboundsLogService logService;


    @ApiOperation(value = "查询质检报告", produces = "application/json")
    @RequestMapping(value = "/stockout-order-no/{stockoutOrderNo}", method = RequestMethod.GET)
    @ResponseBody
    public StockoutQcInboundsResponse getQcInboundsByOutOrderNo(@PathVariable String stockoutOrderNo) {
        return stockoutQcInboundsService.getQcInboundsByOutOrderNo(stockoutOrderNo);
    }

    @ApiOperation(value = "质检确认", produces = "application/json")
    @RequestMapping(value = "/confirm/{stockoutOrderNo}", method = RequestMethod.POST)
    public void replenishment(@PathVariable String stockoutOrderNo, @Valid @RequestBody StockoutQcInboundsItemRequest request) {
        stockoutQcInboundsService.confirm(stockoutOrderNo, request);
    }

    @ApiOperation(value = "发起补货", produces = "application/json")
    @RequestMapping(value = "/replenishment/{stockoutOrderNo}", method = RequestMethod.POST)
    public StockoutNoticeLackPickingTaskResponse replenishment(@PathVariable String stockoutOrderNo, @Valid @RequestBody IdListRequest idListRequest) {
        return stockoutQcInboundsService.replenishment(stockoutOrderNo, idListRequest);
    }

    @ApiOperation(value = "全检完成", produces = "application/json")
    @RequestMapping(value = "/qc-complete/{stockoutOrderNo}", method = RequestMethod.POST)
    public void replenishment(@PathVariable String stockoutOrderNo) {
        stockoutQcInboundsService.qcComplete(stockoutOrderNo);
    }

    @ApiOperation(value = "出库全检列表查询", produces = "application/json")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutQcInboundsPageResponse> getStockoutQcInboundsPage(@RequestBody StockoutQcInboundsPageRequest request) {
        return stockoutQcInboundsService.pageInfo(request);
    }

    @ApiOperation(value = "出库全检明细信息", produces = "application/json")
    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
    @ResponseBody
    public StockoutQcInboundsDetailResponse getDetailInfo(@PathVariable Integer id) {
        return stockoutQcInboundsItemService.getDetailInfo(id);
    }

    @ApiOperation(value = "出库全检明细信息", produces = "application/json")
    @RequestMapping(value = "/detail/page", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutQcInboundsDetailInfo> getDetailPageInfo(@RequestBody StockoutQcInboundsDetailPageRequest request) {
        return stockoutQcInboundsItemService.getDetailPageInfo(request);
    }

    @ApiOperation(value = "出库全检明细附件信息", produces = "application/json")
    @RequestMapping(value = "/attach/list/{id}", method = RequestMethod.GET)
    @ResponseBody
    public List<StockoutQcInboundsAttachEntity> getAttachInfo(@PathVariable Integer id) {
        return attachService.getAttachInfo(id);
    }

    @ApiOperation(value = "出库全检日志查询", produces = "application/json")
    @RequestMapping(value = "/log/page", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockoutQcInboundsLogResponse> getStockoutQcInboundsLogPage(@RequestBody StockoutQcInboundsLogRequest request) {
        return logService.getStockoutQcInboundsLogPage(request);
    }

}
