package com.nsy.wms.controller.overseas;

import com.nsy.api.wms.request.overseas.OverseasWarehouseOrderPackageTimeUpdateRequest;
import com.nsy.api.wms.request.overseas.OverseasWarehouseOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.overseas.OverseasWarehouseOrderResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.wms.business.service.stockout.StockoutOverseasWarehouseOrderService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 海外仓订单控制器
 *
 * <AUTHOR>
 * @since 1.0
 */
@Api(tags = "海外仓订单接口")
@RestController
@RequestMapping("/overseas-order")
public class OverseasWarehouseOrderController extends BaseController {

    @Resource
    private StockoutOverseasWarehouseOrderService overseasWarehouseOrderService;

    @ApiOperation(value = "分页查询海外仓订单", notes = "支持多条件筛选和分页查询")
    @GetMapping("/page")
    public PageResponse<OverseasWarehouseOrderResponse> page(@Valid OverseasWarehouseOrderPageRequest request) {
        return overseasWarehouseOrderService.queryByPage(request);
    }

    @ApiOperation("根据ID查询订单详情")
    @GetMapping("/{id}")
    public OverseasWarehouseOrderResponse getById(@PathVariable("id") Integer id) {
        return overseasWarehouseOrderService.getOneById(id);
    }

    @ApiOperation("根据状态统计订单数量")
    @GetMapping("/count/status")
    public List<StatusCountResponse> countByStatus() {
        return overseasWarehouseOrderService.countByStatus();
    }

    @ApiOperation("删除海外仓订单")
    @DeleteMapping("/{id}")
    public void delete(@PathVariable("id") Integer id) {
        overseasWarehouseOrderService.delete(id);
    }

    @ApiOperation(value = "更新海外仓订单包裹时间", notes = "根据物流单号更新包裹提取时间和签收时间")
    @PutMapping("/package-time")
    public void updatePackageTime(@Valid @RequestBody OverseasWarehouseOrderPackageTimeUpdateRequest request) {
        overseasWarehouseOrderService.updatePackageTime(request);
    }
}
