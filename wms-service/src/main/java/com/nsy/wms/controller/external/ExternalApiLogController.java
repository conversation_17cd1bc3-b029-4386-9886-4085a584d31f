package com.nsy.wms.controller.external;

import com.nsy.api.wms.domain.external.ExternalApiLog;
import com.nsy.api.wms.domain.external.ExternalApiLogItem;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.request.bd.ConsumerRequest;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.external.ExternalApiLogListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.wms.business.service.QConsumerRecordService;
import com.nsy.wms.business.service.stockout.StockoutCheckQueueService;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.business.service.external.ExternalApiAsyncQueueService;
import com.nsy.wms.business.service.external.ExternalApiLogItemService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@Api(tags = "外部系统接口跟踪")
public class ExternalApiLogController extends BaseController {

    @Autowired
    ExternalApiLogService externalApiLogService;
    @Autowired
    ExternalApiLogItemService externalApiLogItemService;
    @Autowired
    ExternalApiAsyncQueueService externalApiAsyncQueueServicep;
    @Autowired
    QConsumerRecordService qConsumerRecordService;
    @Autowired
    StockoutCheckQueueService stockoutCheckQueueService;

    @ApiOperation(value = "调用外部系统接口列表", notes = "调用外部系统接口列表", produces = "application/json")
    @RequestMapping(value = "/external-api-log/list", method = RequestMethod.POST)
    public PageResponse<ExternalApiLog> getExternalApiLogList(@RequestBody ExternalApiLogListRequest request) {
        return externalApiLogService.getExternalApiLogList(request);
    }

    @ApiOperation(value = "request、response详情", notes = "request、response详情", produces = "application/json")
    @RequestMapping(value = "/external-api-log-item", method = RequestMethod.GET)
    public ExternalApiLogItem getApiLogItemByApiLogId(@RequestParam Integer apiLogId) {
        return externalApiLogItemService.getApiLogItemByApiLogId(apiLogId);
    }

    @ApiOperation(value = "外部接口名称下拉框数据源", notes = "外部接口名称下拉框数据源", produces = "application/json")
    @RequestMapping(value = "/external-api-log/name-select", method = RequestMethod.GET)
    public List<SelectModel> getApiLogItemByApiLogId() {
        return externalApiLogService.externalApiNameList();
    }

//    @ApiOperation(value = "接口重推", notes = "接口重推", produces = "application/json")
//    @RequestMapping(value = "/external-api-log/re-push/{apiLogId}", method = RequestMethod.POST)
//    public void rePush(@Valid @PathVariable("apiLogId") Integer apiLogId) {
//        externalApiLogService.rePush(apiLogId);
//    }

    @ApiOperation(value = "异步接口数据修复", notes = "异步接口数据修复", produces = "application/json", hidden = true)
    @RequestMapping(value = "/external-api-queue/status/{status}", method = RequestMethod.POST)
    public void repair(@RequestBody IdListRequest request, @Valid @PathVariable("status") String status) {
        externalApiAsyncQueueServicep.repair(request, status);
    }

    @ApiOperation(value = "kafka消费消息修复", notes = "kafka消费消息修复", produces = "application/json", hidden = true)
    @RequestMapping(value = "/consumer/repair", method = RequestMethod.POST)
    public void repair(@RequestBody ConsumerRequest request) {
        qConsumerRecordService.repairConsumer(request);
    }

    @ApiOperation(value = "出库核对队列手动修复", notes = "出库核对队列手动修复", produces = "application/json", hidden = true)
    @RequestMapping(value = "/stockout-check-queue/repair", method = RequestMethod.POST)
    public void stockoutCheckRepair(@RequestBody IdListRequest request) {
        stockoutCheckQueueService.repairQueue(request);
    }
}
