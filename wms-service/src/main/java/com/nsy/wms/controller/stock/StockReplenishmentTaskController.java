package com.nsy.wms.controller.stock;

import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stock.StockReplenishmentTaskPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stock.StockReplenishmentTaskPageResponse;
import com.nsy.wms.business.service.stock.StockReplenishmentTaskService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "补货任务接口")
@RestController
public class StockReplenishmentTaskController extends BaseController {
    @Autowired
    StockReplenishmentTaskService stockReplenishmentTaskService;

    @ApiOperation(value = "分页", produces = "application/json")
    @RequestMapping(value = "/stock-replenishment-task/page", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockReplenishmentTaskPageResponse> page(@RequestBody StockReplenishmentTaskPageRequest request) {
        return stockReplenishmentTaskService.page(request);
    }


    @ApiOperation(value = "完成", produces = "application/json")
    @RequestMapping(value = "/stock-replenishment-task/finish/{taskId}", method = RequestMethod.PUT)
    @ResponseBody
    public void finish(@PathVariable Integer taskId) {
        stockReplenishmentTaskService.finish(taskId);
    }

    @ApiOperation(value = "打印", produces = "application/json")
    @RequestMapping(value = "/stock-replenishment-task/print", method = RequestMethod.POST)
    public PrintListResponse print(@Valid @RequestBody IdListRequest request) {
        return stockReplenishmentTaskService.print(request);
    }
}
