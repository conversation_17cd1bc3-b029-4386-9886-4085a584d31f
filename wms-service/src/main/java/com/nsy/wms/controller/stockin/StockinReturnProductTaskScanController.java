package com.nsy.wms.controller.stockin;

import com.nsy.api.wms.request.stockin.StockinReturnProductTaskScanFinishedFromStockPositionRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductTaskScanFinishedRequest;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskScanFromStockPositionResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskScanPositionResponse;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskScanFromStockPositionService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskScanService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "退货任务扫描相关接口")
@RestController
public class StockinReturnProductTaskScanController extends BaseController {
    @Resource
    StockinReturnProductTaskScanService stockinReturnProductTaskScanService;
    @Resource
    StockinReturnProductTaskScanFromStockPositionService stockinReturnProductTaskScanFromStockPositionService;

    @GetMapping("return-product-task-scan/scan-position/{positionCode}")
    @ApiOperation(value = "扫描库位", produces = "application/json")
    public StockinReturnProductTaskScanPositionResponse scanPosition(@PathVariable String positionCode) {
        return stockinReturnProductTaskScanService.scanPosition(positionCode);
    }

    @PostMapping("return-product-task-scan/scan-finished")
    @ApiOperation(value = "扫描完成", produces = "application/json")
    public void scanFinished(@RequestBody StockinReturnProductTaskScanFinishedRequest request) {
        stockinReturnProductTaskScanService.scanFinished(request);
    }

    @GetMapping("return-product-task-scan/scan-stock-position/{positionCode}")
    @ApiOperation(value = "扫描库位 - 存储库位", produces = "application/json")
    public StockinReturnProductTaskScanFromStockPositionResponse scanStockPosition(@PathVariable String positionCode) {
        return stockinReturnProductTaskScanFromStockPositionService.scanStockPosition(positionCode);
    }

    @PostMapping("return-product-task-scan/scan-stock-position-finished")
    @ApiOperation(value = "扫描完成 - 存储库位", produces = "application/json")
    public void scanStockPositionFinished(@RequestBody StockinReturnProductTaskScanFinishedFromStockPositionRequest request) {
        stockinReturnProductTaskScanFromStockPositionService.scanStockPositionFinished(request);
    }
}
