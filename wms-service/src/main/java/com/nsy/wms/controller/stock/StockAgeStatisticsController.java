package com.nsy.wms.controller.stock;

import com.nsy.wms.controller.BaseController;
import com.nsy.api.wms.request.stock.StockAgeStatisticsListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockAgeStatisticsListResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockAgeRecordService;
import com.nsy.wms.business.service.stock.StockAgeStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@Api(tags = "库龄统计接口")
public class StockAgeStatisticsController extends BaseController {

    @Autowired
    StockAgeStatisticsService statisticsService;
    @Autowired
    StockAgeRecordService recordService;
    @Autowired
    LoginInfoService loginInfoService;

    @ApiOperation(value = "商品库龄列表", produces = "application/json")
    @RequestMapping(value = "/stock-age-statistics/list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockAgeStatisticsListResponse> getList(@RequestBody StockAgeStatisticsListRequest request) {
        return statisticsService.getListByRequest(request);
    }

    @ApiOperation(value = "库龄记录变化", produces = "application/json")
    @RequestMapping(value = "/stock-age-record-handle/{recordId}", method = RequestMethod.PUT)
    public void handleRecord(@Valid @PathVariable("recordId") Integer recordId) {
        recordService.handleRecord(recordId, loginInfoService.getUserName());
    }
}
