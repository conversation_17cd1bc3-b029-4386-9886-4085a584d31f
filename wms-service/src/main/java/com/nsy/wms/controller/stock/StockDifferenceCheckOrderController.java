package com.nsy.wms.controller.stock;

import com.nsy.api.wms.request.stockin.TabCountRequest;
import com.nsy.api.wms.response.stockin.TaskListCountResponse;
import com.nsy.wms.controller.BaseController;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stock.ConfirmDifferenceRequest;
import com.nsy.api.wms.request.stock.StockDifferenceCheckOrderListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockDifferenceCheckOrderItemResponse;
import com.nsy.api.wms.response.stock.StockDifferenceCheckOrderListResponse;
import com.nsy.wms.business.service.stock.StockDifferenceCheckOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "差异核对单相关接口")
@RestController
public class StockDifferenceCheckOrderController extends BaseController {

    @Autowired
    private StockDifferenceCheckOrderService stockDifferenceCheckOrderService;

    @ApiOperation(value = "差异核对单列表", produces = "application/json")
    @RequestMapping(value = "/stock-difference-check-order/page-list", method = RequestMethod.POST)
    public PageResponse<StockDifferenceCheckOrderListResponse> pageList(@RequestBody StockDifferenceCheckOrderListRequest request) {
        return stockDifferenceCheckOrderService.pageList(request);
    }

    @ApiOperation(value = "差异核对单状态数量", notes = "差异核对单状态数量", produces = "application/json")
    @RequestMapping(value = "/stock-difference-check-order/count", method = RequestMethod.POST)
    public List<TaskListCountResponse> getTaskListCount(@RequestBody TabCountRequest request) {
        return stockDifferenceCheckOrderService.getTaskListCount(request);
    }

    @ApiOperation(value = "差异核对单明细列表", produces = "application/json")
    @RequestMapping(value = "/stock-difference-check-order/item-list/{checkOrderId}", method = RequestMethod.GET)
    public List<StockDifferenceCheckOrderItemResponse> itemList(@PathVariable Integer checkOrderId) {
        return stockDifferenceCheckOrderService.itemList(checkOrderId);
    }

    @ApiOperation(value = "确定差异", produces = "application/json")
    @RequestMapping(value = "/stock-difference-check-order/confirm-difference", method = RequestMethod.POST)
    public void confirmDifference(@RequestBody ConfirmDifferenceRequest request) {
        stockDifferenceCheckOrderService.confirmDifference(request);
    }

    @ApiOperation(value = "生成盘点任务", produces = "application/json")
    @RequestMapping(value = "/stock-difference-check-order/create-task-stock-task", method = RequestMethod.POST)
    public void createTakeStockTask(@RequestBody IdListRequest request) {
        stockDifferenceCheckOrderService.createTakeStockTask(request);
    }
}
