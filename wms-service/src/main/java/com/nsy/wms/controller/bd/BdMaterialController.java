package com.nsy.wms.controller.bd;

import com.nsy.wms.controller.BaseController;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.request.bd.BdMaterialAddRequest;
import com.nsy.api.wms.request.bd.BdMaterialListRequest;
import com.nsy.api.wms.request.bd.BdMaterialUpdateDescriptionRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdMaterialListResponse;
import com.nsy.wms.business.service.bd.BdMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "物料接口")
@RestController
public class BdMaterialController extends BaseController {

    @Autowired
    BdMaterialService materialService;

    @ApiOperation(value = "新增物料", notes = "新增物料", produces = "application/json")
    @RequestMapping(value = "/bd-material", method = RequestMethod.POST)
    public void addMaterial(@Valid @RequestBody BdMaterialAddRequest request) {
        materialService.addMaterial(request);
    }

    @ApiOperation(value = "查询物料列表", notes = "查询物料列表", produces = "application/json")
    @RequestMapping(value = "/bd-material/list", method = RequestMethod.POST)
    public PageResponse<BdMaterialListResponse> getMaterialList(@Valid @RequestBody BdMaterialListRequest request) {
        return materialService.getMaterialList(request);
    }

    @ApiOperation(value = "物料名称下拉框", notes = "物料名称下拉框", produces = "application/json")
    @RequestMapping(value = "/bd-material-name-select", method = RequestMethod.GET)
    public List<SelectModel> getMaterialNameSelect() {
        return materialService.getAllMaterialName();
    }

    @ApiOperation(value = "修改物料备注", notes = "修改物料备注", produces = "application/json")
    @RequestMapping(value = "/bd-material/description/{materialId}", method = RequestMethod.PUT)
    public void updateMaterialDescription(@PathVariable Integer materialId, @Valid @RequestBody BdMaterialUpdateDescriptionRequest request) {
        materialService.updateMaterialDescription(materialId, request);
    }
}
