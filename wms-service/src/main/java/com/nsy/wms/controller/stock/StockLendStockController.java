package com.nsy.wms.controller.stock;

import com.nsy.api.wms.request.stock.StockLendStockChangeLogListRequest;
import com.nsy.api.wms.request.stock.StockLendStockListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockLendStockChangeLogListResponse;
import com.nsy.api.wms.response.stockin.StockLendStockCountResponse;
import com.nsy.api.wms.response.stockin.StockLendStockListResponse;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.business.service.stock.StockLendStockChangeLogService;
import com.nsy.wms.business.service.stock.StockLendStockService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "借用库存相关接口")
@RestController
public class StockLendStockController extends BaseController {
    @Autowired
    StockLendStockService stockLendStockService;

    @Autowired
    StockLendStockChangeLogService stockLendStockChangeLogService;

    @ApiOperation(value = "分页", produces = "application/json")
    @PostMapping("/stock-lend-stock/page-list")
    public PageResponse<StockLendStockListResponse> pageList(@RequestBody StockLendStockListRequest request) {
        return stockLendStockService.pageList(request);
    }

    @ApiOperation(value = "统计", produces = "application/json")
    @PostMapping("/stock-lend-stock/page-count")
    public StockLendStockCountResponse pageCount(@RequestBody StockLendStockListRequest request) {
        return stockLendStockService.pageCount(request);
    }

    @ApiOperation(value = "日志分页", produces = "application/json")
    @PostMapping("/stock-lend-stock-log/page-list")
    public PageResponse<StockLendStockChangeLogListResponse> pageListLog(@RequestBody StockLendStockChangeLogListRequest request) {
        return stockLendStockChangeLogService.pageList(request);
    }
}
