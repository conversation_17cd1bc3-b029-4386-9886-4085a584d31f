package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.domain.stockout.StockoutShipmentItemDetail;
import com.nsy.api.wms.domain.stockout.StockoutShipmentItemInfo;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemOpListRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemOpRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemSearchByStockoutOrderRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentItemSearchRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.wms.controller.BaseController;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 装箱清单明细
 *
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@Api(tags = "装箱清单明细")
public class StockoutShipmentItemController extends BaseController {
    @Autowired
    private StockoutShipmentItemService stockoutShipmentItemService;

    @RequestMapping(value = "/stockout-shipment-item/list", method = RequestMethod.POST)
    @ApiOperation(value = "装箱清单明细-列表", produces = "application/json")
    public PageResponse<StockoutShipmentItemInfo> searchList(@Valid @RequestBody StockoutShipmentItemSearchRequest request) {
        return stockoutShipmentItemService.searchList(request);
    }

    @ApiOperation(value = "装箱条码打印", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment-item/print-barcode", method = RequestMethod.POST)
    public PrintListResponse printBarcode(@Valid @RequestBody IdListRequest idListRequest) {
        return stockoutShipmentItemService.printBarcode(idListRequest);
    }

    @ApiOperation(value = "装箱打印B2B条码", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment-item/print-b2b-barcode", method = RequestMethod.POST)
    public PrintListResponse printB2BBarcode(@Valid @RequestBody IdListRequest idListRequest) {
        return stockoutShipmentItemService.printB2bBarcode(idListRequest);
    }

    @ApiOperation(value = "回退装箱清单明细数量", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment-item/withdraw", method = RequestMethod.POST)
    public void deleteShipmentItem(@Valid @RequestBody StockoutShipmentItemOpRequest request) {
        stockoutShipmentItemService.deleteShipmentItemAndSyncErp(request);
    }

    @RequestMapping(value = "/stockout-shipment-item/list-by-stockout-order", method = RequestMethod.POST)
    @ApiOperation(value = "按出库单撤货-装箱清单明细", produces = "application/json")
    public PageResponse<StockoutShipmentItemDetail> searchListByStockoutOrder(@Valid @RequestBody StockoutShipmentItemSearchByStockoutOrderRequest request) {
        return stockoutShipmentItemService.searchListByStockoutOrder(request);
    }

    @ApiOperation(value = "批量回退装箱清单明细数量", produces = "application/json")
    @RequestMapping(value = "/stockout-shipment-item/batch-withdraw", method = RequestMethod.POST)
    public void batchDeleteShipmentItem(@Valid @RequestBody StockoutShipmentItemOpListRequest request) {
        stockoutShipmentItemService.batchDeleteShipmentItem(request);
    }
}
