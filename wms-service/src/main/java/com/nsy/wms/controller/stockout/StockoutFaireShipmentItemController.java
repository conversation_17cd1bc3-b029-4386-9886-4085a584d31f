package com.nsy.wms.controller.stockout;

import com.nsy.api.wms.domain.stockout.StockoutFaireShipmentItemInfo;
import com.nsy.api.wms.request.stockout.StockoutFaireShipmentItemSearchRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutFaireShipmentScanItemResponse;
import com.nsy.wms.business.service.stockout.StockoutFaireShipmentItemService;
import com.nsy.wms.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@Api(tags = "Faire装箱清单明细")
public class StockoutFaireShipmentItemController extends BaseController {

    @Autowired
    StockoutFaireShipmentItemService faireShipmentItemService;

    @RequestMapping(value = "/stockout-faire-shipment-item/list", method = RequestMethod.POST)
    @ApiOperation(value = "Faire装箱清单明细-列表", produces = "application/json")
    public PageResponse<StockoutFaireShipmentItemInfo> searchList(@Valid @RequestBody StockoutFaireShipmentItemSearchRequest request) {
        return faireShipmentItemService.searchList(request);
    }

    @RequestMapping(value = "/stockout-faire-merge-shipment-item/list", method = RequestMethod.POST)
    @ApiOperation(value = "Faire合并装箱清单明细-列表", produces = "application/json")
    public PageResponse<StockoutFaireShipmentScanItemResponse> searchListByMergeBoxCode(@Valid @RequestBody StockoutFaireShipmentItemSearchRequest request) {
        return faireShipmentItemService.searchListByMergeBoxCode(request);
    }
}
