package com.nsy.wms.controller.bd;

import com.nsy.wms.controller.BaseController;
import com.nsy.api.wms.domain.bd.BdHsCodeModel;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.wms.business.service.bd.BdHsCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 海关编码
 *
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@Api(tags = "海关编码")
public class BdHsCodeController extends BaseController {

    @Autowired
    private BdHsCodeService bdHsCodeService;

    @GetMapping("/bd-hs-code/select-model")
    @ApiOperation(value = "海关编码-下拉框数据源", produces = "application/json")
    public List<SelectModel> hsCodeSelectModelList() {
        return bdHsCodeService.hsCodeSelectModelList();
    }

    @GetMapping("/bd-hs-code/{hsCode}")
    @ApiOperation(value = "海关编码详细", produces = "application/json")
    public BdHsCodeModel hsCode(@PathVariable("hsCode") String hsCode) {
        return bdHsCodeService.getByHsCode(hsCode);
    }
}
