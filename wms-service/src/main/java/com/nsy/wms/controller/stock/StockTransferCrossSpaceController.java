package com.nsy.wms.controller.stock;

import com.nsy.wms.controller.BaseController;
import com.nsy.api.wms.request.stock.StockTransferClearAddRequest;
import com.nsy.api.wms.request.stock.StockTransferClearSkuRequest;
import com.nsy.api.wms.request.stock.StockTransferCrossSpaceRequest;
import com.nsy.api.wms.request.stock.StockTransferCrossSpaceSkuRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockTransferClearSkuResponse;
import com.nsy.api.wms.response.stock.StockTransferCrossSkuResponse;
import com.nsy.api.wms.response.stock.StockTransferCrossSpaceDetailResponse;
import com.nsy.api.wms.response.stock.StockTransferCrossSpaceResponse;
import com.nsy.wms.business.service.stock.StockTransferCrossSpaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Api(tags = "仓间调拨列表")
@RestController
public class StockTransferCrossSpaceController extends BaseController {

    @Autowired
    StockTransferCrossSpaceService stockTransferCrossSpaceService;

    @ApiOperation(value = "仓间调拨列表查询", produces = "application/json")
    @RequestMapping(value = "/stock-transfer-space/list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockTransferCrossSpaceResponse> getTransferCrossSpaceList(@RequestBody StockTransferCrossSpaceRequest request) {
        return stockTransferCrossSpaceService.getTransferCrossSpaceList(request);
    }

    @ApiOperation(value = "仓间调拨详情查询", produces = "application/json")
    @RequestMapping(value = "/stock-transfer-space/detail/{transferId}", method = RequestMethod.GET)
    @ApiImplicitParams({@ApiImplicitParam(name = "transferId", value = "调拨单主键", dataType = "Integer", paramType = "path")})
    @ResponseBody
    public StockTransferCrossSpaceDetailResponse getTransferCrossSpaceList(@Valid @PathVariable Integer transferId) {
        return stockTransferCrossSpaceService.getTransferCrossSpaceDetail(transferId);
    }

    @ApiOperation(value = "取消调拨", produces = "application/json")
    @RequestMapping(value = "/stock-transfer-space/cancel/{transferTaskNo}", method = RequestMethod.PUT)
    @ApiImplicitParams({@ApiImplicitParam(name = "transferTaskNo", value = "调拨任务号", dataType = "String", paramType = "path")})
    public void cancelTransfer(@Valid @PathVariable String transferTaskNo) {
        stockTransferCrossSpaceService.cancelTransfer(transferTaskNo);
    }

    @ApiOperation(value = "查询出库任务是否已经生成波次", produces = "application/json")
    @RequestMapping(value = "/stock-transfer-space/is-generate-batch/{transferTaskNo}", method = RequestMethod.GET)
    @ApiImplicitParams({@ApiImplicitParam(name = "String", value = "调拨任务号", dataType = "String", paramType = "path")})
    public Boolean isGenerateBatch(@Valid @PathVariable String transferTaskNo) {
        return stockTransferCrossSpaceService.isGenerateBatch(transferTaskNo);
    }


    @ApiOperation(value = "仓间调拨明细列表查询", produces = "application/json")
    @RequestMapping(value = "/stock-transfer-space/item", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockTransferCrossSkuResponse> getTransferSkuList(@RequestBody StockTransferCrossSpaceSkuRequest request) {
        return stockTransferCrossSpaceService.getTransferSkuList(request);
    }

    @ApiOperation(value = "新增清仓出库列表查询", produces = "application/json")
    @RequestMapping(value = "/stock-transfer-space/clear-list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<StockTransferClearSkuResponse> getClearSkuList(@RequestBody StockTransferClearSkuRequest request) {
        return stockTransferCrossSpaceService.getClearSkuList(request);
    }

    @ApiOperation(value = "新增清仓出库计划", produces = "application/json")
    @RequestMapping(value = "/stock-transfer-space/clear-add", method = RequestMethod.POST)
    @ResponseBody
    public void addClearSku(@RequestBody StockTransferClearAddRequest request) {
        stockTransferCrossSpaceService.addClearSku(request);
    }


}
