package com.nsy.wms.repository.entity.stockin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;
/**
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "stockin_shelve_split_task")
@TableName("stockin_shelve_split_task")
public class StockinShelveSplitTaskEntity {

    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer shelveSplitTaskId;

    /** 上架分拣任务号 */
    private String shelveSplitTaskNo;

    /** 地区 */
    private String location;

    /** 分拣次数 */
    private Integer taskIndex;

    /** 仓库id */
    private Integer spaceId;

    /** 仓库 */
    private String spaceName;

    /** 分拣库区数 */
    private Integer spaceAreaQty;

    /** 状态 */
    private String status;

    /** 分拣人 */
    private String operator;

    /** 开始分拣时间 */
    private Date operateStartDate;

    /** 结束分拣时间 */
    private Date operateEndDate;

    /** 创建时间 */
    private Date createDate;

    /** 创建者 */
    private String createBy;

    /** 更新时间 */
    private Date updateDate;

    /** 更新者 */
    private String updateBy;

    /** 版本号 */
    private Integer version;


    public Integer getShelveSplitTaskId() {
        return shelveSplitTaskId;
    }

    public void setShelveSplitTaskId(Integer shelveSplitTaskId) {
        this.shelveSplitTaskId = shelveSplitTaskId;
    }

    public String getShelveSplitTaskNo() {
        return shelveSplitTaskNo;
    }

    public void setShelveSplitTaskNo(String shelveSplitTaskNo) {
        this.shelveSplitTaskNo = shelveSplitTaskNo;
    }
    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
    public Integer getTaskIndex() {
        return taskIndex;
    }

    public void setTaskIndex(Integer taskIndex) {
        this.taskIndex = taskIndex;
    }
    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }
    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }
    public Integer getSpaceAreaQty() {
        return spaceAreaQty;
    }

    public void setSpaceAreaQty(Integer spaceAreaQty) {
        this.spaceAreaQty = spaceAreaQty;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    public Date getOperateStartDate() {
        return operateStartDate;
    }

    public void setOperateStartDate(Date operateStartDate) {
        this.operateStartDate = operateStartDate;
    }
    public Date getOperateEndDate() {
        return operateEndDate;
    }

    public void setOperateEndDate(Date operateEndDate) {
        this.operateEndDate = operateEndDate;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
}
