package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.domain.stockout.AutoMachineSortStatistic;
import com.nsy.api.wms.domain.stockout.AutoMachineSortStatisticQty;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitTaskList;
import com.nsy.api.wms.request.stockout.AutoMachineSortListRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitTaskListRequest;
import com.nsy.api.wms.request.stockout.StockoutPrintOrderRequest;
import com.nsy.api.wms.response.stockout.AutoMachineSortListResponse;
import com.nsy.api.wms.response.stockout.StockoutAutoMachineSortCountResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskPrintOrderInfo;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskPrintResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutBatchSplitTaskPageCount;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StockoutBatchSplitTaskMapper extends BaseMapper<StockoutBatchSplitTaskEntity> {
    IPage<StockoutBatchSplitTaskList> pageSearchBatchSplitTask(IPage page, @Param("query") StockoutBatchSplitTaskListRequest request);

    Integer statusCount(@Param("status") String status);

    List<AutoMachineSortListResponse> autoMachineSortList(Page page, @Param("request") AutoMachineSortListRequest request);

    List<AutoMachineSortStatistic> autoMachineSortStatistic(@Param("taskIdList") List<Integer> taskIdList);

    List<StockoutBatchSplitTaskPrintResponse> queryPrintData(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList);

    List<StockoutBatchSplitTaskPrintOrderInfo> queryOrderInfo(@Param("idList") List<Integer> idList);

    List<StockoutBatchSplitTaskPrintOrderInfo> queryOrderInfoByOrderNo(@Param("request") StockoutPrintOrderRequest request);

    List<StockoutAutoMachineSortCountResponse> countByStatus();

    List<AutoMachineSortStatisticQty> getStockoutOrderQtyByBatchId(@Param("batchIdList") List<Integer> batchIdList);

    Integer maxLackQty(@Param("stockoutOrderNo") String stockoutOrderNo, @Param("subBatchId") Integer subBatchId);

    /**
     * 根据任务ID列表查找统计值（供列表返回使用）
     *
     * @param taskIdList
     * @return
     */
    List<StockoutBatchSplitTaskPageCount> pageCountByTaskIdList(List<Integer> taskIdList);
}
