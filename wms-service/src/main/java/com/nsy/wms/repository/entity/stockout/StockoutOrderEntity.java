package com.nsy.wms.repository.entity.stockout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "stockout_order")
@TableName("stockout_order")
public class StockoutOrderEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer stockoutOrderId;

    /**
     * 地区
     */
    private String location;

    /**
     * 仓库Id
     */
    private Integer spaceId;

    /**
     * 仓间调拨 - 目的仓库Id
     */
    private Integer desSpaceId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 出库单号
     */
    private String stockoutOrderNo;

    /**
     * erp拣货单id
     */
    private Integer erpPickId;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流标签规格
     */
    private String logisticsLabelSize;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 预估重量
     */
    private BigDecimal estimateWeight;

    /**
     * 是否急单
     */
    private Boolean isUrgent;

    /**
     * 状态
     */
    private String status;

    /**
     * 最晚发货时间
     */
    private Date latestDeliveryDate;

    /**
     * 发货时间
     */
    private Date deliveryDate;

    /**
     * 出库业务类型
     */
    private String stockoutType;

    /**
     * 通知发货状态
     */
    private String notifyShipStatus;

    /**
     * 是否缺货
     */
    private Boolean isLack;

    /**
     * 是否fba补货新流程    1是0否
     */
    private Boolean isFbaReplenish;

    /**
     * 拣货模式
     */
    private String pickingType;

    /**
     * 工作区
     */
    private String workspace;

    /**
     * 合并状态
     */
    private String mergeState;

    /**
     * 合并出库单id
     */
    private Integer mergeStockoutOrderId;

    /**
     * 报关方式
     */
    private String customsDeclareType;

    /**
     * 部门
     */
    private String businessType;

    /**
     * 店铺
     */
    private String storeName;

    /**
     * 店铺Id
     */
    private Integer storeId;

    /**
     * 业务员
     */
    private String saler;

    /**
     * 业务员
     */
    private String salerUserName;

    /**
     * 收货人
     */
    private String receiverName;

    /**
     * 业务员
     */
    private String platformName;

    /**
     * 订单审核人
     */
    private String auditName;

    /**
     * 下单时间
     */
    private Date readyDate;

    /**
     * 备注
     */
    private String description;

    /**
     * 是否需要加工
     */
    private Boolean isNeedProcess;

    /**
     * 是否包含PACK商品
     */
    private Boolean hasPack;

    /**
     * 是否快进快出
     */
    private Boolean fifo;

    /**
     * 是否跨仓
     */
    private Integer multipleSpace;

    /**
     * 是否网红/赠品
     */
    private Integer isGift;

    /**
     * 转运单号
     */
    private String secondaryNumber;


    /**
     * pdd订单来源方式 1-导入 2-抓取
     */
    private Integer originType;

    /**
     * 订单类型 SampleOrder：样品订单
     */
    private String orderType;

    /**
     * 亚马逊购买服务:None：未设置；Enable：已设置；Disable：取消设置
     */
    private String amazonBuyShippingInfo;

    /**
     * FBA箱贴状态（待申请INIT，申请中APPLYING，申请完成COMPLETE, 申请异常EXCEPTION）
     */
    private String fbaLabelStatus;

    // 强制建单类型 2：先装箱后分仓 3：先分仓后装箱
    private Integer forceDockingType;

    /**
     * 包装规格类型:标准件/大号件
     */
    private String productSizeSegment;

    // fba补货类型 直接创建/发货计划/先分仓后装箱
    private String fbaReplenishType;

    private String replenishOrder;

    public Integer getForceDockingType() {
        return forceDockingType;
    }

    public void setForceDockingType(Integer forceDockingType) {
        this.forceDockingType = forceDockingType;
    }

    public String getReplenishOrder() {
        return replenishOrder;
    }

    public void setReplenishOrder(String replenishOrder) {
        this.replenishOrder = replenishOrder;
    }

    public String getFbaReplenishType() {
        return fbaReplenishType;
    }

    public void setFbaReplenishType(String fbaReplenishType) {
        this.fbaReplenishType = fbaReplenishType;
    }

    public String getProductSizeSegment() {
        return productSizeSegment;
    }

    public void setProductSizeSegment(String productSizeSegment) {
        this.productSizeSegment = productSizeSegment;
    }

    public Boolean getFbaReplenish() {
        return isFbaReplenish;
    }

    public void setFbaReplenish(Boolean fbaReplenish) {
        isFbaReplenish = fbaReplenish;
    }

    public String getFbaLabelStatus() {
        return fbaLabelStatus;
    }

    public void setFbaLabelStatus(String fbaLabelStatus) {
        this.fbaLabelStatus = fbaLabelStatus;
    }

    public String getSecondaryNumber() {
        return secondaryNumber;
    }

    public void setSecondaryNumber(String secondaryNumber) {
        this.secondaryNumber = secondaryNumber;
    }

    public Integer getMultipleSpace() {
        return multipleSpace;
    }

    public void setMultipleSpace(Integer multipleSpace) {
        this.multipleSpace = multipleSpace;
    }

    public Boolean getFifo() {
        return fifo;
    }

    public void setFifo(Boolean fifo) {
        this.fifo = fifo;
    }

    public Integer getStockoutOrderId() {
        return stockoutOrderId;
    }

    public void setStockoutOrderId(Integer stockoutOrderId) {
        this.stockoutOrderId = stockoutOrderId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public BigDecimal getEstimateWeight() {
        return estimateWeight;
    }

    public void setEstimateWeight(BigDecimal estimateWeight) {
        this.estimateWeight = estimateWeight;
    }

    public Boolean getUrgent() {
        return isUrgent;
    }

    public void setUrgent(Boolean urgent) {
        isUrgent = urgent;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getLatestDeliveryDate() {
        return latestDeliveryDate;
    }

    public void setLatestDeliveryDate(Date latestDeliveryDate) {
        this.latestDeliveryDate = latestDeliveryDate;
    }

    public String getStockoutType() {
        return stockoutType;
    }

    public void setStockoutType(String stockoutType) {
        this.stockoutType = stockoutType;
    }

    public String getNotifyShipStatus() {
        return notifyShipStatus;
    }

    public void setNotifyShipStatus(String notifyShipStatus) {
        this.notifyShipStatus = notifyShipStatus;
    }

    public Boolean getLack() {
        return isLack;
    }

    public void setLack(Boolean lack) {
        isLack = lack;
    }

    public String getPickingType() {
        return pickingType;
    }

    public void setPickingType(String pickingType) {
        this.pickingType = pickingType;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }

    public String getMergeState() {
        return mergeState;
    }

    public void setMergeState(String mergeState) {
        this.mergeState = mergeState;
    }

    public Integer getMergeStockoutOrderId() {
        return mergeStockoutOrderId;
    }

    public void setMergeStockoutOrderId(Integer mergeStockoutOrderId) {
        this.mergeStockoutOrderId = mergeStockoutOrderId;
    }

    public String getCustomsDeclareType() {
        return customsDeclareType;
    }

    public void setCustomsDeclareType(String customsDeclareType) {
        this.customsDeclareType = customsDeclareType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getSaler() {
        return saler;
    }

    public void setSaler(String saler) {
        this.saler = saler;
    }

    public Date getReadyDate() {
        return readyDate;
    }

    public void setReadyDate(Date readyDate) {
        this.readyDate = readyDate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getNeedProcess() {
        return isNeedProcess;
    }

    public void setNeedProcess(Boolean needProcess) {
        isNeedProcess = needProcess;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getLogisticsLabelSize() {
        return logisticsLabelSize;
    }

    public void setLogisticsLabelSize(String logisticsLabelSize) {
        this.logisticsLabelSize = logisticsLabelSize;
    }

    public Integer getErpPickId() {
        return erpPickId;
    }

    public void setErpPickId(Integer erpPickId) {
        this.erpPickId = erpPickId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Boolean getHasPack() {
        return hasPack;
    }

    public void setHasPack(Boolean hasPack) {
        this.hasPack = hasPack;
    }

    public Integer getIsGift() {
        return isGift;
    }

    public void setIsGift(Integer isGift) {
        this.isGift = isGift;
    }

    public String getAuditName() {
        return auditName;
    }

    public void setAuditName(String auditName) {
        this.auditName = auditName;
    }

    public Integer getDesSpaceId() {
        return desSpaceId;
    }

    public void setDesSpaceId(Integer desSpaceId) {
        this.desSpaceId = desSpaceId;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getSalerUserName() {
        return salerUserName;
    }

    public void setSalerUserName(String salerUserName) {
        this.salerUserName = salerUserName;
    }

    public Integer getOriginType() {
        return originType;
    }

    public void setOriginType(Integer originType) {
        this.originType = originType;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getAmazonBuyShippingInfo() {
        return amazonBuyShippingInfo;
    }

    public void setAmazonBuyShippingInfo(String amazonBuyShippingInfo) {
        this.amazonBuyShippingInfo = amazonBuyShippingInfo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }
}
