package com.nsy.wms.repository.jpa.mapper.qa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.api.wms.response.qa.BdQaFullInspectRuleProductItemInfoResponse;
import com.nsy.wms.repository.entity.qa.BdQaFullInspectRuleProductItemEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-04 15:32
 */
@org.apache.ibatis.annotations.Mapper
public interface BdQaFullInspectRuleProductItemMapper extends BaseMapper<BdQaFullInspectRuleProductItemEntity> {
    
    List<BdQaFullInspectRuleProductItemInfoResponse> getProductInfoList(Integer ruleId);
}