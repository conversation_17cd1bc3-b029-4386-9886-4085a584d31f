package com.nsy.wms.repository.entity.stockout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/10/16 9:19
 */
@Entity
@Table(name = "stockout_qc_inbounds_attach")
@TableName("stockout_qc_inbounds_attach")
public class StockoutQcInboundsAttachEntity extends BaseMpEntity {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    /**
     * 全检明细id
     */
    private Integer stockoutQcInboundsItemId;

    /**
     * 文件名
     */
    private String originName;

    /**
     * 文件路径
     */
    private String name;

    /**
     * 访问路径
     */
    private String url;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockoutQcInboundsItemId() {
        return stockoutQcInboundsItemId;
    }

    public void setStockoutQcInboundsItemId(Integer stockoutQcInboundsItemId) {
        this.stockoutQcInboundsItemId = stockoutQcInboundsItemId;
    }

    public String getOriginName() {
        return originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
