package com.nsy.wms.repository.entity.stockout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
/**
 * FBA出库批次(StockoutLogisticsBatch)实体类
 *
 * <AUTHOR>
 * @since 2023-03-27 09:50:21
 */
@Entity
@Table(name = "stockout_logistics_batch")
@TableName("stockout_logistics_batch")
public class StockoutLogisticsBatchEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    /**
     * referenceId
     */
    private Integer referenceId;

    /**
     * 物流追踪波次
     */
    private String logisticsBatch;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 状态(待处理WAIT_DEAL、核对不通过DIFFERENCE、核对完成COMPLETION)
     */
    private String status;

    /**
     * 差异类型(1待差异判断，2无差异，3重量差异，4单价差异，5重量和单价差异)
     */
    private Integer differenceType;

    // 仓库id
    private String destinationFulfillmentCenterId;

    /**
     * 价格id
     */
    private Integer predictPriceId;

    // 预估单价
    private BigDecimal predictUnitPrice;

    // 预估总价
    private BigDecimal predictTotalPrice;

    private BigDecimal predictWeight;

    private BigDecimal chargeWeight;
    /**
     * 是否当天(1是 0否)
     */
    private Boolean exactDay;

    /**
     * 实际重量
     */
    private BigDecimal realWeight;

    /**
     * 实际单价
     */
    private BigDecimal realUnitPrice;

    /**
     * 实际总价
     */
    private BigDecimal realPrice;

    /**
     * 税费
     */
    private BigDecimal realTax;

    /**
     * 报关费用
     */
    private BigDecimal realDeclarePrice;

    /**
     * 核对结果(1核对通过，0核对不通过)
     */
    private Integer reviewResult;
    /**
     * 财务推送状态(0-未推送,1-已推送,2-已二次推送)
     */
    private Integer financialPushStatus;

    /**
     * 核对文件id
     */
    private Integer checkDocumentId;

    /**
     * 附件地址
     */
    private String memoDocumentUrl;


    // 运费差异
    private BigDecimal diffPrice;

    // 重量差异
    private BigDecimal diffWeight;

    /**
     * 偏远等级（空标识 非偏远地区）
     */
    private String extendAreaLevel;

    /**
     * 偏远费
     */
    private BigDecimal estimateRemoteAreaFee;


    /**
     * 税费
     */
    private BigDecimal estimateTax;


    public String getExtendAreaLevel() {
        return extendAreaLevel;
    }

    public void setExtendAreaLevel(String extendAreaLevel) {
        this.extendAreaLevel = extendAreaLevel;
    }

    public BigDecimal getEstimateRemoteAreaFee() {
        return estimateRemoteAreaFee;
    }

    public void setEstimateRemoteAreaFee(BigDecimal estimateRemoteAreaFee) {
        this.estimateRemoteAreaFee = estimateRemoteAreaFee;
    }

    public BigDecimal getEstimateTax() {
        return estimateTax;
    }

    public void setEstimateTax(BigDecimal estimateTax) {
        this.estimateTax = estimateTax;
    }

    public Integer getReviewResult() {
        return reviewResult;
    }

    public void setReviewResult(Integer reviewResult) {
        this.reviewResult = reviewResult;
    }

    public Integer getFinancialPushStatus() {
        return financialPushStatus;
    }

    public void setFinancialPushStatus(Integer financialPushStatus) {
        this.financialPushStatus = financialPushStatus;
    }

    public Integer getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(Integer referenceId) {
        this.referenceId = referenceId;
    }

    public BigDecimal getChargeWeight() {
        return chargeWeight;
    }

    public void setChargeWeight(BigDecimal chargeWeight) {
        this.chargeWeight = chargeWeight;
    }

    public String getDestinationFulfillmentCenterId() {
        return destinationFulfillmentCenterId;
    }

    public void setDestinationFulfillmentCenterId(String destinationFulfillmentCenterId) {
        this.destinationFulfillmentCenterId = destinationFulfillmentCenterId;
    }

    public BigDecimal getPredictTotalPrice() {
        return predictTotalPrice;
    }

    public void setPredictTotalPrice(BigDecimal predictTotalPrice) {
        this.predictTotalPrice = predictTotalPrice;
    }

    public BigDecimal getDiffPrice() {
        return diffPrice;
    }

    public void setDiffPrice(BigDecimal diffPrice) {
        this.diffPrice = diffPrice;
    }

    public BigDecimal getDiffWeight() {
        return diffWeight;
    }

    public void setDiffWeight(BigDecimal diffWeight) {
        this.diffWeight = diffWeight;
    }

    public BigDecimal getPredictWeight() {
        return predictWeight;
    }

    public void setPredictWeight(BigDecimal predictWeight) {
        this.predictWeight = predictWeight;
    }

    public BigDecimal getPredictUnitPrice() {
        return predictUnitPrice;
    }

    public void setPredictUnitPrice(BigDecimal predictUnitPrice) {
        this.predictUnitPrice = predictUnitPrice;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLogisticsBatch() {
        return logisticsBatch;
    }

    public void setLogisticsBatch(String logisticsBatch) {
        this.logisticsBatch = logisticsBatch;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getDifferenceType() {
        return differenceType;
    }

    public void setDifferenceType(Integer differenceType) {
        this.differenceType = differenceType;
    }

    public Integer getPredictPriceId() {
        return predictPriceId;
    }

    public void setPredictPriceId(Integer predictPriceId) {
        this.predictPriceId = predictPriceId;
    }

    public Boolean getExactDay() {
        return exactDay;
    }

    public void setExactDay(Boolean exactDay) {
        this.exactDay = exactDay;
    }

    public BigDecimal getRealWeight() {
        return realWeight;
    }

    public void setRealWeight(BigDecimal realWeight) {
        this.realWeight = realWeight;
    }

    public BigDecimal getRealUnitPrice() {
        return realUnitPrice;
    }

    public void setRealUnitPrice(BigDecimal realUnitPrice) {
        this.realUnitPrice = realUnitPrice;
    }

    public BigDecimal getRealPrice() {
        return realPrice;
    }

    public void setRealPrice(BigDecimal realPrice) {
        this.realPrice = realPrice;
    }

    public BigDecimal getRealTax() {
        return realTax;
    }

    public void setRealTax(BigDecimal realTax) {
        this.realTax = realTax;
    }

    public BigDecimal getRealDeclarePrice() {
        return realDeclarePrice;
    }

    public void setRealDeclarePrice(BigDecimal realDeclarePrice) {
        this.realDeclarePrice = realDeclarePrice;
    }

    public Integer getCheckDocumentId() {
        return checkDocumentId;
    }

    public void setCheckDocumentId(Integer checkDocumentId) {
        this.checkDocumentId = checkDocumentId;
    }

    public String getMemoDocumentUrl() {
        return memoDocumentUrl;
    }

    public void setMemoDocumentUrl(String memoDocumentUrl) {
        this.memoDocumentUrl = memoDocumentUrl;
    }


}

