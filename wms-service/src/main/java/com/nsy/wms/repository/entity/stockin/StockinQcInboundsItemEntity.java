package com.nsy.wms.repository.entity.stockin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "stockin_qc_inbounds_item")
@TableName("stockin_qc_inbounds_item")
public class StockinQcInboundsItemEntity extends BaseMpEntity {

    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    private Integer stockinQcInboundsId;

    /**
     * 让步接收数
     */
    private Integer concessionsCount;

    private BigDecimal concessionPrice;

    private Integer arrivalCount;

    /**
     * 退货数量
     */
    private Integer returnQty;

    /**
     * 接收单号
     */
    private String receiveOrderNo;

    /**
     * 入库单id
     */
    private Integer stockinOrderId;

    private Integer stockinOrderItemId;

    /**
     * 采购单号
     */
    private String purchasePlanNo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockinQcInboundsId() {
        return stockinQcInboundsId;
    }

    public void setStockinQcInboundsId(Integer stockinQcInboundsId) {
        this.stockinQcInboundsId = stockinQcInboundsId;
    }

    public Integer getConcessionsCount() {
        return concessionsCount;
    }

    public void setConcessionsCount(Integer concessionsCount) {
        this.concessionsCount = concessionsCount;
    }

    public Integer getArrivalCount() {
        return arrivalCount;
    }

    public void setArrivalCount(Integer arrivalCount) {
        this.arrivalCount = arrivalCount;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public String getReceiveOrderNo() {
        return receiveOrderNo;
    }

    public void setReceiveOrderNo(String receiveOrderNo) {
        this.receiveOrderNo = receiveOrderNo;
    }

    public Integer getStockinOrderId() {
        return stockinOrderId;
    }

    public void setStockinOrderId(Integer stockinOrderId) {
        this.stockinOrderId = stockinOrderId;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public BigDecimal getConcessionPrice() {
        return concessionPrice;
    }

    public void setConcessionPrice(BigDecimal concessionPrice) {
        this.concessionPrice = concessionPrice;
    }

    public Integer getStockinOrderItemId() {
        return stockinOrderItemId;
    }

    public void setStockinOrderItemId(Integer stockinOrderItemId) {
        this.stockinOrderItemId = stockinOrderItemId;
    }
}
