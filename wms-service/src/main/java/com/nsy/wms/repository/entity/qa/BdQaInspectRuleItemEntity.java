
package com.nsy.wms.repository.entity.qa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 质检稽查规则详情
 *
 * <AUTHOR>
 * @Date 2025-02-24 10:34
 */
@Entity
@TableName("bd_qa_inspect_rule_item")
@Table(name = "bd_qa_inspect_rule_item")
public class BdQaInspectRuleItemEntity extends BaseMpEntity {


    /**
     * Id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 区域
     */
    private String location;
    /**
     * 质检规则ID
     */
    private Integer bdQaInspectRuleId;
    /**
     * 配置类型
     */
    private String itemName;
    /**
     * 配置值
     */
    private String itemValue;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getBdQaInspectRuleId() {
        return bdQaInspectRuleId;
    }

    public void setBdQaInspectRuleId(Integer bdQaInspectRuleId) {
        this.bdQaInspectRuleId = bdQaInspectRuleId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }
}
