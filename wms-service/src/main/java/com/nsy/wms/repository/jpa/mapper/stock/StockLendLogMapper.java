package com.nsy.wms.repository.jpa.mapper.stock;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.stock.StockLendLog;
import com.nsy.wms.repository.entity.stock.StockLendLogEntity;
import com.nsy.api.wms.request.stock.StockLendLogListRequest;
import org.apache.ibatis.annotations.Param;

public interface StockLendLogMapper extends BaseMapper<StockLendLogEntity> {
    IPage<StockLendLog> pageSearch(IPage iPage, @Param("query") StockLendLogListRequest request);
}
