package com.nsy.wms.repository.entity.stockin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "stockin_return_product_task_item")
@TableName("stockin_return_product_task_item")
public class StockinReturnProductTaskItemEntity extends BaseMpEntity {
    /**
     * 唯一标识
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 退货任务id
     */
    private Integer returnProductTaskId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品规格id
     */
    private Integer specId;

    /**
     * 商品编码
     */
    private String sku;

    /**
     * 库位id
     */
    private Integer positionId;

    /**
     * 库位编码
     */
    private String positionCode;

    /**
     * 工厂出库单号
     */
    private String supplierDeliveryNo;

    /**
     * 采购单号
     */
    private String purchasePlanNo;

    /**
     * already_generate: 已生成,returning: 退货中,return_success: 退货完成
     */
    private String status;

    /**
     * 待退货数
     */
    private Integer waitReturnQty;

    /**
     * 真实退货数（扫描数）
     */
    private Integer actualReturnQty;

    /**
     * 缺货数
     */
    private Integer lackProductQty;

    /**
     * 地区
     */
    private String location;

    /**
     * 退货原因
     */
    private String unqualifiedReason;

    private String unqualifiedCategory;

    /**
     * 出库箱码
     */
    private String supplierDeliveryBoxCode;

    /**
     * 入库单号
     */
    private String stockinOrderNo;

    private String reworkStatus;

    //品牌名
    private String brandName;

    private Integer sourceAreaId;

    //包装方式
    private String packingMethod;

    //版本号
    private String versionNo;

    public String getReworkStatus() {
        return reworkStatus;
    }

    public void setReworkStatus(String reworkStatus) {
        this.reworkStatus = reworkStatus;
    }

    /**
     * 唯一标识
     */
    public Integer getId() {
        return id;
    }

    /**
     * 唯一标识
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 退货任务id
     */
    public Integer getReturnProductTaskId() {
        return returnProductTaskId;
    }

    /**
     * 退货任务id
     */
    public void setReturnProductTaskId(Integer returnProductTaskId) {
        this.returnProductTaskId = returnProductTaskId;
    }

    /**
     * 商品id
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * 商品id
     */
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * 商品规格id
     */
    public Integer getSpecId() {
        return specId;
    }

    /**
     * 商品规格id
     */
    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    /**
     * 商品编码
     */
    public String getSku() {
        return sku;
    }

    /**
     * 商品编码
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * 库位id
     */
    public Integer getPositionId() {
        return positionId;
    }

    /**
     * 库位id
     */
    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    /**
     * 库位编码
     */
    public String getPositionCode() {
        return positionCode;
    }

    /**
     * 库位编码
     */
    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getSupplierDeliveryNo() {
        return supplierDeliveryNo;
    }

    public void setSupplierDeliveryNo(String supplierDeliveryNo) {
        this.supplierDeliveryNo = supplierDeliveryNo;
    }

    /**
     * 待退货数
     */
    public Integer getWaitReturnQty() {
        return waitReturnQty;
    }

    /**
     * 待退货数
     */
    public void setWaitReturnQty(Integer waitReturnQty) {
        this.waitReturnQty = waitReturnQty;
    }

    /**
     * 真实退货数（扫描数）
     */
    public Integer getActualReturnQty() {
        return actualReturnQty;
    }

    /**
     * 真实退货数（扫描数）
     */
    public void setActualReturnQty(Integer actualReturnQty) {
        this.actualReturnQty = actualReturnQty;
    }

    /**
     * 缺货数
     */
    public Integer getLackProductQty() {
        return lackProductQty;
    }

    /**
     * 缺货数
     */
    public void setLackProductQty(Integer lackProductQty) {
        this.lackProductQty = lackProductQty;
    }

    /**
     * 地区
     */
    public String getLocation() {
        return location;
    }

    /**
     * 地区
     */
    public void setLocation(String location) {
        this.location = location;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUnqualifiedReason() {
        return unqualifiedReason;
    }

    public void setUnqualifiedReason(String unqualifiedReason) {
        this.unqualifiedReason = unqualifiedReason;
    }

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public String getStockinOrderNo() {
        return stockinOrderNo;
    }

    public void setStockinOrderNo(String stockinOrderNo) {
        this.stockinOrderNo = stockinOrderNo;
    }

    public String getUnqualifiedCategory() {
        return unqualifiedCategory;
    }

    public void setUnqualifiedCategory(String unqualifiedCategory) {
        this.unqualifiedCategory = unqualifiedCategory;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Integer getSourceAreaId() {
        return sourceAreaId;
    }

    public void setSourceAreaId(Integer sourceAreaId) {
        this.sourceAreaId = sourceAreaId;
    }

    public String getPackingMethod() {
        return packingMethod;
    }

    public void setPackingMethod(String packingMethod) {
        this.packingMethod = packingMethod;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

}
