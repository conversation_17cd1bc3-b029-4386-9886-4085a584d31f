package com.nsy.wms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 补货任务
 *
 * @TableName stock_replenishment_task
 */
@Entity
@Table(name = "stock_replenishment_task")
@TableName("stock_replenishment_task")
public class StockReplenishmentTaskEntity extends BaseMpEntity {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    /**
     * 商品规格
     */
    private String sku;

    /**
     * 状态
     */
    private String status;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private Date operateDate;

    /**
     * 需补货库位id
     */
    private Integer toPositionId;

    /**
     * 需补货库位编码
     */
    private String toPositionCode;

    /**
     * 补货库位Id
     */
    private Integer fromPositionId;

    /**
     * 补货库位编码
     */
    private String fromPositionCode;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public Integer getToPositionId() {
        return toPositionId;
    }

    public void setToPositionId(Integer toPositionId) {
        this.toPositionId = toPositionId;
    }

    public String getToPositionCode() {
        return toPositionCode;
    }

    public void setToPositionCode(String toPositionCode) {
        this.toPositionCode = toPositionCode;
    }

    public Integer getFromPositionId() {
        return fromPositionId;
    }

    public void setFromPositionId(Integer fromPositionId) {
        this.fromPositionId = fromPositionId;
    }

    public String getFromPositionCode() {
        return fromPositionCode;
    }

    public void setFromPositionCode(String fromPositionCode) {
        this.fromPositionCode = fromPositionCode;
    }
}
