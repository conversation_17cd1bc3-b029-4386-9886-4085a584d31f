package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.stockout.StockoutQcInboundsDetailPageRequest;
import com.nsy.api.wms.request.stockout.StockoutQcInboundsPageRequest;
import com.nsy.api.wms.response.stockout.StockoutQcInboundsDetailExportResponse;
import com.nsy.api.wms.response.stockout.StockoutQcInboundsDetailInfo;
import com.nsy.wms.repository.entity.stockout.StockoutQcInboundsItemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/10/16 9:27
 */
@Mapper
public interface StockoutQcInboundsItemMapper extends BaseMapper<StockoutQcInboundsItemEntity> {

    /**
     * 明细分页信息
     * @param page
     * @param request
     * @return
     */
    IPage<StockoutQcInboundsDetailInfo> getDetailInfo(IPage page, @Param("query") StockoutQcInboundsDetailPageRequest request);

    /**
     * 分页导出数据
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockoutQcInboundsDetailExportResponse> pageDetailInfo(IPage page, @Param("query") StockoutQcInboundsPageRequest request);
}
