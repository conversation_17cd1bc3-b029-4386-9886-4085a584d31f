package com.nsy.wms.repository.jpa.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.product.ProductTryOnTaskPageResponse;
import com.nsy.api.wms.request.product.ProductTryOnTaskPageRequest;
import com.nsy.wms.repository.entity.product.ProductTryOnTaskEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProductTryOnTaskMapper extends BaseMapper<ProductTryOnTaskEntity> {

    /**
     * 分页查询
     * @param page
     * @param request
     * @return
     */
    IPage<ProductTryOnTaskPageResponse> pageList(IPage<ProductTryOnTaskPageRequest> page, @Param("request") ProductTryOnTaskPageRequest request);
}
