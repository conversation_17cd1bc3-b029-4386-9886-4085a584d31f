package com.nsy.wms.repository.jpa.mapper.stockin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.stockin.StockinShelveSplitTaskInfo;
import com.nsy.wms.repository.entity.stockin.StockinShelveSplitTaskEntity;
import com.nsy.api.wms.request.stockin.StockinShelveSplitTaskListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
public interface StockinShelveSplitTaskMapper extends BaseMapper<StockinShelveSplitTaskEntity> {

    IPage<StockinShelveSplitTaskInfo> searchShelveSplitTaskList(IPage page, @Param("query") StockinShelveSplitTaskListRequest request);

    List<Integer> statusCount(@Param("query") StockinShelveSplitTaskListRequest request);
}
