package com.nsy.wms.repository.jpa.mapper.stockin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingDetailRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingPageRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingSkuPageRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingValidRequest;
import com.nsy.api.wms.response.stockin.StockinHeightRecordMappingExportResponse;
import com.nsy.api.wms.response.stockin.StockinHeightRecordMappingPageDetailResponse;
import com.nsy.api.wms.response.stockin.StockinHeightRecordMappingResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingDetailResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingExportResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageDetailResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingSkuPageResponse;
import com.nsy.wms.repository.entity.stockin.StockinVolumeWeightRecordMappingEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-04 9:52
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinVolumeWeightRecordMappingMapper extends BaseMapper<StockinVolumeWeightRecordMappingEntity> {

    /**
     * 是否存在对应记录
     *
     * @param request
     * @return
     */
    int countRecordMapping(@Param("request") StockinVolumeWeightRecordMappingValidRequest request);

    /**
     * 查询信息列表 -- 测量体积重
     *
     * @param request
     * @return
     */
    List<StockinVolumeWeightRecordMappingResponse> getVolumeWeightRecordMappingList(@Param("request") StockinVolumeWeightRecordMappingRequest request);

    /**
     * 分页查询信息列表
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinVolumeWeightRecordMappingPageResponse> pageList(IPage page, @Param("request") StockinVolumeWeightRecordMappingPageRequest request);

    /**
     * 分页查询信息列表 --sku条件下
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinVolumeWeightRecordMappingSkuPageResponse> pageSkuList(IPage page, @Param("request") StockinVolumeWeightRecordMappingSkuPageRequest request);

    /**
     * 分页查询信息列表 --测量详情
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinVolumeWeightRecordMappingPageDetailResponse> pageDetailList(IPage page, @Param("request") StockinVolumeWeightRecordMappingDetailRequest request);

    /**
     * 获取分页总数
     *
     * @param request
     * @return
     */
    Integer getPageCount(@Param("request") StockinVolumeWeightRecordMappingPageRequest request);

    /**
     * 获取分页总数在sku条件下
     *
     * @param request
     * @return
     */
    Integer getPageSkuCount(@Param("request") StockinVolumeWeightRecordMappingSkuPageRequest request);

    /**
     * 获取详情
     *
     * @param request
     * @return
     */
    StockinVolumeWeightRecordMappingDetailResponse getdetail(@Param("request") StockinVolumeWeightRecordMappingDetailRequest request);

    /**
     * 分页信息导出
     *
     * @param page
     * @param downloadRequest
     * @return
     */
    IPage<StockinVolumeWeightRecordMappingExportResponse> getPageExportList(IPage page, @Param("request") StockinVolumeWeightRecordMappingPageRequest downloadRequest);

    /**
     * 查询是否测量过
     *
     * @param request
     * @return
     */
    int countScanInfo(@Param("request") StockinVolumeWeightRecordMappingRequest request);

    /**
     * 查询已经测量过的记录信息
     *
     * @param supplierDeliveryNoList
     * @param sku
     * @param internalBoxCode
     * @param measureType
     * @return
     */
    List<Integer> getExistRecordInfo(@Param("supplierDeliveryNoList") List<String> supplierDeliveryNoList, @Param("sku") String sku,
                                     @Param("internalBoxCode") String internalBoxCode, @Param("measureType") String measureType);

    /**
     * 获取高度测量信息
     *
     * @param request
     * @return
     */
    List<StockinHeightRecordMappingResponse> getHeightRecordMappingList(@Param("request") StockinVolumeWeightRecordMappingRequest request);

    /**
     * 分页查询信息列表 --sku条件下
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinVolumeWeightRecordMappingSkuPageResponse> heightPageSkuList(IPage page, @Param("request") StockinVolumeWeightRecordMappingSkuPageRequest request);

    /**
     * 分页查询信息列表 --测量详情
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockinHeightRecordMappingPageDetailResponse> heightPageDetailList(IPage page, @Param("request") StockinVolumeWeightRecordMappingDetailRequest request);

    /**
     * 高度详情分页信息导出
     *
     * @param page
     * @param downloadRequest
     * @return
     */
    IPage<StockinHeightRecordMappingExportResponse> getHeightPageExportList(IPage page, @Param("request") StockinVolumeWeightRecordMappingPageRequest downloadRequest);
}
