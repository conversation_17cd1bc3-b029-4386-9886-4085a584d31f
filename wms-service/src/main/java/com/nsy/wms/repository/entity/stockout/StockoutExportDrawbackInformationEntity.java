package com.nsy.wms.repository.entity.stockout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/22
 */

@Entity
@Table(name = "stockout_export_drawback_information")
@TableName("stockout_export_drawback_information")
public class StockoutExportDrawbackInformationEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer stockoutExportDrawbackInformationId;

    /**
     * 退税资料类型(非退税资料默认0)
     */
    private Integer informationType;

    /**
     * 退税资料关键字
     */
    private String informationKey;

    /**
     * 文件链接
     */
    private String fileUrl;
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 地区
     */
    private String location;

    public Integer getStockoutExportDrawbackInformationId() {
        return stockoutExportDrawbackInformationId;
    }

    public void setStockoutExportDrawbackInformationId(Integer stockoutExportDrawbackInformationId) {
        this.stockoutExportDrawbackInformationId = stockoutExportDrawbackInformationId;
    }


    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getInformationType() {
        return informationType;
    }

    public void setInformationType(Integer informationType) {
        this.informationType = informationType;
    }

    public String getInformationKey() {
        return informationKey;
    }

    public void setInformationKey(String informationKey) {
        this.informationKey = informationKey;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
