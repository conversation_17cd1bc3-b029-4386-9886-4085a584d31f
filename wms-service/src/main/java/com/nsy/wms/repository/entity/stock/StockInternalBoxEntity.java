package com.nsy.wms.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@TableName("stock_internal_box")
@Entity
@Table(name = "stock_internal_box")
public class StockInternalBoxEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer internalBoxId;

    /**
     * 地区
     */
    private String location;

    /**
     * 仓库Id
     */
    private Integer spaceId;

    /**
     * 编码
     */
    private String internalBoxCode;

    /**
     * 类型
     */
    private String internalBoxType;

    /**
     * 内部箱物料规格
     */
    private String internalBoxMaterialSize;

    /**
     * 工作区域
     */
    private String workspace;

    /**
     * 库区名称
     */
    private String spaceAreaName;

    /**
     * 库区id
     */
    private Integer spaceAreaId;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否打印(1为是0为否)
     */
    private Integer isPrint;

    /**
     * 是否已删除
     */
    private Integer isDeleted;


    public Integer getInternalBoxId() {
        return internalBoxId;
    }

    public void setInternalBoxId(Integer internalBoxId) {
        this.internalBoxId = internalBoxId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getInternalBoxType() {
        return internalBoxType;
    }

    public void setInternalBoxType(String internalBoxType) {
        this.internalBoxType = internalBoxType;
    }

    public String getInternalBoxMaterialSize() {
        return internalBoxMaterialSize;
    }

    public void setInternalBoxMaterialSize(String internalBoxMaterialSize) {
        this.internalBoxMaterialSize = internalBoxMaterialSize;
    }

    public Integer getSpaceAreaId() {
        return spaceAreaId;
    }

    public void setSpaceAreaId(Integer spaceAreaId) {
        this.spaceAreaId = spaceAreaId;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIsPrint() {
        return isPrint;
    }

    public void setIsPrint(Integer isPrint) {
        this.isPrint = isPrint;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }
}
