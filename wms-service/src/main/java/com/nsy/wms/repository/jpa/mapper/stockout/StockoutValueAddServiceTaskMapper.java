package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.stockout.StockoutVasTaskList;
import com.nsy.api.wms.request.stockout.StockoutVasTaskListRequest;
import com.nsy.api.wms.response.stockout.StockoutVasTaskCountResponse;
import com.nsy.wms.business.domain.dto.stockout.StockoutStorePositionVasTaskDTO;
import com.nsy.wms.repository.entity.stockout.StockoutValueAddServiceTaskEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StockoutValueAddServiceTaskMapper extends BaseMapper<StockoutValueAddServiceTaskEntity> {

    IPage<StockoutVasTaskList> pageSearchList(IPage page, @Param("query") StockoutVasTaskListRequest request);

    StockoutVasTaskCountResponse pageStatistics(@Param("query") StockoutVasTaskListRequest request);
    
    List<StockoutStorePositionVasTaskDTO> getVasTypeInfo(@Param("stockoutOrderNoList") List<String> stockoutOrderNoList);
}
