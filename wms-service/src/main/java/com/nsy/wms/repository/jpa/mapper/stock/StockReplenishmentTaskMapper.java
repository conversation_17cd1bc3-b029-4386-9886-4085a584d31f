package com.nsy.wms.repository.jpa.mapper.stock;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.stock.StockReplenishmentTaskPageRequest;
import com.nsy.api.wms.response.stock.StockReplenishmentTaskPageResponse;
import com.nsy.wms.repository.entity.bd.StockReplenishmentTaskEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface StockReplenishmentTaskMapper extends BaseMapper<StockReplenishmentTaskEntity> {

    /**
     * 列表
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockReplenishmentTaskPageResponse> searchPage(IPage page, @Param("query") StockReplenishmentTaskPageRequest request);
}
