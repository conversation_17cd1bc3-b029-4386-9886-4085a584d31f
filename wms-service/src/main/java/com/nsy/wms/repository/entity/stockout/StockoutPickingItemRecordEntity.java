
package com.nsy.wms.repository.entity.stockout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 拣货任务明细
 *
 * <AUTHOR>
 * @Date 2021-08-11 18:13
 */
@Entity
@Table(name = "stockout_picking_item_record")
@TableName("stockout_picking_item_record")
public class StockoutPickingItemRecordEntity extends BaseMpEntity {


    /**
     * PickingItemRecordId
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(value = "picking_item_record_id", type = IdType.AUTO)
    private Integer pickingItemRecordId;
    /**
     * 地区
     */
    private String location;
    /**
     * 拣货任务id
     */
    private Integer taskId;
    /**
     * 拣货任务明细id
     */
    private Integer taskItemId;
    private String spaceAreaName;
    /**
     * 商品id
     */
    private Integer productId;
    /**
     * 规格id
     */
    private Integer specId;
    /**
     * 规格编码
     */
    private String sku;
    /**
     * 商品条形码
     */
    private String barcode;
    /**
     * 内部箱号
     */
    private String internalBoxCode;
    /**
     * 已拣数量
     */
    private Integer pickedQty;

    public Integer getPickingItemRecordId() {
        return pickingItemRecordId;
    }

    public void setPickingItemRecordId(Integer pickingItemRecordId) {
        this.pickingItemRecordId = pickingItemRecordId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    public Integer getTaskItemId() {
        return taskItemId;
    }

    public void setTaskItemId(Integer taskItemId) {
        this.taskItemId = taskItemId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public Integer getPickedQty() {
        return pickedQty;
    }

    public void setPickedQty(Integer pickedQty) {
        this.pickedQty = pickedQty;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }
}
