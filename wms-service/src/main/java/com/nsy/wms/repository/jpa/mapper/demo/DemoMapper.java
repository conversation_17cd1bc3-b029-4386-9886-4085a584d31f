package com.nsy.wms.repository.jpa.mapper.demo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.wms.repository.entity.demo.DemoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DemoMapper extends BaseMapper<DemoEntity> {


    List<DemoEntity> searchDemo(IPage page, @Param("query") Map<String, String> queryMap);

    IPage<DemoEntity> pageSearchDemo(IPage page, @Param("query") Map<String, String> queryMap);
}
