package com.nsy.wms.repository.jpa.mapper.bd;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.bd.BdVolumeWeightRecordPageRequest;
import com.nsy.wms.repository.entity.bd.BdVolumeWeightRecordEntity;
import org.apache.ibatis.annotations.Param;

public interface BdVolumeWeightRecordMapper extends BaseMapper<BdVolumeWeightRecordEntity> {

    /**
     * 分页查询体积重称重记录，支持通过spuList关联ProductInfo表进行查询
     * MyBatis Plus会自动处理分页和count查询
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<BdVolumeWeightRecordEntity> selectPageWithSpu(@Param("page") Page<BdVolumeWeightRecordEntity> page, @Param("request") BdVolumeWeightRecordPageRequest request);

}
