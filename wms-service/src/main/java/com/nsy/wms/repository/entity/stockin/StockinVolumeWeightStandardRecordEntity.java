package com.nsy.wms.repository.entity.stockin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 质检测量时的工艺标准
 *
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-01-20 10:00
 */
@Entity
@Table(name = "stockin_volume_weight_standrd_record")
@TableName("stockin_volume_weight_standrd_record")
public class StockinVolumeWeightStandardRecordEntity extends BaseMpEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    /**
     * sku
     */
    private String sku;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 商品Id
     */
    private Integer productId;

    /**
     * 长度，单位：CM
     */
    private BigDecimal length;

    /**
     * 宽度，单位：CM
     */
    private BigDecimal width;

    /**
     * 高度，单位：CM
     */
    private BigDecimal height;

    /**
     * 重量，单位：G
     */
    private BigDecimal weight;

    /**
     * 长度，单位：英寸
     */
    private BigDecimal lengthInch;
    
    /**
     * 宽度，单位：英寸
     */
    private BigDecimal widthInch;

    /**
     * 高度，单位：英寸
     */
    private BigDecimal heightInch;

    /**
     * 重量，单位：磅
     */
    private BigDecimal weightPound;

    /**
     * 体积重(kg)
     */
    private BigDecimal volumeWeightKg;

    /**
     * 体积重(磅)
     */
    private BigDecimal volumeWeightPound;

    /**
     * FBA配送费
     */
    private BigDecimal fbaCost;

    /**
     * 计费重量(kg)
     */

    private BigDecimal chargedWeightKg;
    /**
     * 标准类型
     */
    private String standardType;
    /**
     * 计费重量(磅)
     */
    private BigDecimal chargedWeight;
    /**
     * 包装方式
     */
    private String packageName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public BigDecimal getLength() {
        return length;
    }

    public void setLength(BigDecimal length) {
        this.length = length;
    }

    public BigDecimal getWidth() {
        return width;
    }

    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    public BigDecimal getHeight() {
        return height;
    }

    public void setHeight(BigDecimal height) {
        this.height = height;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getLengthInch() {
        return lengthInch;
    }

    public void setLengthInch(BigDecimal lengthInch) {
        this.lengthInch = lengthInch;
    }

    public BigDecimal getWidthInch() {
        return widthInch;
    }

    public void setWidthInch(BigDecimal widthInch) {
        this.widthInch = widthInch;
    }

    public BigDecimal getHeightInch() {
        return heightInch;
    }

    public void setHeightInch(BigDecimal heightInch) {
        this.heightInch = heightInch;
    }

    public BigDecimal getWeightPound() {
        return weightPound;
    }

    public void setWeightPound(BigDecimal weightPound) {
        this.weightPound = weightPound;
    }

    public BigDecimal getVolumeWeightPound() {
        return volumeWeightPound;
    }

    public void setVolumeWeightPound(BigDecimal volumeWeightPound) {
        this.volumeWeightPound = volumeWeightPound;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public BigDecimal getChargedWeightKg() {
        return chargedWeightKg;
    }

    public void setChargedWeightKg(BigDecimal chargedWeightKg) {
        this.chargedWeightKg = chargedWeightKg;
    }

    public String getStandardType() {
        return standardType;
    }

    public void setStandardType(String standardType) {
        this.standardType = standardType;
    }

    public BigDecimal getChargedWeight() {
        return chargedWeight;
    }

    public void setChargedWeight(BigDecimal chargedWeight) {
        this.chargedWeight = chargedWeight;
    }

    public BigDecimal getVolumeWeightKg() {
        return volumeWeightKg;
    }

    public void setVolumeWeightKg(BigDecimal volumeWeightKg) {
        this.volumeWeightKg = volumeWeightKg;
    }
} 