package com.nsy.wms.repository.entity.bd;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 亚马逊物流配送费重量范围
 */
@Entity
@Table(name = "bd_volume_weight_range")
@TableName("bd_volume_weight_range")
public class BdVolumeWeightRangeEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;
    //标准类型
    private String standardType;
    //FBA配送费
    private BigDecimal fbaCost;
    //重量（磅）-- 最小值
    private BigDecimal minWeightPound;
    //重量（磅）-- 最大值
    private BigDecimal maxWeightPound;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getStandardType() {
        return standardType;
    }

    public void setStandardType(String standardType) {
        this.standardType = standardType;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public BigDecimal getMinWeightPound() {
        return minWeightPound;
    }

    public void setMinWeightPound(BigDecimal minWeightPound) {
        this.minWeightPound = minWeightPound;
    }

    public BigDecimal getMaxWeightPound() {
        return maxWeightPound;
    }

    public void setMaxWeightPound(BigDecimal maxWeightPound) {
        this.maxWeightPound = maxWeightPound;
    }
}
