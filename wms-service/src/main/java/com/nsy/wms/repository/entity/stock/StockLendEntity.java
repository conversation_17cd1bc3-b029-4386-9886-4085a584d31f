package com.nsy.wms.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@TableName("stock_lend")
@Entity
@Table(name = "stock_lend")
public class StockLendEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer lendId;

    private String location;

    /**
     * 借用单号
     */
    private String stockLendCode;

    /**
     * 仓库id
     */
    private Integer spaceId;

    /**
     * 仓库名称
     */
    private String spaceName;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 店铺id
     */
    private Integer storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 申请人账号
     */
    private String userName;

    /**
     * 部门
     */
    private String businessType;

    /**
     * 状态 待处理(WAIT_DEAL)待归还(WAIT_RETURN)已取消(CANCELLED)已归还(RETURNED)部分归还(PARTIAL_RETURN)无法归还(CANT_RETURN)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预计归还时间
     */
    private Date expectReturnDate;

    /**
     * 归还时间
     */
    private Date returnDate;

    /**
     * 超期取消时间
     */
    private Date overdueCancelDate;

    /**
     * 延期原因
     */
    private String delayReason;

    /**
     * 总价
     */
    private BigDecimal total;

    /**
     * 延期次数
     */
    private Integer delayTimes;

    /**
     * 延期时间
     */
    private Date delayDate;

    /**
     * 发货日期
     */
    private Date shipmentDate;

    public Integer getLendId() {
        return lendId;
    }

    public void setLendId(Integer lendId) {
        this.lendId = lendId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getStockLendCode() {
        return stockLendCode;
    }

    public void setStockLendCode(String stockLendCode) {
        this.stockLendCode = stockLendCode;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getExpectReturnDate() {
        return expectReturnDate;
    }

    public void setExpectReturnDate(Date expectReturnDate) {
        this.expectReturnDate = expectReturnDate;
    }

    public Date getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(Date returnDate) {
        this.returnDate = returnDate;
    }

    public Date getOverdueCancelDate() {
        return overdueCancelDate;
    }

    public void setOverdueCancelDate(Date overdueCancelDate) {
        this.overdueCancelDate = overdueCancelDate;
    }

    public String getDelayReason() {
        return delayReason;
    }

    public void setDelayReason(String delayReason) {
        this.delayReason = delayReason;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public Integer getDelayTimes() {
        return delayTimes;
    }

    public void setDelayTimes(Integer delayTimes) {
        this.delayTimes = delayTimes;
    }

    public Date getDelayDate() {
        return delayDate;
    }

    public void setDelayDate(Date delayDate) {
        this.delayDate = delayDate;
    }

    public Date getShipmentDate() {
        return shipmentDate;
    }

    public void setShipmentDate(Date shipmentDate) {
        this.shipmentDate = shipmentDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
}
