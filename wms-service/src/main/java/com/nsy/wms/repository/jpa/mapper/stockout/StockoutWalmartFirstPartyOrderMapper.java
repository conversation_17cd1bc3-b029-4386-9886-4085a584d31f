package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.stockout.StockoutWalmartFirstPartyOrderPageRequest;
import com.nsy.api.wms.request.stockout.StockoutWalmartFirstPartyOrderResponse;
import com.nsy.wms.repository.entity.stockout.StockoutWalmartFirstPartyOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 沃尔玛1p采购单(StockoutWalmartFirstPartyOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-20 15:39:01
 */
@Mapper
public interface StockoutWalmartFirstPartyOrderMapper extends BaseMapper<StockoutWalmartFirstPartyOrderEntity> {
    IPage<StockoutWalmartFirstPartyOrderResponse> getPage(Page<StockoutWalmartFirstPartyOrderResponse> page, @Param("request") StockoutWalmartFirstPartyOrderPageRequest request);
}

