package com.nsy.wms.repository.jpa.mapper.stockin;

import com.nsy.api.wms.request.stockin.StockinReturnProductOrderDownloadRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductOrderListRequest;
import com.nsy.api.wms.request.stockin.TabCountRequest;
import com.nsy.api.wms.response.stockin.StockinReturnProductOrderDownloadResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductOrderListResponse;
import com.nsy.api.wms.response.stockin.TaskListCountResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductOrderEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface StockinReturnProductOrderMapper extends BaseMapper<StockinReturnProductOrderEntity> {
    /**
     * 查询退货单列表（分页）
     */
    IPage<StockinReturnProductOrderListResponse> getOrderList(Page<StockinReturnProductOrderListResponse> page, @Param("request") StockinReturnProductOrderListRequest request);

    List<TaskListCountResponse> countByStatus(@Param("query") TabCountRequest request);

    IPage<StockinReturnProductOrderDownloadResponse> getOrderItemList(Page<StockinReturnProductOrderDownloadResponse> page, @Param("request") StockinReturnProductOrderDownloadRequest request);
}




