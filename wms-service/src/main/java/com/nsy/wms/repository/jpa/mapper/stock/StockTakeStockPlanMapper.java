package com.nsy.wms.repository.jpa.mapper.stock;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.wms.repository.entity.stock.StockTakeStockPlanEntity;
import com.nsy.api.wms.request.stock.StockTakeStockPlanRequest;
import com.nsy.api.wms.response.stock.StockTakeStockPlanListResponse;
import com.nsy.api.wms.response.stock.StockTakeStockPlanResponse;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface StockTakeStockPlanMapper extends BaseMapper<StockTakeStockPlanEntity> {
    /**
     * 分页查询
     * @param page
     * @param request
     * @return
     */
    IPage<StockTakeStockPlanResponse> pageList(Page<StockTakeStockPlanResponse> page, @Param("request") StockTakeStockPlanRequest request);

    /**
     * 查询盘点计划详情列表
     * @return
     */
    StockTakeStockPlanListResponse list(Integer planId);
}




