package com.nsy.wms.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "stock_difference_check_order_item")
@TableName("stock_difference_check_order_item")
public class StockDifferenceCheckOrderItemEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer checkOrderItemId;
    /**
     * 地区
     */
    private String location;
    /**
     * 差异核对单id
     */
    private Integer checkOrderId;
    /**
     * 仓库Id
     */
    private Integer spaceId;

    /**
     * 仓库名称
     */
    private String spaceName;

    /**
     * 区域Id
     */
    private Integer areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 库区Id
     */
    private Integer spaceAreaId;

    /**
     * 库区名称
     */
    private String spaceAreaName;

    /**
     * 商品Id
     */
    private Integer productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 规格id
     */
    private Integer specId;
    /**
     * sku
     */
    private String sku;
    /**
     * 库位id
     */
    private Integer positionId;

    /**
     * 库位名称
     */
    private String positionCode;

    /**
     * 确认数
     */
    private Integer confirmQty;

    public Integer getCheckOrderItemId() {
        return checkOrderItemId;
    }

    public void setCheckOrderItemId(Integer checkOrderItemId) {
        this.checkOrderItemId = checkOrderItemId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getCheckOrderId() {
        return checkOrderId;
    }

    public void setCheckOrderId(Integer checkOrderId) {
        this.checkOrderId = checkOrderId;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public Integer getConfirmQty() {
        return confirmQty;
    }

    public void setConfirmQty(Integer confirmQty) {
        this.confirmQty = confirmQty;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getSpaceAreaId() {
        return spaceAreaId;
    }

    public void setSpaceAreaId(Integer spaceAreaId) {
        this.spaceAreaId = spaceAreaId;
    }

    public String getSpaceAreaName() {
        return spaceAreaName;
    }

    public void setSpaceAreaName(String spaceAreaName) {
        this.spaceAreaName = spaceAreaName;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }
}
