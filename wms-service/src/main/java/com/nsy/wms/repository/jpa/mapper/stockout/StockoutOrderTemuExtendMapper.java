package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.stockout.StockoutTemuCancellingPageRequest;
import com.nsy.api.wms.request.stockout.StockoutTemuDeliveryOrderRequest;
import com.nsy.api.wms.response.stockout.StockoutTemuCancellingOrderResponse;
import com.nsy.api.wms.response.stockout.StockoutTemuDeliveryOrderResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutTemuNoUrgencyShiporderInfoBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutUnGetShiporderBo;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuExtendEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockoutOrderTemuExtendMapper extends BaseMapper<StockoutOrderTemuExtendEntity> {
    /**
     * 相同仓库的
     *
     * @param shortName
     * @return
     */
    List<String> getStockoutOrderNoBySameWarehouse(@Param("shortName") String shortName, @Param("storeName") String storeName);

    /**
     * 没有获取发货单
     *
     * @return
     */
    List<StockoutUnGetShiporderBo> getUnGetShiporderList(List<Integer> stockoutOrderIdList);

    /**
     * 查找未创建发货单的列表
     *
     * @param status
     * @return
     */
    List<StockoutOrderTemuExtendEntity> findNoCreateShiporderList(String status);

    /**
     * 非急单列表
     *
     * @return
     */
    List<StockoutTemuNoUrgencyShiporderInfoBo> findNoUrgencyShiporderInfoList(List<String> orderNoList);

    /**
     * 列表
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockoutTemuDeliveryOrderResponse> findDeliveryOrderList(IPage page, @Param("request") StockoutTemuDeliveryOrderRequest request);

    /**
     * 通过订单号查找
     *
     * @param orderNo
     * @return
     */
    StockoutOrderTemuExtendEntity findByOrderNo(String orderNo);

    /**
     * 超过三天为抢到仓库
     *
     * @return
     */
    List<StockoutOrderTemuExtendEntity> findNoWarehouseOverThreeDay();

    /**
     * 统计未抢占仓库数
     *
     * @return
     */
    Integer countNoSeizureWarehouse();

    /**
     * 查找已作废但是未取消的
     *
     * @param page
     * @return
     */
    IPage<StockoutTemuCancellingOrderResponse> findCancellingOrderList(IPage page, @Param("request") StockoutTemuCancellingPageRequest request);

    List<String> getReadyList(Boolean isGetLabel);

    /**
     * 获取待作废列表
     *
     * @return
     */
    List<String> getReadyCancelList();
}
