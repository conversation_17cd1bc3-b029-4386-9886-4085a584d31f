package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.stockout.LogisticsReceiveRecordPageRequest;
import com.nsy.api.wms.response.stockout.LogisticsReceiveRecordPageResponse;
import com.nsy.wms.repository.entity.stockout.LogisticsReceiveRecordEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/2/22 15:16
 */
@Mapper
public interface LogisticsReceiveRecordMapper extends BaseMapper<LogisticsReceiveRecordEntity> {

    IPage<LogisticsReceiveRecordPageResponse> pageList(IPage page, @Param("query") LogisticsReceiveRecordPageRequest request);
}
