package com.nsy.wms.repository.entity.stockin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "stockin_order_time")
@TableName("stockin_order_time")
public class StockinOrderTimeEntity extends BaseMpEntity {

    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;


    private Integer stockinOrderId;

    /**
     * 地区
     */
    private String location;

    private Date generateDate;
    private Date receivedDate;
    private Date startQcDate;
    private Date completeQcDate;
    private Date shelveDate;
    private Date completeShelvedDate;
    private Date checkedDate;
    private Date confirmDate;
    private Date completeDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getStockinOrderId() {
        return stockinOrderId;
    }

    public void setStockinOrderId(Integer stockinOrderId) {
        this.stockinOrderId = stockinOrderId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Date getGenerateDate() {
        return generateDate;
    }

    public void setGenerateDate(Date generateDate) {
        this.generateDate = generateDate;
    }

    public Date getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(Date receivedDate) {
        this.receivedDate = receivedDate;
    }

    public Date getStartQcDate() {
        return startQcDate;
    }

    public void setStartQcDate(Date startQcDate) {
        this.startQcDate = startQcDate;
    }

    public Date getCompleteQcDate() {
        return completeQcDate;
    }

    public void setCompleteQcDate(Date completeQcDate) {
        this.completeQcDate = completeQcDate;
    }

    public Date getShelveDate() {
        return shelveDate;
    }

    public void setShelveDate(Date shelveDate) {
        this.shelveDate = shelveDate;
    }

    public Date getCompleteShelvedDate() {
        return completeShelvedDate;
    }

    public void setCompleteShelvedDate(Date completeShelvedDate) {
        this.completeShelvedDate = completeShelvedDate;
    }

    public Date getCheckedDate() {
        return checkedDate;
    }

    public void setCheckedDate(Date checkedDate) {
        this.checkedDate = checkedDate;
    }

    public Date getConfirmDate() {
        return confirmDate;
    }

    public void setConfirmDate(Date confirmDate) {
        this.confirmDate = confirmDate;
    }

    public Date getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(Date completeDate) {
        this.completeDate = completeDate;
    }
}
