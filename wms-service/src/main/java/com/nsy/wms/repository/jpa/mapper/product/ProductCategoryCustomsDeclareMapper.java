package com.nsy.wms.repository.jpa.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.product.ProductCategoryCustomsDeclare;
import com.nsy.api.wms.request.product.ProductCategoryCustomsDeclareRequest;
import com.nsy.wms.repository.entity.product.ProductCategoryCustomsDeclareEntity;
import org.apache.ibatis.annotations.Param;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 报关信息（category级别Mapper
 * @date: 2021-07-26 15:30
 */
@org.apache.ibatis.annotations.Mapper
public interface ProductCategoryCustomsDeclareMapper extends BaseMapper<ProductCategoryCustomsDeclareEntity> {

    void syncProductName(Integer categoryId, String name, String englishName, String location);

    /**
     * 列表
     *
     * @param page
     * @param request
     * @return
     */
    IPage<ProductCategoryCustomsDeclare> searchPage(IPage page, @Param("request") ProductCategoryCustomsDeclareRequest request);
}
