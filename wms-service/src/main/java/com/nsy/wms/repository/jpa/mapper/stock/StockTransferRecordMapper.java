package com.nsy.wms.repository.jpa.mapper.stock;

import com.nsy.api.wms.domain.stock.StockTransferRecordList;
import com.nsy.api.wms.request.stock.StockTransferRecordListRequest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.wms.repository.entity.stock.StockTransferRecordEntity;
import org.apache.ibatis.annotations.Param;

public interface StockTransferRecordMapper extends BaseMapper<StockTransferRecordEntity> {
    IPage<StockTransferRecordList> pageSearchList(IPage page, @Param("query") StockTransferRecordListRequest request);
}
