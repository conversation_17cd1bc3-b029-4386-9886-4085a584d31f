package com.nsy.wms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "bd_change_log")
@TableName("bd_change_log")
public class BdChangeLogEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 地区 */
    private String location;

    /** 事件类型 */
    private String type;

    /** 日志消息 */
    private String content;

    /** 变更表 */
    private String changeTable;

    /** 日志明细 */
    @Column(columnDefinition = "text")
    private String detail;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    public String getChangeTable() {
        return changeTable;
    }

    public void setChangeTable(String changeTable) {
        this.changeTable = changeTable;
    }
    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
}
