
package com.nsy.wms.repository.entity.stockout;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 报关订单列表
 *
 * <AUTHOR>
 * @Date 2022-03-01 15:33
 */
@TableName("stockout_customs_declare_order")
@Entity
@Table(name = "stockout_customs_declare_order")
public class StockoutCustomsDeclareOrderEntity extends BaseMpEntity implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 5116646332733721434L;
    /**
     * DeclareOrderId
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer declareOrderId;
    /**
     * 地区
     */
    private String location;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * FBA出库单号
     */
    private String fbaShipmentId;
    /**
     * 出库单号
     */
    private String stockoutOrderNo;
    /**
     * 报关类型
     */
    private String declareOrderType;
    /**
     * 状态
     */
    private String status;
    /**
     * 亚马逊仓库名称
     */
    private String amazonSpaceName;

    private String referenceId;
    private Integer canDeclare;
    /**
     * 店铺ID
     */
    private Integer storeId;
    /**
     * 邮编
     */
    private String zipCode;
    /**
     * 订单收货地址
     */
    private String shippingAddress;
    /**
     * 进口国
     */
    private String importCountry;
    /**
     * 清关发票文件地址
     */
    private String proformaInvoiceUrl;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 平台名称
     */
    private String platformName;


    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public Integer getDeclareOrderId() {
        return declareOrderId;
    }

    public void setDeclareOrderId(Integer declareOrderId) {
        this.declareOrderId = declareOrderId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getDeclareOrderType() {
        return declareOrderType;
    }

    public void setDeclareOrderType(String declareOrderType) {
        this.declareOrderType = declareOrderType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAmazonSpaceName() {
        return amazonSpaceName;
    }

    public void setAmazonSpaceName(String amazonSpaceName) {
        this.amazonSpaceName = amazonSpaceName;
    }

    public Integer getCanDeclare() {
        return canDeclare;
    }

    public void setCanDeclare(Integer canDeclare) {
        this.canDeclare = canDeclare;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getShippingAddress() {
        return shippingAddress;
    }

    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }

    public String getImportCountry() {
        return importCountry;
    }

    public void setImportCountry(String importCountry) {
        this.importCountry = importCountry;
    }

    public String getProformaInvoiceUrl() {
        return proformaInvoiceUrl;
    }

    public void setProformaInvoiceUrl(String proformaInvoiceUrl) {
        this.proformaInvoiceUrl = proformaInvoiceUrl;
    }

    public String getFbaShipmentId() {
        return fbaShipmentId;
    }

    public void setFbaShipmentId(String fbaShipmentId) {
        this.fbaShipmentId = fbaShipmentId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }
}
