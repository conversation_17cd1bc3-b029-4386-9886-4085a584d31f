package com.nsy.wms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 海关编码
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "bd_hs_code")
@TableName("bd_hs_code")
public class BdHsCodeEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer hsCodeId;

    /** 海关编码 */
    private String hsCode;

    /** 海关编码描述 */
    private String hsCodeCn;

    /** 单位编码，多个以 / 间隔 */
    private String unit;

    /** 单位编码名称，多个以 / 间隔 */
    private String unitCn;


    public Integer getHsCodeId() {
        return hsCodeId;
    }

    public void setHsCodeId(Integer hsCodeId) {
        this.hsCodeId = hsCodeId;
    }
    public String getHsCode() {
        return hsCode;
    }

    public void setHsCode(String hsCode) {
        this.hsCode = hsCode;
    }
    public String getHsCodeCn() {
        return hsCodeCn;
    }

    public void setHsCodeCn(String hsCodeCn) {
        this.hsCodeCn = hsCodeCn;
    }
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
    public String getUnitCn() {
        return unitCn;
    }

    public void setUnitCn(String unitCn) {
        this.unitCn = unitCn;
    }
}
