package com.nsy.wms.repository.jpa.mapper.overseas;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.overseas.OverseasWarehouseOrderPageRequest;
import com.nsy.api.wms.response.overseas.OverseasWarehouseOrderResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.wms.repository.entity.overseas.OverseasWarehouseOrderEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 海外仓订单Mapper接口
 *
 * <AUTHOR>
 * @since 1.0
 */
@Mapper
public interface OverseasWarehouseOrderMapper extends BaseMapper<OverseasWarehouseOrderEntity> {

    /**
     * 分页查询海外仓订单
     *
     * @param page 分页参数
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<OverseasWarehouseOrderResponse> pageSearchOverseasWarehouseOrder(Page<OverseasWarehouseOrderEntity> page, @Param("param") OverseasWarehouseOrderPageRequest param);

    /**
     * 根据状态统计订单数量
     *
     * @return 状态统计结果
     */
    List<StatusCountResponse> countByStatus();
}
