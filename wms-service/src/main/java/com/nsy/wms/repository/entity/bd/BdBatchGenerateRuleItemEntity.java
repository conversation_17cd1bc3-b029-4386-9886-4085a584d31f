package com.nsy.wms.repository.entity.bd;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "bd_batch_generate_rule_item")
@TableName("bd_batch_generate_rule_item")
public class BdBatchGenerateRuleItemEntity extends BaseMpEntity {

    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer batchGenerateRuleItemId;

    /**
     * 地区
     */
    private String location;

    /**
     * 波次生成规则ID
     */
    private Integer batchGenerateRuleId;

    /**
     * 拣货模式
     */
    private String pickingType;

    /**
     * 是否自动生成波次 1-是，0-否
     */
    private Integer isAutoGenerate;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否已删除 1-是，0-否
     */
    private Integer isDeleted;

    public Integer getBatchGenerateRuleItemId() {
        return batchGenerateRuleItemId;
    }

    public void setBatchGenerateRuleItemId(Integer batchGenerateRuleItemId) {
        this.batchGenerateRuleItemId = batchGenerateRuleItemId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getBatchGenerateRuleId() {
        return batchGenerateRuleId;
    }

    public void setBatchGenerateRuleId(Integer batchGenerateRuleId) {
        this.batchGenerateRuleId = batchGenerateRuleId;
    }

    public String getPickingType() {
        return pickingType;
    }

    public void setPickingType(String pickingType) {
        this.pickingType = pickingType;
    }

    public Integer getIsAutoGenerate() {
        return isAutoGenerate;
    }

    public void setIsAutoGenerate(Integer isAutoGenerate) {
        this.isAutoGenerate = isAutoGenerate;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

}
