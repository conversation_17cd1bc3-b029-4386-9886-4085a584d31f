package com.nsy.wms.repository.jpa.mapper.stockin;

import com.nsy.api.wms.request.stockin.StockinOrderLogListRequest;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.wms.repository.entity.stockin.StockinOrderLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: YongHuo
 * @version: v1.0
 * @description: 入库日志Mapper
 * @date: 2021-09-30 17:33
 */
@org.apache.ibatis.annotations.Mapper
public interface StockinOrderLogMapper extends BaseMapper<StockinOrderLogEntity> {

    @InterceptorIgnore(tenantLine = "true")
    IPage<StockinOrderLogEntity> pageSearchStockinOrderLog(IPage page, @Param("query") StockinOrderLogListRequest request);

    @InterceptorIgnore(tenantLine = "true")
    List<StockinOrderLogEntity> findAllByStockinOrderIdListIgnoreTenant(@Param("stockinOrderIdList") List<Integer> stockinOrderIdList);
}
