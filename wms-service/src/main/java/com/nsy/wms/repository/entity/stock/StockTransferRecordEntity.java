package com.nsy.wms.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "stock_transfer_record")
@TableName("stock_transfer_record")
public class StockTransferRecordEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    /**
     * 调拨类型
     */
    private String type;

    /**
     * 调拨数量
     */
    private Integer transferSkuQty;

    /**
     * 调入后库存数
     */
    private Integer afterInStock;

    /**
     * 待处理调出数
     */
    private Integer unProcessQty;

    /**
     * 异常库位数
     */
    private Integer exceptionQty;

    /**
     * 调出编码
     */
    private String transferOutCode;

    /**
     * 调入编码
     */
    private String transferInCode;

    /**
     * 来源区域ID
     */
    private Integer sourceAreaId;

    /**
     * 来源库位
     */
    private String sourcePositionCode;

    /**
     * 是否跨区域
     */
    private Integer isCross;

    /**
     * sku
     */
    private String sku;

    /**
     * 同步erp状态
     */
    private String syncErpStatus;

    /**
     * 同步etl状态
     */
    private String syncEtlStatus;

    /**
     * erp调拨id
     */
    private Integer erpTransferId;

    public String getSourcePositionCode() {
        return sourcePositionCode;
    }

    public void setSourcePositionCode(String sourcePositionCode) {
        this.sourcePositionCode = sourcePositionCode;
    }

    public Integer getSourceAreaId() {
        return sourceAreaId;
    }

    public void setSourceAreaId(Integer sourceAreaId) {
        this.sourceAreaId = sourceAreaId;
    }

    public Integer getIsCross() {
        return isCross;
    }

    public void setIsCross(Integer isCross) {
        this.isCross = isCross;
    }

    public String getSyncEtlStatus() {
        return syncEtlStatus;
    }

    public void setSyncEtlStatus(String syncEtlStatus) {
        this.syncEtlStatus = syncEtlStatus;
    }

    public String getSyncErpStatus() {
        return syncErpStatus;
    }

    public void setSyncErpStatus(String syncErpStatus) {
        this.syncErpStatus = syncErpStatus;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getTransferSkuQty() {
        return transferSkuQty;
    }

    public void setTransferSkuQty(Integer transferSkuQty) {
        this.transferSkuQty = transferSkuQty;
    }

    public String getTransferOutCode() {
        return transferOutCode;
    }

    public void setTransferOutCode(String transferOutCode) {
        this.transferOutCode = transferOutCode;
    }

    public String getTransferInCode() {
        return transferInCode;
    }

    public void setTransferInCode(String transferInCode) {
        this.transferInCode = transferInCode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getUnProcessQty() {
        return unProcessQty;
    }

    public void setUnProcessQty(Integer unProcessQty) {
        this.unProcessQty = unProcessQty;
    }

    public Integer getExceptionQty() {
        return exceptionQty;
    }

    public void setExceptionQty(Integer exceptionQty) {
        this.exceptionQty = exceptionQty;
    }

    public Integer getAfterInStock() {
        return afterInStock;
    }

    public void setAfterInStock(Integer afterInStock) {
        this.afterInStock = afterInStock;
    }

    public Integer getErpTransferId() {
        return erpTransferId;
    }

    public void setErpTransferId(Integer erpTransferId) {
        this.erpTransferId = erpTransferId;
    }
}
