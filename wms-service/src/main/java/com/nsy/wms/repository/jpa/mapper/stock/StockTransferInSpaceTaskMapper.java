package com.nsy.wms.repository.jpa.mapper.stock;

import com.nsy.api.wms.domain.stock.StockTransferTaskListExport;
import com.nsy.api.wms.domain.stock.StockTransferTaskRequest;
import com.nsy.api.wms.request.stockin.TabCountRequest;
import com.nsy.api.wms.response.stockin.TaskListCountResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.stock.StockTransferInSpaceTaskList;
import com.nsy.wms.repository.entity.stock.StockTransferInSpaceTaskEntity;
import com.nsy.api.wms.request.stock.StockTransferInSpaceTaskListRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockTransferInSpaceTaskMapper extends BaseMapper<StockTransferInSpaceTaskEntity> {
    IPage<StockTransferInSpaceTaskList> pageSearchTransferTask(IPage page, @Param("query") StockTransferInSpaceTaskListRequest request);

    List<TaskListCountResponse> countByStatus(@Param("query") TabCountRequest request);

    List<StockTransferTaskListExport> exportTaskList(@Param("query") StockTransferTaskRequest request);
}
