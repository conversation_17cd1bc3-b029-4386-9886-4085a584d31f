package com.nsy.wms.repository.entity.stockout;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * faire面单
 */
@Entity
@Table(name = "stockout_order_faire_logistics_sheet")
@TableName("stockout_order_faire_logistics_sheet")
public class StockoutOrderFaireLogisticsSheetEntity extends BaseMpEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    String location;
    //出库单ID
    private Integer stockoutOrderId;
    //出库单号
    private String stockoutOrderNo;
    //物流面单
    private String logisticsSheetUrl;
    //是否删除 0-否 1-是
    private Boolean isDel;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockoutOrderId() {
        return stockoutOrderId;
    }

    public void setStockoutOrderId(Integer stockoutOrderId) {
        this.stockoutOrderId = stockoutOrderId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getLogisticsSheetUrl() {
        return logisticsSheetUrl;
    }

    public void setLogisticsSheetUrl(String logisticsSheetUrl) {
        this.logisticsSheetUrl = logisticsSheetUrl;
    }

    public Boolean getIsDel() {
        return isDel;
    }

    public void setIsDel(Boolean isDel) {
        this.isDel = isDel;
    }
}
