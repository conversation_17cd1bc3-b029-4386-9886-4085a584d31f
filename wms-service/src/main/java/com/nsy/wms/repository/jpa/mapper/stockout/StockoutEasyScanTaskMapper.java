package com.nsy.wms.repository.jpa.mapper.stockout;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.request.stockout.StockoutEasyScanTaskListRequest;
import com.nsy.api.wms.response.stockout.StockoutEasyScanTaskCountResponse;
import com.nsy.api.wms.response.stockout.StockoutEasyScanTaskListResponse;
import com.nsy.wms.business.domain.bo.base.CountStatusBo;
import com.nsy.wms.repository.entity.stockout.StockoutEasyScanTaskEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockoutEasyScanTaskMapper extends BaseMapper<StockoutEasyScanTaskEntity> {
    /**
     * 统计状态
     *
     * @return
     */
    List<CountStatusBo> statusCount();

    /**
     * 列表
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockoutEasyScanTaskListResponse> searchPage(IPage page, @Param("query") StockoutEasyScanTaskListRequest request);

    /**
     * 统计
     *
     * @param request
     * @return
     */
    StockoutEasyScanTaskCountResponse searchCount(@Param("query") StockoutEasyScanTaskListRequest request);

    /**
     * 查找未取消的任务
     *
     * @param logisticsNo
     * @return
     */
    StockoutEasyScanTaskEntity findNoCancelByLogisticsNo(String logisticsNo);
}
