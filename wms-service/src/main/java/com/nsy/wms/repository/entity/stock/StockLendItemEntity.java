package com.nsy.wms.repository.entity.stock;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@TableName("stock_lend_item")
@Entity
@Table(name = "stock_lend_item")
public class StockLendItemEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer lendItemId;

    private String location;

    /**
     * 借用id
     */
    private Integer lendId;

    /**
     * 规格id
     */
    private Integer specId;

    /**
     * 商品Id
     */
    private Integer productId;

    private String sku;
    /**
     * 商品条形码
     */
    private String barcode;

    /**
     * 库位Id
     */
    private Integer positionId;

    /**
     * 库位编码
     */
    private String positionCode;

    /**
     * 申请借用数量
     */
    private Integer applyLendQty;

    /**
     * 借出数量
     */
    private Integer lendQty;

    /**
     * 归还数量
     */
    private Integer returnQty;

    /**
     * 预计归还时间
     */
    private Date expectReturnDate;

    /**
     * 归还时间
     */
    private Date returnDate;

    /**
     * sku版本号
     */
    private String productVersion;

    /**
     * 备注
     */
    private String remark;

    /**
     * 次品数
     */
    private Integer inferiorQty;

    /**
     * 次品备注
     */
    private String inferiorRemark;

    /**
     * 吊牌价
     */
    private BigDecimal price;

    /**
     * 总价
     */
    private BigDecimal total;

    public Integer getLendItemId() {
        return lendItemId;
    }

    public void setLendItemId(Integer lendItemId) {
        this.lendItemId = lendItemId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getLendId() {
        return lendId;
    }

    public void setLendId(Integer lendId) {
        this.lendId = lendId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getApplyLendQty() {
        return applyLendQty;
    }

    public void setApplyLendQty(Integer applyLendQty) {
        this.applyLendQty = applyLendQty;
    }

    public Integer getLendQty() {
        return lendQty;
    }

    public void setLendQty(Integer lendQty) {
        this.lendQty = lendQty;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public Date getExpectReturnDate() {
        return expectReturnDate;
    }

    public void setExpectReturnDate(Date expectReturnDate) {
        this.expectReturnDate = expectReturnDate;
    }

    public Date getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(Date returnDate) {
        this.returnDate = returnDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getInferiorQty() {
        return inferiorQty;
    }

    public void setInferiorQty(Integer inferiorQty) {
        this.inferiorQty = inferiorQty;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public String getInferiorRemark() {
        return inferiorRemark;
    }

    public void setInferiorRemark(String inferiorRemark) {
        this.inferiorRemark = inferiorRemark;
    }

    public String getProductVersion() {
        return productVersion;
    }

    public void setProductVersion(String productVersion) {
        this.productVersion = productVersion;
    }
}
