package com.nsy.wms.repository.entity.bd;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 *
 */
@Entity
@Table(name = "bd_prematch_rule_item")
@TableName("bd_prematch_rule_item")
public class BdPrematchRuleItemEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer prematchRuleItemId;

    private String location;

    private Integer prematchRuleId;
    /**
     * 库位类型
     */

    private String positionType;
    /**
     * 规则名称
     */

    private String ruleItemType;
    /**
     * 规则描述
     */

    private String ruleItemValue;

    private Integer sort;

    private String description;

    private Integer isDeleted;

    /**
     * 1：少于 0：大于等于 sku阈值
     */
    private Integer isLessThanSkuLimit;


    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }


    public Integer getPrematchRuleItemId() {
        return prematchRuleItemId;
    }

    public void setPrematchRuleItemId(Integer prematchRuleItemId) {
        this.prematchRuleItemId = prematchRuleItemId;
    }

    public Integer getPrematchRuleId() {
        return prematchRuleId;
    }

    public void setPrematchRuleId(Integer prematchRuleId) {
        this.prematchRuleId = prematchRuleId;
    }

    public String getPositionType() {
        return positionType;
    }

    public void setPositionType(String positionType) {
        this.positionType = positionType;
    }

    public String getRuleItemType() {
        return ruleItemType;
    }

    public void setRuleItemType(String ruleItemType) {
        this.ruleItemType = ruleItemType;
    }

    public String getRuleItemValue() {
        return ruleItemValue;
    }

    public void setRuleItemValue(String ruleItemValue) {
        this.ruleItemValue = ruleItemValue;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getIsLessThanSkuLimit() {
        return isLessThanSkuLimit;
    }

    public void setIsLessThanSkuLimit(Integer isLessThanSkuLimit) {
        this.isLessThanSkuLimit = isLessThanSkuLimit;
    }
}
