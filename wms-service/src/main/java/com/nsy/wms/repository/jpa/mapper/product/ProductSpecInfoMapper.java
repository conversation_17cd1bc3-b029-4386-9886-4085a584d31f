package com.nsy.wms.repository.jpa.mapper.product;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.api.wms.domain.product.ProductSku;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 商品规格信息Mapper
 * @date: 2021-07-26 15:30
 */
@org.apache.ibatis.annotations.Mapper
public interface ProductSpecInfoMapper extends BaseMapper<ProductSpecInfoEntity> {

    List<ProductSpecInfo> selectProductSpecInfo(@Param("cursor") Integer cursor, @Param("pageSize") Integer pageSize);

    Integer selectProductSpecInfoCount();

    IPage<ProductSku> selectProductSpecInfoBySku(IPage page, @Param("sku") String sku, @Param("barcode") String barcode);

    /**
     * 获取相同规格、同尺码不同颜色商品实际重量
     *
     * @param productId
     * @param size
     * @return
     */
    BigDecimal selectSimilarityActualWeight(@Param("productId") Integer productId, @Param("size") String size);

    /**
     * 获取相同规格、同尺码不同颜色商品预估重量
     *
     * @param productId
     * @param size
     * @return
     */
    BigDecimal selectSimilarityWeight(@Param("productId") Integer productId, @Param("size") String size);

    /**
     * 根据sku查询productspecid
     * @param sku
     * @return
     */
    Integer getProductSpecIdBySku(String sku);

    ProductSpecInfoEntity findByBarcodeAndLocation(String barcode);
}
