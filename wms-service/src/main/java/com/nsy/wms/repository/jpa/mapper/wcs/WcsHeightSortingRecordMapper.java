package com.nsy.wms.repository.jpa.mapper.wcs;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.wms.request.wcs.WcsHeightSortingRecordPageRequest;
import com.nsy.wms.repository.entity.wcs.WcsHeightSortingRecordEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 测量高度分拣记录Mapper
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@org.apache.ibatis.annotations.Mapper
public interface WcsHeightSortingRecordMapper extends BaseMapper<WcsHeightSortingRecordEntity> {

    /**
     * 分页查询测量高度分拣记录
     * MyBatis Plus会自动处理分页和count查询
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 分页结果
     */
    Page<WcsHeightSortingRecordEntity> selectPageWithConditions(@Param("page") Page<WcsHeightSortingRecordEntity> page, @Param("request") WcsHeightSortingRecordPageRequest request);

} 