package com.nsy.wms.repository.entity.overseas;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.wms.repository.entity.base.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 海外仓订单表
 *
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "stockout_overseas_warehouse_order")
@TableName("stockout_overseas_warehouse_order")
public class OverseasWarehouseOrderEntity extends BaseMpEntity {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 地区
     */
    private String location;

    /**
     * 出库单ID
     */
    private Integer stockoutOrderId;

    /**
     * 出库单号
     */
    private String stockoutOrderNo;

    /**
     * 海外仓订单号
     */
    private String overseasOrderNo;

    /**
     * 店铺ID
     */
    private Integer storeId;

    /**
     * 店铺
     */
    private String storeName;

    /**
     * 平台
     */
    private String platformName;

    /**
     * 状态（PUSH_ERROR:推送异常, PENDING_SHIP:待发货, SHIPPED:已发货, SIGNED:已签收）
     */
    private String status;

    /**
     * 海外仓ID
     */
    private Integer overseasSpaceId;

    /**
     * 海外仓
     */
    private String overseasSpaceName;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 出库单创建时间
     */
    private Date stockoutCreateTime;

    /**
     * 推送海外仓时间
     */
    private Date pushOverseasTime;

    /**
     * 海外仓发货时间
     */
    private Date overseasShipTime;

    /**
     * 包裹提取时间
     */
    private Date packagePickupTime;

    /**
     * 包裹签收时间
     */
    private Date packageSignedTime;

    /**
     * 推送海外仓时长（h）
     */
    private BigDecimal pushOverseasDuration;

    /**
     * 海外仓处理时长（h）
     */
    private BigDecimal overseasProcessDuration;

    /**
     * 包裹提取时长（h）
     */
    private BigDecimal packagePickupDuration;

    /**
     * 包裹派送时长（天）
     */
    private BigDecimal packageDeliveryDuration;

    /**
     * 发货总时长（天）
     */
    private BigDecimal totalShipDuration;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockoutOrderId() {
        return stockoutOrderId;
    }

    public void setStockoutOrderId(Integer stockoutOrderId) {
        this.stockoutOrderId = stockoutOrderId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOverseasOrderNo() {
        return overseasOrderNo;
    }

    public void setOverseasOrderNo(String overseasOrderNo) {
        this.overseasOrderNo = overseasOrderNo;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getOverseasSpaceId() {
        return overseasSpaceId;
    }

    public void setOverseasSpaceId(Integer overseasSpaceId) {
        this.overseasSpaceId = overseasSpaceId;
    }

    public String getOverseasSpaceName() {
        return overseasSpaceName;
    }

    public void setOverseasSpaceName(String overseasSpaceName) {
        this.overseasSpaceName = overseasSpaceName;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getStockoutCreateTime() {
        return stockoutCreateTime;
    }

    public void setStockoutCreateTime(Date stockoutCreateTime) {
        this.stockoutCreateTime = stockoutCreateTime;
    }

    public Date getPushOverseasTime() {
        return pushOverseasTime;
    }

    public void setPushOverseasTime(Date pushOverseasTime) {
        this.pushOverseasTime = pushOverseasTime;
    }

    public Date getOverseasShipTime() {
        return overseasShipTime;
    }

    public void setOverseasShipTime(Date overseasShipTime) {
        this.overseasShipTime = overseasShipTime;
    }

    public Date getPackagePickupTime() {
        return packagePickupTime;
    }

    public void setPackagePickupTime(Date packagePickupTime) {
        this.packagePickupTime = packagePickupTime;
    }

    public Date getPackageSignedTime() {
        return packageSignedTime;
    }

    public void setPackageSignedTime(Date packageSignedTime) {
        this.packageSignedTime = packageSignedTime;
    }

    public BigDecimal getPushOverseasDuration() {
        return pushOverseasDuration;
    }

    public void setPushOverseasDuration(BigDecimal pushOverseasDuration) {
        this.pushOverseasDuration = pushOverseasDuration;
    }

    public BigDecimal getOverseasProcessDuration() {
        return overseasProcessDuration;
    }

    public void setOverseasProcessDuration(BigDecimal overseasProcessDuration) {
        this.overseasProcessDuration = overseasProcessDuration;
    }

    public BigDecimal getPackagePickupDuration() {
        return packagePickupDuration;
    }

    public void setPackagePickupDuration(BigDecimal packagePickupDuration) {
        this.packagePickupDuration = packagePickupDuration;
    }

    public BigDecimal getPackageDeliveryDuration() {
        return packageDeliveryDuration;
    }

    public void setPackageDeliveryDuration(BigDecimal packageDeliveryDuration) {
        this.packageDeliveryDuration = packageDeliveryDuration;
    }

    public BigDecimal getTotalShipDuration() {
        return totalShipDuration;
    }

    public void setTotalShipDuration(BigDecimal totalShipDuration) {
        this.totalShipDuration = totalShipDuration;
    }
}
