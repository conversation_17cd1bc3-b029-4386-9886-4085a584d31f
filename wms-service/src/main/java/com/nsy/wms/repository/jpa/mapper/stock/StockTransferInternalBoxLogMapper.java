package com.nsy.wms.repository.jpa.mapper.stock;

import com.nsy.api.wms.domain.stock.StockTransferInSpaceLogList;
import com.nsy.api.wms.request.stock.StockTransferInSpaceLogListRequest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.wms.repository.entity.stock.StockTransferInternalBoxLogEntity;
import org.apache.ibatis.annotations.Param;

public interface StockTransferInternalBoxLogMapper extends BaseMapper<StockTransferInternalBoxLogEntity> {
    IPage<StockTransferInSpaceLogList> pageSearchTransferLog(IPage page, @Param("query") StockTransferInSpaceLogListRequest request);
}
