package com.nsy.wms.repository.jpa.mapper.stockout;

import com.nsy.api.wms.request.stockout.StockoutEasyScanTaskInfoListRequest;
import com.nsy.api.wms.response.stockout.StockoutEasyScanTaskScanItemResponse;
import com.nsy.api.wms.response.stockout.StockoutEasyScanTaskSkuListResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.nsy.wms.repository.entity.stockout.StockoutEasyScanTaskItemEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StockoutEasyScanTaskItemMapper extends BaseMapper<StockoutEasyScanTaskItemEntity> {
    /**
     * sku列表
     *
     * @param page
     * @param request
     * @return
     */
    IPage<StockoutEasyScanTaskSkuListResponse> searchSkuList(IPage page, @Param("query") StockoutEasyScanTaskInfoListRequest request);


    /**
     * 扫描明细返回
     *
     * @param taskId
     * @return
     */
    List<StockoutEasyScanTaskScanItemResponse> searchScanItemList(Integer taskId);
}
