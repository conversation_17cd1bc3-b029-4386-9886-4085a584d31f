package com.nsy.wms.repository.jpa.mapper.stock;

import com.nsy.api.wms.request.stock.StockLendStockListRequest;
import com.nsy.api.wms.response.stockin.StockLendStockCountResponse;
import com.nsy.api.wms.response.stockin.StockLendStockListResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.wms.repository.entity.stock.StockLendStockEntity;
import org.apache.ibatis.annotations.Param;

public interface StockLendStockMapper extends BaseMapper<StockLendStockEntity> {
    Page<StockLendStockListResponse> pageList(IPage page, @Param("query") StockLendStockListRequest query);

    StockLendStockCountResponse pageCount(@Param("query") StockLendStockListRequest query);
}
