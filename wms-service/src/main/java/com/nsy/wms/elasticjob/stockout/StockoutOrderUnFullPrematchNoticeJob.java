package com.nsy.wms.elasticjob.stockout;

import com.nsy.wms.business.manage.notify.NotifyApiService;
import com.nsy.wms.business.manage.notify.request.DingTalkRobotWebhookSendRequest;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.utils.WmsDateUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component
public class StockoutOrderUnFullPrematchNoticeJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderUnFullPrematchNoticeJob.class);

    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;
    @Autowired
    NotifyApiService notifyApiService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("StockoutOrderUnFullPrematchNoticeJob start");

        Integer count = stockoutOrderService.getBaseMapper().countUnFullPrematchOrder(WmsDateUtils.getDateBefore(new DateTime(new Date()), 1));

        if (count > 0) {
            DingTalkRobotWebhookSendRequest request = new DingTalkRobotWebhookSendRequest();
            request.setMsgType("text");
            String format = String.format("共有%s条超过%s未完全预配的出库单，请及时处理。sql:【select * from stockout_order where status = 'UN_FULL_PRE_MATCH';】", count, "24小时");
            request.setText(new DingTalkRobotWebhookSendRequest.Text("仓库提示：" + format));
            notifyApiService.sendWebhookMessage(request);
        }
        LOGGER.info("StockoutOrderUnFullPrematchNoticeJob end");
    }
}
