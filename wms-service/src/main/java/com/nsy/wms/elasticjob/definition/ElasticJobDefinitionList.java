package com.nsy.wms.elasticjob.definition;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> 9/5/2020
 */

@XmlRootElement(name = "jobs")
@XmlAccessorType(XmlAccessType.FIELD)
public class ElasticJobDefinitionList {
    private static final long serialVersionUID = -3121344636865637353L;
    @XmlElement(name = "job")
    private List<ElasticJobDefinition> elasticJobs;

    public ElasticJobDefinition getElasticJobDefinitionByName(String jobName) {
        List<ElasticJobDefinition> elasticJobDefinitions = getJobs();
        for (ElasticJobDefinition jobDefinition : elasticJobDefinitions) {
            if (jobName.equals(jobDefinition.getJobName())) {
                return jobDefinition;
            }
        }
        return null;
    }

    public List<ElasticJobDefinition> getJobs() {
        if (elasticJobs == null) {
            elasticJobs = new ArrayList();
        }
        return elasticJobs;
    }

    public void setJobs(List<ElasticJobDefinition> elasticJobs) {
        this.elasticJobs = elasticJobs;
    }


}
