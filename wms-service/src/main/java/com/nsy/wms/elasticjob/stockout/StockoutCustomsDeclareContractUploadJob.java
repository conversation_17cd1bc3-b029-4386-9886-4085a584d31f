package com.nsy.wms.elasticjob.stockout;

import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareContractFileService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 关单生成合同
 */
@Component
public class StockoutCustomsDeclareContractUploadJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareContractUploadJob.class);

    @Autowired
    StockoutCustomsDeclareContractFileService contractFileService;


    public static final String JOB_NAME = "StockoutCustomsDeclareContractUploadJob";

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        try {
            LOGGER.info("StockoutCustomsDeclareContractUploadJob start");
            LoginInfoService.setName(JOB_NAME);
            contractFileService.uploadFile();
            LOGGER.info("StockoutCustomsDeclareContractUploadJob end");
        } finally {
            LoginInfoService.removeName();
        }
    }
}

