package com.nsy.wms.elasticjob.base;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.wms.business.manage.user.UserDictionaryCacheService;
import com.nsy.wms.business.service.QConsumerRecordService;
import com.nsy.wms.mq.QConsumerRecordStatusEnum;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.repository.entity.QConsumerRecordEntity;
import com.nsy.wms.utils.KafkaUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public abstract class BaseKafkaUpdateDataFlowStreamJob<T> extends BaseDataFlowJob<QMessage<T>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(BaseKafkaUpdateDataFlowStreamJob.class);
    @Value("${elastic-job.kafka.bootstrap-server}")
    private String bootstrapServers;

    @Value("${elastic-job.kafka.consumer-group-id}")
    private String consumerGroupId;
    @Autowired
    QConsumerRecordService qConsumerRecordService;
    @Autowired
    UserDictionaryCacheService cacheService;
    @Autowired
    private ObjectMapper objectMapper;

    //每次查询QConsumerRecord
    static final int QUERY_Q_CONSUMER_RECORD_PAGE_SIZE = 50;

    //是否需要重试
    protected Boolean needRetry = Boolean.TRUE;


    protected List<QMessage<T>> doKafkaFetchData(ShardingContext shardingContext, String topic, Integer topicPartitionNum) {
        List<QMessage<T>> qMessageList = new ArrayList<>();
        KafkaConsumer<String, String> autoConsumerClient = new KafkaConsumer<>(KafkaUtils.consumerProperties(bootstrapServers, consumerGroupId));
        LOGGER.debug("当前consumer:{}", autoConsumerClient);
        String jobParameter = shardingContext.getJobParameter();
        String realTopic;
        if (cacheService.systemLocation().stream().anyMatch(systemLocation -> systemLocation.getLocation().equals(jobParameter))) {
            realTopic = topic + "-" + jobParameter;
        } else {
            realTopic = topic;
        }
        LOGGER.info("当前topicNew:{}", realTopic);
        autoConsumerClient.assign(KafkaUtils.partitions(realTopic, topicPartitionNum, shardingContext.getShardingTotalCount(), shardingContext.getShardingItem()));
        ConsumerRecords<String, String> records = autoConsumerClient.poll(3000);
        for (ConsumerRecord<String, String> record : records) {
            LOGGER.debug("当前分片={}，分区={}, 消费位移 = {}", shardingContext.getShardingItem(), record.partition(), record.offset());
            String wrapperMessage = record.value();
            LOGGER.debug("消息:{}", wrapperMessage);
            if (wrapperMessage == null) {
                continue;
            }
            QMessage<T> productUpdateDataQMessage = null;
            try {
                productUpdateDataQMessage = objectMapper.readValue(wrapperMessage, new TypeReference<QMessage<T>>() {
                });
            } catch (JsonProcessingException e) {
                LOGGER.error(e.getMessage(), e);
            }
            qMessageList.add(productUpdateDataQMessage);
        }
        try {
            autoConsumerClient.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }
        //获取数据库中为状态-1的数据
/*        String format = DateFormatUtils.format(new Date(System.currentTimeMillis() - 12 * 60 * 60 * 1000), "yyyy-MM-dd HH:mm:ss");
        List<QConsumerRecordEntity> allByTopicAndStatus = qConsumerRecordService.findAllByTopicAndStatus(topic, QConsumerRecordStatusEnum.CONSUME_FAILED.getCode(), format);
        allByTopicAndStatus.stream().forEach(entity -> {
            QMessage<T> objectQMessage = gson.fromJson(entity.getMessageContent(), type);
            objectQMessage.setBusinessMark(entity.getBusinessMark());
            objectQMessage.setDestination(entity.getTopic());
            objectQMessage.setMessageId(entity.getMessageId());
            objectQMessage.setStatus(entity.getStatus());
            qMessageList.add(objectQMessage);
        });*/
        return qMessageList;
    }

    protected abstract void doBusiness(QMessage<T> message);

    protected void recordData(ShardingContext shardingContext, List<QMessage<T>> messageList) {
        //分页取qConsumerRecord
        int total = (int) Math.ceil(messageList.size() * 1.0f / QUERY_Q_CONSUMER_RECORD_PAGE_SIZE);
        List<QConsumerRecordEntity> qConsumerRecordList = new ArrayList<>();
        for (int i = 0; i < total; i++) {
            int begin = i * QUERY_Q_CONSUMER_RECORD_PAGE_SIZE;
            List<QMessage<T>> subMessageList = messageList.subList(begin, Math.min(begin + QUERY_Q_CONSUMER_RECORD_PAGE_SIZE, messageList.size()));
            List<String> messageIdList = subMessageList.stream().map(QMessage::getMessageId).collect(Collectors.toList());
            qConsumerRecordList.addAll(qConsumerRecordService.listByMessageIdList(messageIdList));
        }
        Map<String, QConsumerRecordEntity> qConsumerRecordMap = qConsumerRecordList.stream().collect(Collectors.toMap(QConsumerRecordEntity::getMessageId, Function.identity(), (v1, v2) -> v1));

        messageList.stream()
                .filter(message -> StringUtils.hasText(message.getMessageId()) && null != message.getMessageContent())
                .forEach(message -> {
                    String messageId = message.getMessageId();
                    QConsumerRecordEntity qConsumerRecordEntity = qConsumerRecordMap.get(messageId);
                    // 保证消息不重复消费
                    if (null != qConsumerRecordEntity && QConsumerRecordStatusEnum.CONSUME_COMPLETE.getCode().equals(qConsumerRecordEntity.getStatus())) {
                        LOGGER.warn("重复消费消息，messageId:{}", messageId);
                        return;
                    }

                    if (Objects.isNull(qConsumerRecordEntity)) {
                        qConsumerRecordEntity = qConsumerRecordService.createQConsumerRecord(message);
                    } else {
                        qConsumerRecordEntity.setRetryCount(qConsumerRecordEntity.getRetryCount() + 1);
                    }
                    try {
                        doBusiness(message);
                        qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.CONSUME_COMPLETE.getCode());
                    } catch (Exception e) {
                        if (needRetry) {
                            qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.CONSUME_FAILED.getCode());
                        } else {
                            qConsumerRecordEntity.setStatus(QConsumerRecordStatusEnum.CONSUME_COMPLETE.getCode());
                        }
                        qConsumerRecordEntity.setErrorMsg(org.apache.commons.lang3.StringUtils.substring(e.getMessage(), 0, 450));
                        LOGGER.error("kafka consumer:{}, job exception，e:{}", message.getDestination(), e);
                    } finally {
                        qConsumerRecordService.persistQConsumerRecord(qConsumerRecordEntity);
                    }
                });
    }

}
