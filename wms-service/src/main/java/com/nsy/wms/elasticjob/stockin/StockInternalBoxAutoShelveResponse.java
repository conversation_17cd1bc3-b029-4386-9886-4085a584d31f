package com.nsy.wms.elasticjob.stockin;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-04-28 17:44
 */
public class StockInternalBoxAutoShelveResponse {

    /**
     * 上架任务id
     */
    private Integer shelveTaskId;

    /**
     * 内部箱号
     */
    private String internalBoxCode;

    /**
     * 规格编码
     */
    private String sku;

    /**
     * 上架库位
     */
    private String positionCode;

    /**
     * 上架数量
     */
    private Integer stockinQty;

    public String getInternalBoxCode() {
        return internalBoxCode;
    }

    public void setInternalBoxCode(String internalBoxCode) {
        this.internalBoxCode = internalBoxCode;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getStockinQty() {
        return stockinQty;
    }

    public void setStockinQty(Integer stockinQty) {
        this.stockinQty = stockinQty;
    }

    public Integer getShelveTaskId() {
        return shelveTaskId;
    }

    public void setShelveTaskId(Integer shelveTaskId) {
        this.shelveTaskId = shelveTaskId;
    }
}
