package com.nsy.wms.elasticjob.stockout;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderTemuPopExtendService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuPopExtendEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * TemuPop获取发货结果任务
 * 获取所有平台是TemuPop的准备中的出库单并且packageSn非空的，然后请求发货结果，如果发货结果OK，再去请求面单
 */
@Component
public class TemuPopGetShipmentResultJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(TemuPopGetShipmentResultJob.class);

    public static final String JOB_NAME = "TemuPopGetShipmentResultJob";

    @Resource
    private StockoutOrderTemuPopExtendService temuPopExtendService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("TemuPopGetShipmentResultJob start");
        LoginInfoService.setName(JOB_NAME);
        try {
            processGetShipmentResult();
        } catch (RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        } finally {
            LOGGER.info(JOB_NAME);
            LoginInfoService.removeName();
            LOGGER.info("TemuPopGetShipmentResultJob end");
        }
    }

    /**
     * 处理获取发货结果
     */
    private void processGetShipmentResult() {
        // 查询需要获取发货结果的出库单
        List<StockoutOrderTemuPopExtendEntity> orderList =
                temuPopExtendService.listOrdersForGetShipmentResult();

        if (CollectionUtils.isEmpty(orderList)) {
            LOGGER.info("没有需要获取发货结果的TemuPop出库单");
            return;
        }

        LOGGER.info("找到{}个需要获取发货结果的TemuPop出库单", orderList.size());

        // 逐个处理获取发货结果
        for (StockoutOrderTemuPopExtendEntity extendEntity : orderList) {
            try {
                temuPopExtendService.getShipmentResult(extendEntity);
            } catch (Exception e) {
                LOGGER.error("获取发货结果失败，出库单号: {}, packageSn: {}, 错误: {}",
                        extendEntity.getStockoutOrderNo(), extendEntity.getPackageSn(), e.getMessage(), e);
            }
        }

        LOGGER.info("TemuPop获取发货结果处理完成，共处理{}个订单", orderList.size());
    }
} 