package com.nsy.wms.elasticjob.stockin;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.NoneDataException;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.product.SendDingDingMessage;
import com.nsy.api.wms.enumeration.stockin.ReturnProductStatusEnum;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskItemService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发货日期5天后工厂端没有操作收货确认操作，触发钉钉消息给至采购
 *
 * <AUTHOR>
 * @since 2022-08-12
 */
@Component
public class StockinReturnTaskReceiptReminderJob extends BaseSimpleJob {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockinReturnTaskReceiptReminderJob.class);

    @Autowired
    private StockinReturnProductTaskService returnProductTaskService;
    @Autowired
    private StockinReturnProductTaskItemService returnProductTaskItemService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private UserApiService userApiService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("StockinReturnTaskReceiptReminderJob start");
        List<StockinReturnProductTaskEntity> taskEntityList = returnProductTaskService.list(new LambdaQueryWrapper<StockinReturnProductTaskEntity>()
                .lt(StockinReturnProductTaskEntity::getDeliveryDate, DateUtils.addDays(new Date(), -5))
                .eq(StockinReturnProductTaskEntity::getStatus, ReturnProductStatusEnum.IN_TRANSIT.name()));
        if (CollectionUtils.isEmpty(taskEntityList)) {
            throw new NoneDataException();
        }
        List<Integer> supplierIdList = taskEntityList.stream().map(StockinReturnProductTaskEntity::getSupplierId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, SupplierDto> supplierDtoMap = scmApiService.getSupplierInfoList(supplierIdList).stream().collect(Collectors.toMap(SupplierDto::getSupplierId, Function.identity()));
        List<Integer> returnTaskIdList = taskEntityList.stream().map(StockinReturnProductTaskEntity::getReturnProductTaskId).distinct().collect(Collectors.toList());
        Map<Integer, List<StockinReturnProductTaskItemEntity>> taskItemEntityListMap = returnProductTaskItemService.list(new LambdaQueryWrapper<StockinReturnProductTaskItemEntity>()
                .in(StockinReturnProductTaskItemEntity::getReturnProductTaskId, returnTaskIdList)).stream()
                .collect(Collectors.groupingBy(StockinReturnProductTaskItemEntity::getReturnProductTaskId));
        this.buildPurchaseInfo(taskEntityList, supplierDtoMap);
        taskEntityList.stream().collect(Collectors.groupingBy(StockinReturnProductTaskEntity::getPurchaserUserName))
                .forEach((purchaserUserName, list) -> {
                    String remindStr = list.stream().map(taskEntity -> {
                        List<StockinReturnProductTaskItemEntity> taskItemEntityList = taskItemEntityListMap.getOrDefault(taskEntity.getReturnProductTaskId(), Lists.newArrayList());
                        if (CollectionUtils.isEmpty(taskItemEntityList)) {
                            return StringUtils.EMPTY;
                        }
                        SupplierDto supplierDto = supplierDtoMap.getOrDefault(taskEntity.getSupplierId(), null);
                        String supplierName = supplierDto != null ? supplierDto.getSupplierName() : taskEntity.getSupplierName();
                        int returnedQty = taskItemEntityList.stream().mapToInt(StockinReturnProductTaskItemEntity::getActualReturnQty).sum();
                        int days = com.nsy.api.core.apicore.util.DateUtils.daysBetween(taskEntity.getDeliveryDate(), new Date());
                        return String.format("%s  有返工退货单【%s】【%s】发出的退货共【%d】件【%d】天了还未收货确认，请及时跟进", supplierName, taskEntity.getReturnProductTaskId(),
                                com.nsy.api.core.apicore.util.DateUtils.format(taskEntity.getDeliveryDate()), returnedQty, days);
                    }).collect(Collectors.joining("\n"));
                    SendDingDingMessage sendDingDingMessage = new SendDingDingMessage();
                    sendDingDingMessage.setText(remindStr);
                    sendDingDingMessage.setUserNameList(Collections.singletonList(purchaserUserName));
                    //发送kafka消息
                    String businessMark = KafkaConstant.WMS_SEND_DINGDING_MESSAGE_NAME;
                    messageProducer.sendMessage(businessMark, KafkaConstant.WMS_SEND_DINGDING_MESSAGE_TOPIC, sendDingDingMessage);
                });
        LOGGER.info("StockinReturnTaskReceiptReminderJob end");
    }

    private void buildPurchaseInfo(List<StockinReturnProductTaskEntity> taskEntityList, Map<Integer, SupplierDto> supplierDtoMap) {
        Map<Integer, SysUserInfo> userInfoMap = new HashMap<>();
        for (StockinReturnProductTaskEntity detail : taskEntityList) {
            try {
                if (!userInfoMap.containsKey(detail.getSupplierId())) {
                    userInfoMap.put(detail.getSupplierId(), userApiService.getUserInfoByUserId(supplierDtoMap.get(detail.getSupplierId()).getContactPurchaserEmpId()));
                }
                detail.setPurchaserUserName(userInfoMap.get(detail.getSupplierId()).getUserAccount());
                detail.setPurchaserRealName(userInfoMap.get(detail.getSupplierId()).getUserName());
                detail.setPurchaseUserId(userInfoMap.get(detail.getSupplierId()).getUserId());
            } catch (BusinessServiceException e) {
                LOGGER.error("获取工厂对应的采购员失败,按退货单上人员发送消息,未获取到的采购员id为:{} ", detail.getSupplierId());
            }
        }
    }
}
