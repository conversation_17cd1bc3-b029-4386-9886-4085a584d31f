package com.nsy.wms.elasticjob.stockout;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderScanTaskService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderScanTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
public class StockoutScanTaskAutoCompleteJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutScanTaskAutoCompleteJob.class);

    public static final String JOB_NAME = "StockoutScanTaskAutoCompleteJob";

    @Resource
    private StockoutOrderScanTaskMapper scanTaskMapper;
    @Resource
    private StockoutOrderScanTaskService scanTaskService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("StockoutScanTaskAutoCompleteJob start");
        LoginInfoService.setName(JOB_NAME);
        try {
            autoComplete();
        } catch (RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        } finally {
            LOGGER.info(JOB_NAME);
            LoginInfoService.removeName();
            LOGGER.info("StockoutScanTaskAutoCompleteJob end");
        }
    }

    private void autoComplete() {
        List<StockoutOrderScanTaskEntity> shouldAutoCompleteList = scanTaskMapper.getShouldAutoCompleteList();
        shouldAutoCompleteList.forEach(scanTask -> {
            try {
                scanTaskService.cancelScanTask(scanTask);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        });
    }

}
