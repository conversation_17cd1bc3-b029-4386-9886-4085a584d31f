package com.nsy.wms.elasticjob.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.wms.business.service.stockin.StcokinOrderTimeService;
import com.nsy.wms.business.service.stockin.StockinOrderLogService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderLogEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 初始化stockin_order_time表，用完既删
 */
@Component
public class StockinOrderTimeJob extends BaseSimpleJob {

    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StcokinOrderTimeService stcokinOrderTimeService;
    @Autowired
    StockinOrderLogService stockinOrderLogService;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        Integer pageIndex = 1;
        Page<StockinOrderEntity> objectPage = new Page<>(pageIndex, 100);
        LambdaQueryWrapper<StockinOrderEntity> wrapper = new LambdaQueryWrapper<StockinOrderEntity>().select(StockinOrderEntity::getStockinOrderId, StockinOrderEntity::getCreateDate, StockinOrderEntity::getUpdateDate);
        //查询stockin_order_item最小的值，防止重复插入
        Integer minId = stcokinOrderTimeService.getBaseMapper().getMinId();
        if (Objects.nonNull(minId))
            wrapper.lt(StockinOrderEntity::getStockinOrderId, minId);
        Page<StockinOrderEntity> page = stockinOrderService.page(objectPage, wrapper);
        LambdaQueryWrapper<StockinOrderLogEntity> select = new LambdaQueryWrapper<StockinOrderLogEntity>().select(StockinOrderLogEntity::getStockinOrderId, StockinOrderLogEntity::getStockinOrderLogType, StockinOrderLogEntity::getCreateDate, StockinOrderLogEntity::getUpdateDate);
        while (page.getSize() > 0) {
            List<Integer> collect = page.getRecords().stream().map(StockinOrderEntity::getStockinOrderId).collect(Collectors.toList());
            select.clear();
            select.select(StockinOrderLogEntity::getStockinOrderId, StockinOrderLogEntity::getStockinOrderLogType, StockinOrderLogEntity::getCreateDate, StockinOrderLogEntity::getUpdateDate);
            Map<Integer, List<StockinOrderLogEntity>> logMap = stockinOrderLogService.list(select.in(StockinOrderLogEntity::getStockinOrderId, collect)).stream().collect(Collectors.groupingBy(StockinOrderLogEntity::getStockinOrderId));
            stcokinOrderTimeService.initData(page.getRecords(), logMap);
            pageIndex += 1;
            objectPage.setCurrent(pageIndex);
            page = stockinOrderService.page(objectPage, wrapper);
        }
    }
}
