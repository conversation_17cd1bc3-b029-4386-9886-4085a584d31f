package com.nsy.wms.elasticjob.base;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.nsy.api.core.apicore.log.ActionLog;
import com.nsy.api.core.apicore.log.ActionLoggerImpl;
import com.nsy.api.core.apicore.log.ActionResult;
import com.nsy.api.core.apicore.log.LogConstants;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.core.apicore.util.TraceIdUtil;
import com.nsy.wms.repository.entity.QuartzJobLogEntity;
import com.nsy.wms.business.manage.user.UserDictionaryCacheService;
import com.nsy.wms.business.service.QuartzJobLogService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import javax.inject.Inject;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class BaseJob {
    @Inject
    QuartzJobLogService quartzJobLogService;
    @Autowired
    private UserDictionaryCacheService dictionaryCacheService;

    protected QuartzJobLogEntity createJobLog(Map<String, Object> jobDataMap) {
        QuartzJobLogEntity jobLogEntity = new QuartzJobLogEntity();
        jobLogEntity.setJobName(getJobName(jobDataMap));
        jobLogEntity.setParams(getJobParams(jobDataMap));
        jobLogEntity.setStatus("new");
        jobLogEntity.setBeginDate(new Date());
        jobLogEntity.setCreateBy(getJobName(jobDataMap));
        jobLogEntity.setCreateDate(new Date());
        jobLogEntity.setLogPath(TraceIdUtil.getTraceId());
        quartzJobLogService.save(jobLogEntity);
        return jobLogEntity;
    }

    protected String getJobName(Map<String, Object> jobDataMap) {
        return jobDataMap.get("jobName").toString();
    }

    protected void buildActionLog() {
        ActionLoggerImpl actionLogger = ActionLoggerImpl.get();
        actionLogger.currentActionLog().setRequestId(TraceIdUtil.getTraceId());
        actionLogger.currentActionLog().setAction(MDC.get(LogConstants.MDC_ACTION));
    }


    protected boolean flushTraceLog() {
        ActionLoggerImpl actionLogger = ActionLoggerImpl.get();
        if (actionLogger.currentActionLog().getResult() != ActionResult.SUCCESS) {
            return true;
        }
        return false;
    }

    private String getJobParams(Map<String, Object> objects) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entity : objects.entrySet()) {
            sb.append(entity.getKey()).append('=').append(entity.getValue()).append('&');
        }
        return sb.toString();
    }

    protected void updateJobLogStatus(QuartzJobLogEntity jobLogEntity, String status) {
        jobLogEntity.setStatus(status);
        jobLogEntity.setEndDate(new Date());
        jobLogEntity.setUpdateDate(new Date());
        quartzJobLogService.updateById(jobLogEntity);
    }

    protected void updateJobLogPath(QuartzJobLogEntity jobLogEntity) {
        ActionLog actionLog = ActionLoggerImpl.get().currentActionLog();
        if (actionLog != null && StringUtils.hasText(actionLog.getTraceLogPath())) {
            jobLogEntity.setLogPath(actionLog.getTraceLogPath());
            jobLogEntity.setUpdateBy("BaseJob");
            jobLogEntity.setUpdateDate(new Date());
            quartzJobLogService.updateById(jobLogEntity);
        }
        LoginInfoService.removeName();
    }


    protected Map<String, Object> getJobDataMap(ShardingContext shardingContext) {
        Map<String, Object> jobDataMap = new HashMap<>();
        jobDataMap.put("currentTreadId", Thread.currentThread().getId());
        jobDataMap.put("shardingTotalCount", shardingContext.getShardingTotalCount());
        jobDataMap.put("shardingItem", shardingContext.getShardingItem());
        jobDataMap.put("shardingParameter", shardingContext.getShardingParameter());
        jobDataMap.put("jobName", shardingContext.getJobName());
        jobDataMap.put("jobParameter", shardingContext.getJobParameter());
        return jobDataMap;
    }

    protected void setLocation(ShardingContext shardingContext) {
        String location = shardingContext.getJobParameter();
        if (dictionaryCacheService.systemLocation().stream().anyMatch(systemLocation -> systemLocation.getLocation().equals(location))) {
            TenantContext.setTenant(location);
        }
    }
}
