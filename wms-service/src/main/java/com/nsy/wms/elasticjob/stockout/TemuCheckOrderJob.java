package com.nsy.wms.elasticjob.stockout;

import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderTemuExtendCancelService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 拼多多前置自动同步抢仓
 */
@Component
public class TemuCheckOrderJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(TemuCheckOrderJob.class);

    @Resource
    StockoutOrderTemuExtendCancelService stockoutOrderTemuExtendCancelService;


    public static final String JOB_NAME = "TemuCheckOrderJob";

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {

        LOGGER.info("TemuCheckOrderJob start");
        LoginInfoService.setName(JOB_NAME);
        try {
            stockoutOrderTemuExtendCancelService.checkCancel();
            LOGGER.info("TemuCheckOrderJob end");
        } catch (RuntimeException e) {
            LOGGER.info("TemuCheckOrderJob exception message: {}", e.getMessage());
            throw e;
        } finally {
            LoginInfoService.removeName();
        }


    }
}

