package com.nsy.wms.elasticjob.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.wms.business.service.qa.StockinQaOrderSkuTypeInfoService;
import com.nsy.wms.elasticjob.base.BaseSimpleJob;
import com.nsy.wms.repository.entity.qa.StockinQaOrderSkuTypeInfoEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-05-19 10:44
 */
@Component
public class StockinQaOrderSkuTypeInitJob extends BaseSimpleJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQaOrderSkuTypeInitJob.class);

    @Autowired
    StockinQaOrderSkuTypeInfoService qaOrderSkuTypeInfoService;

    private static final int PAGE_SIZE = 200;

    @Override
    protected void run(Map<String, Object> jobDataMap) throws Exception {
        LOGGER.info("StockinQaOrderSkuTypeInitJob start");
        processData();
        LOGGER.info("StockinQaOrderSkuTypeInitJob end");
    }

    private void processData() {
        LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaOrderSkuTypeInfoEntity::getTaskId, 0);
        //如果没有任务id为空数据不做处理
        if (qaOrderSkuTypeInfoService.count(queryWrapper) == 0) {
            return;
        }
        int pageIndex = 1;
        while (true) {
            try {
                Page<StockinQaOrderSkuTypeInfoEntity> page = new Page<>(pageIndex, PAGE_SIZE);
                PageResponse<StockinQaOrderSkuTypeInfoEntity> result = qaOrderSkuTypeInfoService.pageList(page);
                List<StockinQaOrderSkuTypeInfoEntity> records = result.getContent();
                if (records.isEmpty()) {
                    break;
                }
                qaOrderSkuTypeInfoService.updateBatchById(records);
                //由于更新之后总数量减少,但是页码一直加1导致有数据会漏更新,所以需要重新获取总数量判断是否结束循环
                if (qaOrderSkuTypeInfoService.count(queryWrapper) == 0) {
                    break;
                }
            } catch (Exception e) {
                LOGGER.error("StockinQaOrderSkuTypeInitJob error", e);
            }
        }
    }
}