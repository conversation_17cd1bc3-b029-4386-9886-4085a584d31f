package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.wms.repository.entity.qa.StockinQaProductSampleRecordImgEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaProductSampleRecordImgMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 产前样记录图片表业务实现
 * @date: 2024-11-13 10:09
 */
@Service
public class StockinQaProductSampleRecordImgService extends ServiceImpl<StockinQaProductSampleRecordImgMapper, StockinQaProductSampleRecordImgEntity> {

    public List<StockinQaProductSampleRecordImgEntity> getImagListByRecordId(Integer recordId) {
        LambdaQueryWrapper<StockinQaProductSampleRecordImgEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaProductSampleRecordImgEntity::getRecordId, recordId);
        return this.list(queryWrapper);
    }

    /**
     * 保存图片信息
     *
     * @param recordId
     * @param processName
     * @param imgList
     */
    public void saveImageList(Integer recordId, String processName, List<String> imgList) {
        LambdaQueryWrapper<StockinQaProductSampleRecordImgEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaProductSampleRecordImgEntity::getRecordId, recordId);
        queryWrapper.eq(StockinQaProductSampleRecordImgEntity::getProcessName, processName);
        List<StockinQaProductSampleRecordImgEntity> recordImgEntityList = this.list(queryWrapper);
        //如果原先有图片则删除对应图片
        if (!CollectionUtil.isEmpty(recordImgEntityList)) {
            this.removeByIds(recordImgEntityList.stream().map(StockinQaProductSampleRecordImgEntity::getId).collect(Collectors.toList()));
        }
        //无图片则不操作
        if (CollectionUtil.isEmpty(imgList)) {
            return;
        }
        List<StockinQaProductSampleRecordImgEntity> saveEntityList = new ArrayList<>();
        imgList.forEach(detail -> {
            StockinQaProductSampleRecordImgEntity imgEntity = new StockinQaProductSampleRecordImgEntity();
            imgEntity.setLocation(TenantContext.getTenant());
            imgEntity.setImgUrl(detail);
            imgEntity.setRecordId(recordId);
            imgEntity.setProcessName(processName);
            saveEntityList.add(imgEntity);
        });
        this.saveBatch(saveEntityList);
    }
}
