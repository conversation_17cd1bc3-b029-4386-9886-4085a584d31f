package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.wms.repository.entity.stockin.StockinOrderPushRecordsEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinOrderPushRecordsMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/10/19 11:25
 */
@Service
public class StockinOrderPushRecordsService extends ServiceImpl<StockinOrderPushRecordsMapper, StockinOrderPushRecordsEntity> {

    /**
     * 忽略地区查询推送记录
     *
     * @param recordId
     * @return
     */
    public StockinOrderPushRecordsEntity getPushRecordIgnoreTenant(String recordId) {
        if (!StringUtils.hasText(recordId)) {
            return null;
        }
        return this.baseMapper.getPushRecordIgnoreTenant(Integer.valueOf(recordId));
    }

    public Integer getTotalPushQtyByStockinOrderItemId(Integer stockinItemId) {
        return this.baseMapper.getTotalPushQtyByStockinOrderItemId(stockinItemId);
    }

}
