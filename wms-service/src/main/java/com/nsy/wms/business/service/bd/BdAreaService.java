package com.nsy.wms.business.service.bd;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.domain.bd.BdAreaModel;
import com.nsy.api.wms.domain.bd.BdSpaceArea;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.enumeration.bd.BdChangLogTypeEnum;
import com.nsy.api.wms.request.bd.BdAreaListRequest;
import com.nsy.api.wms.request.bd.BdAreaRequest;
import com.nsy.api.wms.request.bd.BdSpaceAreaSelectRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.AreaResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.config.RedisConfig;
import com.nsy.wms.repository.entity.bd.BdAreaEntity;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdAreaMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdSpaceMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.RedisClient;
import com.nsy.wms.utils.Validator;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.Table;
import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
public class BdAreaService extends ServiceImpl<BdAreaMapper, BdAreaEntity> {

    @Autowired
    BdSpaceService spaceService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    BdChangeLogService changeLogService;
    @Autowired
    RedisClient redisClient;
    @Autowired
    BdSpaceAreaService spaceAreaService;
    @Autowired
    BdSpaceMapper bdSpaceMapper;
    @Autowired
    BdErpSpaceMappingService bdErpSpaceMappingService;

    public PageResponse<BdAreaModel> getAreaList(BdAreaListRequest request) {
        Page page = new Page(request.getPageIndex(), request.getPageSize());
        List<BdAreaModel> bdAreaModelList = this.baseMapper.pageList(page, request);
        PageResponse<BdAreaModel> pageResponse = new PageResponse<>();
        pageResponse.setContent(bdAreaModelList);
        pageResponse.setTotalCount(page.getTotal());
        return pageResponse;
    }

    public BdAreaEntity findByAreaNameAndSpaceId(String areaName, Integer spaceId) {
        LambdaQueryWrapper<BdAreaEntity> queryWrapper = new LambdaQueryWrapper<BdAreaEntity>().eq(BdAreaEntity::getAreaName, areaName).eq(BdAreaEntity::getSpaceId, spaceId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    public BdAreaEntity getByAreaNameAndSpaceId(String areaName, Integer spaceId) {
        BdAreaEntity area = findByAreaNameAndSpaceId(areaName, spaceId);
        if (Objects.isNull(area))
            throw new BusinessServiceException(String.format("%s %s的区域不存在", spaceId, areaName));
        return area;
    }

    @Transactional
    public void addArea(BdAreaRequest request) {
        validateAreaAddRequest(request);
        // 校验仓库名称是否已存在
        BdAreaEntity tempAreaEntity = this.findByAreaNameAndSpaceId(request.getAreaName(), request.getSpaceId());
        if (Objects.nonNull(tempAreaEntity)) {
            BdSpaceEntity tempSpaceEntity = spaceService.getById(tempAreaEntity.getSpaceId());
            throw new BusinessServiceException(String.format("仓库【%s】,【%s】区域已存在, 请更换区域名称", tempSpaceEntity.getSpaceName(), request.getAreaName()));
        }
        BdAreaEntity entity = new BdAreaEntity();
        BeanUtilsEx.copyProperties(request, entity, "areaId");
        entity.setCreateBy(loginInfoService.getName());
        entity.setCreateDate(new Date());
        entity.setUpdateBy(loginInfoService.getName());
        entity.setUpdateDate(new Date());
        entity.setIsDeleted(0);
        entity.setLocation(TenantContext.getTenant());
        this.save(entity);
        String content = String.format("%s 新增区域:%s", loginInfoService.getName(), entity.getAreaName());
        changeLogService.addChangeLog(BdChangLogTypeEnum.AREA, content, BdAreaEntity.class.getAnnotation(TableName.class).value(), null);
    }

    @Transactional
    @CacheEvict(value = {CacheKeyConstant.AREA_NAME_SELECT, CacheKeyConstant.ALL_AREA_NAME_SELECT}, allEntries = true)
    public void updateArea(Integer areaId, BdAreaRequest request) {
        validateAreaAddRequest(request);
        BdAreaEntity entity = this.baseMapper.selectById(areaId);
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("区域不存在");
        }
        BdAreaEntity tempAreaEntity = this.findByAreaNameAndSpaceId(request.getAreaName(), request.getSpaceId());
        if (Objects.nonNull(tempAreaEntity) && !entity.getAreaId().equals(tempAreaEntity.getAreaId())) {
            BdSpaceEntity tempSpaceEntity = spaceService.getById(tempAreaEntity.getSpaceId());
            throw new BusinessServiceException(String.format("仓库【%s】,【%s】区域已存在", tempSpaceEntity.getSpaceName(), request.getAreaName()));
        }
        String detail = JsonMapper.toJson(entity);
        entity.setAreaName(request.getAreaName());
        entity.setDescription(request.getDescription());
        entity.setSpaceId(request.getSpaceId());
        entity.setFloor(request.getFloor());
        entity.setUpdateBy(loginInfoService.getName());
        entity.setUpdateDate(new Date());
        entity.setLocation(TenantContext.getTenant());
        entity.setBrandName(StringUtils.hasText(request.getBrandName()) ? request.getBrandName() : "");
        Boolean hasKey = redisClient.hasKey(RedisConfig.MODULE_NAME + ":S:AREA_NAME_SELECT:" + entity.getSpaceId());
        if (hasKey) {
            redisClient.del(RedisConfig.MODULE_NAME + ":S:AREA_NAME_SELECT:" + entity.getSpaceId());
        }
        String content = String.format("%s 修改区域:%s", loginInfoService.getName(), entity.getAreaName());
        this.baseMapper.updateById(entity);
        changeLogService.addChangeLog(BdChangLogTypeEnum.AREA, content, BdAreaEntity.class.getAnnotation(Table.class).name(), detail);
    }

    @Transactional
    @CacheEvict(value = {CacheKeyConstant.AREA_NAME_SELECT, CacheKeyConstant.ALL_AREA_NAME_SELECT}, allEntries = true)
    public void changeStatus(Integer areaId, Integer isDeleted) {
        String status = isDeleted == 1 ? "停用" : "启用";
        BdAreaEntity entity = this.baseMapper.selectById(areaId);
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("区域不存在");
        }
        if (isDeleted.compareTo(entity.getIsDeleted()) == 0)
            throw new InvalidRequestException(String.format("%s已是%s状态", entity.getAreaName(), status));
        entity.setIsDeleted(isDeleted);
        entity.setUpdateBy(loginInfoService.getName());
        entity.setLocation(TenantContext.getTenant());
        Boolean hasKey = redisClient.hasKey(RedisConfig.MODULE_NAME + ":S:AREA_NAME_SELECT:" + entity.getSpaceId());
        if (hasKey) {
            redisClient.del(RedisConfig.MODULE_NAME + ":S:AREA_NAME_SELECT:" + entity.getSpaceId());
        }
        this.baseMapper.updateById(entity);
        String content = String.format("%s %s%s", loginInfoService.getName(), status, entity.getAreaName());
        changeLogService.addChangeLog(BdChangLogTypeEnum.AREA, content, BdAreaEntity.class.getAnnotation(Table.class).name());
        // 区域停用触发库区停用
        if (isDeleted == 1) {
            spaceAreaService.deleteSpaceAreaByAreaId(areaId);
        }
    }

    // 仓库停用，触发区域停用
    @Transactional
    @CacheEvict(value = {CacheKeyConstant.AREA_NAME_SELECT, CacheKeyConstant.ALL_AREA_NAME_SELECT}, allEntries = true)
    public void deleteAreaBySpaceId(Integer spaceId) {
        LambdaQueryWrapper<BdAreaEntity> queryWrapper = new LambdaQueryWrapper<BdAreaEntity>().eq(BdAreaEntity::getSpaceId, spaceId).eq(BdAreaEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<BdAreaEntity> areaEntityList = this.baseMapper.selectList(queryWrapper);
        areaEntityList.forEach(entity -> {
            entity.setIsDeleted(IsDeletedConstant.DELETED);
            entity.setUpdateBy(loginInfoService.getName());
            this.baseMapper.updateById(entity);
            String content = String.format("%s 停用 %s,(仓库停用触发区域停用)", loginInfoService.getName(), entity.getAreaName());
            changeLogService.addChangeLog(BdChangLogTypeEnum.AREA, content, BdAreaEntity.class.getAnnotation(Table.class).name());
            spaceAreaService.deleteSpaceAreaByAreaId(entity.getAreaId());
        });
    }

    public AreaResponse getAreaInfo(Integer areaId) {
        AreaResponse response = new AreaResponse();
        BdAreaEntity entity = this.baseMapper.selectById(areaId);
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("区域不存在");
        }
        BeanUtilsEx.copyProperties(entity, response);
        return response;
    }

    @Cacheable(value = CacheKeyConstant.AREA_NAME_SELECT, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public List<SelectIntegerModel> getAreaSelect() {
        LambdaQueryWrapper<BdAreaEntity> queryWrapper = new LambdaQueryWrapper<BdAreaEntity>().eq(BdAreaEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<BdAreaEntity> areaEntityList = this.baseMapper.selectList(queryWrapper);
        return areaEntityList.stream().map(entity -> new SelectIntegerModel(entity.getAreaId(), entity.getAreaName())).collect(Collectors.toList());
    }

    @Cacheable(value = CacheKeyConstant.ALL_AREA_NAME_SELECT, key = "T(com.nsy.wms.utils.mp.TenantContext).getTenant")
    public List<SelectIntegerModel> getAllAreaSelect() {
        List<BdAreaEntity> areaEntityList = this.baseMapper.selectList(null);
        return areaEntityList.stream().map(entity -> new SelectIntegerModel(entity.getAreaId(), entity.getAreaName())).collect(Collectors.toList());
    }

    // 新增修改，基本数据校验
    public void validateAreaAddRequest(BdAreaRequest request) {
        Validator.isValid(request.getSpaceId(), Objects::nonNull, "请选择仓库类型");
        Validator.isValid(request.getAreaName(), Objects::nonNull, "请填写区域名称");
    }

    // 通过仓库id查找区域
    @Cacheable(value = CacheKeyConstant.AREA_NAME_SELECT, key = "#spaceId")
    public List<SelectIntegerModel> getAreaSelectBySpaceId(Integer spaceId) {
        LambdaQueryWrapper<BdAreaEntity> queryWrapper = new LambdaQueryWrapper<BdAreaEntity>().eq(BdAreaEntity::getSpaceId, spaceId).eq(BdAreaEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<BdAreaEntity> areaEntityList = this.baseMapper.selectList(queryWrapper);
        return areaEntityList.stream().map(entity -> new SelectIntegerModel(entity.getAreaId(), entity.getAreaName())).collect(Collectors.toList());
    }

    @Cacheable(value = CacheKeyConstant.ALL_AREA_NAME_SELECT, key = "#spaceId")
    public List<SelectIntegerModel> getAllAreaSelectBySpaceId(Integer spaceId) {
        LambdaQueryWrapper<BdAreaEntity> queryWrapper = new LambdaQueryWrapper<BdAreaEntity>().eq(BdAreaEntity::getSpaceId, spaceId);
        List<BdAreaEntity> areaEntityList = this.baseMapper.selectList(queryWrapper);
        return areaEntityList.stream().map(entity -> new SelectIntegerModel(entity.getAreaId(), entity.getAreaName())).collect(Collectors.toList());
    }

    /**
     * TODO: 根据 wms space_id 获取推荐 area_id
     */
    public Integer getAreaIdBySpaceId(Integer spaceId) {
        BdErpSpaceMappingEntity entity = bdErpSpaceMappingService.getDefaultEntityBySpaceId(spaceId);
        if (entity == null)
            return null;
        else
            return entity.getAreaId();
    }

    /**
     * 根据库区ID获取区域ID
     */
    public Integer getAreaIdBySpaceAreaId(Integer spaceAreaId) {
        BdSpaceArea bdSpaceArea = spaceAreaService.getSpaceAreaById(spaceAreaId);
        if (Objects.isNull(bdSpaceArea))
            throw new BusinessServiceException(String.format("未找到库区：%s", spaceAreaId));
        return bdSpaceArea.getAreaId();
    }

    public BdAreaEntity getAreaById(Integer areaId) {
        BdAreaEntity areaEntity = getById(areaId);
        if (Objects.isNull(areaEntity))
            throw new BusinessServiceException(String.format("区域 %s 不存在", areaId));
        return areaEntity;
    }

    public List<SelectIntegerModel> getAreaSelectBySpaceIdList(BdSpaceAreaSelectRequest request) {
        LambdaQueryWrapper<BdAreaEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BdAreaEntity::getAreaId, BdAreaEntity::getAreaName);
        queryWrapper.eq(BdAreaEntity::getIsDeleted, 0);
        if (!CollectionUtils.isEmpty(request.getSpaceIdList()))
            queryWrapper.in(BdAreaEntity::getSpaceId, request.getSpaceIdList());
        if (!CollectionUtils.isEmpty(request.getAreaIdList()))
            queryWrapper.in(BdAreaEntity::getAreaId, request.getAreaIdList());

        List<BdAreaEntity> areaEntityList = this.baseMapper.selectList(queryWrapper);
        return areaEntityList.stream().map(entity -> new SelectIntegerModel(entity.getAreaId(), entity.getAreaName())).collect(Collectors.toList());
    }

    public String findAreaName(Integer areaId) {
        if (ObjectUtil.isNull(areaId)) {
            return StrUtil.EMPTY;
        }

        BdAreaEntity area = getById(areaId);
        if (ObjectUtil.isNull(area)) {
            return StrUtil.EMPTY;
        }

        return area.getAreaName();
    }
}
