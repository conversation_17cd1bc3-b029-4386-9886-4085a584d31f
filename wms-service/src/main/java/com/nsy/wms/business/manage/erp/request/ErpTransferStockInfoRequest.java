package com.nsy.wms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ErpTransferStockInfoRequest {

    /**
     * 操作人
     */
    @JsonProperty("UserName")
    private String userName;

    /**
     * 地区
     */
    @JsonProperty("Location")
    private String location;

    /**
     * erp 仓间调拨上架库存信息
     */
    @JsonProperty("ErpTransferStockInfos")
    private List<ErpTransferStockInfo> erpTransferStockInfos;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<ErpTransferStockInfo> getErpTransferStockInfos() {
        return erpTransferStockInfos;
    }

    public void setErpTransferStockInfos(List<ErpTransferStockInfo> erpTransferStockInfos) {
        this.erpTransferStockInfos = erpTransferStockInfos;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
