package com.nsy.wms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ErpBackTradeRequest {
    @JsonProperty("TradeIds")
    private List<String> tradeIds;
    @JsonProperty("PickIds")
    private List<Integer> pickIds;
    @JsonProperty("UserName")
    private String userName;
    @JsonProperty("EmpName")
    private String empName;
    @JsonProperty("IpAddress")
    private String ipAddress;
    @JsonProperty("DeliveryLocation")
    private String location;
    @JsonProperty("IsForceCancel")
    private Integer isForceCancel;

    private Integer cancelType;

    public Integer getCancelType() {
        return cancelType;
    }

    public void setCancelType(Integer cancelType) {
        this.cancelType = cancelType;
    }

    public List<String> getTradeIds() {
        return tradeIds;
    }

    public void setTradeIds(List<String> tradeIds) {
        this.tradeIds = tradeIds;
    }

    public List<Integer> getPickIds() {
        return pickIds;
    }

    public void setPickIds(List<Integer> pickIds) {
        this.pickIds = pickIds;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getIsForceCancel() {
        return isForceCancel;
    }

    public void setIsForceCancel(Integer isForceCancel) {
        this.isForceCancel = isForceCancel;
    }
}
