package com.nsy.wms.business.manage.external.response;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXD
 * 2022/12/19
 **/

public class DeclareFormBaseResponse {

    @JsonProperty("Code")
    private String code;
    @JsonProperty("Info")
    private String info;

    @JsonProperty("Success")
    private String success;

    @JsonProperty("MessageType")
    private String messageType;

    @JsonProperty("Data")
    private Data data;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getSuccess() {
        return success;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public static class Data {
        private DecMessage decMessage;

        public DecMessage getDecMessage() {
            return decMessage;
        }

        public void setDecMessage(DecMessage decMessage) {
            this.decMessage = decMessage;
        }
    }
}
