package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.request.stockout.StockoutBatchListRequest;
import com.nsy.api.wms.response.stockout.StockoutBatchListCountResponse;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockoutBatchCountService {

    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;

    @Autowired
    StockoutOrderItemService stockoutOrderItemService;

    @Autowired
    StockoutPickingTaskService stockoutPickingTaskService;

    @Autowired
    private StockoutBatchMapper stockoutBatchMapper;


    public StockoutBatchListCountResponse pageSearchCount(StockoutBatchListRequest request) {
        if (Objects.isNull(request.getBatchIds())) {
            request.setBatchIds(new ArrayList<>());
        }

        if (StringUtils.hasText(request.getStockoutOrderNo())) {
            List<Integer> batchIdsByStockoutOrderNo = stockoutBatchOrderService.getBaseMapper().getBatchIdsByStockoutOrderNo(request.getStockoutOrderNo());
            if (CollectionUtils.isEmpty(batchIdsByStockoutOrderNo))
                return new StockoutBatchListCountResponse();
            request.getBatchIds().addAll(batchIdsByStockoutOrderNo);
        }

        if (StringUtils.hasText(request.getOrderNo())) {
            List<StockoutOrderItemEntity> list = stockoutOrderItemService.list(new LambdaQueryWrapper<StockoutOrderItemEntity>().select(StockoutOrderItemEntity::getStockoutOrderId)
                    .eq(StockoutOrderItemEntity::getOrderNo, request.getOrderNo()));
            if (CollectionUtils.isEmpty(list)) {
                return new StockoutBatchListCountResponse();
            }
            LambdaQueryWrapper<StockoutBatchOrderEntity> lambdaQueryWrapper = new LambdaQueryWrapper<StockoutBatchOrderEntity>()
                    .in(StockoutBatchOrderEntity::getStockoutOrderId, list.stream().map(StockoutOrderItemEntity::getStockoutOrderId).collect(Collectors.toList()));
            List<StockoutBatchOrderEntity> stockoutBatchOrderList = stockoutBatchOrderService.list(lambdaQueryWrapper);
            List<Integer> batchIdList = stockoutBatchOrderList.stream().map(StockoutBatchOrderEntity::getBatchId).collect(Collectors.toList());
            request.getBatchIds().addAll(batchIdList);
        }

        if (Objects.nonNull(request.getPickingTaskId())) {
            StockoutPickingTaskEntity byId = stockoutPickingTaskService.getById(request.getPickingTaskId());
            if (Objects.isNull(byId)) {
                return new StockoutBatchListCountResponse();
            }
            request.getBatchIds().add(byId.getBatchId());
        }

        if (StringUtils.hasText(request.getStockoutWavePlanType()))
            request.setIsMergeBatch(StockoutWavePlanTypeEnum.GROUP_WAVE.name().equals(request.getStockoutWavePlanType()) ? 1 : 0);

        return getResponseBySplitDate(request);
    }

    private StockoutBatchListCountResponse getResponseBySplitDate(StockoutBatchListRequest request) {
        StockoutBatchListCountResponse response = new StockoutBatchListCountResponse();

        List<StockoutBatchListCountResponse> resultList = new ArrayList<>();

        // 时间范围拆解，三天循环
        Long startTime = request.getStartDate().getTime();
        Long endTime = request.getEndDate().getTime();
        Long threeDays = 1000 * 60 * 60 * 24 * 3L;

        Long tempStartTime = startTime;
        Long tempEndTime = startTime + threeDays;
        while (tempEndTime <= endTime) {
            addQtyBySearchResult(resultList, tempStartTime, tempEndTime, request);

            tempStartTime = tempEndTime;
            tempEndTime += threeDays;
        }
        if (tempStartTime < endTime) {
            addQtyBySearchResult(resultList, tempStartTime, endTime, request);
        }

        response.setQty(resultList.stream().mapToInt(StockoutBatchListCountResponse::getQty).sum());
        response.setSkuQty((int) resultList.stream().map(StockoutBatchListCountResponse::getSku).filter(StringUtils::hasText).distinct().count());
        response.setOrderQty((int) resultList.stream().map(StockoutBatchListCountResponse::getOrderNo).filter(StringUtils::hasText).distinct().count());
        response.setFirstOrderByStoreQty(resultList.stream().filter(detail -> !Objects.isNull(detail.getIsFirstOrderByStore()) && 1 == detail.getIsFirstOrderByStore()).mapToInt(StockoutBatchListCountResponse::getQty).sum());

        return response;
    }

    private void addQtyBySearchResult(List<StockoutBatchListCountResponse> resultList, Long startTime, Long endTime, StockoutBatchListRequest request) {
        Date tempStartDate = new Date(startTime);
        Date tempEndDate = new Date(endTime);
        request.setStartDate(tempStartDate);
        request.setEndDate(tempEndDate);

        resultList.addAll(stockoutBatchMapper.pageSearchCount(request));
    }

}
