package com.nsy.wms.business.service.bd;

import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.wms.repository.entity.bd.BdCountryEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdCountryMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;


@Service
public class BdCountryService extends ServiceImpl<BdCountryMapper, BdCountryEntity> {

    public String getByCountryEnNameOrCode(StockoutReceiverInfo receiver) {
        LambdaQueryWrapper<BdCountryEntity> wp = new LambdaQueryWrapper<>();
        wp.eq(BdCountryEntity::getCountryEnglish, receiver.getReceiverCountry());
        List<BdCountryEntity> list = list(wp);
        if (CollectionUtils.isEmpty(list) && StringUtils.hasText(receiver.getCountryCode())) {
            wp.clear();
            wp.eq(BdCountryEntity::getCountryEnglish, receiver.getCountryCode());
            list = list(wp);
        }
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.hasText(receiver.getCountryCode()) ? receiver.getCountryCode() : receiver.getReceiverCountry();
        }
        return list.get(0).getCountryCode();
    }
}
