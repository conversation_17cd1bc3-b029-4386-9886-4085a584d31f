package com.nsy.wms.business.manage.erp.request;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class ErpSaveReceiveItemRequest {

    @JsonProperty("Sku")
    private String sku;

    @JsonProperty("ReceivingQty")
    private Integer receivingQty;

    @JsonProperty("ReturnQty")
    private Integer returnQty;

    @JsonProperty("PurchasePlanNo")
    private String purchasePlanNo;

    @JsonProperty("Status")
    private String status;

    @JsonProperty("ReceiveDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiveDate;

    @JsonProperty("DetailId")
    private Integer detailId;

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getReceivingQty() {
        return receivingQty;
    }

    public void setReceivingQty(Integer receivingQty) {
        this.receivingQty = receivingQty;
    }

    public Integer getReturnQty() {
        return returnQty;
    }

    public void setReturnQty(Integer returnQty) {
        this.returnQty = returnQty;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }
}
