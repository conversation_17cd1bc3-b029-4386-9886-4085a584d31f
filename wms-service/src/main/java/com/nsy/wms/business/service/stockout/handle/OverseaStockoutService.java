package com.nsy.wms.business.service.stockout.handle;

import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockout.CancelOrderDTO;
import com.nsy.api.wms.domain.stockout.StockoutOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutOrderItemErpSpace;
import com.nsy.api.wms.domain.stockout.StockoutShipmentMessage;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.overseas.OverseasWarehouseOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPackingListStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutOrderAddRequest;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.erp.request.ErpFinishPartialPickItemRequest;
import com.nsy.wms.business.manage.erp.request.ErpFinishPartialPickRequest;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOverseasWarehouseOrderService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutShipmentErpPickingBoxService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOverseasWarehouseOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLogEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoGenerateUtil;
import com.nsy.wms.utils.randomid.FormNoTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 海外出库流程： 谷仓/仓搜 等海外仓发货出库
 * HXD
 * 2022/7/14
 **/
@Service
public class OverseaStockoutService implements StockoutTypeHandle {
    @Autowired
    StockoutOrderLogService logService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    private StockoutShipmentService shipmentService;
    @Autowired
    private StockoutShipmentItemService shipmentItemService;
    @Autowired
    private StockoutShipmentErpPickingBoxService pickingBoxService;
    @Autowired
    StockoutOverseasWarehouseOrderService overseasWarehouseOrderService;
    @Autowired
    private BdSpaceService spaceService;
    @Autowired
    MessageProducer producer;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    BdErpSpaceMappingService bdErpSpaceMappingService;

    @Override
    public CancelOrderDTO cancelOutOrder(StockoutOrderEntity stockoutOrderEntity) {
        if (StockoutOrderStatusEnum.DELIVERED.name().equals(stockoutOrderEntity.getStatus())) {
            throw new BusinessServiceException("取消失败，货物已经出库发货，实物追回后，走销售退货登记入库");
        }
        // 其余的 业务确认海外仓没发货后，可以直接取消
        stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.CANCELLED.name());
        producer.sendMessage(KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC_MARK, KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), stockoutOrderEntity.getStockoutOrderId()));
        return new CancelOrderDTO();
    }

    @Override
    public void addLogisticsNoLog(StockoutOrderEntity stockoutOrderEntity, BaseGetLogisticsNoResponse response) {
        stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.READY_OUTBOUND.name());
        logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.PUSH_OVERSEA,
                "已推送[海外仓],获取海外仓单号：" + stockoutOrderEntity.getLogisticsNo() + "等待下一步海外仓人员审核发货");
        // 推送海外仓成功后，更新海外仓订单状态为待发货
        try {
            StockoutOverseasWarehouseOrderEntity overseasOrder = overseasWarehouseOrderService.findByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            if (overseasOrder != null) {
                overseasWarehouseOrderService.updateStatusAndOverseasOrderNo(overseasOrder, OverseasWarehouseOrderStatusEnum.PENDING_SHIP.name(), response.getLogisticsTid(), null);
                logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.OVERSEA_PUSH_RESULT,
                        "推送海外仓成功，海外仓订单状态已更新为待发货");
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.OVERSEA_PUSH_RESULT,
                    "推送海外仓成功，但更新海外仓订单状态失败：" + e.getMessage());
        }
    }

    @Override
    public ErpFinishPartialPickRequest buildFinishPartialPickRequest(StockoutOrderEntity stockoutOrderEntity) {
        List<StockoutOrderItemEntity> orderItemList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        List<ErpFinishPartialPickItemRequest> itemRequestList = new LinkedList<>();
        //获取对应库位的erpSpaceId
        List<StockoutOrderItemErpSpace> stockoutOrderItemErpSpaces = bdErpSpaceMappingService.getBaseMapper().getErpSpaceIdByStockoutOrderItem(orderItemList.stream().map(StockoutOrderItemEntity::getStockoutOrderItemId).collect(Collectors.toList()));
        Map<Integer, List<StockoutOrderItemErpSpace>> collect = stockoutOrderItemErpSpaces.stream().collect(Collectors.groupingBy(StockoutOrderItemErpSpace::getStockoutOrderItemId));

        for (StockoutOrderItemEntity orderItemEntity : orderItemList) {
            List<StockoutOrderItemErpSpace> itemErpSpaces = collect.get(orderItemEntity.getStockoutOrderItemId());
            //加工的默认泉州台商仓
            Integer erpSpaceId = CollectionUtils.isEmpty(itemErpSpaces)
                    ? null : StockConstant.ENABLE.equals(itemErpSpaces.get(0).getIsNeedProcess())
                    ? SpaceAreaMapConstant.getProcessSpaceId(LocationEnum.valueOf(stockoutOrderEntity.getLocation())) : itemErpSpaces.get(0).getErpSpaceId();
            itemRequestList.add(new ErpFinishPartialPickItemRequest(orderItemEntity.getOrderItemId(), orderItemEntity.getSku(), orderItemEntity.getScanQty(), erpSpaceId, orderItemEntity.getErpPickItemId()));
        }
        ErpFinishPartialPickRequest request = new ErpFinishPartialPickRequest(stockoutOrderEntity.getErpPickId(), loginInfoService.getName(), loginInfoService.getName(), loginInfoService.getIpAddress());
        request.setItems(itemRequestList);
        return request;
    }

    /**
     * 生成装箱清单并同步订单系统
     * 1.生成装箱清单明细
     * 2.生成装箱清单
     * 3.同步erp
     * */
    @Override
    public StockoutShipmentEntity createShipmentAndSyncErp(Integer taskId, StockoutOrderEntity stockoutOrderEntity, Integer batchId, BigDecimal weight, String company) {
        List<StockoutOrderItemEntity> orderItemList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        if (CollectionUtils.isEmpty(orderItemList))
            throw new BusinessServiceException("找不到出库单明细无法装箱");
        List<StockoutShipmentItemEntity> shipmentItemList = new ArrayList<>();
        // 1.生成装箱清单明细
        orderItemList.forEach(item -> {
            item.setScanQty(item.getQty());
            item.setShipmentQty(item.getQty());
            StockoutShipmentItemEntity stockoutShipmentItemEntity = new StockoutShipmentItemEntity();
            BeanUtilsEx.copyProperties(item, stockoutShipmentItemEntity);
            stockoutShipmentItemEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            stockoutShipmentItemEntity.setBatchId(null);
            stockoutShipmentItemEntity.setCreateBy(loginInfoService.getName());
            stockoutShipmentItemEntity.setUpdateBy(loginInfoService.getName());
            shipmentItemList.add(stockoutShipmentItemEntity);
        });
        stockoutOrderItemService.updateBatchById(orderItemList);
        // 2.生成装箱清单
        StockoutShipmentEntity stockoutShipmentEntity = StockoutBuilding.buildSortShipment(stockoutOrderEntity,
                loginInfoService.getName(), FormNoGenerateUtil.generateFormNo(FormNoTypeEnum.STOCKOUT_SHIPMENT));
        stockoutShipmentEntity.setStatus(StockoutPackingListStatusEnum.PACKING_END.name());
        if (weight != null && stockoutShipmentEntity.getWeight() == null)
            stockoutShipmentEntity.setWeight(weight);
        if (StrUtil.isNotBlank(company))
            stockoutShipmentEntity.setLogisticsCompany(company);
        shipmentService.save(stockoutShipmentEntity);
        shipmentItemList.forEach(o -> o.setShipmentId(stockoutShipmentEntity.getShipmentId()));
        shipmentItemService.saveBatch(shipmentItemList);
        // 3.同步erp
        StockoutShipmentMessage message = new StockoutShipmentMessage();
        message.setShipmentId(stockoutShipmentEntity.getShipmentId());
        message.setUserName(loginInfoService.getUserName());
        message.setIpAddress(loginInfoService.getIpAddress());
        pickingBoxService.handleErpPickingBoxSync(message);
        return stockoutShipmentEntity;
    }

    @Override
    public void shipLog(StockoutOrderEntity order) {
        StockoutOrderLogEntity stockoutOrderLogEntity = new StockoutOrderLogEntity();
        stockoutOrderLogEntity.setStockoutOrderNo(order.getStockoutOrderNo());
        stockoutOrderLogEntity.setOrderLogType(StockoutOrderLogTypeEnum.DELIVERY_CONFIRM.getType());
        stockoutOrderLogEntity.setContent("此出库单【第三方仓库发货】单号：" + order.getStockoutOrderNo() + ",已发货确认");
        stockoutOrderLogEntity.setLocation(TenantContext.getTenant());
        stockoutOrderLogEntity.setCreateBy(loginInfoService.getName());
        logService.save(stockoutOrderLogEntity);
    }

    @Override
    public void setStatusAndLogisticsInfo(StockoutOrderInfo stockoutOrderInfo, StockoutOrderEntity stockoutOrderEntity, StockoutOrderAddRequest addRequest) {
        BdSpaceEntity space = spaceService.getById(stockoutOrderEntity.getSpaceId());
        if (space == null || !StringUtils.hasText(space.getSpaceName())) {
            throw new BusinessServiceException(stockoutOrderEntity.getStockoutOrderNo() + "该出库单无法找到对应仓库");
        }
        if (SpaceAreaMapConstant.WmsSpace.GUCANG_SPACE.equals(space.getSpaceName())) {
            stockoutOrderEntity.setLogisticsNo(null);
            addRequest.setLabelInfo(null);
        }
    }
}
