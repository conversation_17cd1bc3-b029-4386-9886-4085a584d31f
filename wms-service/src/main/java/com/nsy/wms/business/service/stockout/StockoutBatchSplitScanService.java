package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitTaskItemSowWall;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskStatus;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitScanBarcodeRequest;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskSkuResponse;
import com.nsy.wms.business.service.stockout.query.StockoutBatchSplitTaskItemQueryWrapper;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchOrderItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchSplitTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchSplitTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 波次扫描
 */
@Service
public class StockoutBatchSplitScanService extends ServiceImpl<StockoutBatchSplitTaskMapper, StockoutBatchSplitTaskEntity> implements IBatchSplitService {
    @Autowired
    StockoutBatchSplitTaskMapper taskMapper;
    @Autowired
    StockoutBatchSplitTaskItemMapper taskItemMapper;
    @Autowired
    StockoutBatchSplitTaskItemService splitItemService;
    @Autowired
    StockoutBatchOrderItemMapper stockoutBatchOrderItemMapper;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    StockoutBatchMapper batchMapper;
    @Autowired
    StockoutBatchSplitTaskItemQueryWrapper batchSplitTaskItemQueryWrapper;
    @Autowired
    StockoutBatchSplitTaskService stockoutBatchSplitTaskService;
    @Autowired
    StockoutBatchService batchService;
    @Autowired
    StockoutBatchSplitCommonService sortCommonService;
    @Autowired
    StockoutBatchSplitCommonCheckService sortCommonCheckService;
    @Autowired
    StockoutBatchSplitCommonBuildService sortCommonBuildService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutBatchOrderItemService stockoutBatchOrderItemService;
    @Autowired
    StockoutBatchSplitSecondaryService stockoutBatchSplitSecondaryService;
    @Autowired
    StockoutOrderNoticeLackService noticeLackService;

    @Override
    public StockoutBatchSplitTaskItemSowWall getStockoutBatchSplitTaskItemSowWall(StockoutBatchSplitScanBarcodeRequest request, StockoutBatchSplitTaskSkuResponse response) {
        request.setOutlet(0);
        List<StockoutBatchSplitTaskItemSowWall> allItemList = taskItemMapper.searchTaskItemByBatchId(request.getBatchId());
        if (CollectionUtils.isEmpty(allItemList)) {
            throw new BusinessServiceException(String.format("找不到波次号：【%s】的分拣任务信息", request.getBatchId()));
        }
        // 校验扫描条码
        sortCommonCheckService.validateSplitScanRequest(request);
        // 校验是否需要撤货：返回null表示需要撤货走撤货处理，不需要撤货返回对应需要分拣的item
        return sortCommonCheckService.checkWithdrawal(allItemList, request, response);
    }

    @Override
    public void createEasyScanTask(StockoutBatchSplitScanBarcodeRequest request, boolean isStockoutOrderScanComplete) {

    }

    // 更新波次状态
    @Override
    public void updateBatchStatusToSorting(Integer batchId) {
        StockoutBatchEntity batchEntity = batchMapper.selectById(batchId);
        if (Objects.isNull(batchEntity)) {
            throw new BusinessServiceException("找不到波次");
        }
       /* if (!StockoutWaveTaskStatusEnum.WAIT_SORT.name().equals(batchEntity.getStatus()) && !StockoutWaveTaskStatusEnum.SORTING.name().equals(batchEntity.getStatus())) {
            throw new BusinessServiceException(String.format("波次：%s 不是待分拣 或分拣中", batchId));
        }*/
        if (batchEntity.getStatus() != null && batchEntity.getStatus().equals(StockoutWaveTaskStatusEnum.WAIT_SORT.name())) {
            batchService.updateBatchStatus(batchEntity, StockoutWaveTaskStatusEnum.SORTING);
        }
    }

    // 波次出库单数
    public Integer getBatchStockoutOrderQty(Integer batchId) {
        LambdaQueryWrapper<StockoutBatchOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutBatchOrderEntity::getBatchId, batchId);
        List<StockoutBatchOrderEntity> stockoutBatchOrderEntityList = batchOrderService.list(queryWrapper);
        if (CollectionUtils.isEmpty(stockoutBatchOrderEntityList)) {
            throw new BusinessServiceException("找不到波次单信息");
        }
        return stockoutBatchOrderEntityList.size();
    }

    public StockoutBatchSplitTaskEntity getBatchSplitTask(Integer batchId) {
        LambdaQueryWrapper<StockoutBatchSplitTaskEntity> queryWrapper = buildStockoutBatchSplitTask(batchId);
        StockoutBatchSplitTaskEntity stockoutBatchSplitTaskEntity = taskMapper.selectOne(queryWrapper);
        if (Objects.isNull(stockoutBatchSplitTaskEntity)) {
            throw new BusinessServiceException("分拣任务不存在");
        }
        return stockoutBatchSplitTaskEntity;
    }

    public LambdaQueryWrapper<StockoutBatchSplitTaskEntity> buildStockoutBatchSplitTask(Integer batchId) {
        LambdaQueryWrapper<StockoutBatchSplitTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(batchId)) {
            queryWrapper.eq(StockoutBatchSplitTaskEntity::getBatchId, batchId);
        }
        queryWrapper.last("limit 1");
        return queryWrapper;
    }

    @Override
    public void checkBatch(StockoutBatchEntity batchEntity) {
        if (batchEntity.getIsMergeBatch() != null && batchEntity.getIsMergeBatch() == 1)
            throw new BusinessServiceException("请选择正常波次分拣");
    }

    @Override
    public void checkSplitTask(StockoutBatchEntity batchEntity, StockoutBatchSplitTaskEntity stockoutBatchSplitTaskEntity) {
        if (Objects.isNull(stockoutBatchSplitTaskEntity)) {
            throw new BusinessServiceException("分拣任务不存在");
        }
        if (stockoutBatchSplitTaskEntity.getStatus() != null && stockoutBatchSplitTaskEntity.getStatus().equals(StockoutBatchSplitTaskStatus.SORTED.name()))
            throw new BusinessServiceException("此波次已分拣完成");
        if (!batchEntity.getPickingType().equals(StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name()) && !batchEntity.getPickingType().equals(StockoutPickingTypeEnum.FIND_GOODS_BY_DOC.name()))
            throw new BusinessServiceException("请选择以货找单或者以单找货的波次!");
    }

}
