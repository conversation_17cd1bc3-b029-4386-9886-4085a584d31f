package com.nsy.wms.business.service.stock;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.product.SendDingDingMessage;
import com.nsy.api.wms.enumeration.stock.StockLendLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockLendReturnLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockLendReturnRegistTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockLendReturnStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockLendStockChangeLogTypeEnum;
import com.nsy.api.wms.request.stock.StockLendReturnAutoDistributeRequest;
import com.nsy.api.wms.request.stock.StockLendReturnDistributeItemRequest;
import com.nsy.api.wms.request.stock.StockLendReturnDistributeRequest;
import com.nsy.wms.business.domain.bo.stock.StockLendItemInferiorRemarkBo;
import com.nsy.wms.business.domain.bo.stock.StockLendReturnDistributeBo;
import com.nsy.wms.business.domain.bo.stock.StockLendStockUpdateWaitDistributedBo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.stock.StockLendEntity;
import com.nsy.wms.repository.entity.stock.StockLendItemEntity;
import com.nsy.wms.repository.entity.stock.StockLendReturnEntity;
import com.nsy.wms.repository.entity.stock.StockLendReturnItemEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockLendReturnOpService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockLendReturnOpService.class);

    @Autowired
    StockLendService stockLendService;
    @Autowired
    StockLendItemService stockLendItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockLendReturnItemService stockLendReturnItemService;
    @Autowired
    StockLendReturnLogService stockLendReturnLogService;
    @Autowired
    StockLendStockService stockLendStockService;
    @Autowired
    StockLendReturnService stockLendReturnService;
    @Autowired
    StockLendPushErpService lendPushErpService;
    @Autowired
    MessageProducer messageProducer;

    public void autoDistribute(StockLendReturnAutoDistributeRequest request) {
        request.getReturnItemIdList().forEach(returnItemId -> {
            try {
                SpringUtil.getBean(StockLendReturnOpService.class).autoDistribute(returnItemId);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        });
    }

    /**
     * 自动分配Z
     */
    @Transactional
    @JLock(keyConstant = "stockLendAutoDistribute", lockKey = "#returnItemId")
    public void autoDistribute(Integer returnItemId) {
        StockLendReturnItemEntity returnItem = stockLendReturnItemService.getById(returnItemId);
        if (returnItem.getWaitDistributedQty() <= 0)
            throw new BusinessServiceException("该明细 【待分配数】 为 0");
        StockLendReturnEntity returnEntity = stockLendReturnService.getById(returnItem.getReturnId());
        if (!StockLendReturnRegistTypeEnum.BUSINESS.name().equals(returnEntity.getRegistType()))
            throw new BusinessServiceException("该归还单不是部门登记");
        if (!StockLendReturnStatusEnum.WAIT_DISTRIBUTED.name().equals(returnEntity.getStatus())
                && !StockLendReturnStatusEnum.PARTIAL_DISTRIBUTED.name().equals(returnEntity.getStatus()))
            throw new BusinessServiceException("该归还单状态不是待分、部分分配");

        List<StockLendEntity> lendList = stockLendService.getCanRegistListByBusinessType(returnEntity.getBusinessType());
        List<Integer> lendIdList = lendList.stream().map(StockLendEntity::getLendId).collect(Collectors.toList());
        List<StockLendItemEntity> lendItemList = stockLendItemService.getCanRegistListByLendIdListAndSku(lendIdList, returnItem.getSku());

        StockLendReturnDistributeBo bo = new StockLendReturnDistributeBo();
        bo.setReturnEntity(returnEntity);
        bo.setReturnItem(returnItem);
        bo.setLendList(lendList);
        bo.setLendItemList(lendItemList);
        bo.setStockChangeLogType(StockLendStockChangeLogTypeEnum.AUTO_DISTRIBUTE);
        bo.setLendLogType(StockLendLogTypeEnum.AUTO_DISTRIBUTE);
        bo.setLendReturnLogType(StockLendReturnLogTypeEnum.AUTO_DISTRIBUTE);

        List<StockLendReturnDistributeItemRequest> distributeItemList = new ArrayList<>(lendItemList.size());
        int needInferiorQty = returnItem.getWaitInferiorQty();
        for (StockLendItemEntity temp : lendItemList) {
            StockLendReturnDistributeItemRequest distributeItemRequest = new StockLendReturnDistributeItemRequest();
            distributeItemRequest.setLendItemId(temp.getLendItemId());
            distributeItemRequest.setQty(temp.getLendQty() - temp.getReturnQty());
            //按创建时间顺序优先级进行消费次品数
            int useInferiorQty = Math.min(needInferiorQty, distributeItemRequest.getQty());
            if (useInferiorQty > 0) {
                distributeItemRequest.setInferiorQty(useInferiorQty);
                needInferiorQty -= useInferiorQty;
            } else
                distributeItemRequest.setInferiorQty(0);

            distributeItemList.add(distributeItemRequest);
        }

        bo.setDistributeItemList(distributeItemList);
        distribute(bo);
    }

    /**
     * 手动分配
     *
     * @param request
     */
    @Transactional
    public void distribute(StockLendReturnDistributeRequest request) {
        StockLendReturnItemEntity returnItem = stockLendReturnItemService.getById(request.getReturnItemId());
        if (returnItem.getWaitDistributedQty() <= 0)
            throw new BusinessServiceException("该明细 【待分配数】 为 0");
        StockLendReturnEntity returnEntity = stockLendReturnService.getById(returnItem.getReturnId());
        if (!StockLendReturnRegistTypeEnum.BUSINESS.name().equals(returnEntity.getRegistType()))
            throw new BusinessServiceException("该归还单不是部门登记");
        if (!StockLendReturnStatusEnum.WAIT_DISTRIBUTED.name().equals(returnEntity.getStatus())
                && !StockLendReturnStatusEnum.PARTIAL_DISTRIBUTED.name().equals(returnEntity.getStatus()))
            throw new BusinessServiceException("该归还单状态不是待分、部分分配");

        request.setItemList(request.getItemList().stream().filter(temp -> temp.getQty() > 0 || temp.getInferiorQty() > 0).collect(Collectors.toList()));
        List<Integer> lendItemIdList = request.getItemList().stream()
                .map(StockLendReturnDistributeItemRequest::getLendItemId).collect(Collectors.toList());
        if (lendItemIdList.isEmpty()) throw new BusinessServiceException("请填入明细");
        List<StockLendItemEntity> lendItemList = stockLendItemService.listByIds(lendItemIdList);
        Map<Integer, StockLendItemEntity> lendItemMap = lendItemList.stream().collect(Collectors.toMap(StockLendItemEntity::getLendItemId, Function.identity()));

        //验证参数是否合法
        int totalDistributeQty = 0;
        int totalDistributeInferiorQty = 0;
        for (StockLendReturnDistributeItemRequest itemRequest : request.getItemList()) {
            if (itemRequest.getQty() < itemRequest.getInferiorQty())
                throw new BusinessServiceException(String.format("次品数不能大于归还数 【%s】", itemRequest.getLendItemId()));
            StockLendItemEntity lendItem = lendItemMap.get(itemRequest.getLendItemId());
            if (lendItem.getReturnQty() + itemRequest.getQty() > lendItem.getLendQty())
                throw new BusinessServiceException(String.format("超出可归还范围 【%s】", itemRequest.getLendItemId()));
            totalDistributeQty += itemRequest.getQty();
            totalDistributeInferiorQty += itemRequest.getInferiorQty();
        }
        //分配数是否小于可分配
        if (totalDistributeQty > returnItem.getWaitDistributedQty())
            throw new BusinessServiceException(String.format("分配总数【%s】大于待分配数【%s】", totalDistributeQty, returnItem.getWaitDistributedQty()));
        if (totalDistributeInferiorQty > returnItem.getWaitInferiorQty())
            throw new BusinessServiceException(String.format("分配次品总数【%s】大于待分配次品数【%s】", totalDistributeInferiorQty, returnItem.getWaitInferiorQty()));
        List<Integer> lendIdList = lendItemList.stream().map(StockLendItemEntity::getLendId).collect(Collectors.toList());
        List<StockLendEntity> lendList = stockLendService.listByIds(lendIdList);

        StockLendReturnDistributeBo bo = new StockLendReturnDistributeBo();
        bo.setReturnEntity(returnEntity);
        bo.setReturnItem(returnItem);
        bo.setLendList(lendList);
        bo.setLendItemList(lendItemList);
        bo.setStockChangeLogType(StockLendStockChangeLogTypeEnum.DISTRIBUTE);
        bo.setLendLogType(StockLendLogTypeEnum.DISTRIBUTE);
        bo.setLendReturnLogType(StockLendReturnLogTypeEnum.DISTRIBUTE);
        bo.setDistributeItemList(request.getItemList());

        distribute(bo);
    }

    /**
     * 分配
     * <p>
     * 1.更新归还单待分配数 更新归还单状态
     * 2.更新借用库存待分配数
     * 3.更新借用表 修改状态 绑定内部箱
     *
     * @param bo
     */
    private void distribute(StockLendReturnDistributeBo bo) {
        Map<Integer, StockLendEntity> lendMap = bo.getLendList().stream().collect(Collectors.toMap(StockLendEntity::getLendId, Function.identity()));
        int totalQty = bo.getReturnItem().getWaitDistributedQty();
        int totalInferiorQty = bo.getReturnItem().getInferiorQty();

        Map<Integer, StockLendItemEntity> lendItemMap = bo.getLendItemList().stream().collect(Collectors.toMap(StockLendItemEntity::getLendItemId, Function.identity()));
        List<StockLendItemInferiorRemarkBo> inferiorRemarkBoList = new ArrayList<>();
        for (StockLendReturnDistributeItemRequest distributeItemRequest : bo.getDistributeItemList()) {
            StockLendItemEntity lendItem = lendItemMap.get(distributeItemRequest.getLendItemId());
            StockLendEntity lend = lendMap.get(lendItem.getLendId());
            if (!lendItem.getSku().equals(bo.getReturnItem().getSku()))
                throw new BusinessServiceException(String.format("归还sku 【%s】与借用sku 【%s】不一致", bo.getReturnItem().getSku(), lendItem.getSku()));
            stockLendService.checkCanRegist(lend.getStockLendCode());

            int needQty = distributeItemRequest.getQty();
            int useQty = Math.min(totalQty, needQty);
            int useInferiorQty = Math.min(distributeItemRequest.getInferiorQty(), totalInferiorQty);
            //更新归还单待分配数 更新归还单状态
            updateQtyForDistribute(lend.getStockLendCode(), bo.getReturnItem().getReturnItemId(), useQty, useInferiorQty, bo.getLendReturnLogType());
            //更新借用库存待分配数
            StockLendStockUpdateWaitDistributedBo stockUpdate = new StockLendStockUpdateWaitDistributedBo();
            stockUpdate.setLendReturn(bo.getReturnEntity());
            stockUpdate.setLend(lend);
            stockUpdate.setBusinessType(bo.getReturnEntity().getBusinessType());
            stockUpdate.setSku(bo.getReturnItem().getSku());
            stockUpdate.setChangeLogType(bo.getStockChangeLogType());
            stockUpdate.setQty(useQty);
            stockUpdate.setInferiorQty(useInferiorQty);
            stockLendStockService.updateWaitDistributed(stockUpdate);
            //更新借用表 修改状态 绑定内部箱
            stockLendService.updateQty(lendItem.getLendItemId(), useQty, useInferiorQty, bo.getLendLogType(), bo.getReturnEntity().getInternalBoxCode(), bo.getReturnEntity().getStockLendReturnCode());
            //检查并更改归还单已归还
            stockLendService.checkAndUpdateReturnCompleteStatus(lend.getLendId());
            //同步erp
            lendPushErpService.updateQtyErp(lend, lendItem, useQty);
            if (StringUtils.hasText(bo.getReturnItem().getInferiorRemark())) {
                StockLendItemInferiorRemarkBo inferiorRemarkBo = new StockLendItemInferiorRemarkBo();
                inferiorRemarkBo.setLendItemId(lendItem.getLendItemId());
                if (useInferiorQty > 0)
                    inferiorRemarkBo.setInferiorRemark(bo.getReturnItem().getInferiorRemark());
                inferiorRemarkBoList.add(inferiorRemarkBo);
            }
            totalQty -= useQty;
            totalInferiorQty -= useInferiorQty;
            if (totalQty <= 0) break;
        }
        //记录次品备注
        stockLendItemService.recordInferiorRemark(inferiorRemarkBoList);
    }


    /**
     * 更新归还单待分配数 更新归还单状态
     *
     * @param lendCode
     * @param returnItemId
     * @param qty
     * @param inferiorQty
     */
    private void updateQtyForDistribute(String lendCode, Integer returnItemId, Integer qty, Integer inferiorQty, StockLendReturnLogTypeEnum logType) {
        //更新明细
        StockLendReturnItemEntity returnItem = stockLendReturnItemService.getById(returnItemId);
        returnItem.setWaitDistributedQty(returnItem.getWaitDistributedQty() - qty);
        returnItem.setWaitInferiorQty(returnItem.getWaitInferiorQty() - inferiorQty);
        returnItem.setUpdateBy(loginInfoService.getName());
        if (returnItem.getWaitDistributedQty() < returnItem.getWaitInferiorQty())
            throw new BusinessServiceException("待分配数不能小于待分配次品数");
        stockLendReturnItemService.updateById(returnItem);
        //更新主表
        List<StockLendReturnItemEntity> returnItemList = stockLendReturnItemService.getListByReturnId(returnItem.getReturnId());
        AtomicInteger waitDistributedQty = new AtomicInteger();
        AtomicInteger waitInferiorQty = new AtomicInteger();
        returnItemList.forEach(item -> {
            waitDistributedQty.addAndGet(item.getWaitDistributedQty());
            waitInferiorQty.addAndGet(item.getWaitInferiorQty());
        });
        StockLendReturnEntity lendReturn = stockLendReturnService.getById(returnItem.getReturnId());
        lendReturn.setWaitDistributedQty(waitDistributedQty.get());
        lendReturn.setWaitInferiorQty(waitInferiorQty.get());
        lendReturn.setUpdateBy(loginInfoService.getName());
        if (lendReturn.getWaitInferiorQty() < 0) throw new BusinessServiceException("次品数大于归还单待分配次品数");
        if (lendReturn.getWaitDistributedQty() < 0) throw new BusinessServiceException("分配数大于归还单待分配数");
        stockLendReturnService.updateById(lendReturn);

        //添加日志
        String logContent = String.format("%s，分配到借用单【%s】，待分配数%s %s件，总待分配数 %s件，待分配次品数%s %s件，总待分配次品数 %s件",
                returnItem.getSku(), lendCode,
                qty > 0 ? "减少" : "增加", qty, returnItem.getWaitDistributedQty(),
                inferiorQty > 0 ? "减少" : "增加", inferiorQty, returnItem.getWaitInferiorQty());
        stockLendReturnLogService.addLendLog(returnItem.getReturnId(), logType, logContent);

        checkDistributeStatus(lendReturn.getLendReturnId());
    }

    /**
     * 检查更新归还单状态
     *
     * @param returnId
     */
    private void checkDistributeStatus(Integer returnId) {
        StockLendReturnEntity returnEntity = stockLendReturnService.getById(returnId);
        //待分配 -》 部分分配
        if (StockLendReturnStatusEnum.WAIT_DISTRIBUTED.name().equals(returnEntity.getStatus())) {
            stockLendReturnService.update(new LambdaUpdateWrapper<StockLendReturnEntity>()
                    .set(StockLendReturnEntity::getStatus, StockLendReturnStatusEnum.PARTIAL_DISTRIBUTED.name())
                    .eq(StockLendReturnEntity::getLendReturnId, returnId));

            //添加日志
            stockLendReturnLogService.addLendLog(returnId, StockLendReturnLogTypeEnum.START_DISTRIBUTE, "状态更改【部分分配】");
        }

        List<StockLendReturnItemEntity> returnItemList = stockLendReturnItemService.getListByReturnId(returnId);
        boolean allDistribute = returnItemList.stream().allMatch(returnItem -> returnItem.getWaitDistributedQty() <= 0 && returnItem.getWaitInferiorQty() <= 0);
        //全部分配 而且状态是待分配或者部分分配
        if (allDistribute && (StockLendReturnStatusEnum.WAIT_DISTRIBUTED.name().equals(returnEntity.getStatus()) || StockLendReturnStatusEnum.PARTIAL_DISTRIBUTED.name().equals(returnEntity.getStatus()))) {
            stockLendReturnService.update(new LambdaUpdateWrapper<StockLendReturnEntity>()
                    .set(StockLendReturnEntity::getStatus, StockLendReturnStatusEnum.HAS_DISTRIBUTED.name())
                    .eq(StockLendReturnEntity::getLendReturnId, returnId));
            //添加日志
            stockLendReturnLogService.addLendLog(returnId, StockLendReturnLogTypeEnum.FINISH_DISTRIBUTE, "状态更改【已分配】");
        }
    }


    /**
     * 通知次品
     *
     * @param sku
     * @param inferiorQty
     */
    public void notifyInferior(StockLendEntity stockLend, String sku, Integer inferiorQty) {
        SendDingDingMessage sendDingDingMessage = new SendDingDingMessage();
        sendDingDingMessage.setText(String.format("借用单号【%s】，规格编码【%s】，次品数量 %s", stockLend.getStockLendCode(), sku, inferiorQty));
        sendDingDingMessage.setUserNameList(Collections.singletonList(stockLend.getUserName()));
        messageProducer.sendMessage(KafkaConstant.WMS_SEND_DINGDING_MESSAGE_NAME, KafkaConstant.WMS_SEND_DINGDING_MESSAGE_TOPIC, sendDingDingMessage);
    }
}
