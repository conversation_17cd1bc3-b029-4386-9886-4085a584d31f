package com.nsy.wms.business.manage.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel("DHL物流额外信息")
public class DhlInfo {

    @ApiModelProperty(value = "发票形式，DHL如需要商业发票需必填 PFI:形式发票，CMI：商业发票", name = "invoiceType")
    private String invoiceType;

    @ApiModelProperty(value = "第三方付款账号", name = "billingAccountNumber")
    private String billingAccountNumber;

    @ApiModelProperty(value = "DHL产品代码 P:包裹,D:文件,N:本地件", name = "productCode")
    private String productCode;

    @ApiModelProperty(value = "包裹类型(EE:DHL Express Envelope, OD:Other DHL Packaging, CP:Customer-provided, JB-Jumbo box, JJ-Junior jumbo Box, DF-DHL Flyer, YP-Your)", name = "packageType")
    private String packageType;

    @ApiModelProperty(value = "主要品名", name = "contents")
    private String contents;

    @ApiModelProperty(value = "包裹数,DHL系统并不效验, 以实际包裹件数为准,整数", name = "packageCount")
    private Integer packageCount;

    @ApiModelProperty(value = "是否使用DHL系统创建发票", name = "useDHLInvoice")
    private Boolean useDHLInvoice;

    @ApiModelProperty(value = "手续费", name = "handingFee")
    private Double handingFee;

    @ApiModelProperty(value = "运费", name = "freightCharges")
    private Double freightCharges;

    @ApiModelProperty(value = "贸易条款", name = "tradeTerms")
    private String tradeTerms;

    public String getTradeTerms() {
        return tradeTerms;
    }

    public void setTradeTerms(String tradeTerms) {
        this.tradeTerms = tradeTerms;
    }

    public Double getHandingFee() {
        return handingFee;
    }

    public void setHandingFee(Double handingFee) {
        this.handingFee = handingFee;
    }

    public Integer getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(Integer packageCount) {
        this.packageCount = packageCount;
    }

    public Double getFreightCharges() {
        return freightCharges;
    }

    public void setFreightCharges(Double freightCharges) {
        this.freightCharges = freightCharges;
    }

    public Boolean getUseDHLInvoice() {
        return useDHLInvoice;
    }

    public void setUseDHLInvoice(Boolean useDHLInvoice) {
        this.useDHLInvoice = useDHLInvoice;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getBillingAccountNumber() {
        return billingAccountNumber;
    }

    public void setBillingAccountNumber(String billingAccountNumber) {
        this.billingAccountNumber = billingAccountNumber;
    }
}
