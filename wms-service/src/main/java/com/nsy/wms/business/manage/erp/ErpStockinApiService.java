package com.nsy.wms.business.manage.erp;

import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.stock.SyncErpTypeEnum;
import com.nsy.wms.business.domain.bo.mq.SyncErpMessage;
import com.nsy.wms.business.manage.erp.request.ErpFeedbackReceivedRequest;
import com.nsy.wms.business.manage.erp.request.SyncReceivingByOtherStockIn;
import com.nsy.wms.business.manage.erp.request.SyncSupplierDeliveryDetailRequest;
import com.nsy.wms.business.service.external.ExternalApiAsyncQueueService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class ErpStockinApiService {

    //private static final Logger LOGGER = LoggerFactory.getLogger(ErpStockinApiService.class);

    @Value("${nsy.service.url.erp}")
    private String erpApiServiceUrl;
    @Autowired
    private ExternalApiLogService externalApiLogService;
    @Autowired
    ExternalApiAsyncQueueService externalApiAsyncQueueService;
    @Autowired
    private MessageProducer messageProducer;

    // 工厂多发,回填【出库数】或者增加出库明细记录
    public void syncSupplierDeliveryDetail(SyncSupplierDeliveryDetailRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLogRequired(ExternalApiInfoEnum.DELIVERY_OVER_SHIPMENT_ERP, String.format("%s/NewWms/SyncSupplierDeliveryDetail", erpApiServiceUrl),
                JsonMapper.toJson(request), request.getSupplierDeliveryNo() + "-" + request.getSku(), String.format("工厂多发，工厂出库单号【%s】【%s】", request.getSupplierDeliveryNo(), request.getSku()));
        //保存到异步调用队列
        externalApiAsyncQueueService.addEntityToKafka(apiLogEntity, request.getSupplierDeliveryBoxCode());
        //发送消息给kafka
        SyncErpMessage<Integer> syncErpMessage = new SyncErpMessage<>(TenantContext.getTenant(), request.getOperator(), request.getSupplierDeliveryBoxCode(), SyncErpTypeEnum.SHELVE, apiLogEntity.getApiLogId());
        syncErpMessage.setApiName(apiLogEntity.getApiName());
        messageProducer.sendMessage(KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC_NAME, KafkaConstant.STOCK_SYNC_TO_ERP_TOPIC, Key.of(TenantContext.getTenant() + "_" + request.getSku()), syncErpMessage);
    }

    // 工厂多发,处理工厂多发入库类型的接收单
    public void syncReceivingByOtherStockIn(ErpFeedbackReceivedRequest request) {

        List<ErpFeedbackReceivedRequest.ErpFeedbackReceivedItem> feedbackReceivedItemList = request.getFeedbackReceivedItemList();
        feedbackReceivedItemList.forEach(item -> {
            SyncReceivingByOtherStockIn stockIn = new SyncReceivingByOtherStockIn();
            BeanUtilsEx.copyProperties(item, stockIn);
            stockIn.setSupplierDeliveryBoxCode(request.getSupplierDeliveryBoxCode());
            stockIn.setPositionCode(request.getPositionCode());
            stockIn.setOperator(request.getOperator());
            stockIn.setLocation(request.getLocation());
            stockIn.setShelfTime(new Date());
            ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLogRequired(ExternalApiInfoEnum.DELIVERY_OVER_SHIPMENT_ERP, String.format("%s/NewWms/SyncReceivingByOtherStockIn", erpApiServiceUrl),
                    JsonMapper.toJson(stockIn), stockIn.getSupplierDeliveryBoxCode() + "-" + stockIn.getSku(), String.format("工厂多发，处理其他入库类型的接收单【%s】【%s】", stockIn.getSupplierDeliveryBoxCode(), stockIn.getSku()));
            //保存到异步调用队列
            externalApiAsyncQueueService.add(apiLogEntity, request.getSupplierDeliveryBoxCode());
        });
    }
}
