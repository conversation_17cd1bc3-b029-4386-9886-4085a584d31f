package com.nsy.wms.business.service.overseas;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.overseas.OverseasWarehouseOrderStatusEnum;
import com.nsy.api.wms.request.overseas.OverseasWarehouseOrderPackageTimeUpdateRequest;
import com.nsy.api.wms.request.overseas.OverseasWarehouseOrderPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.overseas.OverseasWarehouseOrderResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.overseas.OverseasWarehouseOrderEntity;
import com.nsy.wms.repository.jpa.mapper.overseas.OverseasWarehouseOrderMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 海外仓订单服务类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class OverseasWarehouseOrderService extends ServiceImpl<OverseasWarehouseOrderMapper, OverseasWarehouseOrderEntity> {


    @Resource
    private EnumConversionChineseUtils enumConversionChineseUtils;

    @Resource
    private LoginInfoService loginInfoService;

    /**
     * 分页查询
     */
    public PageResponse<OverseasWarehouseOrderResponse> queryByPage(OverseasWarehouseOrderPageRequest request) {
        PageResponse<OverseasWarehouseOrderResponse> pageResponse = new PageResponse<>();
        Page<OverseasWarehouseOrderEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<OverseasWarehouseOrderResponse> iPage = baseMapper.pageSearchOverseasWarehouseOrder(page, request);
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_OVERSEAS_ORDER_STATUS.getName());
        Map<String, String> stockoutPlatformNameEnumMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_STOCKOUT_ORDER_PLATFORM_NAME.getName());
        iPage.getRecords().forEach(resp -> {
            resp.setStatusCn(statusMap.get(resp.getStatus()));
            resp.setPlatformNameCn(StrUtil.isEmpty(resp.getPlatformName()) ? "" : stockoutPlatformNameEnumMap.get(resp.getPlatformName()));
        });
        pageResponse.setContent(iPage.getRecords());
        pageResponse.setTotalCount(iPage.getTotal());
        return pageResponse;
    }

    /**
     * 根据ID查询订单
     */
    public OverseasWarehouseOrderResponse getOneById(Integer id) {
        OverseasWarehouseOrderEntity entity = getById(id);
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_OVERSEAS_ORDER_STATUS.getName());
        OverseasWarehouseOrderResponse resp = new OverseasWarehouseOrderResponse();
        BeanUtils.copyProperties(entity, resp);
        resp.setStatusCn(statusMap.get(entity.getStatus()));
        return resp;
    }

    /**
     * 根据状态统计订单数量
     */
    public List<StatusCountResponse> countByStatus() {
        // 获取数据库中的统计结果
        Map<String, List<StatusCountResponse>> collect = baseMapper.countByStatus().stream()
                .collect(Collectors.groupingBy(StatusCountResponse::getStatus));
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversionByType(DictionaryNameEnum.WMS_OVERSEAS_ORDER_STATUS.getName());
        List<StatusCountResponse> list = new ArrayList<>();
        statusMap.forEach((k, v) -> {
            StatusCountResponse response = new StatusCountResponse();
            List<StatusCountResponse> responses = collect.get(k);
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(k);
            response.setValue(k);
            response.setLabel(v);
            list.add(response);
        });
        // 添加"所有"统计项
        StatusCountResponse allResponse = new StatusCountResponse();
        allResponse.setQty(list.stream().mapToInt(StatusCountResponse::getQty).sum());
        allResponse.setStatus("ALL");
        allResponse.setValue("ALL");
        allResponse.setLabel("所有");
        list.add(allResponse);
        return list;
    }

    /**
     * 更新订单状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusAndOverseasOrderNo(OverseasWarehouseOrderEntity entity, String status, String overseasOrderNo, String logisticsNo) {
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }
        entity.setStatus(status);
        if (StrUtil.equalsIgnoreCase(status, OverseasWarehouseOrderStatusEnum.SHIPPED.name())) {
            entity.setOverseasShipTime(new Date());
        } else if (StrUtil.equalsIgnoreCase(status, OverseasWarehouseOrderStatusEnum.PENDING_SHIP.name())) {
            entity.setPushOverseasTime(new Date());
        }
        if (StrUtil.isNotBlank(overseasOrderNo)) {
            entity.setOverseasOrderNo(overseasOrderNo);
        }
        if (StrUtil.isNotBlank(logisticsNo)) {
            entity.setLogisticsNo(logisticsNo);
        }
        entity.setUpdateBy(loginInfoService.getName());
        calculateAndUpdateDurations(entity);
        updateById(entity);

    }




    /**
     * 创建海外仓订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void create(OverseasWarehouseOrderEntity entity) {
        // 校验出库单号是否已存在
        LambdaQueryWrapper<OverseasWarehouseOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OverseasWarehouseOrderEntity::getStockoutOrderNo, entity.getStockoutOrderNo());
        int count = count(wrapper);
        if (count > 0) {
            throw new BusinessServiceException("出库单号已存在");
        }
        entity.setLocation(TenantContext.getTenant());
        entity.setCreateBy(loginInfoService.getName());
        save(entity);
    }

    /**
     * 根据出库单号查找海外仓订单
     */
    public OverseasWarehouseOrderEntity findByStockoutOrderNo(String stockoutOrderNo) {
        LambdaQueryWrapper<OverseasWarehouseOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OverseasWarehouseOrderEntity::getStockoutOrderNo, stockoutOrderNo);
        return getOne(wrapper);
    }

    /**
     * 删除海外仓订单
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
        OverseasWarehouseOrderEntity entity = getById(id);
        if (Objects.isNull(entity)) {
            throw new InvalidRequestException("没有找到对应数据");
        }

        removeById(id);
    }

    /**
     * 根据物流单号更新海外仓订单包裹时间
     *
     * @param request 包裹时间更新请求
     * @return 更新后的海外仓订单信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePackageTime(OverseasWarehouseOrderPackageTimeUpdateRequest request) {
        // 根据物流单号查找海外仓订单
        OverseasWarehouseOrderEntity entity = findByLogisticsNo(request.getLogisticsNo());
        if (entity == null) {
            throw new InvalidRequestException("根据物流单号未找到对应的海外仓订单：" + request.getLogisticsNo());
        }
        // 更新包裹时间
        boolean updated = false;
        if (request.getPackagePickupTime() != null) {
            entity.setPackagePickupTime(request.getPackagePickupTime());
            updated = true;
        }
        if (request.getPackageSignedTime() != null) {
            entity.setPackageSignedTime(request.getPackageSignedTime());
            updated = true;
            // 如果设置了签收时间，同时更新订单状态为已签收
            entity.setStatus(OverseasWarehouseOrderStatusEnum.SIGNED.name());
        }
        if (!updated) {
            throw new InvalidRequestException("请至少提供一个时间字段进行更新");
        }

        // 计算并更新时长字段
        calculateAndUpdateDurations(entity);

        entity.setUpdateBy(loginInfoService.getName());
        updateById(entity);
    }

    /**
     * 根据物流单号查找海外仓订单
     *
     * @param logisticsNo 物流单号
     * @return 海外仓订单实体
     */
    public OverseasWarehouseOrderEntity findByLogisticsNo(String logisticsNo) {
        if (StrUtil.isBlank(logisticsNo)) {
            return null;
        }
        LambdaQueryWrapper<OverseasWarehouseOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OverseasWarehouseOrderEntity::getLogisticsNo, logisticsNo).last(MybatisQueryConstant.QUERY_FIRST);
        return getOne(wrapper);
    }

    /**
     * 计算并更新时长字段
     * 计算规则：
     * 推送海外仓时长 = 推送海外仓时间-客户下单时间
     * 海外仓处理时长 = 海外仓发货时间-推送海外仓时间
     * 包裹提取时长 = 包裹提取时间-海外仓操作发货时间
     * 包裹派送时长 = 包裹签收时间-包裹提取时间
     * 发货总时长 = 从客户下单到海外仓发货的时间
     *
     * @param entity 海外仓订单实体
     */
    private void calculateAndUpdateDurations(OverseasWarehouseOrderEntity entity) {
        // 推送海外仓时长 = 推送海外仓时间-客户下单时间
        if (entity.getPushOverseasTime() != null && entity.getOrderTime() != null) {
            long diffMillis = entity.getPushOverseasTime().getTime() - entity.getOrderTime().getTime();
            BigDecimal hours = BigDecimal.valueOf(diffMillis).divide(BigDecimal.valueOf(1000 * 60 * 60), 2, RoundingMode.HALF_UP);
            entity.setPushOverseasDuration(hours);
        }

        // 海外仓处理时长 = 海外仓发货时间-推送海外仓时间
        if (entity.getOverseasShipTime() != null && entity.getPushOverseasTime() != null) {
            long diffMillis = entity.getOverseasShipTime().getTime() - entity.getPushOverseasTime().getTime();
            BigDecimal hours = BigDecimal.valueOf(diffMillis).divide(BigDecimal.valueOf(1000 * 60 * 60), 2, RoundingMode.HALF_UP);
            entity.setOverseasProcessDuration(hours);
        }

        // 包裹提取时长 = 包裹提取时间-海外仓操作发货时间
        if (entity.getPackagePickupTime() != null && entity.getOverseasShipTime() != null) {
            long diffMillis = entity.getPackagePickupTime().getTime() - entity.getOverseasShipTime().getTime();
            BigDecimal hours = BigDecimal.valueOf(diffMillis).divide(BigDecimal.valueOf(1000 * 60 * 60), 2, RoundingMode.HALF_UP);
            entity.setPackagePickupDuration(hours);
        }

        // 包裹派送时长 = 包裹签收时间-包裹提取时间
        if (entity.getPackageSignedTime() != null && entity.getPackagePickupTime() != null) {
            long diffMillis = entity.getPackageSignedTime().getTime() - entity.getPackagePickupTime().getTime();
            BigDecimal days = BigDecimal.valueOf(diffMillis).divide(BigDecimal.valueOf(1000 * 60 * 60 * 24), 2, RoundingMode.HALF_UP);
            entity.setPackageDeliveryDuration(days);
        }

        // 发货总时长 = 从客户下单到海外仓发货的时间
        if (entity.getOverseasShipTime() != null && entity.getOrderTime() != null) {
            long diffMillis = entity.getOverseasShipTime().getTime() - entity.getOrderTime().getTime();
            BigDecimal days = BigDecimal.valueOf(diffMillis).divide(BigDecimal.valueOf(1000 * 60 * 60 * 24), 2, RoundingMode.HALF_UP);
            entity.setTotalShipDuration(days);
        }
    }
}
