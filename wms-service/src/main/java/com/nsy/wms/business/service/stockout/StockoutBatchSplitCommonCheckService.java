package com.nsy.wms.business.service.stockout;

import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ProcessConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitTaskItemSowWall;
import com.nsy.api.wms.domain.stockout.StockoutWithdrawalInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSewTaskTypeEnum;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitScanBarcodeRequest;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskSkuResponse;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 波次扫描、二次分拣，二次分拣点灯 公用 校验
 */
@Service
public class StockoutBatchSplitCommonCheckService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutBatchSplitCommonCheckService.class);

    private static final Integer WITHDRAWAL_TYPE_WRONG = 1; // 错拣货撤货
    private static final Integer WITHDRAWAL_TYPE_MORE = 2; // 多拣货撤货
    private static final Integer WITHDRAWAL_TYPE_CANCEL = 3; // 出库单取消撤货
    private static final Integer IS_WITHDRAWAL = 1; // 是否撤货，1是

    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutBatchSplitTaskItemService splitItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private BdPositionService bdPositionService;

    /**
     * 校验撤货
     * 1.sku条码不存在该波次中撤货
     * 2.出库取消需要撤货
     * 3.对应分拣任务已经分拣完成，需要撤货
     * 不需要撤货返回需要分拣的item
     */
    public StockoutBatchSplitTaskItemSowWall checkWithdrawal(List<StockoutBatchSplitTaskItemSowWall> allItemList, StockoutBatchSplitScanBarcodeRequest request, StockoutBatchSplitTaskSkuResponse response) {
        List<StockoutBatchSplitTaskItemSowWall> filterContainBarcodeItemList = allItemList.stream().filter(item -> item.getBarcode().equals(request.getBarcode())).collect(Collectors.toList());
        // 1.sku不存在撤货
        if (CollectionUtils.isEmpty(filterContainBarcodeItemList)) {
            LOGGER.info("barcode: {} 对应sku不在波次： {} 中，准备撤货", request.getBarcode(), request.getBatchId());
            response.setWithdrawalType(WITHDRAWAL_TYPE_WRONG);
            response.setBarcode(request.getBarcode());
            ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcode(request.getBarcode());
            if (productSpecInfoEntity == null) {
                throw new BusinessServiceException("不存在该条码的商品信息");
            }
            String message = String.format("当前SKU: %s 条码不存在该波次中，请撤货", productSpecInfoEntity.getSku());
            buildWithdrawalResponse(message, 0, 0, request.getScanQty(), response);
            return null;
        }

        List<StockoutBatchSplitTaskItemSowWall> filterWaitSortList = allItemList.stream().filter(item -> item.getBarcode().equals(request.getBarcode())
            && !item.getBatchQty().equals(item.getScanQty())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterWaitSortList)) {
            // 2.出库单取消撤货, 将出库单状态修改为【已取消】
            List<StockoutBatchSplitTaskItemSowWall> filterList = filterWaitSortList.stream().filter(item -> !item.getStatus().equals(StockoutOrderStatusEnum.CANCELLING.name())
                && !item.getStatus().equals(StockoutOrderStatusEnum.CANCELLED.name())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                int batchQty = filterWaitSortList.stream().mapToInt(StockoutBatchSplitTaskItemSowWall::getBatchQty).sum();
                int scanQty = filterWaitSortList.stream().mapToInt(StockoutBatchSplitTaskItemSowWall::getScanQty).sum();
                int withdrawalQty = filterWaitSortList.stream().mapToInt(StockoutBatchSplitTaskItemSowWall::getWithdrawalQty).sum();
                List<String> stockoutOrderNoList = filterWaitSortList.stream().map(StockoutBatchSplitTaskItemSowWall::getStockoutOrderNo).distinct().collect(Collectors.toList());
                buildWithdrawalResponse(String.format("当前出库单【%s】已取消，其中包含 %s %s件,请输入撤货总件数", String.join(",", stockoutOrderNoList), filterWaitSortList.get(0).getSku(), batchQty - scanQty - withdrawalQty),
                    filterWaitSortList.get(0).getTaskItemId(), filterWaitSortList.get(0).getWithdrawalQty(), request.getScanQty(), response);
                List<StockoutWithdrawalInfo> withdrawalInfoList = filterWaitSortList.stream().map(item -> {
                    StockoutWithdrawalInfo stockoutWithdrawalInfo = new StockoutWithdrawalInfo();
                    BeanUtilsEx.copyProperties(item, stockoutWithdrawalInfo);
                    return stockoutWithdrawalInfo;
                }).collect(Collectors.toList());
                response.setWithdrawalInfoList(withdrawalInfoList);
                response.setWithdrawalType(WITHDRAWAL_TYPE_CANCEL);
                response.setBarcode(request.getBarcode());
                return null;
            }
            return getTaskItemSowWallByIsSew(filterList, request.getStockoutItemId(), request.getIsSew(), request.getBatchId());
            // 3.sku多,撤货
        } else {
            LOGGER.info("barcode: {} 对应分拣任务已经分拣完成，准备撤货", request.getBarcode());
            response.setWithdrawalType(WITHDRAWAL_TYPE_MORE);
            response.setBarcode(request.getBarcode());
            String message = String.format("当前SKU: %s 条码已经完成分拣完，请撤货", filterContainBarcodeItemList.get(0).getSku());
            buildWithdrawalResponse(message, 0, 0, request.getScanQty(), response);
            return null;
        }
    }

    /**
     * 获取分拣明细，优先匹配是否缝制
     */
    private StockoutBatchSplitTaskItemSowWall getTaskItemSowWallByIsSew(List<StockoutBatchSplitTaskItemSowWall> filterList, Integer stockouorderItemId, Integer isSew, Integer batchId) {
        if (stockouorderItemId != null && stockouorderItemId > 0) {
            return filterList.stream().filter(o -> o.getStockoutOrderItemId() != null && o.getStockoutOrderItemId().equals(stockouorderItemId)).max(Comparator.comparing(StockoutBatchSplitTaskItemSowWall::getProcessQualifiedQty))
                .orElseGet(() -> filterList.stream().filter(o -> StringUtils.hasText(o.getChangeType())).max(Comparator.comparing(StockoutBatchSplitTaskItemSowWall::getProcessQualifiedQty)).orElse(new StockoutBatchSplitTaskItemSowWall()));
        } else if (isSew != null && isSew.equals(1)) {
            return filterList.stream().filter(o -> StringUtils.hasText(o.getChangeType()) && (StockoutSewTaskTypeEnum.CHANGE.name().equals(o.getChangeType()) || StockoutSewTaskTypeEnum.DROP_CHANGE.name().equals(o.getChangeType())))
                .max(Comparator.comparing(StockoutBatchSplitTaskItemSowWall::getProcessQualifiedQty))
                .orElse(new StockoutBatchSplitTaskItemSowWall());
        }
        //填入来源库位数量
        List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.getBaseMapper().searchInternalBoxItemListByBatchIdsAndSku(Lists.newArrayList(batchId), StockInternalBoxTypeEnum.PICKING_BOX.name(), filterList.get(0).getSku());
        for (StockoutBatchSplitTaskItemSowWall item : filterList) {
            List<StockInternalBoxItemEntity> boxItemEntities;
            if (StockConstant.ENABLE.equals(item.getIsNeedProcess())) {
                //加工取来源为加工发货库位明细
                boxItemEntities = internalBoxItemEntityList.stream().filter(boxItem -> ProcessConstant.AFTER_PROCESS_POSITION_CODE.equals(boxItem.getSourcePositionCode())).collect(Collectors.toList());
            } else {
                //非加工款按区域取箱内数
                BdPositionEntity positionByCode = bdPositionService.getPositionByCode(item.getPositionCode());
                if (BdPositionTypeEnum.ACTIVITY_POSITION.name().equals(positionByCode.getPositionType())) {
                    boxItemEntities = internalBoxItemEntityList.stream().filter(boxItem -> positionByCode.getPositionCode().equals(boxItem.getSourcePositionCode())).collect(Collectors.toList());
                } else {
                    boxItemEntities = internalBoxItemEntityList.stream().filter(boxItem -> positionByCode.getAreaId().equals(boxItem.getSourceAreaId())).collect(Collectors.toList());
                }

            }
            item.setBoxQty(boxItemEntities.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum());
        }

        return filterList.stream().filter(o -> !StringUtils.hasText(o.getChangeType())).max(Comparator.comparing(StockoutBatchSplitTaskItemSowWall::getBoxQty))
            .orElseGet(() -> filterList.stream().filter(o -> StringUtils.hasText(o.getChangeType())).max(Comparator.comparing(StockoutBatchSplitTaskItemSowWall::getBoxQty)).orElse(new StockoutBatchSplitTaskItemSowWall()));

    }

    void buildWithdrawalResponse(String message, Integer taskItemId, Integer withdrawalQty, Integer scanQty, @NotNull StockoutBatchSplitTaskSkuResponse response) {
        response.setTaskItemId(taskItemId);
        response.setMessage(message);
        response.setIsWithdrawal(IS_WITHDRAWAL);
        response.setWithdrawalQty(withdrawalQty + scanQty);
        response.setScanQty(scanQty);
    }

    /**
     * 校验 扫描条码
     */
    public void validateSplitScanRequest(StockoutBatchSplitScanBarcodeRequest request) {
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcode(request.getBarcode());
        if (productSpecInfoEntity == null) {
            // 支持传出库单明细id 改码特殊case
            LOGGER.info("改码扫描传出库单明细id:{}", request.getBarcode());
            StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getById(request.getBarcode());
            if (stockoutOrderItemEntity != null) {
                request.setBarcode(stockoutOrderItemEntity.getBarcode());
                request.setStockoutItemId(stockoutOrderItemEntity.getStockoutOrderItemId());
                request.setIsSew(1);
            } else {
                throw new BusinessServiceException("不存在该条码的商品信息");
            }
        } else {
            request.setBarcode(productSpecInfoEntity.getBarcode());
        }
    }

    // 检验出库单对应的分拣明细的分拣数是否=出库数，相当则释放分拣口
    public void checkStockoutOrderScanQty(StockoutBatchSplitTaskItemEntity itemEntity) {
        List<StockoutBatchSplitTaskItemEntity> itemList = splitItemService.getByTaskIdOrderByTaskIdAndStockoutOrderNo(itemEntity.getTaskId(), itemEntity.getStockoutOrderNo());
        if (!CollectionUtils.isEmpty(itemList)) {
            List<StockoutBatchSplitTaskItemEntity> filterStockoutOrderList = itemList.stream().filter(m -> !m.getBatchQty().equals(m.getScanQty())
                && !m.getTaskItemId().equals(itemEntity.getTaskItemId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterStockoutOrderList)) {
                LOGGER.info("出库单：【 {}】分拣完成释放分拣口", itemEntity.getStockoutOrderNo());
                List<StockoutBatchSplitTaskItemEntity> filterNotEqualItemList = itemList.stream().filter(m -> !m.getTaskItemId().equals(itemEntity.getTaskItemId())).collect(Collectors.toList());
                filterNotEqualItemList.add(itemEntity);
                List<StockoutBatchSplitTaskItemEntity> filterReleaseList = buildReleaseOutlet(filterNotEqualItemList);
                splitItemService.updateBatchById(filterReleaseList);
            } else
                splitItemService.updateById(itemEntity);
        }
    }

    // 释放分拣口
    private List<StockoutBatchSplitTaskItemEntity> buildReleaseOutlet(List<StockoutBatchSplitTaskItemEntity> list) {
        return list.stream().peek(item -> {
            item.setOutlet(0);
            item.setUpdateBy(loginInfoService.getName());
        }).collect(Collectors.toList());
    }

    public boolean checkStockoutOrderSplitTaskScanComplete(List<StockoutBatchSplitTaskItemEntity> splitTaskItemList, String stockoutOrderNo) {
        List<StockoutBatchSplitTaskItemEntity> stockoutOrderNoTaskItemList = splitTaskItemList.stream().filter(item -> stockoutOrderNo.equals(item.getStockoutOrderNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockoutOrderNoTaskItemList)) {
            throw new BusinessServiceException(String.format("根据分拣任务id【%s】,出库单号【%s】找不到分拣任务明细", splitTaskItemList.get(0).getTaskId(), stockoutOrderNo));
        }
        List<StockoutBatchSplitTaskItemEntity> notCompleteScanItemList = stockoutOrderNoTaskItemList.stream().filter(item -> item.getBatchQty().compareTo(item.getScanQty()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notCompleteScanItemList)) {
            LOGGER.info("出库单: {} 分拣完成-----------------------------------------------------------", stockoutOrderNo);
            return true;
        }
        return false;
    }

    public boolean checkSplitTaskScanComplete(List<StockoutBatchSplitTaskItemEntity> splitTaskItemEntityList) {
        List<StockoutBatchSplitTaskItemEntity> notScanCompleteItemList = splitTaskItemEntityList.stream().filter(item -> item.getBatchQty().compareTo(item.getScanQty()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notScanCompleteItemList)) {
            LOGGER.info("分拣任务: {} 分拣完成-----------------------------------------------------------", splitTaskItemEntityList.get(0).getTaskId());
            return true;
        }
        return false;
    }

}
