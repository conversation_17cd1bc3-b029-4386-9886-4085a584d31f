package com.nsy.wms.business.service.stock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.nsy.api.wms.constants.StockoutSpaceTrackConstant;
import com.nsy.api.wms.domain.stock.StockCountDto;
import com.nsy.api.wms.domain.stock.StockSumItemDto;
import com.nsy.api.wms.domain.stockout.OverseaSpaceSkuDTO;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.wms.business.manage.notify.NotifyApiService;
import com.nsy.wms.business.manage.notify.request.DingTalkRobotWebhookMarkdownSendRequest;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.manage.tms.request.OverseaStockRequest;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.system.AliyunOssService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockOverseaSpaceEntity;
import com.nsy.wms.repository.entity.stock.StockPrematchInfoEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class StockSpaceOverseaService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockSpaceOverseaService.class);

    @Autowired
    private BdSpaceService spaceService;
    @Autowired
    NotifyApiService notifyApiService;
    @Autowired
    StockService stockService;
    @Autowired
    StockPrematchInfoService prematchInfoService;
    @Autowired
    StockOverseaSpaceService stockOverseaSpaceService;
    @Autowired
    BdPositionService positionService;

    @Autowired
    private BdSystemParameterService systemParameterService;
    @Autowired
    private TmsApiService tmsApiService;
    @Autowired
    LoginInfoService loginInfoService;
    @Resource
    AliyunOssService aliyunOssService;

    public void checkSpaceOverseaStock(Integer notifyDingding) {
        String value = systemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_OVERSEA_SPACE_CHECK.getKey());
        if (StrUtil.isBlank(value)) {
            LOGGER.info("海外仓库存核对未配置仓库！");
            return;
        }
        String[] split = value.split(",");
        List<StockCountDto> result = new LinkedList<>();
        for (String spaceName : split) {
            try {
                List<StockCountDto> stockCountDtos = SpringUtil.getBean(StockSpaceOverseaService.class).startCheck(spaceName, notifyDingding, null);
                result.addAll(stockCountDtos);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
                LOGGER.error("执行{}海外仓库存核对时发生异常!", spaceName);
            }
        }
        if (CollectionUtils.isEmpty(result)) {
            LOGGER.info("海外仓库存核对无数据！");
            return;
        }
        DingTalkRobotWebhookMarkdownSendRequest requestActivity = new DingTalkRobotWebhookMarkdownSendRequest();
        requestActivity.setMsgType("markdown");
        String title = "## 【" + LocationEnum.getNameBy(TenantContext.getTenant()) + "海外仓-wms 监控】\n| **仓库名称** | wms数 | 海外仓数 | 差异 | 对账结果 |\n| --- | --- | --- | --- | --- |\n";
        List<String> tableString = new ArrayList<>(16);
        for (StockCountDto item : result) {
            tableString.add("| **" + item.getShortName() + "** |" + item.getWmsSum() + '|' + item.getOverseaSpaceSum() + '|' + item.getDiffSum() + '|');
            String url = item.getUrl();
            tableString.add(url != null && !url.isEmpty() ? String.format("[下载](%s)|", url) : "|");
            tableString.add("\n");
        }
        String info = title + String.join("", tableString);
        requestActivity.setMarkdown(new DingTalkRobotWebhookMarkdownSendRequest.Markdown("仓库提示：海外库存比对", info));
        if (notifyDingding == 1) {
            notifyApiService.sendWebhookStockCheckMarkdownMessage(requestActivity);
        } else {
            LOGGER.info(info);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public List<StockCountDto> startCheck(String spaceName, Integer notifyDingding, List<String> skuList) {
        OverseaStockRequest request = new OverseaStockRequest();
        List<StockCountDto> result = new ArrayList<>();
        BdSpaceEntity space = spaceService.getBySpaceName(spaceName);
        if (space == null) {
            LOGGER.info("海外仓库存: {}查不到该仓库数据", spaceName);
            return result;
        }
        request.setSpaceName(space.getSpaceName());
        request.setSkuList(skuList);
        List<OverseaSpaceSkuDTO> overseaSpaceProductStock = tmsApiService.getOverseaSpaceProductStock(request);
        if (CollectionUtils.isEmpty(overseaSpaceProductStock)) {
            LOGGER.info("海外仓库存: {}查询暂无库存数据！", spaceName);
            return result;
        }
        // 有数据，开始比对
        List<BdPositionEntity> allBySpaceIdAndPositionType = positionService.findAllBySpaceIdAndPositionType(space.getSpaceId(), BdPositionTypeEnum.STOCK_POSITION.name());
        if (CollectionUtils.isEmpty(allBySpaceIdAndPositionType)) {
            LOGGER.info("海外仓库存: {}查询不到存储库位！", spaceName);
            return result;
        }
        return buildStockDiff(space, overseaSpaceProductStock, allBySpaceIdAndPositionType, skuList, notifyDingding);
    }


    public List<StockCountDto> buildStockDiff(BdSpaceEntity space, List<OverseaSpaceSkuDTO> overseaSpaceProductStock, List<BdPositionEntity> allBySpaceIdAndPositionType, List<String> skuList, Integer notifyDingding) {
        List<StockEntity> allStock = stockService.findByPositionCodeInAndSkuList(allBySpaceIdAndPositionType.stream().map(BdPositionEntity::getPositionCode).collect(Collectors.toList()), skuList);
        List<StockCountDto> result = new ArrayList<>();
        Map<String, List<OverseaSpaceSkuDTO>> overseaSpaceMap = overseaSpaceProductStock.stream().collect(Collectors.groupingBy(OverseaSpaceSkuDTO::getSku));
        Map<String, Integer> stockMap = new HashMap<>();
        Map<String, Set<String>> positionCodeMap = new HashMap<>();
        for (StockEntity erpAllStock : allStock) {
            stockMap.merge(erpAllStock.getSku(), erpAllStock.getStock(), Integer::sum);
            positionCodeMap.computeIfAbsent(erpAllStock.getSku(), k -> new HashSet<>()).add(erpAllStock.getPositionCode());
        }
        StockCountDto dto = new StockCountDto();
        dto.setShortName(space.getSpaceName());
        // 预占数，部分仓库会用到
        Map<String, Integer> stockPreMatchMap = new HashMap<>();
        List<StockPrematchInfoEntity> spacePreMatchInfoList = prematchInfoService.findBySpaceIdAndSkuList(space.getSpaceId(), skuList);
        spacePreMatchInfoList.forEach(matchInfo -> stockPreMatchMap.merge(matchInfo.getSku(), matchInfo.getPrematchQty(), Integer::sum));
        List<StockSumItemDto> sumItemDtos = new ArrayList<>();
        String positionCodeJoin = allBySpaceIdAndPositionType.stream().map(BdPositionEntity::getPositionCode).collect(Collectors.joining(","));
        stockMap.forEach((erpSku, erpStockReal) -> {
            Integer erpStock = erpStockReal;
            List<OverseaSpaceSkuDTO> overseaSpaceSkuDTOList = overseaSpaceMap.getOrDefault(erpSku, CollectionUtil.newArrayList(new OverseaSpaceSkuDTO(erpSku)));
            // 用于内部计算
            int otherSpaceStock;
            if (StrUtil.contains(space.getDescription(), StockoutSpaceTrackConstant.SPACE_DESC_ZHIYUN)) {
                erpStock = erpStockReal - stockPreMatchMap.getOrDefault(erpSku, 0);
                otherSpaceStock = overseaSpaceSkuDTOList.stream().mapToInt(OverseaSpaceSkuDTO::caleSaleableQty).sum();
                if (otherSpaceStock < 0)
                    otherSpaceStock = 0;
            } else {
                otherSpaceStock = overseaSpaceSkuDTOList.stream().mapToInt(OverseaSpaceSkuDTO::getQty).sum();
            }
            dto.setOverseaSpaceSum(otherSpaceStock + dto.getOverseaSpaceSum());
            dto.setWmsSum(erpStock + dto.getWmsSum());
            if (!NumberUtil.equals(erpStock, otherSpaceStock)) {
                StockSumItemDto itemDto = buildStockSumItemDto(stockPreMatchMap, erpSku, erpStockReal, erpStock, otherSpaceStock);
                dto.setDiffSum(Math.abs(erpStock - otherSpaceStock) + dto.getDiffSum());
                itemDto.setPositionCode(StrUtil.join(",", positionCodeMap.get(erpSku)));
                sumItemDtos.add(itemDto);
            }
            saveOverseaStockEntity(overseaSpaceSkuDTOList, space, erpStock,
                    stockPreMatchMap.getOrDefault(erpSku, 0), StrUtil.join(",", positionCodeMap.get(erpSku)));
            overseaSpaceMap.remove(erpSku);
        });
        // 反向对比，找出海外仓有，而我们没有的数据
        overseaSpaceMap.forEach((erpSku, skuSpaceStockList) -> {
            StockSumItemDto itemDto = new StockSumItemDto();
            buildItemDtoZero(erpSku, skuSpaceStockList, itemDto);
            dto.setDiffSum(itemDto.getDiffStock() + dto.getDiffSum());
            itemDto.setPositionCode(positionCodeJoin);
            sumItemDtos.add(itemDto);
            saveOverseaStockEntity(skuSpaceStockList, space, 0,
                    stockPreMatchMap.getOrDefault(erpSku, 0), positionCodeJoin);
        });
        if (!CollectionUtils.isEmpty(sumItemDtos)) {
            String url = buildBrandExcel(space, LocationEnum.getNameBy(space.getLocation()), sumItemDtos, notifyDingding);
            dto.setUrl(url);
        }
        result.add(dto);
        return result;
    }

    @NotNull
    private StockSumItemDto buildStockSumItemDto(Map<String, Integer> stockPreMatchMap, String erpSku, Integer erpStockReal, Integer erpStock, int otherSpaceStock) {
        StockSumItemDto itemDto = new StockSumItemDto();
        itemDto.setOverseaSpaceStock(otherSpaceStock);
        itemDto.setWmsStock(erpStockReal);
        itemDto.setSku(erpSku);
        itemDto.setWmsStockFreeze(stockPreMatchMap.getOrDefault(erpSku, 0));
        itemDto.setWmsStockEnable(erpStockReal - itemDto.getWmsStockFreeze());
        itemDto.setDiffStock(erpStock - otherSpaceStock);
        return itemDto;
    }

    private void buildItemDtoZero(String erpSku, List<OverseaSpaceSkuDTO> skuSpaceStockList, StockSumItemDto itemDto) {
        itemDto.setOverseaSpaceStock(skuSpaceStockList.stream().mapToInt(OverseaSpaceSkuDTO::getQty).sum());
        itemDto.setWmsStock(0);
        itemDto.setWmsStockFreeze(0);
        itemDto.setWmsStockEnable(0);
        itemDto.setSku(erpSku);
        itemDto.setDiffStock(itemDto.getOverseaSpaceStock());
    }

    private void saveOverseaStockEntity(List<OverseaSpaceSkuDTO> overseaSpaceSkuDTOList, BdSpaceEntity space, Integer erpStock, Integer erpLockQty, String positionCode) {
        StockOverseaSpaceEntity entity = stockOverseaSpaceService.getBySpaceIdAndSku(space.getSpaceId(), overseaSpaceSkuDTOList.get(0).getSku());
        if (entity == null) {
            entity = new StockOverseaSpaceEntity();
            entity.setSpaceId(space.getSpaceId());
            entity.setSpaceName(space.getSpaceName());
            entity.setSku(overseaSpaceSkuDTOList.get(0).getSku());
            entity.setCreateBy(loginInfoService.getName());
        } else {
            entity.setUpdateBy(loginInfoService.getName());
        }
        entity.setErpLockQty(erpLockQty);
        entity.setPositionCode(StrUtil.maxLength(positionCode, 250));
        entity.setStockQty(0);
        entity.setLockQty(0);
        for (OverseaSpaceSkuDTO item : overseaSpaceSkuDTOList) {
            entity.setStockQty(entity.getStockQty() + item.getQty());
            entity.setLockQty(entity.getLockQty() + item.getLockQty());
        }
        entity.setSaleableQty(entity.getStockQty() - entity.getLockQty());
        entity.setFetchDate(new Date());
        if (StrUtil.contains(space.getDescription(), StockoutSpaceTrackConstant.SPACE_DESC_ZHIYUN)) {
            entity.setErpStockQty(erpStock + erpLockQty);
            if (entity.getSaleableQty() < 0) {
                entity.setSaleableQty(0);
            }
            if (!NumberUtil.equals(erpStock, entity.getSaleableQty())) {
                entity.setHasDifference(1);
                entity.setDiffQty(erpStock - entity.getSaleableQty());
            } else {
                entity.setHasDifference(0);
                entity.setDiffQty(0);
            }
        } else {
            entity.setErpStockQty(erpStock);
            if (!NumberUtil.equals(erpStock, entity.getStockQty())) {
                entity.setHasDifference(1);
                entity.setDiffQty(erpStock - entity.getStockQty());
            } else {
                entity.setHasDifference(0);
                entity.setDiffQty(0);
            }
        }
        stockOverseaSpaceService.saveOrUpdate(entity);
    }

    private String buildBrandExcel(BdSpaceEntity space, String location, List<StockSumItemDto> diffList, Integer notifyDingding) {
        if (notifyDingding == 2) {
            LOGGER.info("业务人员手动触发海外仓库存核对：{}", JsonMapper.toJson(notifyDingding));
            return "";
        }
        ExcelWriter writer = ExcelUtil.getWriter();
        List<List<String>> rows = new ArrayList<>();
        String formatDate = DateUtil.formatDate(new Date());
        //标题
        rows.add(CollUtil.newArrayList(location + "-" + space.getSpaceName() + "-" + formatDate));
        String desc = space.getDescription().contains(StockoutSpaceTrackConstant.SPACE_DESC_ZHIYUN) ? "(可用)" : "";
        rows.add(CollUtil.newArrayList("sku", "erp库位", "erp预配", "erp可用库存", "erp库存", "海外仓库存" + desc, "库存差异"));
        diffList.forEach(stockSumItemDto -> {
            rows.add(CollUtil.newArrayList(stockSumItemDto.getSku(), stockSumItemDto.getPositionCode(), stockSumItemDto.getWmsStockFreeze().toString(), String.valueOf(stockSumItemDto.getWmsStockEnable()),
                    stockSumItemDto.getWmsStock().toString(), stockSumItemDto.getOverseaSpaceStock().toString(), String.valueOf(stockSumItemDto.getDiffStock())));
        });
        ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream();
        writer.write(rows, true);
        writer.flush(byteOutputStream);
        byte[] bytes = byteOutputStream.toByteArray();
        String fileName = space.getSpaceName() + "-" + formatDate + ".xlsx";
        return aliyunOssService.putTempObject(new ByteArrayInputStream(bytes), "StockCheckTemp", fileName);
    }
}
