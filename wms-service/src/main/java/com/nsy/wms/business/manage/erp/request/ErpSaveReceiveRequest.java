package com.nsy.wms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ErpSaveReceiveRequest {

    @JsonProperty("SupplierDeliveryBoxCode")
    private String supplierDeliveryBoxCode;

    @JsonProperty("StockInType")
    private String stockinType;

    @JsonProperty("Operator")
    private String operator;

    @JsonProperty("Status")
    private String status;

    @JsonProperty("SpaceId")
    private Integer spaceId;

    @JsonProperty("ReceivingItemList")
    private List<ErpSaveReceiveItemRequest> itemList;

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public String getStockinType() {
        return stockinType;
    }

    public void setStockinType(String stockinType) {
        this.stockinType = stockinType;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public List<ErpSaveReceiveItemRequest> getItemList() {
        return itemList;
    }

    public void setItemList(List<ErpSaveReceiveItemRequest> itemList) {
        this.itemList = itemList;
    }
}
