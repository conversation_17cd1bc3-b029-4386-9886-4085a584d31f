package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CountryCodeConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.bd.BdPosition;
import com.nsy.api.wms.domain.stock.StockPrematchInfo;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskInfo;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskItem;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskList;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskStatusCount;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.domain.stockout.StockoutVasTypeValidDTO;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.PrematchProcessTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingScanningStationEnum;
import com.nsy.api.wms.request.bd.DateRangeRequest;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutPickingForceConfirmRequest;
import com.nsy.api.wms.request.stockout.StockoutPickingLogListRequest;
import com.nsy.api.wms.request.stockout.StockoutPickingQuickConfirmRequest;
import com.nsy.api.wms.request.stockout.StockoutPickingRecordSetRequest;
import com.nsy.api.wms.request.stockout.StockoutPickingTaskListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.common.NoticeMessageResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderBatchPickQtyResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingLogListResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskItemResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskListCountResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskListExport;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskListResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskStatusCountResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stock.StockPrintService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.stockout.valid.StockoutPickingTaskValid;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockPrematchInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingItemRecordEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingLogEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutPickingItemRecordMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutPickingLogMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutPickingTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.WmsDateUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockoutPickingTaskService extends ServiceImpl<StockoutPickingTaskMapper, StockoutPickingTaskEntity> implements IDownloadService {

    @Resource
    StockoutPickingTaskMapper stockoutPickingTaskMapper;
    @Resource
    StockoutPickingLogMapper stockoutPickingLogMapper;
    @Resource
    StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Resource
    StockoutOrderLogService logService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    StockoutBatchService stockoutBatchService;
    @Resource
    StockoutOrderService stockoutOrderService;
    @Resource
    StockoutPickingLogService pickingLogService;
    @Resource
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Resource
    StockoutPickingItemRecordMapper stockoutPickingItemRecordMapper;
    @Resource
    StockoutPickingTaskConfirmService stockoutPickingTaskConfirmService;
    @Resource
    StockPrematchInfoService stockPrematchInfoService;
    @Resource
    StockPrintService stockPrintService;
    @Resource
    StockoutOrderLackItemService stockoutOrderLackItemService;
    @Resource
    BdPositionService bdPositionService;
    @Resource
    MessageProducer messageProducer;
    @Resource
    StockService stockService;
    @Resource
    StockoutBatchOrderService stockoutBatchOrderService;


    /**
     * 拣货任务基础信息
     */
    public StockoutPickingTaskItemResponse getOutPickingTaskDetailByTaskId(Integer taskId) {
        StockoutPickingTaskEntity stockoutPickingTaskEntity = stockoutPickingTaskMapper.selectById(taskId);
        if (Objects.isNull(stockoutPickingTaskEntity)) {
            throw new BusinessServiceException("拣货任务不存在");
        }
        StockoutPickingTaskItemResponse response = new StockoutPickingTaskItemResponse();
        StockoutPickingTaskItem item = stockoutPickingTaskMapper.searchInfo(taskId);
        BeanUtils.copyProperties(item, response);
        StockoutBatchEntity batchEntity = stockoutBatchService.getStockoutBatchById(stockoutPickingTaskEntity.getBatchId());
        if (StringUtils.hasText(batchEntity.getLogisticsCompany())) {
            response.setLogisticsCompany(batchEntity.getLogisticsCompany());
        }
        response.setPickingTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), item.getPickingType()));
        response.setScanTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_SORTING_SCANNING_STATION.getName(), item.getScanType()));
        response.setBatchSplitTypeStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_SORTING_TYPE.getName(), item.getBatchSplitType()));
        response.setWorkspaceStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), batchEntity.getWorkspace()));
        return response;
    }

    /**
     * 拣货任务列表
     */
    public PageResponse<StockoutPickingTaskListResponse> getOutPickingTaskListByRequest(StockoutPickingTaskListRequest request) {
        PageResponse<StockoutPickingTaskListResponse> pageResponse = new PageResponse<>();
        validRequest(request);
        Page<StockoutPickingTaskList> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        if (ObjectUtils.isEmpty(request.isSearchCountFlag()) || request.isSearchCountFlag()) {
            pageResponse.setTotalCount(stockoutPickingTaskMapper.countSearchList(request));
        }
        IPage<StockoutPickingTaskList> pageResult = stockoutPickingTaskMapper.pageSearchList(page, request);
        List<StockoutPickingTaskList> records = pageResult.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            pageResponse.setContent(new ArrayList<>());
            return pageResponse;

        }
        //根据查询出的taskId,继续查询明细内容
        List<Integer> taskIdList = records.stream().map(StockoutPickingTaskList::getTaskId).collect(Collectors.toList());
        List<StockoutPickingTaskList> itemInfoList = new LinkedList<>();
        List<List<Integer>> partition = Lists.partition(taskIdList, 50);
        partition.forEach(taskIds -> {
            itemInfoList.addAll(stockoutPickingTaskMapper.pageSearchItemInfo(taskIds));
        });
        Map<Integer, StockoutPickingTaskList> taskListMap = itemInfoList.stream().collect(Collectors.toMap(StockoutPickingTaskList::getTaskId, Function.identity()));

        Map<String, String> stockoutPickingTaskStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TASK_STATUS.getName());
        Map<String, String> stockoutWorkLocationEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName());
        Map<String, String> stockoutPickingTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName());
        Map<String, String> stockoutSortingScanningStationEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_SORTING_SCANNING_STATION.getName());
        Map<String, String> pickingTaskTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PICKING_TASK_TYPE.getName());
        List<Integer> taskIds = records.stream().map(StockoutPickingTaskList::getTaskId).collect(Collectors.toList());

        Map<Integer, Long> boxQtyMap = buildPickedBoxQty(taskIds);
        List<StockoutPickingTaskListResponse> resultList = records.stream().map(projection -> {
            StockoutPickingTaskListResponse response = new StockoutPickingTaskListResponse();
            BeanUtils.copyProperties(projection, response);

            StockoutPickingTaskList itemInfo = taskListMap.get(projection.getTaskId());
            if (Objects.nonNull(itemInfo)) {
                response.setSpaceAreaName(itemInfo.getSpaceAreaName());
                response.setAreaName(itemInfo.getAreaName());
                response.setPickedQty(itemInfo.getPickedQty());
                response.setIsLack(itemInfo.getIsLack());
                response.setOperator(itemInfo.getOperator());
            }

            response.setStatusStr(stockoutPickingTaskStatusEnumMap.get(projection.getStatus()));
            response.setWorkspaceStr(stockoutWorkLocationEnumMap.get(projection.getWorkspace()));
            response.setPickingTypeStr(stockoutPickingTypeEnumMap.get(projection.getPickingType()));
            response.setScanTypeStr(stockoutSortingScanningStationEnumMap.get(projection.getScanType()));
            response.setTaskTypeStr(pickingTaskTypeEnumMap.get(projection.getTaskType()));
            Long boxQty = boxQtyMap.get(projection.getTaskId());
            response.setPickedBoxQty(Objects.nonNull(boxQty) ? boxQty.intValue() : 0);
            return response;
        }).collect(Collectors.toList());
        if (page.searchCount()) {
            pageResponse.setTotalCount(pageResult.getTotal());
        }
        pageResponse.setContent(resultList);
        return pageResponse;
    }

    private void validRequest(StockoutPickingTaskListRequest request) {
        if (StringUtils.hasText(request.getSku()) && Objects.isNull(request.getBatchId()) && Objects.isNull(request.getTaskId()))
            throw new BusinessServiceException("sku查询时，波次号或任务号不能为空");

    }

    /**
     * 列表统计
     */
    public StockoutPickingTaskListCountResponse pageSearchCount(StockoutPickingTaskListRequest request) {
        validRequest(request);
        //数据量太大，需要拆分查询
        if (Objects.nonNull(request.getCreateStartDate()) && Objects.nonNull(request.getCreateEndDate())) {
            //按一天一次查询
            List<DateRangeRequest> dateRangeRequests = WmsDateUtils.splitDateRange(request.getCreateStartDate(), request.getCreateEndDate(), 1);
            Integer expectedQty = 0;
            Integer pickedQty = 0;
            for (DateRangeRequest dateRangeRequest : dateRangeRequests) {
                request.setCreateStartDate(dateRangeRequest.getStartDate());
                request.setCreateEndDate(dateRangeRequest.getEndDate());
                StockoutPickingTaskListCountResponse response = stockoutPickingTaskMapper.pageSearchCount(request);

                expectedQty += response.getExpectedQty();
                pickedQty += response.getPickedQty();
            }
            StockoutPickingTaskListCountResponse response = new StockoutPickingTaskListCountResponse();
            response.setExpectedQty(expectedQty);
            response.setPickedQty(pickedQty);
            return response;
        }

        return stockoutPickingTaskMapper.pageSearchCount(request);
    }

    // 已拣箱数
    Map<Integer, Long> buildPickedBoxQty(List<Integer> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) return Collections.EMPTY_MAP;
        LambdaQueryWrapper<StockoutPickingItemRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(StockoutPickingItemRecordEntity::getTaskId, StockoutPickingItemRecordEntity::getInternalBoxCode);
        queryWrapper.in(StockoutPickingItemRecordEntity::getTaskId, taskIds);
        List<StockoutPickingItemRecordEntity> list = stockoutPickingItemRecordMapper.selectList(queryWrapper);
        List<StockoutPickingItemRecordEntity> filterList = list.stream().filter(item -> item.getInternalBoxCode() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return Collections.EMPTY_MAP;
        } else {
            return filterList.stream().distinct().collect(Collectors.groupingBy(StockoutPickingItemRecordEntity::getTaskId, Collectors.counting()));
        }
    }

    /**
     * 拣货任务日志列表
     */
    public PageResponse<StockoutPickingLogListResponse> getPickingLogListByRequest(StockoutPickingLogListRequest request) {
        PageResponse<StockoutPickingLogListResponse> pageResponse = new PageResponse<>();
        Page<StockoutPickingLogEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutPickingLogEntity> pageResult = stockoutPickingLogMapper.pageSearchOutPickingLog(page, request);
        List<StockoutPickingLogEntity> logEntityList = pageResult.getRecords();
        List<StockoutPickingLogListResponse> resultList = logEntityList.stream().map(entity -> {
            StockoutPickingLogListResponse log = new StockoutPickingLogListResponse();
            BeanUtils.copyProperties(entity, log);
            return log;
        }).collect(Collectors.toList());
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(resultList);
        return pageResponse;
    }

    /**
     * 各个拣货任务状态对应任务数量
     */
    public StockoutPickingTaskStatusCountResponse getStatusCount(StockoutPickingTaskListRequest request) {
        StockoutPickingTaskStatusCountResponse response = new StockoutPickingTaskStatusCountResponse();

        StockoutPickingTaskListRequest requestAll = new StockoutPickingTaskListRequest();
        BeanUtilsEx.copyProperties(request, requestAll);

        List<StockoutPickingTaskStatusCount> stockoutPickingTaskStatusCounts = stockoutPickingTaskMapper.statusCount(requestAll);
        Map<String, Integer> collect = stockoutPickingTaskStatusCounts.stream().collect(Collectors.toMap(StockoutPickingTaskStatusCount::getStatus, StockoutPickingTaskStatusCount::getCount));

        Integer zero = Integer.valueOf(0);
        response.setWaitPickNum(Objects.isNull(collect.get(StockoutPickingTaskStatusEnum.WAIT_PICK.name())) ? zero : collect.get(StockoutPickingTaskStatusEnum.WAIT_PICK.name()));
        response.setPickingNum(Objects.isNull(collect.get(StockoutPickingTaskStatusEnum.PICKING.name())) ? zero : collect.get(StockoutPickingTaskStatusEnum.PICKING.name()));
        response.setPickedNum(Objects.isNull(collect.get(StockoutPickingTaskStatusEnum.PICKED.name())) ? zero : collect.get(StockoutPickingTaskStatusEnum.PICKED.name()));
        response.setAllNum(response.getWaitPickNum() + response.getPickingNum() + response.getPickedNum());
        return response;
    }

    /**
     * 新增拣货任务
     *
     * @param stockoutBatchEntity 波次
     * @param pickingTaskInfoList 拣货任务信息
     * @param userName            用户名
     */
    public List<Integer> addByStockoutBatch(StockoutBatchEntity stockoutBatchEntity, List<StockoutPickingTaskInfo> pickingTaskInfoList, String userName) {
        List<StockoutPickingTaskEntity> pickingTaskEntityList = new LinkedList<>();
        List<StockoutPickingTaskItemEntity> pickingTaskItemEntityList = new LinkedList<>();
        for (StockoutPickingTaskInfo taskInfo : pickingTaskInfoList) {
            StockoutPickingTaskEntity pickingTaskEntity = StockoutBuilding.buildByStockoutBatch(stockoutBatchEntity);
            pickingTaskEntity.setBatchId(taskInfo.getBatchId());
            pickingTaskEntity.setExpectedQty(taskInfo.getExpectedQty());
            pickingTaskEntity.setPositionQty(taskInfo.getPositionQty());
            boolean isProcess = PrematchProcessTypeEnum.PROCESS.name().equals(taskInfo.getPrematchProcessType().name());
            pickingTaskEntity.setIsNeedProcess(isProcess ? 1 : 0);
            if (isProcess) { //加工
                pickingTaskEntity.setTaskType(StockoutPickingTaskTypeEnum.PROCESSING_PICKING.name());
                pickingTaskEntity.setScanType(StockoutSortingScanningStationEnum.PROCESS_STATION.name());
            } else if (PrematchProcessTypeEnum.SEW.name().equals(taskInfo.getPrematchProcessType().name())) { //缝制
                pickingTaskEntity.setScanType(StockoutSortingScanningStationEnum.SEW_STATION.name());
                pickingTaskEntity.setTaskType(StockoutPickingTaskTypeEnum.SEW_PICKING.name());
            } else {
                pickingTaskEntity.setTaskType(StockoutPickingTaskTypeEnum.STOCK_PICKING.name());
                if (stockoutBatchEntity.getIsNeedProcess().equals(1))
                    pickingTaskEntity.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
            }
            pickingTaskEntity.setCreateBy(userName);
            save(pickingTaskEntity);
            pickingTaskEntityList.add(pickingTaskEntity);
            // 明细
            for (StockoutPickingTaskItemInfo taskItemInfo : taskInfo.getItemInfoList()) {
                StockoutPickingTaskItemEntity taskItemEntity = StockoutBuilding.buildByPickingTaskItemInfo(pickingTaskEntity, taskItemInfo);
                taskItemEntity.setCreateBy(userName);
                pickingTaskItemEntityList.add(taskItemEntity);
            }
        }
        stockoutPickingTaskItemService.saveBatch(pickingTaskItemEntityList);
        // 日志
        List<Integer> taskIdList = pickingTaskEntityList.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList());
        pickingTaskEntityList.forEach(pickingTask -> {
            pickingTask.setPickingBoxCode(stockPrintService.getPickBoxAndUpdatePrint(pickingTask));
            updateById(pickingTask);
        });
        pickingLogService.addBatchLog(taskIdList, "拣货任务生成", "拣货任务生成");
        return taskIdList;
    }

    /**
     * 创建缺货拣货任务
     * 只生成一个任务，不需要走策略
     *
     * @param batchId
     * @param pickingTaskType
     * @param prematchInfoEntityList
     * @return
     */
    @Transactional
    public Integer createLackPickingTask(Integer batchId, List<StockPrematchInfoEntity> prematchInfoEntityList, StockoutPickingTaskTypeEnum pickingTaskType) {
        StockoutBatchEntity batchEntity = stockoutBatchService.getStockoutBatchById(batchId);
        StockoutPickingTaskEntity taskEntity = new StockoutPickingTaskEntity();
        taskEntity.setLocation(batchEntity.getLocation());
        taskEntity.setBatchId(batchEntity.getBatchId());
        taskEntity.setSpaceId(batchEntity.getSpaceId());
        taskEntity.setScanType(batchEntity.getScanType());
        taskEntity.setExpectedQty(prematchInfoEntityList.stream().mapToInt(StockPrematchInfoEntity::getPrematchQty).sum());
        taskEntity.setPositionQty((int) prematchInfoEntityList.stream().map(StockPrematchInfoEntity::getPositionId).distinct().count());
        taskEntity.setTaskType(pickingTaskType.name());
        taskEntity.setIsNeedProcess(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name().equals(pickingTaskType.name()) ? 1 : 0);
        taskEntity.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
        stockoutPickingTaskMapper.insert(taskEntity);
        List<StockoutPickingTaskItemEntity> pickingTaskItemEntityList = new ArrayList<>(16);
        List<Integer> stockoutOrderIdList = prematchInfoEntityList.stream().map(StockPrematchInfoEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
        Map<Integer, StockoutOrderEntity> stockoutOrderMap = stockoutOrderService.listByIds(stockoutOrderIdList).stream().collect(Collectors.toMap(StockoutOrderEntity::getStockoutOrderId, Function.identity(), (v1, v2) -> v1));
        List<StockPrematchInfo> prematchInfoList = stockPrematchInfoService.combinePrematch(prematchInfoEntityList);

        for (StockPrematchInfo prematchInfo : prematchInfoList) {
            if (Objects.isNull(prematchInfo.getPrematchQty()) || prematchInfo.getPrematchQty() <= 0) continue;
            BdPosition bdPosition = bdPositionService.bdPositionById(prematchInfo.getPositionId());
            StockoutOrderEntity stockoutOrder = stockoutOrderMap.get(prematchInfo.getStockoutOrderId());
            if (Objects.isNull(stockoutOrder))
                throw new BusinessServiceException(String.format("出库单 %s 不存在", prematchInfo.getStockoutOrderId()));
            StockoutPickingTaskItemEntity itemEntity = new StockoutPickingTaskItemEntity();
            itemEntity.setLocation(batchEntity.getLocation());
            itemEntity.setTaskId(taskEntity.getTaskId());
            itemEntity.setStockoutOrderNo(stockoutOrder.getStockoutOrderNo());
            itemEntity.setProductId(prematchInfo.getProductId());
            itemEntity.setSpecId(prematchInfo.getSpecId());
            itemEntity.setSku(prematchInfo.getSku());
            itemEntity.setBarcode(prematchInfo.getBarcode());
            itemEntity.setSpaceAreaId(bdPosition.getSpaceAreaId());
            itemEntity.setSpaceAreaName(bdPosition.getSpaceAreaName());
            itemEntity.setPositionId(prematchInfo.getPositionId());
            itemEntity.setPositionCode(prematchInfo.getPositionCode());
            itemEntity.setExpectedQty(prematchInfo.getPrematchQty());
            itemEntity.setPickedQty(0);
            itemEntity.setStatus(StockoutPickingTaskStatusEnum.WAIT_PICK.name());
            pickingTaskItemEntityList.add(itemEntity);
        }
        stockoutPickingTaskItemService.saveBatch(pickingTaskItemEntityList);
        pickingLogService.addLog(taskEntity.getTaskId(), "拣货任务生成", "拣货任务生成");
        taskEntity.setPickingBoxCode(stockPrintService.getPickBoxAndUpdatePrint(taskEntity));
        stockoutPickingTaskMapper.updateById(taskEntity);
        //出库单日志
        stockoutOrderMap.values().forEach(stockoutOrder -> logService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CREATE_LACK_PICKING_TASK, String.format("缺货拣货任务生成 任务号【 %s 】", taskEntity.getTaskId())));
        return taskEntity.getTaskId();
    }


    @Transactional
    public NoticeMessageResponse stockoutPickTaskQuickConfirm(StockoutPickingQuickConfirmRequest request) {
        StockoutPickingTaskValid.validateStockoutPickingQuickConfirmRequest(request);
        StockoutPickingTaskEntity taskEntity = stockoutPickingTaskConfirmService.getStockoutPickingTaskEntity(request.getTaskId());
        if (!StockoutPickingTaskStatusEnum.WAIT_PICK.name().equals(taskEntity.getStatus())) {
            throw new BusinessServiceException("请选择待拣货的拣货任务！");
        }
        return this.pickTaskQuickConfirm(taskEntity, request);
    }

    public NoticeMessageResponse pickTaskQuickConfirm(StockoutPickingTaskEntity taskEntity, StockoutPickingQuickConfirmRequest request) {
        NoticeMessageResponse noticeMessageResponse = new NoticeMessageResponse();
        if (StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())) {
            throw new BusinessServiceException("该拣货任务已经完成！");
        }
        //验证库存
        List<StockoutPickingTaskItemEntity> taskItemEntityList = stockoutPickingTaskItemService.findAllByTaskId(request.getTaskId());
        StringBuilder errorMessage = new StringBuilder();
        taskItemEntityList.forEach(taskItemEntity -> {
            int pickQty = taskItemEntity.getExpectedQty() - taskItemEntity.getPickedQty();
            if (pickQty > 0)
                this.validLockStock(errorMessage, taskItemEntity.getSku(), taskItemEntity.getPositionCode(), pickQty);
        });
        if (!request.getIsConfirm() && errorMessage.length() > 0) {
            errorMessage.append("是否确认快捷拣货？");
            noticeMessageResponse.setNotice(Boolean.TRUE);
            noticeMessageResponse.setNoticeMessage(errorMessage.toString());
            return noticeMessageResponse;
        }
        //验证内部箱状态
        stockoutPickingTaskConfirmService.getInternalBox(request.getTaskId(), request.getInternalBoxCode(), true);

        messageProducer.sendMessage(KafkaConstant.SYNC_STOCKOUT_PICK_TASK_QUICK_CONFIRM_TOPIC_NAME, KafkaConstant.SYNC_STOCKOUT_PICK_TASK_QUICK_CONFIRM_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request));
        return noticeMessageResponse;
    }

    private void validLockStock(StringBuilder errorMessage, String sku, String positionCode, Integer qty) {
        StockEntity skuPositionStock = stockService.getSkuPositionStock(sku, positionCode);
        int stock = Objects.isNull(skuPositionStock) ? 0 : skuPositionStock.getStock();
        if (qty > stock)
            errorMessage.append(String.format("【%s】【%s】库存剩余%s件，无法拣货%s件;", positionCode, sku, stock, qty));
        if (Objects.nonNull(skuPositionStock) && skuPositionStock.getLock()) {
            throw new BusinessServiceException(String.format("【%s】【%s】库存被锁定，请先进行盘点", positionCode, sku));
        }
    }

    @Transactional
    public void pickingQuickConfirm(StockoutPickingQuickConfirmRequest request) {
        StockoutPickingTaskEntity taskEntity = stockoutPickingTaskMapper.selectById(request.getTaskId());
        if (StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())) {
            return;
        }

        List<StockoutPickingTaskItemEntity> taskItemEntityList = stockoutPickingTaskItemService.findAllByTaskId(request.getTaskId());
        //查询预占库存
        List<StockPrematchInfoEntity> list = stockPrematchInfoService.list(new QueryWrapper<StockPrematchInfoEntity>().lambda().eq(StockPrematchInfoEntity::getBatchId, taskEntity.getBatchId()).in(StockPrematchInfoEntity::getSku, taskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getSku).collect(Collectors.toList())).in(StockPrematchInfoEntity::getPositionCode, taskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getPositionCode).collect(Collectors.toList())));

        //遍历保存taskItem数据
        taskItemEntityList.forEach(taskItemEntity -> {
            Integer pickedQty = taskItemEntity.getPickedQty();
            Integer expectedQty = taskItemEntity.getExpectedQty();
            Integer prematchQty = list.stream().filter(prematchInfoEntity -> prematchInfoEntity.getSku().equals(taskItemEntity.getSku()) && prematchInfoEntity.getPositionCode().equals(taskItemEntity.getPositionCode())).mapToInt(StockPrematchInfoEntity::getPrematchQty).sum();
            StockEntity skuPositionStock = stockService.getSkuPositionStock(taskItemEntity.getSku(), taskItemEntity.getPositionCode());
            Integer stock = Objects.nonNull(skuPositionStock) ? skuPositionStock.getStock() : 0;
            Integer waitPickQty = expectedQty - pickedQty;

            //计算需要录入的数量
            // 预占数>=期望数  设置 pickedQty = expectedQty
            // 预占数<期望数   设置 pickedQty = prematchQty
            int pickQty = Math.min(stock, Math.min(waitPickQty, prematchQty));
            if (pickQty > 0) {
                stockoutPickingTaskConfirmService.stockoutPickingRecordSet(request.getTaskId(), new StockoutPickingRecordSetRequest(taskItemEntity.getTaskItemId(), request.getInternalBoxCode(), pickQty));
            }
        });
        //完成拣货任务
        if (Objects.isNull(taskEntity.getOperateStartDate())) {
            taskEntity.setOperateStartDate(new Date());
        }
        taskEntity.setOperateEndDate(new Date());
        taskEntity.setOperator(loginInfoService.getName());
        this.updateById(taskEntity);
        //这个方法会去删除预占库位
        stockoutPickingTaskConfirmService.pickingComplete(request.getTaskId(), Boolean.FALSE);

        //缺货拣货任务完成后，对应的缺货出库单明细OUT_STOCK CHECKED
        if (StockoutPickingTaskTypeEnum.OUT_STOCK_PICKING.name().equals(taskEntity.getTaskType()))
            stockoutOrderLackItemService.completeByBatchId(taskEntity.getBatchId());
    }

    @Transactional
    public void forceComplete(StockoutPickingForceConfirmRequest request) {
        StockoutPickingTaskValid.validateStockoutPickingForceConfirmRequest(request);
        List<Integer> taskIds = request.getTaskIds();
        taskIds.stream().forEach(taskId -> {
            StockoutPickingTaskEntity taskEntity = stockoutPickingTaskConfirmService.getStockoutPickingTaskEntity(taskId);
            if (!StockoutPickingTaskStatusEnum.PICKING.name().equals(taskEntity.getStatus())) {
                throw new BusinessServiceException("请选择【拣货中】的拣货任务！");
            }
            pickTaskQuickConfirm(taskEntity, new StockoutPickingQuickConfirmRequest(taskId, request.getInternalBoxCode(), Boolean.TRUE));
        });
    }

    public List<StockoutPickingTaskEntity> findByBatchIds(List<Integer> batchId) {
        return this.list(new LambdaQueryWrapper<StockoutPickingTaskEntity>().in(StockoutPickingTaskEntity::getBatchId, batchId));
    }

    public void buildReceiver(Map<String, Object> map, StockoutReceiverInfo receiverInfo) {
        map.put("receiverName", receiverInfo.getReceiverName());
        if (StringUtils.hasText(receiverInfo.getReceiverCountry()))
            map.put("receiverCountry", CountryCodeConstant.getCountryCodeMap().get(receiverInfo.getReceiverCountry()));
        map.put("receiverMobile", receiverInfo.getReceiverMobile());
        map.put("receiverPhone", receiverInfo.getReceiverPhone());
        map.put("receiverAddress", receiverInfo.getReceiverAddress());
        map.put("receiverZip", receiverInfo.getReceiverZip());
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKOUT_PICKING_TASK_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockoutPickingTaskListRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockoutPickingTaskListRequest.class);
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        downloadRequest.setSearchCountFlag(false);
        PageResponse<StockoutPickingTaskListResponse> pageResponse = getOutPickingTaskListByRequest(downloadRequest);

        List<StockoutPickingTaskListResponse> list = pageResponse.getContent();
        List<StockoutPickingTaskListExport> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            resultList = list.stream().map(item -> {
                StockoutPickingTaskListExport export = new StockoutPickingTaskListExport();
                BeanUtilsEx.copyProperties(item, export);
                if (item.getIsLack() == null) {
                    export.setIsLackStr("否");
                } else {
                    export.setIsLackStr(item.getIsLack() == 1 ? "是" : "否");
                }
                return export;
            }).collect(Collectors.toList());
        }
        response.setTotalCount((long) (pageResponse.getContent().size() >= request.getPageSize() ? (request.getPageIndex() + 1) * request.getPageSize() : (request.getPageIndex() - 1) * request.getPageSize() + pageResponse.getContent().size()));
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        return response;
    }

    /**
     * 通过状态\波次号\任务类型获取
     */
    public List<StockoutPickingTaskEntity> list(String status, Integer batchId, String taskType) {
        return this.list(new LambdaQueryWrapper<StockoutPickingTaskEntity>().eq(StockoutPickingTaskEntity::getStatus, status).eq(StockoutPickingTaskEntity::getBatchId, batchId).eq(StockoutPickingTaskEntity::getTaskType, taskType));
    }

    public List<Integer> getTaskIdsByBatchId(Integer batchId) {
        return this.getBaseMapper().getTaskIdsByBatchId(batchId);
    }

    public void batchBackToPosition(IdListRequest request) {
        request.getIdList().forEach(o -> {
            stockoutPickingTaskItemService.returnPickingBoxQtyToPosition(o);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePickingStatus(IdListRequest request) {
        this.baseMapper.updatePickingStatus(request.getIdList());
        stockoutPickingTaskItemService.updatePickingStatus(request.getIdList());
        request.getIdList().forEach(detail -> {
            pickingLogService.addLog(detail, "修改状态", "拣货任务手动更改状态为待拣货");
        });
    }

    /**
     * 校验是否存在增值服务，如果有则返回出库单号
     *
     * @return
     */
    public List<Integer> validAddValue(Integer taskId) {
        StockoutPickingTaskEntity stockoutPickingTaskEntity = stockoutPickingTaskMapper.selectById(taskId);
        if (ObjectUtils.isEmpty(stockoutPickingTaskEntity)) {
            return Collections.emptyList();
        }
        StockoutBatchEntity batchInfo = stockoutBatchService.getStockoutBatchById(stockoutPickingTaskEntity.getBatchId());
        if (!ObjectUtils.isEmpty(batchInfo) && !ObjectUtils.isEmpty(batchInfo.getMergeBatchId())) {
            List<StockoutPickingTaskEntity> list = this.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda().in(StockoutPickingTaskEntity::getBatchId, batchInfo.getMergeBatchId()).notIn(StockoutPickingTaskEntity::getTaskType, CollUtil.newArrayList(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name(), StockoutPickingTaskTypeEnum.REPLENISHMENT_PICKING.name())));
            //存在未完成、未加工完成的拣货任务则return
            if (CollectionUtils.isEmpty(list) || list.stream().anyMatch(taskEntity -> !StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())))
                return Collections.emptyList();
        }
        //非合并波次
        if (batchInfo.getIsMergeBatch() == 0) {
            List<StockoutVasTypeValidDTO> vasTypeByTaskIdList = this.getBaseMapper().getVasTypeByTaskId(taskId);
            if (CollectionUtils.isEmpty(vasTypeByTaskIdList)) {
                return Collections.emptyList();
            }
            return vasTypeByTaskIdList.stream().filter(detail -> StringUtils.hasText(detail.getVasType()) || 1 == detail.getIsFirstOrderByStore()).map(StockoutVasTypeValidDTO::getStockoutOrderId).collect(Collectors.toList());
        }
        List<StockoutBatchEntity> subBatchList = stockoutBatchService.getSubBatchIdByBatchId(stockoutPickingTaskEntity.getBatchId());
        if (CollectionUtils.isEmpty(subBatchList)) {
            return Collections.emptyList();
        }
        List<StockoutPickingTaskEntity> list = this.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda().in(StockoutPickingTaskEntity::getBatchId, subBatchList).notIn(StockoutPickingTaskEntity::getTaskType, CollUtil.newArrayList(StockoutPickingTaskTypeEnum.PROCESSING_LACK_PICKING.name(), StockoutPickingTaskTypeEnum.REPLENISHMENT_PICKING.name())));
        //合并波次，且合并波次的的子波次存在未完成、未加工完成的拣货任务则return
        if (CollectionUtils.isEmpty(list) || list.stream().anyMatch(taskEntity -> !StockoutPickingTaskStatusEnum.PICKED.name().equals(taskEntity.getStatus())))
            return Collections.emptyList();

        List<StockoutVasTypeValidDTO> vasTypeInfoByBatchIdList = stockoutBatchOrderService.findVasTypeInfoByBatchIdList(subBatchList.stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(vasTypeInfoByBatchIdList)) {
            return Collections.emptyList();
        }
        return vasTypeInfoByBatchIdList.stream().filter(detail -> StringUtils.hasText(detail.getVasType()) || 1 == detail.getIsFirstOrderByStore()).map(StockoutVasTypeValidDTO::getStockoutOrderId).collect(Collectors.toList());
    }

    /**
     * 获取波次对应已拣货数
     *
     * @param batchIdList
     * @return
     */
    public List<StockoutOrderBatchPickQtyResponse> getBatchPickQty(List<Integer> batchIdList) {
        if (CollectionUtils.isEmpty(batchIdList)) {
            return Collections.emptyList();
        }
        return this.getBaseMapper().getBatchPickQty(batchIdList);
    }
}
