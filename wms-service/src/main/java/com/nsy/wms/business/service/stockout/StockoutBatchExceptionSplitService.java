package com.nsy.wms.business.service.stockout;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stockout.StockoutBatchScanTypeInfo;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskStatus;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutBatchExceptionSplitScanRequest;
import com.nsy.api.wms.response.stockout.StockoutBatchExceptionSplitInfoItemResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchExceptionSplitInfoResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBatchSplitTaskItemMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class StockoutBatchExceptionSplitService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutBatchExceptionSplitService.class);
    @Autowired
    StockoutBatchSplitTaskItemMapper splitTaskItemMapper;
    @Autowired
    StockoutBatchSplitTaskItemService splitTaskItemService;
    @Autowired
    StockoutBatchService batchService;
    @Autowired
    StockoutBatchSplitTaskService splitTaskService;
    @Autowired
    WcsBatchSplitService wcsBatchSplitService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutBatchOrderItemService stockoutBatchOrderItemService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutBatchSplitLogService batchSplitLogService;
    @Autowired
    AutoMachineSortService autoMachineSortService;
    @Autowired
    StockoutBatchScanTypeService batchScanTypeService;


    /**
     * 扫描波次号
     * <p>
     * 1.未设置分拣口，设置分拣口
     * 2.更新分拣任务状态分拣中
     *
     * @param batchId
     * @return
     */
    @Transactional
    public StockoutBatchExceptionSplitInfoResponse getExceptionSplitInfo(Integer batchId) {
        StockoutBatchSplitTaskEntity splitTask = autoMachineSortService.findLastTask(batchId);
        validSplitTask(splitTask);

        List<StockoutBatchSplitTaskItemEntity> splitItemList = splitTaskItemService.getByTaskIdOrderByTaskId(splitTask.getTaskId());
        if (Objects.isNull(splitItemList.get(0).getOutlet())) {
            throw new BusinessServiceException(String.format("未设置分拣口 %s", batchId));
        }
        //更新分拣任务状态分拣中
        if (StockoutBatchSplitTaskStatus.WAIT_SORT.name().equals(splitTask.getStatus())
                || StockoutBatchSplitTaskStatus.SUSPEND_SORT.name().equals(splitTask.getStatus())) {
            updateSplitTaskAndBatchSorting(splitTask.getTaskId());
        }
        //组装返回数据
        List<StockoutBatchExceptionSplitInfoItemResponse> exceptionSplitItemList = splitTaskItemMapper.findExceptionSplitItemList(splitTask.getTaskId());
        AtomicInteger totalExpectedQty = new AtomicInteger();
        AtomicInteger totalScanQty = new AtomicInteger();
        exceptionSplitItemList.forEach(splitItem -> {
            splitItem.setIsTick(Objects.equals(splitItem.getExpectedQty(), splitItem.getScanQty()));
            totalExpectedQty.addAndGet(splitItem.getExpectedQty());
            totalScanQty.addAndGet(splitItem.getScanQty());
        });

        StockoutBatchExceptionSplitInfoResponse response = new StockoutBatchExceptionSplitInfoResponse();
        response.setBatchId(splitTask.getBatchId());
        response.setTotalExpectedQty(totalExpectedQty.get());
        response.setTotalScanQty(totalScanQty.get());
        Map<Boolean, List<StockoutBatchExceptionSplitInfoItemResponse>> itemMap = exceptionSplitItemList.stream().collect(Collectors.groupingBy(item -> item.getExpectedQty().equals(item.getScanQty())));
        List<StockoutBatchExceptionSplitInfoItemResponse> itemList = new ArrayList<>(exceptionSplitItemList.size());
        itemList.addAll(itemMap.getOrDefault(Boolean.FALSE, new ArrayList<>()).stream().sorted(Comparator.comparing(StockoutBatchExceptionSplitInfoItemResponse::getOutlet)).collect(Collectors.toList()));
        itemList.addAll(itemMap.getOrDefault(Boolean.TRUE, new ArrayList<>()));
        response.setItemList(itemList);
        return response;
    }


    /**
     * 扫描明细
     *
     * @param request
     */
    @Transactional
    public void scanSplitItem(StockoutBatchExceptionSplitScanRequest request) {
        StockoutBatchSplitTaskItemEntity splitTaskItem = splitTaskItemService.getById(request.getTaskItemId());
        if (Objects.isNull(splitTaskItem))
            throw new BusinessServiceException("找不到分拣任务明细信息");
        StockoutBatchSplitTaskEntity splitTask = splitTaskService.getBatchSplitTaskById(splitTaskItem.getTaskId());
        if (!splitTask.getIsException())
            throw new BusinessServiceException(String.format("任务【%s】，非【异常分拣】任务", splitTask.getTaskId()));
        StockoutBatchEntity batchEntity = batchService.getStockoutBatchById(splitTask.getBatchId());

        if (StockoutBatchSplitTaskStatus.WAIT_SORT.name().equals(splitTask.getStatus())) {
            updateSplitTaskAndBatchSorting(splitTask.getTaskId());
        }
        Integer originScanQty = splitTaskItem.getScanQty();
        LOGGER.info("异常分拣扫描明细,taskItemId:【 {} 】，originScanQty:【 {} 】,scanQty:【 {} 】", request.getTaskItemId(), originScanQty, request.getQty());
        //更新分拣明细
        updateSplitTaskItem(splitTaskItem.getTaskItemId(), request.getQty());
        //正常波次
        if (0 == batchEntity.getIsMergeBatch()) {
            StockoutBatchScanTypeInfo scanTypeInfo = batchScanTypeService.getScanTypeInfo(batchEntity);
            if (scanTypeInfo.getCreateCheckTask()) return;  //存在复核任务就跳过
            //更改波次单状态，分拣中
            updateBatchOrderSorting(batchEntity.getBatchId(), splitTaskItem.getStockoutOrderNo());
            // 扣减库存：sku扫描拣货箱数,拣货箱库存减，发货库位增加,异常库位变动;
//            splitTaskItemService.scanUpdatePickingBoxQtyAndDeliverPositionQty(batchEntity.getBatchId(), splitTaskItem.getTaskItemId(), originScanQty, request.getQty());
            int totalScanQty = splitTaskItemMapper.sumTotalScanQty(batchEntity.getBatchId(), splitTaskItem.getStockoutOrderItemId());
            // 更新出库单明细扫描数
            stockoutOrderItemService.updateStockoutItemScanQty(batchEntity, splitTaskItem.getStockoutOrderItemId(), totalScanQty);
            // 更新波次单明细扫描数
            stockoutBatchOrderItemService.updateStockoutBatchOrderItemScanQty(batchEntity.getBatchId(), splitTaskItem.getStockoutOrderNo(), splitTaskItem.getStockoutOrderItemId(), totalScanQty);
        }

        //前分拣任务下所有SKU拣货数-已分拣数=0
        if (checkAllSplitItemFinish(splitTaskItem.getTaskId())) {
            scanFinish(splitTask.getBatchId());
            // 正常波次，修改波次状态 出库单分拣完成
            autoMachineSortService.finishBatch(splitTask.getBatchId(), splitTask);
        }
    }

    /**
     * 扫描完成
     *
     * @return
     */
    @Transactional
    public void scanFinish(Integer batchId) {
        StockoutBatchSplitTaskEntity splitTask = autoMachineSortService.findLastTask(batchId);
        if (!splitTask.getIsException())
            throw new BusinessServiceException(String.format("任务【%s】，非【异常分拣】任务", splitTask.getTaskId()));
        //完成分拣任务
        autoMachineSortService.finishSplitTask(splitTask, StockoutBatchSplitTaskTypeEnum.EXCEPTION_SORT);
    }

    /**
     * 更改分拣任务 波次状态  =》 分拣中
     */
    private void updateSplitTaskAndBatchSorting(Integer splitTaskId) {
        StockoutBatchSplitTaskEntity splitTask = splitTaskService.getBatchSplitTaskById(splitTaskId);
        //更改分家你任务状态
        StockoutBatchSplitTaskEntity updateSplitTask = new StockoutBatchSplitTaskEntity();
        updateSplitTask.setTaskId(splitTask.getTaskId());
        if (StockoutBatchSplitTaskStatus.WAIT_SORT.name().equals(splitTask.getStatus())) {
            updateSplitTask.setOperator(loginInfoService.getName());
            updateSplitTask.setOperateStartDate(new Date());
        }
        if (StockoutBatchSplitTaskStatus.WAIT_SORT.name().equals(splitTask.getStatus())
                || StockoutBatchSplitTaskStatus.SUSPEND_SORT.name().equals(splitTask.getStatus())) {
            updateSplitTask.setStatus(StockoutBatchSplitTaskStatus.SORTING.name());
        }
        updateSplitTask.setUpdateBy(loginInfoService.getName());
        splitTaskService.updateById(updateSplitTask);
        //更改波次状态
        StockoutBatchEntity batch = batchService.getStockoutBatchById(splitTask.getBatchId());
        if (StockoutWaveTaskStatusEnum.WAIT_SORT.name().equals(batch.getStatus())) {
            StockoutBatchEntity updateBatch = new StockoutBatchEntity();
            updateBatch.setBatchId(batch.getBatchId());
            updateBatch.setStatus(StockoutWaveTaskStatusEnum.SORTING.name());
            updateBatch.setUpdateBy(loginInfoService.getName());
            batchService.updateById(updateBatch);
        }
    }

    /**
     * 波次出库单 =》 分拣中
     */
    private void updateBatchOrderSorting(Integer batchId, String stockoutOrderNo) {
        StockoutBatchOrderEntity stockoutBatchOrderEntity = stockoutBatchOrderService.getBatchOrderByBatchIdAndStockoutOrderNo(batchId, stockoutOrderNo);
        if (StockoutWaveTaskStatusEnum.WAIT_SORT.name().equals(stockoutBatchOrderEntity.getStatus())) {
            stockoutBatchOrderService.updateStockoutBatchOrderStatus(stockoutBatchOrderEntity.getBatchOrderId(), StockoutWaveTaskStatusEnum.SORTING.name());
        }
    }

    /**
     * 验证分拣任务是否正确
     *
     * @param splitTask
     */
    private void validSplitTask(StockoutBatchSplitTaskEntity splitTask) {
        if (!StockoutBatchSplitTaskStatus.WAIT_SORT.name().equals(splitTask.getStatus())
                && !StockoutBatchSplitTaskStatus.SORTING.name().equals(splitTask.getStatus())
                && !StockoutBatchSplitTaskStatus.SUSPEND_SORT.name().equals(splitTask.getStatus())) {
            throw new BusinessServiceException(String.format("此波次分拣任务【%s】非【待分拣】【分拣中】【暂停分拣】", splitTask.getTaskId()));
        }

        if (!StockoutSortingTypeEnum.AUTO_MACHINE_SORT.name().equals(splitTask.getBatchSplitType())) {
            throw new BusinessServiceException(String.format("该分拣任务【%s】非【窄带分拣】", splitTask.getTaskId()));
        }
        if (!splitTask.getIsException())
            throw new BusinessServiceException(String.format("任务【%s】，非【异常分拣】任务", splitTask.getTaskId()));
    }


    /**
     * 当前分拣任务下所有SKU拣货数-已分拣数=0
     *
     * @param splitTaskId
     * @return
     */
    private Boolean checkAllSplitItemFinish(Integer splitTaskId) {
        List<StockoutBatchSplitTaskItemEntity> splitTaskItemList = splitTaskItemService.getByTaskIdOrderByTaskId(splitTaskId);
        return splitTaskItemList.stream().allMatch(item -> item.getExpectedQty().equals(item.getScanQty()));
    }

    /**
     * 更新分拣明细
     *
     * @param taskItemId
     * @param qty
     */
    private void updateSplitTaskItem(Integer taskItemId, Integer qty) {
        StockoutBatchSplitTaskItemEntity splitTaskItem = splitTaskItemService.getById(taskItemId);
        Integer scanQty = splitTaskItem.getScanQty();
        if (qty <= scanQty)
            throw new BusinessServiceException(String.format("已分拣：%s 件，再次输入分拣数需大于 %s", scanQty, scanQty));
        if (qty > splitTaskItem.getExpectedQty())
            throw new BusinessServiceException(String.format("待分拣：%s 件，输入分拣数需小于 %s", splitTaskItem.getExpectedQty(), splitTaskItem.getExpectedQty()));
        splitTaskItem.setScanQty(qty);
        splitTaskItem.setUpdateBy(loginInfoService.getName());
        splitTaskItemService.updateById(splitTaskItem);
        splitTaskService.addSplitTaskLog(splitTaskItem.getTaskId(), StockoutBatchSplitTaskTypeEnum.EXCEPTION_SORT.getName(), String.format("【%s】分拣【%s】件", splitTaskItem.getSku(), qty - scanQty));
    }

}
