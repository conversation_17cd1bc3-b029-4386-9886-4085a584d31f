package com.nsy.wms.business.domain.bo.stockout;


/**
 * 扣减拣货箱库存,扣减拣货箱明细数量; 若是开始分拣，变更状态为分拣中；若是扣减数量完，变更状态为空箱
 */
public class StockoutBatchSplitTaskMinusPickingStockBo {
    //波次id
    Integer batchId;
    //出库单id
    Integer stockoutOrderId;
    //sku
    Integer specId;
    //箱内总数
    Integer totalQty;
    //是否加工
    Integer isProcess;
    //发货库位数
    Integer deliveryQty;
    //出库单号
    String stockoutOrderNo;

    Integer stockoutOrderItemId;


    public StockoutBatchSplitTaskMinusPickingStockBo() {

    }

    private StockoutBatchSplitTaskMinusPickingStockBo(Builder builder) {
        this.batchId = builder.batchId;
        this.stockoutOrderId = builder.stockoutOrderId;
        this.specId = builder.specId;
        this.totalQty = builder.totalQty;
        this.isProcess = builder.isProcess;
        this.deliveryQty = builder.deliveryQty;
        this.stockoutOrderNo = builder.stockoutOrderNo;
        this.stockoutOrderItemId = builder.stockoutOrderItemId;
    }

    public Integer getStockoutOrderItemId() {
        return stockoutOrderItemId;
    }

    public void setStockoutOrderItemId(Integer stockoutOrderItemId) {
        this.stockoutOrderItemId = stockoutOrderItemId;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }

    public Integer getStockoutOrderId() {
        return stockoutOrderId;
    }

    public void setStockoutOrderId(Integer stockoutOrderId) {
        this.stockoutOrderId = stockoutOrderId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public Integer getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(Integer totalQty) {
        this.totalQty = totalQty;
    }

    public Integer getIsProcess() {
        return isProcess;
    }

    public void setIsProcess(Integer isProcess) {
        this.isProcess = isProcess;
    }

    public Integer getDeliveryQty() {
        return deliveryQty;
    }

    public void setDeliveryQty(Integer deliveryQty) {
        this.deliveryQty = deliveryQty;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public static class Builder {
        Integer batchId;
        //出库单id
        Integer stockoutOrderId;
        //sku
        Integer specId;
        //箱内总数
        Integer totalQty;
        //是否加工
        Integer isProcess;
        //发货库位数
        Integer deliveryQty;
        //出库单号
        String stockoutOrderNo;

        Integer stockoutOrderItemId;

        public Builder setStockoutOrderItemId(Integer stockoutOrderItemId) {
            this.stockoutOrderItemId = stockoutOrderItemId;
            return this;
        }

        public Builder setBatchId(Integer batchId) {
            this.batchId = batchId;
            return this;
        }

        public Builder setStockoutOrderId(Integer stockoutOrderId) {
            this.stockoutOrderId = stockoutOrderId;
            return this;
        }

        public Builder setSpecId(Integer specId) {
            this.specId = specId;
            return this;
        }

        public Builder setTotalQty(Integer totalQty) {
            this.totalQty = totalQty;
            return this;
        }

        public Builder setIsProcess(Integer isProcess) {
            this.isProcess = isProcess;
            return this;
        }

        public Builder setDeliveryQty(Integer deliveryQty) {
            this.deliveryQty = deliveryQty;
            return this;
        }

        public Builder setStockoutOrderNo(String stockoutOrderNo) {
            this.stockoutOrderNo = stockoutOrderNo;
            return this;
        }

        public StockoutBatchSplitTaskMinusPickingStockBo build() {
            return new StockoutBatchSplitTaskMinusPickingStockBo(this);
        }
    }
}
