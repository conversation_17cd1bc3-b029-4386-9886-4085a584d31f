package com.nsy.wms.business.service.stockin.valid;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Sets;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockin.StockinOrderItemSpotSet;
import com.nsy.api.wms.domain.stockin.StockinOrderTaskItemDetail;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxMaterialSizeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.request.stockin.StockinOrderItemSetRequest;
import com.nsy.api.wms.request.stockin.StockinOrderItemSpotSetRequest;
import com.nsy.api.wms.request.stockin.StockinOrderTaskCompleteRequest;
import com.nsy.api.wms.request.stockin.StockinOrderTaskSpotCompleteRequest;
import com.nsy.wms.business.service.qa.StockinQaTaskSearchService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.Validator;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Component
public class StockinOrderTaskValid {

    private final Set<String> internalBoxStatus = Sets.newHashSet(StockInternalBoxStatusEnum.EMPTY.name(),
            StockInternalBoxStatusEnum.PACKING.name(), StockInternalBoxStatusEnum.WAIT_QC.name(),
            StockInternalBoxStatusEnum.WAIT_SHELVE.name());

    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockinQaTaskSearchService stockinQaTaskSearchService;

    public static void validateStockinOrderItemSetRequest(StockinOrderItemSetRequest request) {
        Validator.isValid(request.getInternalBoxCode(), StringUtils::hasText, "内部箱号不能为空");
        Validator.isValid(request.getBarcode(), StringUtils::hasText, "商品条形码不能为空");
        Validator.isValid(request.getQty(), Objects::nonNull, "数量不能为空");
    }

    public static void validateStockinOrderItemSpotSetRequest(StockinOrderItemSpotSetRequest request) {
        Validator.isValid(request.getInternalBoxCode(), StringUtils::hasText, "内部箱号不能为空");
        Validator.isValid(request.getSku(), StringUtils::hasText, "sku不能为空");
        Validator.isValid(request.getQty(), Objects::nonNull, "数量不能为空");
    }

    public static void validateStockinOrderTaskCompleteRequest(StockinOrderTaskCompleteRequest request) {
        Validator.isValid(request.getTaskId(), Objects::nonNull, "入库任务id不能为空");
    }

    public static void validateStockinOrderTaskSpotCompleteRequest(StockinOrderTaskSpotCompleteRequest request) {
        Validator.isValid(request.getTaskId(), Objects::nonNull, "入库任务id不能为空");
        List<StockinOrderItemSpotSet> stockinOrderItemSpotSetList = request.getStockinOrderItemSpotSetList();
        Validator.isValid(stockinOrderItemSpotSetList, sets3 -> !CollectionUtils.isEmpty(sets3), "SKU装箱信息不能为空");
    }

    public static void validateStockinOrderTaskItemDetail(StockinOrderTaskItemDetail request) {
        Validator.isValid(request.getInternalBoxCode(), StringUtils::hasText, "内部箱号不能为空");
        Validator.isValid(request.getSku(), StringUtils::hasText, "sku不能为空");
        Validator.isValid(request.getQty(), Objects::nonNull, "数量不能为空");
    }

    ////校验箱内的商品，不允许调拨入库和正常入库混在一个箱子
    public void validateInternalBox(StockinOrderTaskEntity taskEntity, String internalBoxCode, String sku) {

        //循环箱，收货箱是质检中及后续状态，不允许再往箱子里装商品
        stockInternalBoxService.updateInternalBoxStatusByItem(internalBoxCode);
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.findByInternalBoxCode(internalBoxCode);
        if (StockInternalBoxMaterialSizeEnum.LOOP_BOX.name().equals(stockInternalBoxEntity.getInternalBoxMaterialSize())
                && !internalBoxStatus.contains(stockInternalBoxEntity.getStatus()))
            throw new BusinessServiceException(String.format("箱子状态为%s，不允许再收货！",
                    StockInternalBoxStatusEnum.valueOf(stockInternalBoxEntity.getStatus()).getStatus()));


        LambdaQueryWrapper<StockInternalBoxItemEntity> taskItemQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode)
                .gt(StockInternalBoxItemEntity::getQty, 0).last("limit 1");
        StockInternalBoxItemEntity one = stockInternalBoxItemService.getOne(taskItemQueryWrapper);
        if (Objects.isNull(one))
            return;

        if (LocationEnum.MISI.name().equals(TenantContext.getTenant())) {
            if (stockInternalBoxItemService.validSameSupplierDeliveryNo(internalBoxCode, sku, taskEntity.getSupplierDeliveryNo())) {
                throw new BusinessServiceException("同一个内部箱内已存在不同工厂出库单的相同货物,不允许装入!");
            }
            LambdaQueryWrapper<StockInternalBoxItemEntity> itemQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                    .eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode)
                    .gt(StockInternalBoxItemEntity::getQty, 0);
            List<StockInternalBoxItemEntity> itemList = stockInternalBoxItemService.list(itemQueryWrapper);
            for (StockInternalBoxItemEntity item : itemList) {
                if (stockinQaTaskSearchService.getCountByInfo(item.getInternalBoxCode(), item.getSku()) > 0) {
                    throw new BusinessServiceException("已生成质检任务不允许继续装箱");
                }
            }
        }


        StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(one.getStockInOrderNo());
        if ((StockinTypeEnum.CUSTOM.name().equals(taskEntity.getStockinType()) || StockinTypeEnum.FACTORY.name().equals(taskEntity.getStockinType()))
                && (StockinTypeEnum.CUSTOM.name().equals(stockinOrderEntity.getStockinType()) || StockinTypeEnum.FACTORY.name().equals(stockinOrderEntity.getStockinType()))) {
            return;
        }
        if (!stockinOrderEntity.getStockinType().equals(taskEntity.getStockinType())) {
            throw new BusinessServiceException(String.format("箱中已存在入库类型为【%s】任务的商品，无法在入库【%s】的任务",
                    enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TYPE.getName(), stockinOrderEntity.getStockinType()),
                    enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TYPE.getName(), taskEntity.getStockinType())));
        }


    }

    //校验箱内的商品，不允许同个sku跨工厂收货
    public void validateInternalBoxSku(StockinOrderEntity stockinOrderEntity, Integer productId, String internalBoxCode) {
        List<Integer> supplierIds = stockInternalBoxItemService.getBaseMapper().findSupplierByBoxCodeAndProductId(internalBoxCode, productId);
        if (CollectionUtils.isEmpty(supplierIds)) {
            return;
        }
        if (supplierIds.size() == 1 && supplierIds.get(0).equals(stockinOrderEntity.getSupplierId())) {
            return;
        } else {
            throw new BusinessServiceException("箱内已存在其他工厂的相同spu，无法混工厂收货");
        }
    }
}
