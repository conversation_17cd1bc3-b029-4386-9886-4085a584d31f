package com.nsy.wms.business.service.product;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.pms.dto.product.ProductLabelDTO;
import com.nsy.api.wms.constants.CopyIgnoreFieldConstant;
import com.nsy.api.wms.domain.product.NsyProductProductLabel;
import com.nsy.wms.business.manage.pms.PmsApiService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.mq.TableListenerMessageType;
import com.nsy.wms.repository.entity.product.ProductLabelEntity;
import com.nsy.wms.repository.jpa.mapper.product.ProductLabelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Service
public class ProductLabelService extends ServiceImpl<ProductLabelMapper, ProductLabelEntity> {
    @Autowired
    PmsApiService pmsApiService;
    @Autowired
    LoginInfoService loginInfoService;

    @Transactional
    public void syncProductLabel(TableListenerMessageType type, NsyProductProductLabel message) {
        switch (type) {
            case DELETE:
                this.removeById(message.getId());
                break;
            case INSERT:
            case UPDATE:
                this.updateProductLabel(message);
                break;
            default:
                return;
        }
    }

    private void updateProductLabel(NsyProductProductLabel message) {
        ProductLabelDTO productLabelDTO = pmsApiService.getProductLabelById(message.getId());
        if (Objects.isNull(productLabelDTO)) {
            return;
        }
        ProductLabelEntity productLabelEntity = new ProductLabelEntity();
        BeanUtils.copyProperties(message, productLabelEntity, CopyIgnoreFieldConstant.CREATE_DATE, CopyIgnoreFieldConstant.UPDATE_DATE);
        productLabelEntity.setUpdateBy(loginInfoService.getName());
        productLabelEntity.setSpu(message.getProductSku());
        productLabelEntity.setId(message.getId());
        this.saveOrUpdate(productLabelEntity);
    }
}
