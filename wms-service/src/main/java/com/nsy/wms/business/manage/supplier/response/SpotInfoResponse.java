package com.nsy.wms.business.manage.supplier.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

public class SpotInfoResponse {
    private String purchasePlanNo;
    private String sku;
    private String remark;
    private int purchaseQty;
    private String purchaseUserName;
    private String purchaserRealName;
    private String spaceName;
    private Integer spaceId;
    private String logisticsNo;
    // 工厂发货时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date deliveryDate;
    // 计划箱数
    private Integer planBoxNum;
    // 是否质检
    private Integer isNeedQa;

    // 买手备注
    private String buyerRemark;

    public String getBuyerRemark() {
        return buyerRemark;
    }

    public void setBuyerRemark(String buyerRemark) {
        this.buyerRemark = buyerRemark;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Integer getPlanBoxNum() {
        return planBoxNum;
    }

    public void setPlanBoxNum(Integer planBoxNum) {
        this.planBoxNum = planBoxNum;
    }

    public String getPurchasePlanNo() {
        return purchasePlanNo;
    }

    public void setPurchasePlanNo(String purchasePlanNo) {
        this.purchasePlanNo = purchasePlanNo;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getRemark() {
        return remark;
    }

    public Integer getIsNeedQa() {
        return isNeedQa;
    }

    public void setIsNeedQa(Integer isNeedQa) {
        this.isNeedQa = isNeedQa;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public int getPurchaseQty() {
        return purchaseQty;
    }

    public void setPurchaseQty(int purchaseQty) {
        this.purchaseQty = purchaseQty;
    }

    public String getPurchaseUserName() {
        return purchaseUserName;
    }

    public void setPurchaseUserName(String purchaseUserName) {
        this.purchaseUserName = purchaseUserName;
    }

    public String getPurchaserRealName() {
        return purchaserRealName;
    }

    public void setPurchaserRealName(String purchaserRealName) {
        this.purchaserRealName = purchaserRealName;
    }
}
