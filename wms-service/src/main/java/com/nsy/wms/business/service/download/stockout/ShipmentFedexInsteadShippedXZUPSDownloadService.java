package com.nsy.wms.business.service.download.stockout;

import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockout.StockoutShipmentDownloadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ShipmentFedexInsteadShippedXZUPSDownloadService implements IDownloadService {

    @Autowired
    private StockoutShipmentDownloadService downloadService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_SHIPMENT_FEDEX_INSTEAD_SHIPPED_XZUPS;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        return downloadService.shipByOthersXZUPS(request);
    }
}
