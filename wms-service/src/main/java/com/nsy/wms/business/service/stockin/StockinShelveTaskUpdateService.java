package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.domain.stockin.StockinShelveSyncInfo;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemInfo;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskTypeEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockin.UpShelveRequest;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.query.BdPositionQueryWrapper;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class StockinShelveTaskUpdateService {

    @Autowired
    ShelvePdaService shelvePdaService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    private StockService stockService;
    @Autowired
    private BdPositionService bdPositionService;
    @Autowired
    private StockinOrderService stockinOrderService;
    @Autowired
    StockinShelveTaskService stockinShelveTaskService;

    // 上架更新stock库存中库位库存
    @Transactional
    public void shevleUpdatePositionStock(UpShelveRequest request, List<StockinShelveTaskItemInfo> taskItemInfoList) {
        // 检查库位是否被锁定
        StockEntity stockEntity = stockService.getSkuPositionStock(request.getSku(), request.getPosition());
        if (stockEntity != null && stockEntity.getLock()) {
            throw new BusinessServiceException("该库位已被锁定，无法上架");
        }

        if (!CollectionUtils.isEmpty(taskItemInfoList) && StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(taskItemInfoList.get(0).getTaskType())) {
            int totalQty = request.getQty();
            List<Integer> stockinOrderIdList = taskItemInfoList.stream().map(StockinShelveTaskItemInfo::getSourceId).distinct().collect(Collectors.toList());
            List<StockinOrderEntity> stockinOrderList = stockinOrderService.listByIds(stockinOrderIdList);
            if (CollectionUtils.isEmpty(stockinOrderList))
                throw new BusinessServiceException("找不到出库单信息");
            for (StockinShelveTaskItemInfo item : taskItemInfoList) {
                int pendingQty = item.getStockinQty() - item.getShelvedQty() - item.getReturnedQty();
                StockUpdateRequest positionStockUpdateRequest = new StockUpdateRequest();
                positionStockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.NORMAL_SHELVE);
                positionStockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
                positionStockUpdateRequest.setSku(request.getSku());
                StockinOrderEntity filterStockinOrder = stockinOrderList.stream().filter(order -> order.getStockinOrderId().equals(item.getSourceId())).collect(Collectors.toList())
                        .stream().findFirst().orElse(null);
                positionStockUpdateRequest.setStockInOrderNo(filterStockinOrder != null ? filterStockinOrder.getStockinOrderNo() : null);
                positionStockUpdateRequest.setPurchasePlanNo(item.getPurchasePlanNo());
                positionStockUpdateRequest.setPositionCode(request.getPosition());
                if (pendingQty >= totalQty) {
                    positionStockUpdateRequest.setQty(totalQty);
                    stockService.updateStock(positionStockUpdateRequest);
                    break;
                } else {
                    totalQty = totalQty - pendingQty;
                    positionStockUpdateRequest.setQty(pendingQty);
                    stockService.updateStock(positionStockUpdateRequest);
                }
            }
        } else {
            StockUpdateRequest positionStockUpdateRequest = new StockUpdateRequest();
            positionStockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.NORMAL_SHELVE);
            positionStockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
            positionStockUpdateRequest.setQty(request.getQty());
            positionStockUpdateRequest.setSku(request.getSku());
            positionStockUpdateRequest.setPositionCode(request.getPosition());
            stockService.updateStock(positionStockUpdateRequest);
        }
    }

    // 上架更新stock库存中库位库存
    @Transactional
    public void shevleUpdatePositionStock(StockinShelveSyncInfo syncInfo, UpShelveRequest request, List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList) {
        // 检查库位是否被锁定
        StockEntity stockEntity = stockService.getSkuPositionStock(request.getSku(), request.getPosition());
        if (stockEntity != null && stockEntity.getLock()) {
            throw new BusinessServiceException("该库位已被锁定，无法上架");
        }

        StockinShelveTaskEntity stockinShelveTaskEntity = stockinShelveTaskService.getByInternalBoxCode(request.getInternalBoxCode());
        if (!CollectionUtils.isEmpty(syncInfo.getInfoItemList()) && StockinShelveTaskTypeEnum.STOCKIN_SHELVE.name().equals(stockinShelveTaskEntity.getTaskType())) {
            List<Integer> stockinOrderIdList = syncInfo.getInfoItemList().stream().map(StockinShelveSyncInfo.SyncInfoItem::getSourceId).distinct().collect(Collectors.toList());
            List<StockinOrderEntity> stockinOrderList = stockinOrderService.listByIds(stockinOrderIdList);
            for (StockinShelveSyncInfo.SyncInfoItem item : syncInfo.getInfoItemList()) {
                StockUpdateRequest positionStockUpdateRequest = new StockUpdateRequest();
                positionStockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.NORMAL_SHELVE);
                positionStockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
                positionStockUpdateRequest.setSku(request.getSku());
                StockinOrderEntity filterStockinOrder = stockinOrderList.stream().filter(order -> order.getStockinOrderId().equals(item.getSourceId())).collect(Collectors.toList())
                        .stream().findFirst().orElseThrow(() -> new BusinessServiceException("找不到出库单信息"));
                positionStockUpdateRequest.setStockInOrderNo(filterStockinOrder != null ? filterStockinOrder.getStockinOrderNo() : null);
                positionStockUpdateRequest.setPurchasePlanNo(item.getPurchasePlanNo());
                positionStockUpdateRequest.setPositionCode(request.getPosition());
                positionStockUpdateRequest.setQty(item.getShelvedQty());
                stockService.updateStock(positionStockUpdateRequest);
                Optional<StockInternalBoxItemEntity> any = stockInternalBoxItemEntityList.stream().filter(boxItemEntity ->
                        boxItemEntity.getStockInOrderNo().equals(filterStockinOrder.getStockinOrderNo())
                                && boxItemEntity.getPurchasePlanNo().equals(item.getPurchasePlanNo())
                                && !StockinOrderItemStatusEnum.RETURNED.name().equals(boxItemEntity.getStatus())).findAny();
                //扣减内部箱数量
                this.shelveUpdateBoxItem(any, item, request);
            }
        } else {
            StockUpdateRequest positionStockUpdateRequest = new StockUpdateRequest();
            positionStockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.NORMAL_SHELVE);
            positionStockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.SHELVE);
            positionStockUpdateRequest.setQty(request.getQty());
            positionStockUpdateRequest.setSku(request.getSku());
            positionStockUpdateRequest.setPositionCode(request.getPosition());
            stockService.updateStock(positionStockUpdateRequest);
            //扣减内部箱数量
            Integer boxItemTotalQty = stockInternalBoxItemEntityList.stream().filter(item -> item.getQty() != null).mapToInt(StockInternalBoxItemEntity::getQty).sum();
            if (request.getQty() > boxItemTotalQty) {
                int exceptionQty = request.getQty() - boxItemTotalQty;
                stockService.updateStock(shelvePdaService.buildUpdateExceptionStock(buildExceptionStockUpdateRequest(request), -exceptionQty)); // 3.更新stock异常库位库存
            }
            int shelveQty = request.getQty() < boxItemTotalQty ? request.getQty() : boxItemTotalQty;
            stockInternalBoxItemService.minusStockInternalBoxItemQty(stockInternalBoxItemEntityList, shelveQty, StockChangeLogTypeEnum.NORMAL_SHELVE, StockChangeLogTypeModuleEnum.SHELVE, null);
        }
    }

    private void shelveUpdateBoxItem(Optional<StockInternalBoxItemEntity> any, StockinShelveSyncInfo.SyncInfoItem item, UpShelveRequest request) {
        if (!any.isPresent() || any.get().getQty() < item.getShelvedQty()) {
            int boxItemQty = any.isPresent() ? any.get().getQty() : 0;
            int exceptionQty = item.getShelvedQty() - boxItemQty;
            stockService.updateStock(shelvePdaService.buildUpdateExceptionStock(buildExceptionStockUpdateRequest(request), -exceptionQty));
            if (boxItemQty > 0)
                stockInternalBoxItemService.minusStockInternalBoxItemQty(any.get(), boxItemQty, StockChangeLogTypeEnum.NORMAL_SHELVE, StockChangeLogTypeModuleEnum.SHELVE, null);
        } else {
            stockInternalBoxItemService.minusStockInternalBoxItemQty(any.get(), item.getShelvedQty(), StockChangeLogTypeEnum.NORMAL_SHELVE, StockChangeLogTypeModuleEnum.SHELVE, null);
        }
    }

    UpShelveRequest buildExceptionStockUpdateRequest(UpShelveRequest request) {
        LambdaQueryWrapper<BdPositionEntity> queryWrapperBdPosition1 = BdPositionQueryWrapper.buildBdPositionByPositionCode(request.getPosition());
        BdPositionEntity bdPositionEntity = bdPositionService.getOne(queryWrapperBdPosition1);
        if (bdPositionEntity == null) throw new BusinessServiceException("找不到库位");
        LambdaQueryWrapper<BdPositionEntity> queryWrapperBdPosition2 = BdPositionQueryWrapper.buildBdPositionBySpaceIdAndPositionTypeAndIsDeleted(bdPositionEntity.getSpaceId(), BdPositionTypeEnum.EXCEPTION_POSITION.name(), IsDeletedConstant.NOT_DELETED);
        List<BdPositionEntity> positionEntityList = bdPositionService.list(queryWrapperBdPosition2);
        if (CollectionUtils.isEmpty(positionEntityList)) throw new BusinessServiceException("找不到异常库位");
        UpShelveRequest exceptionStockUpdateRequest = new UpShelveRequest();
        exceptionStockUpdateRequest.setInternalBoxCode(request.getInternalBoxCode());
        exceptionStockUpdateRequest.setPosition(positionEntityList.get(0).getPositionCode());
        exceptionStockUpdateRequest.setSku(request.getSku());
        return exceptionStockUpdateRequest;
    }
}
