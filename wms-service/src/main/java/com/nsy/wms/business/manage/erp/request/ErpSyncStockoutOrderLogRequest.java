package com.nsy.wms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

public class ErpSyncStockoutOrderLogRequest {
    @JsonProperty("Tid")
    private String orderNo;

    @JsonProperty("Operator")
    private String operator;

    @JsonProperty("Content")
    private String content;

    @JsonProperty("IpAddress")
    private String ipAddress;

    @JsonProperty("OperateDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateDate;

    public ErpSyncStockoutOrderLogRequest(String orderNo, String content, String operator, String ipAddress, Date operateDate) {
        this.orderNo = orderNo;
        this.operator = operator;
        this.content = content;
        this.ipAddress = ipAddress;
        this.operateDate = operateDate;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
