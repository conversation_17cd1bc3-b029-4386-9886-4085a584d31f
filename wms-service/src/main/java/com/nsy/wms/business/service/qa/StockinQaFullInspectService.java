package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.qa.QaLogTypeEnum;
import com.nsy.api.wms.enumeration.qa.QaProcessEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaInspectStatusEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderInspectResultEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderProcessStatusEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderStatusEnum;
import com.nsy.api.wms.request.bd.DateRangeRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.qa.StockinQaFullInspectReportPageRequest;
import com.nsy.api.wms.request.qa.StockinQaFullInspectTaskPageRequest;
import com.nsy.api.wms.request.qa.StockinQaInspectDetailRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateFullInspectRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateRequest;
import com.nsy.api.wms.request.qa.StockinQaOperateUnqualifiedRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectReportPageResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectResultDetailResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectTaskPageResponse;
import com.nsy.api.wms.response.qa.StockinQaInspectCountQtyResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.business.service.stockin.StockinQcService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderDetailEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderImgEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderItemEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderProcessEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderSkuTypeInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderUnqualifiedEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.WmsDateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-05 9:16
 */
@Service
public class StockinQaFullInspectService implements IDownloadService {

    @Autowired
    private StockinQaOrderService stockinQaOrderService;
    @Autowired
    private StockinQaOrderOperateService stockinQaOrderOperateService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockinQaOrderLogService stockinQaOrderLogService;
    @Autowired
    private StockinQaOrderProcessService stockinQaOrderProcessService;
    @Autowired
    private StockinQaOrderDetailService stockinQaOrderDetailService;
    @Autowired
    private StockinQaOrderUnqualifiedService stockinQaOrderUnqualifiedService;
    @Autowired
    private StockinQaOrderImgService qaOrderImgService;
    @Autowired
    private StockinQcService stockinQcService;
    @Autowired
    private BdQaInspectRuleService bdQaInspectRuleService;
    @Autowired
    private StockinQaInspectService stockinQaInspectService;
    @Autowired
    private StockinQaTaskService stockinQaTaskService;
    @Autowired
    private StockinQaOrderItemService stockinQaOrderItemService;
    @Autowired
    private StockinQaOrderSkuTypeInfoService stockinQaOrderSkuTypeInfoService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;


    /**
     * sop完成触发全检
     *
     * @param stockinQaOrderEntity
     * @param request
     * @param directReturnCount
     * @param totalDirectReturnCount
     * @param defectCountContent
     */
    @Transactional
    public void stockinQaSopCompleteInspect(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOperateRequest request, int directReturnCount, int totalDirectReturnCount, String defectCountContent) {
        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.FULL_INSPECT);

        StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
        orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        orderEntity.setFullInspectStatus(StockinQaInspectStatusEnum.WAIT_INSPECT.name());
        orderEntity.setSopCompleteDate(new Date());
        orderEntity.setUpdateBy(loginInfoService.getName());
        stockinQaOrderService.updateById(orderEntity);
        //更新不合格数等信息
        stockinQaOrderOperateService.sopCompleteDealData(stockinQaOrderEntity, request, directReturnCount, totalDirectReturnCount);
        //记录日志
        String content = String.format("【%s】操作SOP质检完成%s，触发全检规则，进入全检流程", loginInfoService.getName(), defectCountContent);
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.TRIGGER_FULL_INSPECT, content);
        //生成一条状态是待稽查的流程信息
        this.buildInspectProcessInfo(stockinQaOrderEntity);
    }

    /**
     * 发起全检
     *
     * @param stockinQaOrderEntity
     * @param statusEnum
     * @return
     */
    public void manualFullInspectBy(StockinQaOrderEntity stockinQaOrderEntity, StockinQaOrderProcessStatusEnum statusEnum) {
        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.FULL_INSPECT);

        StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
        orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        orderEntity.setFullInspectStatus(StockinQaInspectStatusEnum.WAIT_INSPECT.name());
        orderEntity.setUpdateBy(loginInfoService.getName());
        stockinQaOrderService.updateById(orderEntity);

        //记录日志
        String content = String.format("【%s】操作%s完成，触发全检规则，进入全检流程", loginInfoService.getName(), statusEnum.getStatus());
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.TRIGGER_FULL_INSPECT, content);
        //生成一条状态是待全检的流程信息
        this.buildInspectProcessInfo(stockinQaOrderEntity);
    }

    private StockinQaOrderProcessEntity buildInspectProcessInfo(StockinQaOrderEntity stockinQaOrderEntity) {
        //新增一条复审的流程记录
        StockinQaOrderProcessEntity lastProcessEntity = stockinQaOrderProcessService.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
                .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, stockinQaOrderEntity.getStockinQaOrderId())
                .orderByDesc(StockinQaOrderProcessEntity::getSort)
                .last("limit 1"));
        StockinQaOrderProcessEntity processEntity = new StockinQaOrderProcessEntity();
        processEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        processEntity.setProcessName(StockinQaOrderProcessStatusEnum.FULL_INSPECT.getStatus());
        processEntity.setLocation(stockinQaOrderEntity.getLocation());
        processEntity.setSort(Objects.isNull(lastProcessEntity.getSort()) ? 1 : lastProcessEntity.getSort() + 1);
        processEntity.setCreateBy(loginInfoService.getName());
        processEntity.setStatus(QaProcessEnum.WAIT_INSPECT.name());
        stockinQaOrderProcessService.save(processEntity);
        return processEntity;
    }

    /**
     * 修改流程状态为对应的状态，如果没有待稽查状态的流程则创建一条（旧数据）
     *
     * @param request
     * @param stockinQaOrderEntity
     * @param qaOrderDetailEntity
     * @return
     */
    public StockinQaOrderProcessEntity updateInspectStatus(StockinQaOperateFullInspectRequest request, StockinQaOrderEntity stockinQaOrderEntity, StockinQaOrderDetailEntity qaOrderDetailEntity) {
        StockinQaOrderProcessEntity waitInspectProcessEntity = stockinQaOrderProcessService.getOne(new LambdaQueryWrapper<StockinQaOrderProcessEntity>()
                .eq(StockinQaOrderProcessEntity::getStockinQaOrderId, request.getStockinQaOrderId())
                .eq(StockinQaOrderProcessEntity::getProcessName, StockinQaOrderProcessStatusEnum.FULL_INSPECT.getStatus())
                .eq(StockinQaOrderProcessEntity::getStatus, QaProcessEnum.WAIT_INSPECT.name())
                .orderByDesc(StockinQaOrderProcessEntity::getSort)
                .last("limit 1"));
        //没有待稽查状态的流程，属于旧数据直接生成一条新的流程记录
        if (Objects.isNull(waitInspectProcessEntity)) {
            waitInspectProcessEntity = this.buildInspectProcessInfo(stockinQaOrderEntity);
        }
        BeanUtils.copyProperties(request, waitInspectProcessEntity);
        waitInspectProcessEntity.setStatus(request.getReturnCount() > 0 ? QaProcessEnum.UNQUALIFIED.name() : QaProcessEnum.QUALIFIED.name());
        waitInspectProcessEntity.setOperator(loginInfoService.getName());
        waitInspectProcessEntity.setUpdateBy(loginInfoService.getName());
        waitInspectProcessEntity.setFullInspectCount(request.getFullInspectCount());
        waitInspectProcessEntity.setQaCount(qaOrderDetailEntity.getTestTotalCount());
        waitInspectProcessEntity.setOperateDate(new Date());
        waitInspectProcessEntity.setUpdateDate(new Date());
        stockinQaOrderProcessService.updateById(waitInspectProcessEntity);
        return waitInspectProcessEntity;
    }

    @Transactional
    @JLock(keyConstant = "fullInspectCommitResult", lockKey = "#request.stockinQaOrderId")
    public void fullInspectCommitResult(StockinQaOperateFullInspectRequest request) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到全检任务");
        if (!StockinQaOrderProcessStatusEnum.FULL_INSPECT.name().equals(stockinQaOrderEntity.getProcessStatus())) {
            throw new BusinessServiceException("该全检任务不是待全检状态");
        }
        if (!StockinQaInspectStatusEnum.WAIT_INSPECT.name().equals(stockinQaOrderEntity.getFullInspectStatus())) {
            throw new BusinessServiceException("该全检任务不是待全检状态");
        }

        if (request.getUnqualifiedCount() > request.getFullInspectCount()) {
            throw new BusinessServiceException("不合格件数不能大于全检件数!");
        }
        StockinQaOrderDetailEntity qaOrderDetailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        if (qaOrderDetailEntity.getDirectReturnCount() + request.getReturnCount() > qaOrderDetailEntity.getBoxQty()) {
            throw new BusinessServiceException("直接退货数 + 退货数不能大于箱内数!");
        }
        int returnCount = qaOrderDetailEntity.getDirectReturnCount() + request.getReturnCount();
        if (qaOrderDetailEntity.getConcessionsCount() != null && (qaOrderDetailEntity.getBoxQty() - returnCount) < qaOrderDetailEntity.getConcessionsCount()) {
            throw new BusinessServiceException("剩余合格件数不能小于让步接收件数!");
        }
        //更新流程信息为已处理
        StockinQaOrderProcessEntity processEntity = this.updateInspectStatus(request, stockinQaOrderEntity, qaOrderDetailEntity);
        //保存附件信息
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.completeOrderDetailByFullInspect(stockinQaOrderEntity, request);

        StockinQaOrderEntity orderEntity = new StockinQaOrderEntity();
        orderEntity.setStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
        orderEntity.setUpdateBy(loginInfoService.getName());

        //如果存在不合格原因，需要新增
        if (StringUtils.hasText(request.getUnqualifiedCategory())) {
            orderEntity.setUnqualifiedCategory(request.getUnqualifiedCategory());
            StockinQaOperateUnqualifiedRequest unqualifiedRequest = new StockinQaOperateUnqualifiedRequest();
            BeanUtils.copyProperties(request, unqualifiedRequest);
            stockinQaOrderUnqualifiedService.saveUnqualifiedByOperate(processEntity, unqualifiedRequest);
            if (Objects.nonNull(stockinQaOrderEntity.getUnqualifiedCategory()) && !stockinQaOrderEntity.getUnqualifiedCategory().equals(request.getUnqualifiedCategory()))
                stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.FULL_INSPECT,
                        String.format("%s将不合格归类由【%s】修改为【%s】", loginInfoService.getName(), StringUtils.hasText(stockinQaOrderEntity.getUnqualifiedCategory()) ? stockinQaOrderEntity.getUnqualifiedCategory() : "空", request.getUnqualifiedCategory()));
        }

        //图片不为空则保存
        if (!CollectionUtils.isEmpty(request.getImageList())) {
            qaOrderImgService.saveImageList(processEntity, request.getImageList());
        }
        orderEntity.setFullInspectStatus(StockinQaInspectStatusEnum.INSPECT_COMPLETE.name());
        //是否需要触发稽查
        if (bdQaInspectRuleService.validInspectByStockinQaOrder(stockinQaOrderEntity.getStockinQaOrderId()) && this.validInspectStatus(request, qaOrderDetailEntity)) {
            //更新全检状态为全检完成
            stockinQaOrderService.updateById(orderEntity);
            //触发稽查
            stockinQaInspectService.auditInspect(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.FULL_INSPECT);
            //进入了稽查/全检如果审核有退货要先退
            stockinQaOrderOperateService.auditReturn(stockinQaOrderEntity, request.getReturnCount(), QaLogTypeEnum.FULL_INSPECT);
            return;
        }

        //无需稽查，走完结流程
        stockinQaOrderService.updateQaOrderProcessStatus(stockinQaOrderEntity, StockinQaOrderProcessStatusEnum.COMPLETED);
        StockinQaOrderStatusEnum statusEnum = stockinQaOrderOperateService.getStatusEnumByDetail(detailEntity);
        orderEntity.setCompleteDate(new Date());
        orderEntity.setResult(statusEnum.name());
        //记录日志
        String content = String.format("【%s】操作质检全检完成，质检结果为【%s】%s", loginInfoService.getName(), statusEnum.getStatus(),
                detailEntity.getUnqualifiedCount() <= 0 ? "" : String.format("，不合格数：%s，退货数：%s",
                        detailEntity.getUnqualifiedCount(), detailEntity.getReturnCount()));
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.FULL_INSPECT, content);
        stockinQaOrderLogService.addLog(stockinQaOrderEntity.getStockinQaOrderId(), QaLogTypeEnum.COMPLETE, String.format("%s完成质检单", loginInfoService.getName()));
        stockinQaOrderService.updateById(orderEntity);

        //反馈入库单
        QcInboundsMessage qcInboundsMessage = stockinQaOrderOperateService.buildQcMessage(stockinQaOrderEntity.getStockinQaOrderId(), request.getReturnCount());
        stockinQcService.qcComplete(qcInboundsMessage);
        //完结质检任务
        stockinQaTaskService.completeTask(stockinQaOrderEntity);
    }

    private boolean validInspectStatus(StockinQaOperateFullInspectRequest request, StockinQaOrderDetailEntity detailEntity) {
        int boxQty = Objects.isNull(detailEntity.getBoxQty()) ? 0 : detailEntity.getBoxQty();
        int returnCount = (Objects.isNull(request.getReturnCount()) ? 0 : request.getReturnCount()) + (Objects.isNull(detailEntity.getDirectReturnCount()) ? 0 : detailEntity.getDirectReturnCount());
        StockinQaOrderStatusEnum stockinQaOrderStatusEnum = returnCount <= 0 ? StockinQaOrderStatusEnum.PUT_ON : returnCount < boxQty
                ? StockinQaOrderStatusEnum.SOME_RETURN : StockinQaOrderStatusEnum.BATCH_RETURN;
        return StockinQaOrderStatusEnum.BATCH_RETURN != stockinQaOrderStatusEnum;
    }

    /**
     * 全检任务查询
     *
     * @param request
     * @return
     */
    public PageResponse<StockinQaFullInspectTaskPageResponse> pageFullInspectTaskList(StockinQaFullInspectTaskPageRequest request) {
        PageResponse<StockinQaFullInspectTaskPageResponse> response = new PageResponse<>();
        Page<StockinQaFullInspectTaskPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        //避免慢查询单独查
        page.setSearchCount(false);
        //全检状态,如果没有就全查
        if (CollectionUtils.isEmpty(request.getInspectStatusList()))
            request.setInspectStatusList(Lists.newArrayList(StockinQaInspectStatusEnum.WAIT_INSPECT.name(), StockinQaInspectStatusEnum.INSPECT_COMPLETE.name()));

        IPage<StockinQaFullInspectTaskPageResponse> pageList = stockinQaOrderService.getBaseMapper().pageFullInspectTaskList(page, request);
        List<StockinQaFullInspectTaskPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setContent(pageList.getRecords());
            response.setTotalCount(page.getTotal());
            return response;
        }
        List<Integer> stockinQaOrderIds = records.stream().map(StockinQaFullInspectTaskPageResponse::getStockinQaOrderId).collect(Collectors.toList());

        Map<Integer, List<StockinQaOrderItemEntity>> itemMap = stockinQaOrderItemService.list(new LambdaQueryWrapper<StockinQaOrderItemEntity>().select(StockinQaOrderItemEntity::getStockinQaOrderId,
                StockinQaOrderItemEntity::getSupplierDeliveryNo).in(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderItemEntity::getStockinQaOrderId));
        //商品标签
        Map<Integer, List<StockinQaOrderSkuTypeInfoEntity>> skuTypeMap = stockinQaOrderSkuTypeInfoService.list(new LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity>().select(StockinQaOrderSkuTypeInfoEntity::getSkuType, StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId).in(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId));
        //结果状态
        Map<String, String> inspectStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_FULL_INSPECT_STATUS.getName());
        records.stream().forEach(entity -> {
            List<StockinQaOrderItemEntity> taskItemEntityList = itemMap.get(entity.getStockinQaOrderId());
            entity.setSupplierDeliveryNo(taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));
            entity.setInspectStatusStr(inspectStatusMap.get(entity.getInspectStatus()));

            List<String> supplierDeliveryNoList = taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
            entity.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, entity.getSku()));
            List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities = skuTypeMap.get(entity.getStockinQaOrderId());
            if (!CollectionUtils.isEmpty(skuTypeInfoEntities)) {
                entity.setSkuTypeList(skuTypeInfoEntities.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).collect(Collectors.toList()));
            }
        });

        response.setContent(pageList.getRecords());
        response.setTotalCount(stockinQaOrderService.getBaseMapper().pageFullInspectTaskCount(request));
        return response;
    }

    /**
     * 全检报告
     *
     * @param request
     * @return
     */
    public PageResponse<StockinQaFullInspectReportPageResponse> pageFullInspectReportList(StockinQaFullInspectReportPageRequest request) {
        PageResponse<StockinQaFullInspectReportPageResponse> response = new PageResponse<>();

        Page<StockinQaFullInspectReportPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        //转换流程状态
        this.buildInspectStatusQueryCondition(request);
        IPage<StockinQaFullInspectReportPageResponse> pageList = stockinQaOrderService.getBaseMapper().pageFullInspectReportList(page, request);
        List<StockinQaFullInspectReportPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setTotalCount(page.getTotal());
            return response;
        }
        List<Integer> stockinQaOrderIds = records.stream().map(StockinQaFullInspectReportPageResponse::getStockinQaOrderId).collect(Collectors.toList());
        List<Integer> stockinQaOrderProcessIds = records.stream().filter(entity -> entity.getStockinQaOrderProcessId() != null).map(StockinQaFullInspectReportPageResponse::getStockinQaOrderProcessId).collect(Collectors.toList());

        Map<Integer, List<StockinQaOrderItemEntity>> itemMap = stockinQaOrderItemService.list(new LambdaQueryWrapper<StockinQaOrderItemEntity>().select(StockinQaOrderItemEntity::getStockinQaOrderId,
                StockinQaOrderItemEntity::getSupplierDeliveryNo).in(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderItemEntity::getStockinQaOrderId));
        Map<Integer, List<StockinQaOrderSkuTypeInfoEntity>> skuTypeMap = stockinQaOrderSkuTypeInfoService.list(new LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity>().select(StockinQaOrderSkuTypeInfoEntity::getSkuType, StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId).in(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId));
        //全检结果
        Map<String, String> inspectResultMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_RESULT.getName());
        List<StockinQaOrderUnqualifiedEntity> mainUnqualifiedInfoByProcessList = stockinQaOrderUnqualifiedService.findMainUnqualifiedInfoByProcessId(stockinQaOrderProcessIds);
        Map<Integer, StockinQaOrderUnqualifiedEntity> unqualifiedMap = mainUnqualifiedInfoByProcessList.stream().collect(Collectors.toMap(StockinQaOrderUnqualifiedEntity::getStockinQaOrderProcessId, Function.identity(), (v1, v2) -> v1));
        records.stream().forEach(entity -> {
            List<StockinQaOrderItemEntity> taskItemEntityList = itemMap.get(entity.getStockinQaOrderId());
            entity.setSupplierDeliveryNo(taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));
            entity.setResultStr(this.convertInspectStatus(entity.getResult(), entity.getBoxQty(), entity.getReturnCount(), inspectResultMap));
            List<String> supplierDeliveryNoList = taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
            entity.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, entity.getSku()));
            List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities = skuTypeMap.get(entity.getStockinQaOrderId());
            if (!CollectionUtils.isEmpty(skuTypeInfoEntities)) {
                entity.setSkuTypeList(skuTypeInfoEntities.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(unqualifiedMap) || !unqualifiedMap.containsKey(entity.getStockinQaOrderProcessId())) {
                return;
            }
            entity.setUnqualifiedCategory(unqualifiedMap.get(entity.getStockinQaOrderProcessId()).getUnqualifiedCategory());
            entity.setUnqualifiedQuestion(unqualifiedMap.get(entity.getStockinQaOrderProcessId()).getUnqualifiedQuestion());
            entity.setUnqualifiedReason(unqualifiedMap.get(entity.getStockinQaOrderProcessId()).getUnqualifiedReason());
        });
        response.setContent(pageList.getRecords());
        response.setTotalCount(stockinQaOrderService.getBaseMapper().pageFullInspectReportCount(request));
        return response;
    }

    /**
     * 流程状态转换成全检报告状态
     *
     * @param result
     * @param boxQty
     * @param returnCount
     * @param statusMap
     * @return
     */
    public String convertInspectStatus(String result, Integer boxQty, Integer returnCount, Map<String, String> statusMap) {
        if (!StringUtils.hasText(result)) {
            return "";
        }
        switch (result) {
            case "QUALIFIED":
                return statusMap.get(StockinQaOrderInspectResultEnum.PUT_ON.name());
            case "UNQUALIFIED":
                if (Objects.nonNull(boxQty) && Objects.nonNull(returnCount) && boxQty.equals(returnCount)) {
                    return statusMap.get(StockinQaOrderInspectResultEnum.BATCH_RETURN.name());
                } else {
                    return statusMap.get(StockinQaOrderInspectResultEnum.SOME_RETURN.name());
                }
            default:
                return "";
        }
    }

    /**
     * 查询流程状态转换成结果
     *
     * @param request
     */
    public void buildInspectStatusQueryCondition(StockinQaFullInspectReportPageRequest request) {
        if (CollectionUtils.isEmpty(request.getResultList())) {
            return;
        }
        List<String> resultList = new ArrayList<>();
        for (String result : request.getResultList()) {
            switch (result) {
                case "PUT_ON":
                    resultList.add(QaProcessEnum.QUALIFIED.name());
                    break;
                case "SOME_RETURN":
                case "BATCH_RETURN":
                    resultList.add(QaProcessEnum.UNQUALIFIED.name());
                    break;
                default:
                    break;
            }
        }
        if (!CollectionUtils.isEmpty(resultList)) {
            request.setResultList(resultList);
        }
        //如果为批退，则需要查询出所有状态为"不合格"的记录
        if (request.getResultList().contains(StockinQaOrderInspectResultEnum.BATCH_RETURN.name()) && !request.getResultList().contains(StockinQaOrderInspectResultEnum.SOME_RETURN.name())) {
            request.setBatchReturnFlag(Boolean.TRUE);
        }
        if (request.getResultList().contains(StockinQaOrderInspectResultEnum.SOME_RETURN.name()) && !request.getResultList().contains(StockinQaOrderInspectResultEnum.BATCH_RETURN.name())) {
            request.setSomeReturnFlag(Boolean.TRUE);
        }
    }

    public StockinQaFullInspectResultDetailResponse getFullInspectDetail(StockinQaInspectDetailRequest request) {
        //校验用户查看的权利
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getBaseMapper().getByIdPermission(request.getStockinQaOrderId());
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单或无权查看");
        StockinQaFullInspectResultDetailResponse response = new StockinQaFullInspectResultDetailResponse();
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(request.getStockinQaOrderId());
        BeanUtils.copyProperties(stockinQaOrderEntity, response, "unqualifiedCategory");
        BeanUtils.copyProperties(detailEntity, response);

        StockinQaTaskEntity taskEntity = stockinQaTaskService.getById(stockinQaOrderEntity.getTaskId());
        response.setQaQty(taskEntity.getQaQty());

        response.setProcessStatusStr(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName(), stockinQaOrderEntity.getProcessStatus()));

        List<StockinQaOrderItemEntity> itemEntityList = stockinQaOrderItemService.findByStockinQaOrderId(request.getStockinQaOrderId());
        response.setPurchasePlanNo(itemEntityList.stream().map(StockinQaOrderItemEntity::getPurchasePlanNo).distinct().collect(Collectors.joining(",")));
        response.setSupplierDeliveryBoxCode(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.joining(",")));
        response.setSupplierDeliveryNo(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));

        List<String> supplierDeliveryNoList = itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
        response.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, stockinQaOrderEntity.getSku()));
        //图片信息
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(stockinQaOrderEntity.getSku());
        response.setImageUrl(productSpecInfoEntity.getImageUrl());
        response.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
        response.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());
        response.setStockinQaProcessId(request.getStockinQaOrderProcessId());

        //赋值不合格信息
        buildUnqualifiedReasonInspect(request.getStockinQaOrderId(), response);


        StockinQaOrderProcessEntity processEntity = stockinQaOrderProcessService.getById(request.getStockinQaOrderProcessId());
        if (Objects.nonNull(processEntity)) {
            //全检结果
            Map<String, String> inspectResultMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_INSPECT_RESULT.getName());
            response.setInspectCompleteDate(processEntity.getOperateDate());
            response.setInspectUserName(processEntity.getOperator());
            response.setUnqualifiedCount(processEntity.getUnqualifiedCount());
            response.setReturnCount(processEntity.getReturnCount());
            response.setFullInspectCount(processEntity.getFullInspectCount());
            response.setInspectUserName(processEntity.getCreateBy());
            response.setAttachmentUrl(processEntity.getAttachmentUrl());
            response.setRemark(processEntity.getRemark());
            response.setResultStr(this.convertInspectStatus(processEntity.getStatus(), response.getReturnCount(), response.getBoxQty(), inspectResultMap));
            List<StockinQaOrderImgEntity> stockinQaOrderImgEntities = qaOrderImgService.getInspectByProcessInfo(request.getStockinQaOrderId(), request.getStockinQaOrderProcessId());
            if (!CollectionUtils.isEmpty(stockinQaOrderImgEntities))
                response.setImageList(stockinQaOrderImgEntities.stream().map(StockinQaOrderImgEntity::getImgUrl).collect(Collectors.toList()));
        }

        return response;
    }

    private void buildUnqualifiedReasonInspect(Integer stockinQaOrderId, StockinQaFullInspectResultDetailResponse response) {
        List<StockinQaOrderUnqualifiedEntity>
                unqualifiedEntityList = stockinQaOrderUnqualifiedService.list(new LambdaQueryWrapper<StockinQaOrderUnqualifiedEntity>()
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderId, stockinQaOrderId)
                .eq(StockinQaOrderUnqualifiedEntity::getStockinQaOrderProcessId, response.getStockinQaProcessId())
                .orderByAsc(StockinQaOrderUnqualifiedEntity::getSort));
        //没有不合格信息
        if (CollectionUtils.isEmpty(unqualifiedEntityList)) return;

        StockinQaOrderUnqualifiedEntity unqualifiedEntity = unqualifiedEntityList.get(0);
        response.setUnqualifiedCategory(unqualifiedEntity.getUnqualifiedCategory());
        response.setUnqualifiedQuestion(unqualifiedEntity.getUnqualifiedQuestion());
        response.setUnqualifiedReason(unqualifiedEntity.getUnqualifiedReason());

        StockinQaOrderUnqualifiedEntity secondaryUnqualifiedEntity = null;
        if (unqualifiedEntityList.size() > 1)
            secondaryUnqualifiedEntity = unqualifiedEntityList.get(1);
        if (Objects.nonNull(secondaryUnqualifiedEntity) && secondaryUnqualifiedEntity.getProcessName().equals(unqualifiedEntity.getProcessName())) {
            response.setUnqualifiedCategorySecondary(secondaryUnqualifiedEntity.getUnqualifiedCategory());
            response.setUnqualifiedQuestionSecondary(secondaryUnqualifiedEntity.getUnqualifiedQuestion());
            response.setUnqualifiedReasonSecondary(secondaryUnqualifiedEntity.getUnqualifiedReason());
        }
    }

    public StockinQaInspectCountQtyResponse countFullInspectQty(StockinQaFullInspectReportPageRequest request) {
        StockinQaInspectCountQtyResponse response = new StockinQaInspectCountQtyResponse();
        if (Objects.nonNull(request.getCompleteStartDate()) && Objects.nonNull(request.getCompleteEndDate())) {
            //按七天一次查询
            List<DateRangeRequest> dateRangeRequests = WmsDateUtils.splitDateRange(request.getCompleteStartDate(), request.getCompleteEndDate(), 7);
            Integer unqualifiedCount = 0;
            Integer returnCount = 0;
            Integer inspectTotalCount = 0;
            for (DateRangeRequest dateRangeRequest : dateRangeRequests) {
                request.setCompleteStartDate(dateRangeRequest.getStartDate());
                request.setCompleteEndDate(dateRangeRequest.getEndDate());
                StockinQaInspectCountQtyResponse countQty = stockinQaOrderService.getBaseMapper().countFullInspectQty(request);
                unqualifiedCount += countQty.getUnqualifiedCount();
                returnCount += countQty.getReturnCount();
                inspectTotalCount += countQty.getInspectTotalCount();
            }
            response.setUnqualifiedCount(unqualifiedCount);
            response.setReturnCount(returnCount);
            response.setInspectTotalCount(inspectTotalCount);
            return response;
        } else {
            response = stockinQaOrderService.getBaseMapper().countFullInspectQty(request);
        }

        return response;
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_FULL_INSPECT_TASK_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest downloadRequest) {
        DownloadResponse response = new DownloadResponse();
        StockinQaFullInspectTaskPageRequest request = JsonMapper.fromJson(downloadRequest.getRequestContent(), StockinQaFullInspectTaskPageRequest.class);
        request.setPageIndex(downloadRequest.getPageIndex());
        request.setPageSize(downloadRequest.getPageSize());
        if (CollectionUtils.isEmpty(request.getInspectStatusList()))
            request.setInspectStatusList(Lists.newArrayList(StockinQaInspectStatusEnum.WAIT_INSPECT.name(), StockinQaInspectStatusEnum.INSPECT_COMPLETE.name()));
        PageResponse<StockinQaFullInspectTaskPageResponse> pageList = this.pageFullInspectTaskList(request);
        List<StockinQaFullInspectTaskPageResponse> records = pageList.getContent();
        if (CollectionUtils.isEmpty(records)) {
            response.setDataJsonStr(JsonMapper.toJson(records));
            response.setTotalCount(0L);
            return response;
        }
        response.setTotalCount(pageList.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(records));
        return response;
    }
}
