package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemSourcePositionBo;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskItemList;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTaskTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutPickingTaskItemListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutPickingTaskItemListResponse;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingItemRecordEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutPickingTaskItemMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
public class StockoutPickingTaskItemService extends ServiceImpl<StockoutPickingTaskItemMapper, StockoutPickingTaskItemEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutPickingTaskItemService.class);
    @Autowired
    StockoutPickingTaskItemMapper stockoutPickingTaskItemMapper;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutPickingItemRecordService recordService;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutPickingTaskService taskService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    StockService stockService;
    @Autowired
    StockoutPickingExceptionService stockoutPickingExceptionService;
    @Autowired
    private BdSystemParameterService bdSystemParameterService;
    @Autowired
    private StockoutOrderItemService stockoutOrderItemService;

    /*
     * 查询上架任务明细列表
     * */
    public PageResponse<StockoutPickingTaskItemListResponse> getPickingItemTaskListByRequest(StockoutPickingTaskItemListRequest request) {
        PageResponse<StockoutPickingTaskItemListResponse> pageResponse = new PageResponse<>();
        if (StringUtils.hasText(request.getBarcode())) {
            ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcode(request.getBarcode());
            if (productSpecInfoEntity == null)
                throw new BusinessServiceException("不存在该条码的商品信息");
            request.setBarcode(productSpecInfoEntity.getBarcode());
        }
        if (Objects.isNull(request.getTaskId()))
            throw new BusinessServiceException("任务id不存在");
        IPage<StockoutPickingTaskItemList> pageResult = stockoutPickingTaskItemMapper.pageSearchList(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        if (pageResult.getTotal() == 0) {
            pageResponse.setTotalCount(pageResult.getTotal());
            pageResponse.setContent(Collections.emptyList());
            return pageResponse;
        }
        List<Integer> taskItemId = pageResult.getRecords().stream().map(StockoutPickingTaskItemList::getTaskItemId).collect(Collectors.toList());
        List<StockoutPickingItemRecordEntity> recordList = recordService.findAllByTaskItemIdIn(taskItemId);
        Map<String, List<StockoutPickingItemRecordEntity>> skuMap = recordList.stream().collect(Collectors.groupingBy(StockoutPickingItemRecordEntity::getSku));
        List<StockoutPickingTaskItemListResponse> resultList = pageResult.getRecords().stream().map(projection -> {
            String boxCode = skuMap.getOrDefault(projection.getSku(), Collections.emptyList()).stream().map(StockoutPickingItemRecordEntity::getInternalBoxCode).distinct().collect(Collectors.joining(","));
            StockoutPickingTaskItemListResponse response = new StockoutPickingTaskItemListResponse();
            BeanUtils.copyProperties(projection, response);
            response.setStatusStr(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TASK_STATUS.getName(), projection.getStatus()));
            response.setInternalBoxCode(boxCode);
            buildSpaceAreaName(response);
            return response;
        }).collect(Collectors.toList());
        pageResponse.setTotalCount(pageResult.getTotal());
        pageResponse.setContent(resultList);
        return pageResponse;
    }

    private void buildSpaceAreaName(StockoutPickingTaskItemListResponse response) {
        if (!StringUtils.hasText(response.getPositionCode())) {
            return;
        }
        BdPositionEntity positionCode = bdPositionService.getPositionByCode(response.getPositionCode());
        if (positionCode == null) {
            return;
        }
        response.setSpaceAreaName(positionCode.getSpaceAreaName());
    }

    public List<StockoutPickingTaskItemEntity> findAllByTaskId(Integer taskId) {
        return this.list(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda()
                .eq(StockoutPickingTaskItemEntity::getTaskId, taskId));
    }

    public List<StockoutPickingTaskItemEntity> findAllByTaskIdList(List<Integer> taskIdList) {
        return this.list(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda()
                .in(StockoutPickingTaskItemEntity::getTaskId, taskIdList));
    }

    public List<StockoutPickingTaskItemEntity> findAllByTaskIdOrderBySpace(Integer taskId) {
        return this.list(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda()
                .eq(StockoutPickingTaskItemEntity::getTaskId, taskId).orderByAsc(StockoutPickingTaskItemEntity::getSpaceAreaName, StockoutPickingTaskItemEntity::getPositionCode));
    }

    public List<StockoutPickingTaskItemEntity> findAllByTaskIdOrderBySpace(List<Integer> taskId) {
        return this.list(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda()
                .in(StockoutPickingTaskItemEntity::getTaskId, taskId)
            .orderByAsc(StockoutPickingTaskItemEntity::getSpaceAreaName, StockoutPickingTaskItemEntity::getPositionCode));
    }

    public List<StockoutPickingTaskItemEntity> findAllByTaskIdIn(List<Integer> taskId) {
        return this.list(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda()
                .in(StockoutPickingTaskItemEntity::getTaskId, taskId));
    }


    public StockoutPickingTaskItemEntity findTopByTaskIdAndBarcode(Integer taskId, String barcode) {
        return this.getOne(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda()
                .eq(StockoutPickingTaskItemEntity::getTaskId, taskId)
                .eq(StockoutPickingTaskItemEntity::getBarcode, barcode).last("limit 1"));
    }

    public StockoutPickingTaskItemEntity findTopByTaskIdAndSku(Integer taskId, String sku) {
        return this.getOne(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda()
                .eq(StockoutPickingTaskItemEntity::getTaskId, taskId)
                .eq(StockoutPickingTaskItemEntity::getSku, sku).last("limit 1"));
    }

    public List<StockoutPickingTaskItemEntity> findAllByTaskIdAndSpaceAreaName(Integer taskId, String spaceAreaName) {
        return this.list(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda()
                .eq(StockoutPickingTaskItemEntity::getTaskId, taskId)
                .eq(StockoutPickingTaskItemEntity::getSpaceAreaName, spaceAreaName));
    }

    public StockoutPickingTaskItemEntity getPickingTaskItem(Integer taskId, String stockoutOrderNo, String sku, Integer positionId) {
        LambdaQueryWrapper<StockoutPickingTaskItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutPickingTaskItemEntity::getTaskId, taskId);
        queryWrapper.eq(StockoutPickingTaskItemEntity::getStockoutOrderNo, stockoutOrderNo);
        queryWrapper.eq(StockoutPickingTaskItemEntity::getSku, sku);
        queryWrapper.eq(StockoutPickingTaskItemEntity::getPositionId, positionId).last("limit 1");
        return getOne(queryWrapper);
    }

    /**
     * 更新拣货箱明细
     */
    public List<StockInternalBoxItemSourcePositionBo> updatePickingBoxItem(List<Integer> batchIds, StockoutOrderEntity stockoutOrderInfo, StockoutShipmentItemEntity shipmentItemEntity, Integer scanQty) {
        String sku = shipmentItemEntity.getSku();

        List<StockInternalBoxItemSourcePositionBo> sourcePositionBos = new ArrayList<>();
        // 拣货箱明细
        List<StockInternalBoxItemEntity> internalBoxItemEntities = stockInternalBoxItemService.getBaseMapper().searchInternalBoxItemListByBatchIdsAndSku(batchIds, StockInternalBoxTypeEnum.PICKING_BOX.name(), sku);
        //赋值出库单号以记录库存交易日志
        internalBoxItemEntities.forEach(item -> item.setStockoutOrderNo(stockoutOrderInfo.getStockoutOrderNo()));
        if (internalBoxItemEntities.size() > 1) {
            StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getById(shipmentItemEntity.getStockoutOrderItemId());
            internalBoxItemEntities.sort(Comparator.comparing(item -> {
                if (StringUtils.hasText(item.getSourcePositionCode()) && item.getSourcePositionCode().equals(stockoutOrderItemEntity.getPositionCode()))
                    return 0;
                return 1;
            }));
        }
        LOGGER.info(" {} 内部箱信息 ： {}", stockoutOrderInfo.getStockoutOrderNo(), JsonMapper.toJson(internalBoxItemEntities));
        int boxQty = CollectionUtils.isEmpty(internalBoxItemEntities) ? 0 : internalBoxItemEntities.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
        // 拣货箱库存扣减
        if (boxQty > 0) {
            sourcePositionBos.addAll(stockInternalBoxItemService.minusStockInternalBoxItemQty(internalBoxItemEntities, Math.min(boxQty, scanQty), StockChangeLogTypeEnum.STOCKOUT_CHECK, StockChangeLogTypeModuleEnum.STOCK_OUT, null));
        }
        //若是不足则扣除原库位
        if (scanQty > boxQty)
            sourcePositionBos.addAll(stockoutPickingExceptionService.minusSourcePositionStock(batchIds.get(batchIds.size() - 1), stockoutOrderInfo.getStockoutOrderId(), productSpecInfoService.getBySku(sku).getSpecId(), scanQty - boxQty, shipmentItemEntity.getStockoutOrderItemId()));
        return sourcePositionBos;

    }

    // 异常库位变动
    public StockUpdateRequest getExceptionStockUpdateRequest(String sku, String positionCode, Integer exceptionQty) {
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setSku(sku);
        stockUpdateRequest.setPositionCode(positionCode);
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.STOCKOUT_CHECK);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_OUT);
        stockUpdateRequest.setQty(exceptionQty);
        return stockUpdateRequest;
    }

    /**
     * 根据波次号，查找加工拣货任务明细
     */
    public List<StockoutPickingTaskItemEntity> findProcessPickingTaskItemByBatchId(Integer bathId) {
        List<StockoutPickingTaskEntity> taskEntities = taskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .eq(StockoutPickingTaskEntity::getBatchId, bathId)
                .eq(StockoutPickingTaskEntity::getTaskType, StockoutPickingTaskTypeEnum.PROCESSING_PICKING.name()));
        if (taskEntities.isEmpty())
            throw new BusinessServiceException(String.format("未找到加工拣货任务，【%s】", bathId));
        List<Integer> taskIds = taskEntities.stream().map(StockoutPickingTaskEntity::getTaskId).distinct().collect(Collectors.toList());
        return this.findAllByTaskIdIn(taskIds);
    }

    /**
     * 通过状体和sku统计已拣数
     *
     * @param status
     * @param sku
     * @return
     */
    public Integer countScanQtyByStatusAndSku(String status, String sku, String positionCode) {
        return this.baseMapper.countScanQtyByStatusAndSku(status, sku, positionCode);
    }

    /**
     * 更新拣货箱剩余库存数，增加原库位库存
     *
     * @param batchId
     */
    @Transactional
    public void returnPickingBoxQtyToPosition(Integer batchId) {

        // 需配置参数 通知缺货是否自动归还拣货箱库存  true or false  未配置默认为false  modify by caish 2024-04-03
        BdSystemParameterEntity systemParameter = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_STOCKOUT_NOTICE_LACK_CLEAR_BOX.getKey());
        if (Objects.isNull(systemParameter) || !StockConstant.TRUE.equalsIgnoreCase(systemParameter.getConfigValue()))
            return;

        // 拣货箱明细
        List<StockInternalBoxItemEntity> internalBoxItemEntityList = stockInternalBoxItemService.getBaseMapper().searchInternalBoxItemListByBatchId(batchId, StockInternalBoxTypeEnum.PICKING_BOX.name());
        if (CollectionUtils.isEmpty(internalBoxItemEntityList)) {
            return;
        }
        StockoutBatchEntity batchEntity = stockoutBatchService.getById(batchId);
        StockChangeLogTypeEnum changeLogTypeEnum = batchEntity.getPickingType().equals(StockoutPickingTypeEnum.WHOLE_PICK.name()) ? StockChangeLogTypeEnum.SCAN_LEVELLING : StockChangeLogTypeEnum.SPLIT_EXCEPTION;

        for (StockInternalBoxItemEntity boxItem : internalBoxItemEntityList) {
            List<StockInternalBoxItemSourcePositionBo> sourceList = Collections.singletonList(stockInternalBoxItemService.minusStockInternalBoxItemQty(boxItem, boxItem.getQty(), changeLogTypeEnum, StockChangeLogTypeModuleEnum.STOCK_OUT, null));
            // 若拣货箱有库存扣减，需要还给原库位，原库位++
            stockoutPickingExceptionService.addSourcePositionStock(sourceList, batchId, changeLogTypeEnum, null);
        }
    }

    public void updatePickingStatus(List<Integer> taskIdList) {
        this.baseMapper.updatePickingStatus(taskIdList);
    }
}
