package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingResponse;
import com.nsy.wms.business.manage.scm.response.PurchaseOrderWorkmanshipInfoResponse;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockin.StockinVolumeWeightStandardRecordEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinVolumeWeightStandardRecordMapper;
import com.nsy.wms.utils.UnitUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 质检测量工艺标准记录服务
 *
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-01-20 10:00
 */
@Service
public class StockinVolumeWeightStandardRecordService extends ServiceImpl<StockinVolumeWeightStandardRecordMapper, StockinVolumeWeightStandardRecordEntity> {

    /**
     * 保存质检测量工艺标准记录，并返回id
     *
     * @param workmanshipInfoResponse
     * @param specInfo
     * @return
     */
    public Integer saveStandInfo(PurchaseOrderWorkmanshipInfoResponse workmanshipInfoResponse, ProductSpecInfoEntity specInfo) {
        if (Objects.isNull(workmanshipInfoResponse)) {
            return 0;
        }
        StockinVolumeWeightStandardRecordEntity entity = new StockinVolumeWeightStandardRecordEntity();
        BeanUtils.copyProperties(workmanshipInfoResponse, entity);
        //赋值工艺标准信息
        entity.setSku(specInfo.getSku());
        entity.setBarcode(specInfo.getBarcode());
        entity.setProductId(specInfo.getProductId());
        entity.setLength(workmanshipInfoResponse.getPackageLong());
        entity.setWidth(workmanshipInfoResponse.getPackageWidth());
        entity.setHeight(workmanshipInfoResponse.getPackageHeight());
        entity.setWeight(workmanshipInfoResponse.getWeight());
        entity.setVolumeWeightKg(UnitUtils.poundsToKilograms(workmanshipInfoResponse.getVolumeWeightPound()));
        entity.setChargedWeightKg(UnitUtils.poundsToKilograms(workmanshipInfoResponse.getChargedWeight()));
        this.save(entity);
        return entity.getId();
    }

    /**
     * 获取工艺测量标准信息
     *
     * @param standardId
     * @return
     */
    public StockinVolumeWeightRecordMappingResponse getStandInfoById(Integer standardId) {
        if (Objects.isNull(standardId)) {
            return null;
        }
        StockinVolumeWeightStandardRecordEntity entity = this.getById(standardId);
        if (Objects.isNull(entity)) {
            return null;
        }
        StockinVolumeWeightRecordMappingResponse response = new StockinVolumeWeightRecordMappingResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }

} 