package com.nsy.wms.business.domain.dto.stockout;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 关单明细同步消息
 *
 * <AUTHOR>
 * @since 2022-12-14 17:13:59
 */
public class CustomsDeclareFormItemMessage implements Serializable {

    private Integer declareFormItemId;
    //关单id
    private Integer declareFormId;
    //项号
    private String gNo;
    //进项数量
    private Integer inputQty;
    //进项金额
    private BigDecimal inputPrice;
    //含税金额
    private BigDecimal taxInclusivePrice;
    //含税单价
    private BigDecimal taxInclusiveUnitPrice;
    //税额
    private BigDecimal taxPrice;
    //进项发票号码
    private String inputInvoiceNo;
    //进项发票代码
    private String inputInvoiceCode;
    //开票时间
    private Date invoiceDate;
    //发票地址
    private String invoiceUrl;

    public Integer getDeclareFormItemId() {
        return declareFormItemId;
    }

    public void setDeclareFormItemId(Integer declareFormItemId) {
        this.declareFormItemId = declareFormItemId;
    }

    public Integer getDeclareFormId() {
        return declareFormId;
    }

    public void setDeclareFormId(Integer declareFormId) {
        this.declareFormId = declareFormId;
    }

    public String getgNo() {
        return gNo;
    }

    public void setgNo(String gNo) {
        this.gNo = gNo;
    }

    public Integer getInputQty() {
        return inputQty;
    }

    public void setInputQty(Integer inputQty) {
        this.inputQty = inputQty;
    }

    public BigDecimal getInputPrice() {
        return inputPrice;
    }

    public void setInputPrice(BigDecimal inputPrice) {
        this.inputPrice = inputPrice;
    }

    public BigDecimal getTaxInclusivePrice() {
        return taxInclusivePrice;
    }

    public void setTaxInclusivePrice(BigDecimal taxInclusivePrice) {
        this.taxInclusivePrice = taxInclusivePrice;
    }

    public BigDecimal getTaxInclusiveUnitPrice() {
        return taxInclusiveUnitPrice;
    }

    public void setTaxInclusiveUnitPrice(BigDecimal taxInclusiveUnitPrice) {
        this.taxInclusiveUnitPrice = taxInclusiveUnitPrice;
    }

    public BigDecimal getTaxPrice() {
        return taxPrice;
    }

    public void setTaxPrice(BigDecimal taxPrice) {
        this.taxPrice = taxPrice;
    }

    public String getInputInvoiceNo() {
        return inputInvoiceNo;
    }

    public void setInputInvoiceNo(String inputInvoiceNo) {
        this.inputInvoiceNo = inputInvoiceNo;
    }

    public String getInputInvoiceCode() {
        return inputInvoiceCode;
    }

    public void setInputInvoiceCode(String inputInvoiceCode) {
        this.inputInvoiceCode = inputInvoiceCode;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public String getInvoiceUrl() {
        return invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }
}

