package com.nsy.wms.business.service.stockout;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutTemuExtendCreateShiporderRequest;
import com.nsy.api.wms.request.temu.TemuAuth;
import com.nsy.api.wms.request.temu.TemuCreateShiporderV2Request;
import com.nsy.api.wms.request.temu.TemuGetLogisticsMatchRequest;
import com.nsy.api.wms.request.temu.TemuGetMallAddressRequest;
import com.nsy.api.wms.request.temu.TemuGetReceiveAddressRequest;
import com.nsy.api.wms.request.temu.TemuGetShiporderRequest;
import com.nsy.api.wms.response.temu.TemuGetLogisticsMatchResponse;
import com.nsy.api.wms.response.temu.TemuGetPurchaseOrderInfoListResponse;
import com.nsy.api.wms.response.temu.TemuGetPurchaseOrderInfoResponse;
import com.nsy.api.wms.response.temu.TemuGetReceiveAddressV2Response;
import com.nsy.api.wms.response.temu.TemuMallAddressGetResponse;
import com.nsy.api.wms.response.temu.TemuPurchaseorderGetResponse;
import com.nsy.api.wms.response.temu.TemuShiporderGetResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutTemuCreateShiporderBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutUnGetShiporderBo;
import com.nsy.wms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuExtendEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderTemuExtendMapper;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class StockoutOrderTemuExtendShiporderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderTemuExtendShiporderService.class);


    @Resource
    StockoutOrderService stockoutOrderService;
    @Resource
    StockoutShipmentService stockoutShipmentService;
    @Resource
    LoginInfoService loginInfoService;
    @Resource
    ThirdPartyApiService thirdPartyApiService;
    @Resource
    StockoutOrderTemuExtendService temuExtendService;
    @Resource
    StockoutShipmentItemService shipmentItemService;
    @Resource
    StockoutOrderTemuExtendInfoService stockoutOrderTemuExtendInfoService;
    @Resource
    StockoutOrderTemuExtendItemService extendItemService;
    @Resource
    BdSystemParameterService bdSystemParameterService;
    @Resource
    ProductSpecInfoService productSpecInfoService;
    @Resource
    StockoutOrderTemuExtendMapper temuExtendMapper;
    @Resource
    StockoutOrderLogService stockoutOrderLogService;
    @Resource
    StockoutOrderItemService stockoutOrderItemService;


    /**
     * 1. 验证参数是否正确
     * 2. 构建【创建发货单请求】
     * 3. 判断是否自动创建
     * * * a. 自动创建
     * * * * * i. 系统变量 【Temu快递黑名单】
     * * * * * ii. 获取【平台推荐快递列表】，过滤掉【Temu快递黑名单】
     * * * * * iii. 默认选第一个快递
     * * * b. 手动创建
     * * * * * 调用【平台推荐快递列表】，验证并获取predictId，用于创建发货单
     * 4. 设置【物流信息】给【创建发货单请求】
     * 5. 更新物流公司
     * 6. 创建发货单
     *
     * @param request
     * @param isAuto
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void createShiporder(StockoutTemuExtendCreateShiporderRequest request, Boolean isAuto) {
        //1. 验证参数是否正确
        if (CollectionUtils.isEmpty(request.getStockoutOrderNoList()))
            throw new BusinessServiceException("未选择");
        List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.getByStockoutOrderNoList(request.getStockoutOrderNoList());
        List<StockoutOrderTemuExtendEntity> stockoutOrderTemuExtendList = temuExtendService.listByStockoutOrderNoList(request.getStockoutOrderNoList());
        //验证
        validCreateShiporder(isAuto, request, stockoutOrderList, stockoutOrderTemuExtendList);
        //2. 构建【创建发货单请求】
        TemuAuth temuAuth = temuExtendService.fetchTemuAuth(stockoutOrderList.get(0).getStoreId());
        StockoutTemuCreateShiporderBo stockoutTemuCreateShiporderBo = buildCreateShiporderRequest(stockoutOrderTemuExtendList, temuAuth);
        TemuCreateShiporderV2Request createShiporderRequest = stockoutTemuCreateShiporderBo.getCreateShiporderV2Request();
        if (CollectionUtils.isEmpty(createShiporderRequest.getDeliveryOrderCreateGroupList()))
            throw new BusinessServiceException("发货单创建失败");
        //3. 判断是否自动创建
        TemuGetLogisticsMatchResponse.ResultItem logisticsMatch;
        BigDecimal totalWeight = stockoutTemuCreateShiporderBo.getWeight();
        int totalPackageNum = stockoutTemuCreateShiporderBo.getTotalPackageNum();
        if (isAuto) {
            BdSystemParameterEntity systemParameterEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.TEMU_EXPRESS_COMPANY_BLACKLIST.getKey());
            if (Objects.isNull(systemParameterEntity))
                throw new BusinessServiceException("未配置系统参数 拼多多快递黑名单");
            String blacklist = systemParameterEntity.getConfigValue();
            List<String> blacklistList = StrUtil.split(blacklist, ",");
            List<TemuGetLogisticsMatchResponse.ResultItem> logisticsMatchList = getLogisticsMatchList(createShiporderRequest.getDeliveryOrderCreateGroupList().get(0), temuAuth, totalPackageNum, totalWeight);
            logisticsMatch = logisticsMatchList.stream().filter(tempLogisticsMatch -> !blacklistList.contains(tempLogisticsMatch.getExpressCompanyName()))
                    .findFirst().orElseThrow(() -> new BusinessServiceException(String.format("选择不到物流商 %s", JsonMapper.toJson(logisticsMatchList))));
        } else {
            List<TemuGetLogisticsMatchResponse.ResultItem> logisticsMatchList = getLogisticsMatchList(createShiporderRequest.getDeliveryOrderCreateGroupList().get(0), temuAuth, totalPackageNum, totalWeight);
            logisticsMatch = logisticsMatchList.stream().filter(tempLogisticsMatch -> tempLogisticsMatch.getExpressCompanyName().equals(request.getDeliveryCompany()))
                    .findFirst().orElseThrow(() -> new BusinessServiceException(String.format("物流商 %s 非推荐物流", request.getDeliveryCompany())));
        }
        //4. 设置【物流信息】给【创建发货单请求】
        createShiporderRequest.getDeliveryOrderCreateGroupList().get(0).setThirdPartyDeliveryInfo(buildThirdPartyDeliveryInfo(logisticsMatch, totalWeight, totalPackageNum, request.getExpectPickUpGoodsTime()));
        //5. 更新物流公司
        updateDeliveryCompany(request.getStockoutOrderNoList(), logisticsMatch.getExpressCompanyId(), logisticsMatch.getExpressCompanyName());
        //6. 创建发货单
        thirdPartyApiService.createShiporderV2(createShiporderRequest);
    }


    /**
     * 获取快递公司列表
     *
     * @param request
     * @return
     */
    public List<String> getDeliveryCompanyList(StockoutTemuExtendCreateShiporderRequest request) {
        //1. 验证参数是否正确
        if (CollectionUtils.isEmpty(request.getStockoutOrderNoList()))
            throw new BusinessServiceException("未选择");
        List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.getByStockoutOrderNoList(request.getStockoutOrderNoList());
        List<StockoutOrderTemuExtendEntity> stockoutOrderTemuExtendList = temuExtendService.listByStockoutOrderNoList(request.getStockoutOrderNoList());
        //验证
        validCreateShiporder(Boolean.FALSE, request, stockoutOrderList, stockoutOrderTemuExtendList);
        List<StockoutShipmentItemEntity> shipmentItemList = shipmentItemService.findItemByStockoutOrderNo(request.getStockoutOrderNoList());
        if (CollectionUtils.isEmpty(shipmentItemList)) throw new BusinessServiceException("装箱清单明细为空");
        List<Integer> shipmentIdList = shipmentItemList.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        List<StockoutShipmentEntity> shipmentList = stockoutShipmentService.findByShipmentIdsList(shipmentIdList);
        if (CollectionUtils.isEmpty(shipmentList)) throw new BusinessServiceException("装箱清单为空");
        //2. 构建【创建发货单请求】
        TemuAuth temuAuth = temuExtendService.fetchTemuAuth(stockoutOrderList.get(0).getStoreId());
        TemuCreateShiporderV2Request createShiporderRequest = buildCreateShiporderRequest(stockoutOrderTemuExtendList, temuAuth).getCreateShiporderV2Request();
        if (CollectionUtils.isEmpty(createShiporderRequest.getDeliveryOrderCreateGroupList()))
            throw new BusinessServiceException("发货单创建失败");
        BigDecimal totalWeight = shipmentList.stream().map(temp -> Optional.ofNullable(temp.getWeight()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(1000))).reduce(BigDecimal.ZERO, BigDecimal::add);
        int totalPackageNum = shipmentIdList.size();
        List<TemuGetLogisticsMatchResponse.ResultItem> logisticsMatchList = getLogisticsMatchList(createShiporderRequest.getDeliveryOrderCreateGroupList().get(0), temuAuth, totalPackageNum, totalWeight);
        BdSystemParameterEntity systemParameterEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.TEMU_EXPRESS_COMPANY_BLACKLIST.getKey());
        if (Objects.isNull(systemParameterEntity))
            throw new BusinessServiceException("未配置系统参数 拼多多快递黑名单");
        String blacklist = systemParameterEntity.getConfigValue();
        List<String> blacklistList = StrUtil.split(blacklist, ",");
        List<TemuGetLogisticsMatchResponse.ResultItem> filterLogisticsMatchList = logisticsMatchList.stream().filter(tempLogisticsMatch -> !blacklistList.contains(tempLogisticsMatch.getExpressCompanyName())).collect(Collectors.toList());
        return filterLogisticsMatchList.stream().map(TemuGetLogisticsMatchResponse.ResultItem::getExpressCompanyName).collect(Collectors.toList());
    }

    /**
     * 构建物流信息对象
     *
     * @param logisticsMatch
     * @param totalWeight
     * @param totalPackageNum
     * @param expectPickUpGoodsTime
     * @return
     */
    private TemuCreateShiporderV2Request.ThirdPartyDeliveryInfo buildThirdPartyDeliveryInfo(TemuGetLogisticsMatchResponse.ResultItem logisticsMatch,
                                                                                            BigDecimal totalWeight, Integer totalPackageNum,
                                                                                            String expectPickUpGoodsTime) {
        TemuCreateShiporderV2Request.ThirdPartyDeliveryInfo thirdPartyDeliveryInfo = BeanUtil.toBean(logisticsMatch, TemuCreateShiporderV2Request.ThirdPartyDeliveryInfo.class);
        thirdPartyDeliveryInfo.setPredictTotalPackageWeight(totalWeight.longValue());
        thirdPartyDeliveryInfo.setExpressPackageNum(Long.valueOf(totalPackageNum));
        thirdPartyDeliveryInfo.setExpectPickUpGoodsTime(DateUtil.parse(expectPickUpGoodsTime, "yyyy-MM-dd HH:mm:ss").getTime());
        return thirdPartyDeliveryInfo;
    }

    /**
     * 获取匹配的物流列表
     *
     * @param createShiporderRequest
     * @param temuAuth
     * @param totalPackageNum
     * @param totalWeight
     * @return
     */
    private List<TemuGetLogisticsMatchResponse.ResultItem> getLogisticsMatchList(TemuCreateShiporderV2Request.DeliveryOrderCreateGroupListItem createShiporderRequest,
                                                                                 TemuAuth temuAuth, Integer totalPackageNum, BigDecimal totalWeight) {
        TemuGetLogisticsMatchRequest getLogisticsMatchRequest = new TemuGetLogisticsMatchRequest();
        getLogisticsMatchRequest.setTotalPackageNum(totalPackageNum);
        getLogisticsMatchRequest.setPredictTotalPackageWeight(totalWeight.longValue());
        getLogisticsMatchRequest.setDeliveryAddressId(createShiporderRequest.getDeliveryAddressId());
        getLogisticsMatchRequest.setSubWarehouseId(createShiporderRequest.getSubWarehouseId());
        getLogisticsMatchRequest.setReceiveAddressInfo(createShiporderRequest.getReceiveAddressInfo());
        getLogisticsMatchRequest.setDeliveryOrderCreateInfos(createShiporderRequest.getDeliveryOrderCreateInfos());
        getLogisticsMatchRequest.setSubPurchaseOrderSnList(createShiporderRequest.getDeliveryOrderCreateInfos().stream()
                .map(TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem::getSubPurchaseOrderSn).collect(Collectors.toList()));
        getLogisticsMatchRequest.setTemuAuth(temuAuth);
        return thirdPartyApiService.getLogisticsMatch(getLogisticsMatchRequest);
    }

    /**
     * 获取收货地址信息
     *
     * @param temuAuth
     * @param subPurchaseOrderSnList
     * @return
     */
    private TemuCreateShiporderV2Request.ReceiveAddressInfo getReceiveAddress(TemuAuth temuAuth, List<String> subPurchaseOrderSnList) {
        TemuGetReceiveAddressRequest request = new TemuGetReceiveAddressRequest();
        request.setTemuAuth(temuAuth);
        request.setSubPurchaseOrderSnList(subPurchaseOrderSnList);
        //收货地址
        TemuGetReceiveAddressV2Response.Result receiveAddress = thirdPartyApiService.getShiporderReceiveaddressV2(request);
        int size = receiveAddress.getSubPurchaseReceiveAddressGroups().size();
        if (size != 1)
            throw new BusinessServiceException(String.format("获取收货地址数量不唯一 %s", size));

        return BeanUtil.toBean(receiveAddress.getSubPurchaseReceiveAddressGroups().get(0).getReceiveAddressInfo(), TemuCreateShiporderV2Request.ReceiveAddressInfo.class);
    }


    /**
     * 构建发货单请求
     */
    private StockoutTemuCreateShiporderBo buildCreateShiporderRequest(List<StockoutOrderTemuExtendEntity> extendEntityList, TemuAuth temuAuth) {
        StockoutTemuCreateShiporderBo bo = new StockoutTemuCreateShiporderBo();
        TemuCreateShiporderV2Request.DeliveryOrderCreateGroupListItem orderCreateGroupListItem = new TemuCreateShiporderV2Request.DeliveryOrderCreateGroupListItem();
        List<String> stockoutOrderNoList = extendEntityList.stream().map(StockoutOrderTemuExtendEntity::getStockoutOrderNo).collect(Collectors.toList());
        orderCreateGroupListItem.setDeliveryOrderCreateInfos(buildDeliveryOrderCreateInfos(stockoutOrderNoList, bo));
        orderCreateGroupListItem.setDeliveryAddressId(getMallAddressId(temuAuth));
        //获取发货地址
        orderCreateGroupListItem.setReceiveAddressInfo(getReceiveAddress(temuAuth, extendEntityList.stream().map(StockoutOrderTemuExtendEntity::getOrderNo).collect(Collectors.toList())));
        orderCreateGroupListItem.setSubWarehouseId(extendEntityList.get(0).getSubWarehouseId());
        //发货方式
        orderCreateGroupListItem.setDeliverMethod(2L);
        //创建发货单
        TemuCreateShiporderV2Request createShiporderV2Request = new TemuCreateShiporderV2Request();
        createShiporderV2Request.setDeliveryOrderCreateGroupList(new ArrayList<>());
        createShiporderV2Request.getDeliveryOrderCreateGroupList().add(orderCreateGroupListItem);
        createShiporderV2Request.setTemuAuth(temuAuth);
        bo.setCreateShiporderV2Request(createShiporderV2Request);
        return bo;
    }


    /**
     * 更新快递公司信息
     *
     * @param stockoutOrderNos
     * @param companyId
     * @param companyName
     */
    public void updateDeliveryCompany(List<String> stockoutOrderNos, Long companyId, String companyName) {
        if (stockoutOrderNos.isEmpty()) return;
        temuExtendService.update(new LambdaUpdateWrapper<StockoutOrderTemuExtendEntity>()
                .set(StockoutOrderTemuExtendEntity::getExpressCompanyId, companyId)
                .set(StockoutOrderTemuExtendEntity::getExpressCompanyName, companyName)
                .set(StockoutOrderTemuExtendEntity::getUpdateBy, loginInfoService.getName())
                .set(StockoutOrderTemuExtendEntity::getShiporderCreateDate, new Date())
                .in(StockoutOrderTemuExtendEntity::getStockoutOrderNo, stockoutOrderNos));

        List<StockoutShipmentEntity> shipmentList = shipmentItemService.findByStockoutOrderNo(stockoutOrderNos);
        if (shipmentList.isEmpty()) return;
        stockoutShipmentService.update(new LambdaUpdateWrapper<StockoutShipmentEntity>()
                .set(StockoutShipmentEntity::getLogisticsCompany, companyName)
                .in(StockoutShipmentEntity::getShipmentId, shipmentList.stream().map(StockoutShipmentEntity::getShipmentId).distinct().collect(Collectors.toList())));
    }

    /**
     * 获取发货地址ID
     *
     * @param temuAuth
     * @return
     */
    private Long getMallAddressId(TemuAuth temuAuth) {
        TemuGetMallAddressRequest request = new TemuGetMallAddressRequest();
        request.setTemuAuth(temuAuth);
        TemuMallAddressGetResponse response = thirdPartyApiService.getMallAddress(request);
        List<TemuMallAddressGetResponse.ResultItem> defaultAddressList = response.getResult().stream().filter(TemuMallAddressGetResponse.ResultItem::getIsDefault).collect(Collectors.toList());
        if (defaultAddressList.isEmpty()) throw new BusinessServiceException("查询默认发货地址为空");
        return defaultAddressList.get(0).getId();
    }

    /**
     * 构建明细列表
     *
     * @param stockoutOrderNos
     * @return
     */
    private List<TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem> buildDeliveryOrderCreateInfos(List<String> stockoutOrderNos, StockoutTemuCreateShiporderBo bo) {
        //获取temu采购单
        TemuGetPurchaseOrderInfoListResponse getPurchaseOrderInfoListResponse = stockoutOrderTemuExtendInfoService.fetchPurchaseOrderInfoWithoutAddr(stockoutOrderNos);
        Map<String, List<TemuPurchaseorderGetResponse.SkuQuantityDetailListItem>> skuQuantityDetailListItemMap = getPurchaseOrderInfoListResponse.getItemList().stream()
                .filter(temp -> Objects.nonNull(temp.getPurchaseOrder()))
                .collect(Collectors.toMap(TemuGetPurchaseOrderInfoResponse::getPurchaseOrderNo, temp -> temp.getPurchaseOrder().getSkuQuantityDetailList(), (v1, v2) -> v1));
        return stockoutOrderNos.stream().map(stockoutOrderNo -> {
            StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
            //准备中提前创建发货单
            if (StockoutOrderStatusEnum.READY.name().equals(stockoutOrder.getStatus())) {
                return buildDeliveryOrderCreateInfosItem(stockoutOrderNo, skuQuantityDetailListItemMap, bo);
            }

            //待发货创建发货单
            List<StockoutShipmentItemEntity> shipmentItemList = shipmentItemService.findItemByStockoutOrderNo(stockoutOrderNo);
            if (shipmentItemList.isEmpty())
                throw new BusinessServiceException(String.format("出库单【%s】装箱明细为空", stockoutOrderNo));

            return buildDeliveryOrderCreateInfosItem(shipmentItemList.get(0).getOrderNo(), shipmentItemList, skuQuantityDetailListItemMap, bo);
        }).collect(Collectors.toList());
    }


    /**
     * 构建明细  (准备中)
     *
     * @param stockoutOrderNo
     * @param skuQuantityDetailListItemMap
     * @return
     */
    private TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem buildDeliveryOrderCreateInfosItem(String stockoutOrderNo,
                                                                                                        Map<String, List<TemuPurchaseorderGetResponse.SkuQuantityDetailListItem>> skuQuantityDetailListItemMap,
                                                                                                        StockoutTemuCreateShiporderBo bo) {
        List<StockoutOrderItemEntity> stockoutOrderItemList = stockoutOrderItemService.getListByStockoutOrderNo(stockoutOrderNo);
        if (stockoutOrderItemList.isEmpty())
            throw new BusinessServiceException(String.format("出库单明细【%s】不存在", stockoutOrderNo));

        TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem infosItem = new TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem();
        String orderNo = stockoutOrderItemList.get(0).getOrderNo();
        infosItem.setSubPurchaseOrderSn(orderNo);
        Map<String, Integer> itemSumMap = stockoutOrderItemList.stream().collect(Collectors.groupingBy(StockoutOrderItemEntity::getSku, Collectors.summingInt(StockoutOrderItemEntity::getQty)));
        infosItem.setDeliverOrderDetailInfos(new ArrayList<>());
        itemSumMap.forEach((itemKey, itemValue) -> {
            Long productSkuId = extendItemService.getProductSkuIdBySku(stockoutOrderNo, itemKey);
            TemuCreateShiporderV2Request.DeliverOrderDetailInfosItem detailInfosItem = new TemuCreateShiporderV2Request.DeliverOrderDetailInfosItem();
            detailInfosItem.setProductSkuId(productSkuId);
            detailInfosItem.setDeliverSkuNum(itemValue.longValue());
            infosItem.getDeliverOrderDetailInfos().add(detailInfosItem);
            //计算重量
            bo.setWeight(bo.getWeight().add(productSpecInfoService.getWeight(itemKey, itemValue)));
        });
        infosItem.setPackageInfos(new ArrayList<>());
        //默认只装一箱
        bo.setTotalPackageNum(bo.getTotalPackageNum() + 1);
        TemuCreateShiporderV2Request.PackageInfosItem packageInfosItem = new TemuCreateShiporderV2Request.PackageInfosItem();
        packageInfosItem.setPackageDetailSaveInfos(new ArrayList<>());
        itemSumMap.forEach((itemSumKey, itemSumValue) -> {
            Long productSkuId = extendItemService.getProductSkuIdBySku(stockoutOrderNo, itemSumKey);
            TemuCreateShiporderV2Request.PackageDetailSaveInfosItem detailSaveInfosItem = new TemuCreateShiporderV2Request.PackageDetailSaveInfosItem();
            detailSaveInfosItem.setProductSkuId(productSkuId);
            detailSaveInfosItem.setSkuNum(Long.valueOf(itemSumValue));
            packageInfosItem.getPackageDetailSaveInfos().add(detailSaveInfosItem);
        });
        infosItem.getPackageInfos().add(packageInfosItem);
        //处理缺货删除的sku
        buildLack(infosItem, skuQuantityDetailListItemMap, orderNo);
        return infosItem;
    }

    /**
     * 构建明细  （待发货）
     *
     * @param orderNo
     * @param shipmentItemList
     * @param skuQuantityDetailListItemMap
     * @return
     */
    private TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem buildDeliveryOrderCreateInfosItem(String orderNo, List<StockoutShipmentItemEntity> shipmentItemList,
                                                                                                        Map<String, List<TemuPurchaseorderGetResponse.SkuQuantityDetailListItem>> skuQuantityDetailListItemMap,
                                                                                                        StockoutTemuCreateShiporderBo bo) {
        String stockoutOrderNo = shipmentItemList.get(0).getStockoutOrderNo();
        TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem infosItem = new TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem();
        infosItem.setSubPurchaseOrderSn(orderNo);
        Map<String, Integer> itemSumMap = shipmentItemList.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getSku, Collectors.summingInt(StockoutShipmentItemEntity::getQty)));
        infosItem.setDeliverOrderDetailInfos(new ArrayList<>());
        itemSumMap.forEach((itemKey, itemValue) -> {
            Long productSkuId = extendItemService.getProductSkuIdBySku(stockoutOrderNo, itemKey);
            TemuCreateShiporderV2Request.DeliverOrderDetailInfosItem detailInfosItem = new TemuCreateShiporderV2Request.DeliverOrderDetailInfosItem();
            detailInfosItem.setProductSkuId(productSkuId);
            detailInfosItem.setDeliverSkuNum(itemValue.longValue());
            infosItem.getDeliverOrderDetailInfos().add(detailInfosItem);
            //计算重量
            bo.setWeight(bo.getWeight().add(productSpecInfoService.getWeight(itemKey, itemValue)));
        });
        infosItem.setPackageInfos(new ArrayList<>());
        Map<Integer, List<StockoutShipmentItemEntity>> itemShipmentMap = shipmentItemList.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getShipmentId));
        itemShipmentMap.values().stream()
                .sorted(Comparator.comparing(o -> o.get(0).getCreateDate()))
                .forEach(itemValue -> {
                    Map<String, Integer> tempItemSumMap = itemValue.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getSku, Collectors.summingInt(StockoutShipmentItemEntity::getQty)));
                    TemuCreateShiporderV2Request.PackageInfosItem packageInfosItem = new TemuCreateShiporderV2Request.PackageInfosItem();
                    packageInfosItem.setPackageDetailSaveInfos(new ArrayList<>());
                    tempItemSumMap.forEach((itemSumKey, itemSumValue) -> {
                        Long productSkuId = extendItemService.getProductSkuIdBySku(stockoutOrderNo, itemSumKey);
                        TemuCreateShiporderV2Request.PackageDetailSaveInfosItem detailSaveInfosItem = new TemuCreateShiporderV2Request.PackageDetailSaveInfosItem();
                        detailSaveInfosItem.setProductSkuId(productSkuId);
                        detailSaveInfosItem.setSkuNum(Long.valueOf(itemSumValue));
                        packageInfosItem.getPackageDetailSaveInfos().add(detailSaveInfosItem);
                    });
                    infosItem.getPackageInfos().add(packageInfosItem);
                    //计算箱数
                    bo.setTotalPackageNum(bo.getTotalPackageNum() + 1);
                });
        //处理缺货删除的sku
        buildLack(infosItem, skuQuantityDetailListItemMap, orderNo);
        return infosItem;
    }

    /**
     * 处理缺货删除的sku
     *
     * @param infosItem
     * @param skuQuantityDetailListItemMap
     * @param orderNo
     */
    private void buildLack(TemuCreateShiporderV2Request.DeliveryOrderCreateInfosItem infosItem, Map<String, List<TemuPurchaseorderGetResponse.SkuQuantityDetailListItem>> skuQuantityDetailListItemMap, String orderNo) {
        //处理缺货删除的sku
        List<TemuPurchaseorderGetResponse.SkuQuantityDetailListItem> skuQuantityDetailListItems = skuQuantityDetailListItemMap.get(orderNo);
        if (Objects.isNull(skuQuantityDetailListItems))
            throw new BusinessServiceException(String.format("找不到采购单 %s", orderNo));
        if (skuQuantityDetailListItems.isEmpty())
            throw new BusinessServiceException(String.format("sku明细为空 %s", orderNo));
        List<Long> productSkuIdList = infosItem.getDeliverOrderDetailInfos().stream().map(TemuCreateShiporderV2Request.DeliverOrderDetailInfosItem::getProductSkuId).collect(Collectors.toList());
        skuQuantityDetailListItems.stream()
                .map(TemuPurchaseorderGetResponse.SkuQuantityDetailListItem::getProductSkuId)
                .filter(productSkuId -> !productSkuIdList.contains(productSkuId))
                .forEach(productSkuId -> {
                    TemuCreateShiporderV2Request.DeliverOrderDetailInfosItem detailInfosItem = new TemuCreateShiporderV2Request.DeliverOrderDetailInfosItem();
                    detailInfosItem.setProductSkuId(productSkuId);
                    detailInfosItem.setDeliverSkuNum(0L);
                    infosItem.getDeliverOrderDetailInfos().add(detailInfosItem);

                    TemuCreateShiporderV2Request.PackageDetailSaveInfosItem detailSaveInfosItem = new TemuCreateShiporderV2Request.PackageDetailSaveInfosItem();
                    detailSaveInfosItem.setProductSkuId(productSkuId);
                    detailSaveInfosItem.setSkuNum(0L);
                    infosItem.getPackageInfos().get(0).getPackageDetailSaveInfos().add(detailSaveInfosItem);
                });
    }

    /**
     * 验证
     *
     * @param request
     * @param stockoutOrderList
     * @param stockoutOrderTemuExtendList
     */
    private void validCreateShiporder(Boolean isAuto, StockoutTemuExtendCreateShiporderRequest request, List<StockoutOrderEntity> stockoutOrderList, List<StockoutOrderTemuExtendEntity> stockoutOrderTemuExtendList) {
        if (stockoutOrderList.isEmpty())
            throw new BusinessServiceException("找不到出库单");
        if (!StringUtils.hasText(request.getExpectPickUpGoodsTime()))
            throw new BusinessServiceException("请输入预约取货时间");
        //手动需要填写物流公司
        if (!isAuto && !StringUtils.hasText(request.getDeliveryCompany()))
            throw new BusinessServiceException("请输入物流公司");
        //a. 判断出库单 【拼多多】
        boolean allPdd = stockoutOrderList.stream().allMatch(stockoutOrder -> StockoutOrderPlatformEnum.PDD.getName().equals(stockoutOrder.getPlatformName()));
        if (!allPdd) throw new BusinessServiceException("部分出库单非拼多多");
        //b. 判断店铺是否一样
        long distinctStore = stockoutOrderList.stream().map(StockoutOrderEntity::getStoreId).distinct().count();
        if (distinctStore != 1)
            throw new BusinessServiceException("店铺不相同");

        //判断是否有获取收货地址
        List<String> notGetReceiveAddressList = stockoutOrderTemuExtendList.stream()
                .filter(extendEntity -> 0L == extendEntity.getSubWarehouseId())
                .map(StockoutOrderTemuExtendEntity::getOrderNo).collect(Collectors.toList());
        if (!notGetReceiveAddressList.isEmpty())
            throw new BusinessServiceException(String.format("%s 存在未获取收货地址的订单", String.join(",", notGetReceiveAddressList)));
        //c. 判断子仓相同
        long distinctWarehouseCount = stockoutOrderTemuExtendList.stream().map(StockoutOrderTemuExtendEntity::getSubWarehouseShortname).distinct().count();
        if (distinctWarehouseCount != 1)
            throw new BusinessServiceException("收货子仓不相同");
        //d.判断是否急单和普通单混发
        long urgentNum = stockoutOrderList.stream().filter(StockoutOrderEntity::getUrgent).count();
        if (urgentNum != 0 && urgentNum != stockoutOrderList.size())
            throw new BusinessServiceException("急单和普单不能混发");
        //判断是否创建过发货单
        stockoutOrderTemuExtendList.forEach(extendEntity -> {
            if (StringUtils.hasText(extendEntity.getExpressCompanyName())) {
                throw new BusinessServiceException(String.format("出库单【%s】已经创建发货单", extendEntity.getStockoutOrderNo()));
            }
        });
    }

    /**
     * 1. 找出所有【已经创建发货单，但是未获取发货单的】（是否有快递公司id字段）,
     * 2. 按店铺分组，进行获取发货单
     * 3. 请求接口，回填发货单信息 扩展表 装箱清单 出库单
     */
    @Transactional
    public void getShiporderAndBackfill(List<Integer> stockoutOrderIdList) {
        //未获取发货单的列表
        List<StockoutUnGetShiporderBo> unGetShiporderList = temuExtendMapper.getUnGetShiporderList(stockoutOrderIdList);
        Map<Integer, List<StockoutUnGetShiporderBo>> unGetShiporderMap = unGetShiporderList.stream().collect(Collectors.groupingBy(StockoutUnGetShiporderBo::getStoreId));
        unGetShiporderMap.forEach((storeId, boList) -> {
            TemuAuth temuAuth = temuExtendService.fetchTemuAuth(storeId);
            TemuGetShiporderRequest request = new TemuGetShiporderRequest();
            request.setTemuAuth(temuAuth);
            request.setPageNo(1L);
            request.setPageSize(100L);
            boList.forEach(bo -> {
                try {
                    SpringUtil.getBean(StockoutOrderTemuExtendShiporderService.class).getShiporderAndBackfill(request, Collections.singletonList(bo));
                    Thread.sleep(100);
                } catch (Exception e) {
                    LOGGER.error(String.format("获取发货单失败 %s: %s", bo.getOrderNo(), e.getMessage()), e);
                }
            });
        });
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void getShiporderAndBackfill(TemuGetShiporderRequest request, List<StockoutUnGetShiporderBo> boList) {
        List<String> orderNoList = boList.stream().map(StockoutUnGetShiporderBo::getOrderNo).collect(Collectors.toList());
        request.setSubPurchaseOrderSnList(orderNoList);
        List<TemuShiporderGetResponse.ListItem> shiporderGetList = thirdPartyApiService.getShiporder(request).getResult().getList();
        //回填
        //待发货：0 待收货：1 已收货：2 已入库：3 已退货：4 已取消：5 部分收货：6
        List<Long> statusList = Arrays.asList(0L, 1L, 2L, 3L, 6L);
        List<String> hasFillBackOrderNoList = shiporderGetList.stream()
                .filter(listItem -> statusList.contains(listItem.getStatus()))
                .map(listItem -> {
                    backFill(listItem);
                    return listItem.getSubPurchaseOrderSn();
                }).collect(Collectors.toList());
        //创建过发货单而且，超过半小时获取不到，则重新创建
        boList.forEach(bo -> {
            if (!hasFillBackOrderNoList.contains(bo.getOrderNo())
                    && ObjectUtil.isNotNull(bo.getShiporderCreateDate())
                    && DateUtil.offset(bo.getShiporderCreateDate(), DateField.MINUTE, 30).before(new Date())) {
                LOGGER.info("重置 {} ", bo.getOrderNo());
                temuExtendService.update(new LambdaUpdateWrapper<StockoutOrderTemuExtendEntity>()
                        .set(StockoutOrderTemuExtendEntity::getExpressCompanyId, null)
                        .set(StockoutOrderTemuExtendEntity::getExpressCompanyName, null)
                        .set(StockoutOrderTemuExtendEntity::getShiporderCreateDate, null)
                        .eq(StockoutOrderTemuExtendEntity::getOrderNo, bo.getOrderNo()));
            }
        });
    }

    /**
     * 回填
     *
     * @param listItem
     */
    private void backFill(TemuShiporderGetResponse.ListItem listItem) {
        List<StockoutOrderTemuExtendEntity> extendList = temuExtendService.listByOrderNo(listItem.getSubPurchaseOrderSn());
        if (extendList.isEmpty()) {
            LOGGER.error("找不到对应扩展表记录，订单号 {}", listItem.getSubPurchaseOrderSn());
            return;
        }
        extendList.forEach(extendEntity -> {
            //出库单
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getById(extendEntity.getStockoutOrderId());
            if (Objects.isNull(stockoutOrderEntity)) {
                LOGGER.error("找不到对应出库单记录，订单号 {}", listItem.getSubPurchaseOrderSn());
                return;
            }
            //已取消的出库单不回填
            if (StockoutOrderStatusEnum.CANCELLED.name().equals(stockoutOrderEntity.getStatus())
                    || StockoutOrderStatusEnum.CANCELLING.name().equals(stockoutOrderEntity.getStatus())) {
                return;
            }

            //扩展表
            extendEntity.setDeliveryOrderSn(listItem.getDeliveryOrderSn());
            extendEntity.setDeliveryMethod(Math.toIntExact(listItem.getDeliveryMethod()));
            extendEntity.setExpressDeliverySn(listItem.getExpressDeliverySn());
            extendEntity.setUpdateBy(loginInfoService.getName());
            if (Objects.isNull(extendEntity.getShiporderCreateDate())) extendEntity.setShiporderCreateDate(new Date());
            temuExtendService.updateById(extendEntity);

            stockoutOrderEntity.setLogisticsCompany(extendEntity.getExpressCompanyName());
            stockoutOrderEntity.setLogisticsNo(listItem.getExpressDeliverySn());
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            stockoutOrderService.updateById(stockoutOrderEntity);

            //记录日志
            stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.PDD_CREATE_SHIPORDER, String.format("拼多多创建发货单，发货单号【%s】，物流公司【%s】，物流单号【%s】",
                    extendEntity.getDeliveryOrderSn(), extendEntity.getExpressCompanyName(), extendEntity.getExpressDeliverySn()));

            if (StockoutOrderStatusEnum.READY.name().equals(stockoutOrderEntity.getStatus())) {
                stockoutOrderEntity = stockoutOrderService.getById(extendEntity.getStockoutOrderId());
                stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
            } else {
                //装箱清单
                List<StockoutShipmentEntity> shipmentList = shipmentItemService.findByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
                if (shipmentList.isEmpty()) {
                    LOGGER.error("找不到对应装箱清单记录，订单号 {}", listItem.getSubPurchaseOrderSn());
                    return;
                }
                shipmentList.forEach(stockoutShipmentEntity -> {
                    stockoutShipmentEntity.setLogisticsCompany(extendEntity.getExpressCompanyName());
                    stockoutShipmentEntity.setLogisticsNo(listItem.getExpressDeliverySn());
                    stockoutShipmentEntity.setUpdateBy(loginInfoService.getName());
                });
                stockoutShipmentService.updateBatchById(shipmentList);
            }
        });
    }
}