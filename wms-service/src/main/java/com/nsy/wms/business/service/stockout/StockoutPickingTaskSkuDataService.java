package com.nsy.wms.business.service.stockout;

import com.nsy.api.wms.domain.stockout.StockoutPickingTaskSkuData;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StockoutPickingTaskSkuDataService {


    public static StockoutPickingTaskSkuData stockoutPickingTaskSkuData(List<StockoutPickingTaskItemEntity> value) {
        StockoutPickingTaskSkuData stockoutPickingTaskSkuData = new StockoutPickingTaskSkuData();
        StockoutPickingTaskItemEntity taskItemEntity = value.get(0);
        stockoutPickingTaskSkuData.setSku(StringUtils.hasText(taskItemEntity.getCustomerSku()) ? taskItemEntity.getCustomerSku() : taskItemEntity.getSku());
        stockoutPickingTaskSkuData.setBarcode(taskItemEntity.getBarcode());
        stockoutPickingTaskSkuData.setIsLack(value.stream().anyMatch(entity -> entity.getIsLack().equals(1)) ? 1 : 0);
        stockoutPickingTaskSkuData.setLackQty(value.stream().mapToInt(StockoutPickingTaskItemEntity::getLackQty).sum());
        stockoutPickingTaskSkuData.setPickedQty(value.stream().mapToInt(StockoutPickingTaskItemEntity::getPickedQty).sum());
        stockoutPickingTaskSkuData.setExpectedQty(value.stream().mapToInt(StockoutPickingTaskItemEntity::getExpectedQty).sum());
        stockoutPickingTaskSkuData.setQty(value.stream().mapToInt(StockoutPickingTaskItemEntity::getPickedQty).sum());
        // 加工的拣货任务明细，需要取加工完成数
        if (StringUtils.hasText(taskItemEntity.getCustomerSku()) && !taskItemEntity.getCustomerSku().equals(taskItemEntity.getSku())) {
            stockoutPickingTaskSkuData.setQty(value.stream().mapToInt(StockoutPickingTaskItemEntity::getProcessCompleteQty).sum());
        }
        stockoutPickingTaskSkuData.setStockoutOrderNo(taskItemEntity.getStockoutOrderNo());
        return stockoutPickingTaskSkuData;
    }

}
