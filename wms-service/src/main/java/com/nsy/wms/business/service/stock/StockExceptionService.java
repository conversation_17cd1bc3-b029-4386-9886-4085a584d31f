package com.nsy.wms.business.service.stock;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stockin.StockinOrderItem;
import com.nsy.api.wms.domain.stockin.StockinShelveTaskItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipment;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.api.wms.request.stock.StockReturnSkuRequest;
import com.nsy.api.wms.request.stockin.StockOrderStatusExceptionRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.business.service.stockin.StockinOrderLogService;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskItemService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutPickingExceptionService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.mq.QMessage;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockShippingPositionEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.transaction.Transactional;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockExceptionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockExceptionService.class);

    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockService stockService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockinOrderLogService stockinOrderLogService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockShippingPositionService stockShippingPositionService;
    @Autowired
    StockoutPickingExceptionService stockoutPickingExceptionService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockinShelveTaskService stockinShelveTaskService;
    @Autowired
    StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    private StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    private StockoutShipmentService stockoutShipmentService;

    @Transactional
    public void returnSku(StockReturnSkuRequest stockReturnSkuRequest) {
        stockReturnSkuRequest.getStockReturnSkuList().stream().distinct().forEach(request -> {
            StockInternalBox internalBox = stockInternalBoxService.getInternalBox(request.getInternalBoxCode());
            if (StockInternalBoxTypeEnum.TRANSFER_BOX.name().equals(internalBox.getInternalBoxType()))
                throw new BusinessServiceException("调拨箱请使用调拨功能！");
            List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, request.getInternalBoxCode())
                .eq(StockInternalBoxItemEntity::getSku, request.getSku()));
            if (CollectionUtils.isEmpty(list))
                throw new BusinessServiceException(String.format("未找到【%s】【%s】的内部箱", request.getInternalBoxCode(), request.getSku()));
            List<StockInternalBoxItemEntity> itemList = list.stream().filter(item -> StringUtils.hasText(item.getSourcePositionCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemList))
                throw new BusinessServiceException(String.format("【%s】【%s】没有来源库位，无法归还", request.getInternalBoxCode(), request.getSku()));

            stockoutPickingExceptionService.returnSourcePosition(itemList, StockChangeLogTypeEnum.EXCEPTION_DEAL, "人工按钮清理");

        });
    }

    /**
     * 修复入库单状态
     *
     * @param request
     */
    @Transactional
    public void updateStockinOrderStatus(StockOrderStatusExceptionRequest request) {
        StockinOrderEntity stockinOrderEntity = stockinOrderService.getById(request.getId());
        String status = stockinOrderEntity.getStatus();
        StockinOrderEntity orderEntity = new StockinOrderEntity();
        orderEntity.setStatus(request.getStatus());
        orderEntity.setStockinOrderId(stockinOrderEntity.getStockinOrderId());
        orderEntity.setUpdateBy(loginInfoService.getName());
        stockinOrderService.updateById(orderEntity);
        String content = String.format("单据状态异常修复，从【%s】修复为【%s】", status, request.getStatus());
        stockinOrderLogService.addLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.DEAL_EXCEPTION.getName(), content);
    }

    @Transactional
    public void updateStockoutOrderStatus(StockOrderStatusExceptionRequest request) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderId(request.getId());
        String status = stockoutOrderEntity.getStatus();
        StockoutOrderEntity orderEntity = new StockoutOrderEntity();
        orderEntity.setStatus(request.getStatus());
        orderEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        orderEntity.setUpdateBy(loginInfoService.getName());
        stockoutOrderService.updateById(orderEntity);
        String content = String.format("单据状态异常修复，从【%s】修复为【%s】", status, request.getStatus());
        stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.CHANGE_STATUS, content);
    }

    /**
     * 清除发货库位记录表
     *
     * @param request
     */
    @Transactional
    public void clearStockShippingPosition(StringListRequest request) {
        if (CollectionUtils.isEmpty(request.getStringList()))
            return;
        List<StockShippingPositionEntity> list = stockShippingPositionService.list(new LambdaQueryWrapper<StockShippingPositionEntity>()
            .in(StockShippingPositionEntity::getStockoutOrderNo, request.getStringList()));
        LOGGER.info("清除发货库位记录表 : {} ", JsonMapper.toJson(list));
        stockShippingPositionService.removeByIds(list.stream().map(StockShippingPositionEntity::getId).collect(Collectors.toList()));
    }

    @Transactional
    public void sendMessage(QMessage qMessage) {
        messageProducer.sendMessage(qMessage.getBusinessMark(), qMessage.getDestination(), qMessage.getMessageContent());
    }

    //修复上架任务状态
    @Transactional
    public void updateStockinShelveTaskStatus(StockOrderStatusExceptionRequest request) {
        StockinShelveTaskEntity byId = stockinShelveTaskService.getById(request.getId());
        byId.setStatus(request.getStatus());
        byId.setUpdateBy(loginInfoService.getName());
        stockinShelveTaskService.updateById(byId);
    }

    //修复上架任务明细
    @Transactional
    public void updateStockinShelveTaskItem(StockinShelveTaskItemInfo request) {
        StockinShelveTaskItemEntity byId = stockinShelveTaskItemService.getById(request.getShelveTaskItemId());
        if (StringUtils.hasText(request.getStatus()))
            byId.setStatus(request.getStatus());
        if (Objects.nonNull(request.getStockinQty()))
            byId.setStockinQty(request.getStockinQty());
        if (Objects.nonNull(request.getShelvedQty()))
            byId.setShelvedQty(request.getShelvedQty());
        if (Objects.nonNull(request.getReturnedQty()))
            byId.setReturnedQty(request.getReturnedQty());
        stockinShelveTaskItemService.updateById(byId);

    }

    @Transactional
    public void updateStockinOrderItem(StockinOrderItem request) {
        StockinOrderItemEntity byId = stockinOrderItemService.getById(request.getStockinOrderItemId());
        if (StringUtils.hasText(request.getStatus()))
            byId.setStatus(request.getStatus());
        if (Objects.nonNull(request.getQty()))
            byId.setQty(request.getQty());
        if (Objects.nonNull(request.getShelvedQty()))
            byId.setShelvedQty(request.getShelvedQty());
        if (Objects.nonNull(request.getReturnQty()))
            byId.setReturnQty(request.getReturnQty());
        stockinOrderItemService.updateById(byId);
    }

    @Transactional
    public void updateStockoutShipment(StockoutShipment request) {
        StockoutShipmentEntity stockoutShipmentEntity = new StockoutShipmentEntity();
        BeanUtils.copyProperties(request, stockoutShipmentEntity);
        stockoutShipmentService.updateById(stockoutShipmentEntity);
    }

    public void updateStockinOrderTaskItem(@Valid StockinOrderTaskItemEntity request) {
        StockinOrderTaskItemEntity taskItemEntity = stockinOrderTaskItemService.getById(request.getTaskItemId());
        if (StringUtils.hasText(request.getAreaName()))
            taskItemEntity.setAreaName(request.getAreaName());
        if (Objects.nonNull(request.getExpectedQty()))
            taskItemEntity.setExpectedQty(request.getExpectedQty());
        taskItemEntity.setUpdateBy(loginInfoService.getName());
        stockinOrderTaskItemService.updateById(taskItemEntity);
    }
}
