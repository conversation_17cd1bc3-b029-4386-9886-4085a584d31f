package com.nsy.wms.business.service.internal.common;

import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.business.base.constant.KafkaTopicConstant;
import com.nsy.business.base.enums.etl.BusinessTypeEnum;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.response.SaStoreDetailResponse;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.repository.entity.bd.BdErpSpaceMappingEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-04-07 9:20
 */
@Service
public class FbaFactoryDeliveryHandlerService implements StockoutPushHandlerStage {

    private static final Logger LOGGER = LoggerFactory.getLogger(FbaFactoryDeliveryHandlerService.class);

    @Autowired
    CommonBaseService commonBaseService;

    @Autowired
    StockoutShipmentItemService stockoutShipmentItemService;

    @Autowired
    OmsApiService omsApiService;
    @Autowired
    BdErpSpaceMappingService erpSpaceMappingService;

    @Override
    public String getHandlerType() {
        return StockoutOrderTypeEnum.FBA_FACTORY_DELIVERY.name();
    }

    @Override
    public void handlerOrder(StockoutOrderEntity stockoutOrderEntity, StockoutShipmentEntity shipmentEntity) {
        //仅推送泉州地区数据
        if (!stockoutOrderEntity.getLocation().equals(LocationEnum.QUANZHOU.name())) {
            return;
        }
        SaStoreDetailResponse storeInfo = omsApiService.getStoreInfoById(stockoutOrderEntity.getStoreId());
        if (Objects.nonNull(storeInfo) && StringUtils.hasText(storeInfo.getPlatformName()) && storeInfo.getPlatformName().contains("亚马逊")) {
            BdErpSpaceMappingEntity spaceMapping = erpSpaceMappingService.getEntityBySpaceIdAndAreaName(stockoutOrderEntity.getSpaceId(), stockoutOrderEntity.getAreaName());
            if (Objects.isNull(spaceMapping)) {
                LOGGER.info("未找到映射区域装箱清单号为号为: {}", shipmentEntity.getShipmentBoxCode());
            }
            //直接调拨单
            commonBaseService.sendEtlCommonMessage(String.format("%s-%s", shipmentEntity.getShipmentBoxCode(), spaceMapping.getErpSpaceId()), BusinessTypeEnum.WMS_FBA_BARN_REPLENISH_ORDER, CommonBaseService.DIRECT_TRANSFER_ORDER_CALLBACK_URL,
                    KafkaConstant.WMS_STOCK_DIRECT_TRANSFER_ORDER_NAME, KafkaTopicConstant.WMS_FBA_BARN_REPLENISH_ORDER_TOPIC);
        }
    }
}
