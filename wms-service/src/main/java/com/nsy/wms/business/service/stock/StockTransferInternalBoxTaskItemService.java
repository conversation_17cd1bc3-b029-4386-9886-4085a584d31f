package com.nsy.wms.business.service.stock;

import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferInternalBoxTaskStatusEnum;
import com.nsy.api.wms.request.stock.StockTransferInternalBoxTaskItemListRequest;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockTransferInternalBoxTaskItemListResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockTransferInternalBoxTaskEntity;
import com.nsy.wms.repository.entity.stock.StockTransferInternalBoxTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockTransferInternalBoxTaskItemMapper;
import com.nsy.wms.business.service.common.LoginInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Service
public class StockTransferInternalBoxTaskItemService extends ServiceImpl<StockTransferInternalBoxTaskItemMapper, StockTransferInternalBoxTaskItemEntity> {

    @Autowired
    StockTransferInternalBoxTaskService taskService;
    @Autowired
    StockTransferInternalBoxTaskItemMapper itemMapper;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockService stockService;
    @Autowired
    LoginInfoService loginInfoService;

    public PageResponse<StockTransferInternalBoxTaskItemListResponse> getListByRequest(StockTransferInternalBoxTaskItemListRequest request) {
        StockTransferInternalBoxTaskEntity taskEntity = taskService.getById(request.getTaskId());
        if (Objects.isNull(taskEntity)) {
            throw new BusinessServiceException("调拨任务不存在");
        }
        PageResponse<StockTransferInternalBoxTaskItemListResponse> pageResponse = new PageResponse<>();
        Page<StockTransferInternalBoxTaskItemListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockTransferInternalBoxTaskItemListResponse> pageResult = itemMapper.pageSearchTransferTaskItem(page, request.getTaskId());
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(pageResult.getRecords());
        return pageResponse;
    }

    public void forceCompleteTaskItem(StockTransferInternalBoxTaskEntity taskEntity, List<StockTransferInternalBoxTaskItemEntity> currentItemList) {
        for (StockTransferInternalBoxTaskItemEntity taskItemEntity : currentItemList) {
            taskItemEntity.setWaitUpShelveQty(0);
            taskItemEntity.setStatus(StockTransferInternalBoxTaskStatusEnum.COMPLETED.name());
            taskItemEntity.setUpdateBy(loginInfoService.getName());
            stockHandle(taskItemEntity);
        }
        this.updateBatchById(currentItemList);
    }

    private void stockHandle(StockTransferInternalBoxTaskItemEntity taskItemEntity) {
        StockInternalBoxItemEntity boxItemEntity = stockInternalBoxItemService.getOne(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
                .eq(StockInternalBoxItemEntity::getSku, taskItemEntity.getSku())
                .eq(StockInternalBoxItemEntity::getTransferInternalBoxTaskId, taskItemEntity.getTransferTaskId())
                .last("limit 1"));
        if (boxItemEntity == null)
            throw new BusinessServiceException(String.format("未找到调拨箱明细，sku【%s】，请确认", taskItemEntity.getSku()));

        // 更新内部箱库存
        stockInternalBoxItemService.minusStockInternalBoxItemQty(boxItemEntity, taskItemEntity.getExpectedTransferQty(), StockChangeLogTypeEnum.POSITION_IN, StockChangeLogTypeModuleEnum.STOCK_INSIDE, null);

        //更新库位库存
        StockUpdateRequest stockUpdateRequest1 = buildByTaskItemEntity(taskItemEntity, taskItemEntity.getExpectedTransferQty());
        stockService.updateStock(stockUpdateRequest1);
    }

    private StockUpdateRequest buildByTaskItemEntity(StockTransferInternalBoxTaskItemEntity taskItemEntity, Integer changeQty) {
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        BeanUtilsEx.copyProperties(taskItemEntity, stockUpdateRequest);
        stockUpdateRequest.setPositionCode(taskItemEntity.getUpShelvePositionCode());
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.POSITION_IN);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_INSIDE);
        stockUpdateRequest.setQty(changeQty);
        return stockUpdateRequest;
    }

    /**
     * 根据transferTaskId,specId修改状态
     */
    @Transactional
    public void updateStatusByTransferTaskIdAndSpecId(Integer transferTaskId, Integer specId, String status) {
        StockTransferInternalBoxTaskItemEntity taskItemEntity = this.getOne(buildQueryByTransferTaskIdAndSpecId(transferTaskId, specId));
        if (Objects.nonNull(taskItemEntity)) {
            taskItemEntity.setStatus(status);
            taskItemEntity.setUpdateBy(loginInfoService.getName());
            this.updateById(taskItemEntity);
        }
    }

    /**
     * 根据transferTaskId,specId更新已分拣数
     */
    @Transactional
    public void updateSortedQtyByTransferTaskIdAndSpecId(Integer transferTaskId, Integer specId, Integer qty) {
        StockTransferInternalBoxTaskEntity taskEntity = taskService.getById(transferTaskId);
        if (Objects.isNull(taskEntity)) {
            throw new BusinessServiceException("找不到箱内调拨任务");
        }
        List<StockTransferInternalBoxTaskItemEntity> taskItemEntityList = this.list(buildQueryByTransferTaskIdAndSpecId(transferTaskId, specId));
        if (CollectionUtils.isEmpty(taskItemEntityList)) {
            throw new BusinessServiceException("找不到箱内调拨任务明细");
        }
        int scanQty = qty;
        for (StockTransferInternalBoxTaskItemEntity itemEntity : taskItemEntityList) {
            List<StockInternalBoxItemEntity> boxItemEntityList = stockInternalBoxItemService.getByInternalBoxCodeAndTransferBoxIdAndSpecId(taskEntity.getTransferBoxCode(), taskEntity.getTransferTaskId(), itemEntity.getSpecId());
            if (!CollectionUtils.isEmpty(boxItemEntityList)) {
                int originWaitSort = boxItemEntityList.stream().filter(item -> item.getQty() != null).mapToInt(StockInternalBoxItemEntity::getQty).sum();
                int pendingSortQty = originWaitSort - (itemEntity.getSortedQty() != null ? itemEntity.getSortedQty() : 0);
                if (pendingSortQty >= scanQty) {
                    int originSortedQty = itemEntity.getSortedQty() != null ? itemEntity.getSortedQty() : 0;
                    itemEntity.setSortedQty(originSortedQty + scanQty);
                    itemEntity.setWaitUpShelveQty(originSortedQty + scanQty);
                    itemEntity.setStatus(StockTransferInternalBoxTaskStatusEnum.WAIT_SHELVE.name());
                    itemEntity.setUpdateBy(loginInfoService.getName());
                    this.updateById(itemEntity);
                    break;
                } else {
                    scanQty = scanQty - pendingSortQty;
                    itemEntity.setSortedQty(originWaitSort);
                    itemEntity.setWaitUpShelveQty(originWaitSort);
                    itemEntity.setStatus(StockTransferInternalBoxTaskStatusEnum.WAIT_SHELVE.name());
                    itemEntity.setUpdateBy(loginInfoService.getName());
                    this.updateById(itemEntity);
                }
            }
        }
    }

    public List<StockTransferInternalBoxTaskItemEntity> getListByTransferTaskId(Integer transferTaskId) {
        List<StockTransferInternalBoxTaskItemEntity> taskItemEntityList = this.list(buildQueryByTransferTaskId(transferTaskId));
        if (Objects.isNull(taskItemEntityList)) {
            throw new BusinessServiceException("找不到箱内调拨任务明细");
        }
        return taskItemEntityList;
    }

    public List<StockTransferInternalBoxTaskItemEntity> getListByTransferTaskIdList(List<Integer> transferTaskIdList) {
        return this.list(buildQueryByTransferTaskIdList(transferTaskIdList));
    }

    public LambdaQueryWrapper<StockTransferInternalBoxTaskItemEntity> buildQueryByTransferTaskIdAndSpecId(Integer transferTaskId, Integer specId) {
        LambdaQueryWrapper<StockTransferInternalBoxTaskItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(transferTaskId)) {
            queryWrapper.eq(StockTransferInternalBoxTaskItemEntity::getTransferTaskId, transferTaskId);
        }
        if (Objects.nonNull(specId)) {
            queryWrapper.eq(StockTransferInternalBoxTaskItemEntity::getSpecId, specId);
        }
        return queryWrapper;
    }

    public LambdaQueryWrapper<StockTransferInternalBoxTaskItemEntity> buildQueryByTransferTaskId(Integer transferTaskId) {
        LambdaQueryWrapper<StockTransferInternalBoxTaskItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(transferTaskId)) {
            queryWrapper.eq(StockTransferInternalBoxTaskItemEntity::getTransferTaskId, transferTaskId);
        }
        return queryWrapper;
    }

    public LambdaQueryWrapper<StockTransferInternalBoxTaskItemEntity> buildQueryByTransferTaskIdList(List<Integer> transferTaskIdList) {
        LambdaQueryWrapper<StockTransferInternalBoxTaskItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(transferTaskIdList)) {
            queryWrapper.in(StockTransferInternalBoxTaskItemEntity::getTransferTaskId, transferTaskIdList);
        }
        return queryWrapper;
    }
}
