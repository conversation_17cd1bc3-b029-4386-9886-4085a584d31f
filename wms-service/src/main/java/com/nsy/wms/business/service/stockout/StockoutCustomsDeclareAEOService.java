package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.bo.stockout.StockoutAEOAsynMsgBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutCustomsDeclareDocumentItemBo;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareCustomerOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareDocumentAggregatedItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclarePurchaseOrderEntity;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AEO
 */
@Service
public class StockoutCustomsDeclareAEOService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutCustomsDeclareAEOService.class);

    @Resource
    StockoutCustomsDeclareDocumentAggregatedItemService declareDocumentAggregatedItemService;
    @Resource
    StockoutCustomsDeclareFormService declareFormService;
    @Resource
    StockoutCustomsDeclareDocumentItemService declareDocumentItemService;
    @Resource
    StockoutCustomsDeclareCustomerOrderService declareCustomerOrderService;
    @Resource
    StockoutCustomsDeclarePurchaseOrderService declarePurchaseOrderService;
    @Resource
    StockoutCustomsDeclareStockinOrderService declareStockinOrderService;
    @Resource
    StockoutCustomsDeclareStockoutOrderService declareStockoutOrderService;
    @Resource
    StockoutCustomsDeclareContractService stockoutCustomsDeclareContractService;
    @Resource
    ScmApiService scmApiService;
    @Resource
    StockoutCustomsDeclareFormService stockoutCustomsDeclareFormService;
    @Autowired
    MessageProducer producer;

    /**
     * 生成AEO单据
     *
     * @param idList
     */

    public void generateFromContract(List<Integer> idList) {
        idList.forEach(id -> {
            try {
                SpringUtil.getBean(StockoutCustomsDeclareAEOService.class).generateFromContract(id);
            } catch (Exception e) {
                LOGGER.error(String.format("合同 %s 无法创建AEO: %s", id, e.getMessage()), e);
            }
        });
    }

    /**
     * 生成 采购单 入库单
     *
     * @param id
     */
    @Transactional
    public void generateFromContract(Integer id) {
        StockoutCustomsDeclareContractEntity contract = stockoutCustomsDeclareContractService.getByDeclareContractId(id);
        List<StockoutCustomsDeclareFormEntity> formList = declareFormService.findListByContractNo(contract.getDeclareContractNo());
        if (CollectionUtil.isEmpty(formList))
            throw new BusinessServiceException(String.format("合同 %s 对应关单不存在", contract.getDeclareContractNo()));

        //查找供应商
        List<SupplierDto> supplierDtoList = scmApiService.getSupplierInfoList(Collections.singletonList(contract.getSupplierId()));
        if (supplierDtoList.isEmpty())
            throw new BusinessServiceException(String.format("供应商 %s 找不到", contract.getSupplierId()));
        SupplierDto supplierDto = supplierDtoList.get(0);

        formList.forEach(form -> {
            //通过报关单据号 和 项号查找具体项
            StockoutCustomsDeclareDocumentAggregatedItemEntity declareDocumentAggregatedItem = declareDocumentAggregatedItemService.findByDeclareDocumentNoAndGNo(form.getProtocolNo(), Integer.valueOf(form.getgNo()));
            //获取单据明细
            List<StockoutCustomsDeclareDocumentItemBo> boList = declareDocumentItemService.getBaseMapper().getBoList(declareDocumentAggregatedItem.getDeclareDocumentAggregatedItemId());
            if (CollectionUtil.isEmpty(boList))
                throw new BusinessServiceException(String.format("协议号 %s 项号 %s 的单据明细为空", form.getProtocolNo(), form.getgNo()));
            StockoutCustomsDeclareDocumentItemBo firstDocumentItemBo = boList.get(0);
            Map<String, List<StockoutCustomsDeclareDocumentItemBo>> boMap;
            if (StrUtil.isNotEmpty(firstDocumentItemBo.getFbaShipmentId())) {
                boMap = boList.stream().collect(Collectors.groupingBy(StockoutCustomsDeclareDocumentItemBo::getFbaShipmentId));
            } else if (StrUtil.isNotEmpty(firstDocumentItemBo.getOrderNo())) {
                boMap = boList.stream().collect(Collectors.groupingBy(StockoutCustomsDeclareDocumentItemBo::getOrderNo));
            } else {
                throw new BusinessServiceException(String.format("关单 %s 存在fbaShipmentId、订单号均为空的情况", form.getDeclareFormId()));
            }

            boMap.forEach((fbaShipmentId, splitBoList) -> {
                //采购单
                StockoutCustomsDeclarePurchaseOrderEntity declarePurchaseOrderEntity = declarePurchaseOrderService.add(fbaShipmentId, contract.getDeclareContractNo(), splitBoList, supplierDto, form);
                //入库单
                declareStockinOrderService.add(declarePurchaseOrderEntity, splitBoList, form);
            });

        });
    }


    /**
     * 生成AEO单据
     *
     * @param idList
     */
    public void generateFromForm(List<Integer> idList) {
        idList.forEach(id -> {
            try {
                SpringUtil.getBean(StockoutCustomsDeclareAEOService.class).generateFromForm(id);
            } catch (Exception e) {
                LOGGER.error(String.format("关单 %s 无法创建AEO: %s", id, e.getMessage()), e);
            }
        });
    }

    /**
     * 从关单生成 客户订单 出库单
     */
    @Transactional
    public void generateFromForm(Integer id) {
        StockoutCustomsDeclareFormEntity form = stockoutCustomsDeclareFormService.findById(id);
        StockoutCustomsDeclareDocumentAggregatedItemEntity declareDocumentAggregatedItem = declareDocumentAggregatedItemService.findByDeclareDocumentNoAndGNo(form.getProtocolNo(), Integer.valueOf(form.getgNo()));
        //获取单据明细
        List<StockoutCustomsDeclareDocumentItemBo> boList = declareDocumentItemService.getBaseMapper().getBoList(declareDocumentAggregatedItem.getDeclareDocumentAggregatedItemId());
        if (CollectionUtil.isEmpty(boList))
            throw new BusinessServiceException(String.format("协议号 %s 项号 %s 的单据明细为空", form.getProtocolNo(), form.getgNo()));

        StockoutCustomsDeclareDocumentItemBo firstDocumentItemBo = boList.get(0);
        Map<String, List<StockoutCustomsDeclareDocumentItemBo>> boMap;
        if (StrUtil.isNotEmpty(firstDocumentItemBo.getFbaShipmentId())) {
            boMap = boList.stream().collect(Collectors.groupingBy(StockoutCustomsDeclareDocumentItemBo::getFbaShipmentId));
        } else if (StrUtil.isNotEmpty(firstDocumentItemBo.getOrderNo())) {
            boMap = boList.stream().collect(Collectors.groupingBy(StockoutCustomsDeclareDocumentItemBo::getOrderNo));
        } else {
            throw new BusinessServiceException(String.format("关单 %s 存在fbaShipmentId、订单号均为空的情况", form.getDeclareFormId()));
        }

        boMap.forEach((key, value) -> {
            try {
                //客户订单
                StockoutCustomsDeclareCustomerOrderEntity customerOrderEntity = declareCustomerOrderService.add(key, value, form);
                //出库单
                declareStockoutOrderService.add(customerOrderEntity, value, form);
            } catch (Exception e) {
                LOGGER.error(String.format("通过关单生成AEO报错 %s %s ", form.getDeclareFormId(), e.getMessage()), e);
            }
        });
    }


    /**
     * 删除
     *
     * @param declareContractCode
     */
    @Transactional
    public void removeAEO(String declareContractCode) {
        //删除采购单
        List<String> purchaseOrderNoList = declarePurchaseOrderService.removeByContractNo(declareContractCode);
        //删除入库单
        purchaseOrderNoList.forEach(purchaseOrderNo -> {
            declareStockinOrderService.removeByPurchaseOrderNo(purchaseOrderNo);
        });
    }

    public void sendAsynOpMsg(StockoutAEOAsynMsgBo msgBo) {
        LocationWrapperMessage<StockoutAEOAsynMsgBo> locationWrapperMessage = new LocationWrapperMessage<>(TenantContext.getTenant(), msgBo);
        producer.sendMessage(KafkaConstant.AEO_OPERATE_MARK, KafkaConstant.AEO_OPERATE_TOPIC, locationWrapperMessage);
    }


}
