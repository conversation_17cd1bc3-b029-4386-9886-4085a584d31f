package com.nsy.wms.business.manage.tms;

import com.beust.jcommander.internal.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.pms.dto.response.BaseStringResponse;
import com.nsy.api.wms.domain.shared.SelectIntegerModel;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.domain.stockout.OverseaSpaceSkuDTO;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.request.stock.CangSouStockinOrderRequest;
import com.nsy.api.wms.request.stock.DianCangStockinOrderRequest;
import com.nsy.api.wms.request.stock.DianCangStockinOrderSearchRequest;
import com.nsy.api.wms.request.stock.DmxsmartStockInOrderDetail;
import com.nsy.api.wms.request.stock.JiuFangYunCangCreateStockInOrderRequest;
import com.nsy.api.wms.request.stock.JiuFangYunCangPrintStockInOrderRequest;
import com.nsy.api.wms.request.stock.OverseaPrintShipmentLabelRequest;
import com.nsy.api.wms.request.stock.TmsGetCangSouStockInOrderInfo;
import com.nsy.api.wms.request.stock.WmsCreateStockInOrderRequest;
import com.nsy.api.wms.request.stock.ZuoHaiOrderProduct;
import com.nsy.api.wms.request.stockout.AmazonBuyShippingCancelRequest;
import com.nsy.api.wms.request.stockout.ChannelForwarderPriceRecordExistRequest;
import com.nsy.api.wms.request.stockout.DeliveryAddressMatchRequest;
import com.nsy.api.wms.request.stockout.GetSpaceSkuRequest;
import com.nsy.api.wms.request.stockout.LabelRequest;
import com.nsy.api.wms.request.stockout.ReplaceSecondaryNumberRequest;
import com.nsy.api.wms.request.stockout.SyncTmsStockoutShipmentRequest;
import com.nsy.api.wms.response.stock.DmxsmartStockInOrderCreateResponse;
import com.nsy.api.wms.response.stock.DmxsmartStockInOrderLabelResponse;
import com.nsy.api.wms.response.stock.DmxsmartStockInOrderProductInfoResponse;
import com.nsy.api.wms.response.stock.GoodCangStockInOrderNoResponse;
import com.nsy.api.wms.response.stockin.CangSouBillInDetails;
import com.nsy.api.wms.response.stockin.StockinOrderProduct;
import com.nsy.api.wms.response.stockout.ChannelForwarderPriceRecordResponse;
import com.nsy.api.wms.response.stockout.GetLogisticsCompanyResponse;
import com.nsy.api.wms.response.stockout.GetSecondaryNumResponse;
import com.nsy.api.wms.response.stockout.LogisticsAddressResponse;
import com.nsy.api.wms.response.stockout.PrintLabelResponse;
import com.nsy.api.wms.response.stockout.StockTransferTrackingResponse;
import com.nsy.api.wms.response.stockout.TmsPackageDetailInfo;
import com.nsy.wms.business.manage.tms.request.GenerateOrderRequest;
import com.nsy.wms.business.manage.tms.request.OrderInfo;
import com.nsy.wms.business.manage.tms.request.OverseaStockRequest;
import com.nsy.wms.business.manage.tms.request.StockInRequest;
import com.nsy.wms.business.manage.tms.request.UpsPaperlessRequest;
import com.nsy.wms.business.manage.tms.response.GenerateOrderResponse;
import com.nsy.wms.business.manage.tms.response.LogisticsAccountInfo;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCountryProvinceMapping;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class TmsApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TmsApiService.class);

    @Autowired
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.tms}")
    private String tmsServiceUrl;
    @Autowired
    private ExternalApiLogService externalApiLogService;
    @Inject
    Environment env;
    @Autowired
    TmsCacheService tmsCacheService;

    /**
     * 获取面单
     *
     * @param generateOrderRequest
     * @return
     */
    public ResponseEntity<GenerateOrderResponse> printOrder(GenerateOrderRequest generateOrderRequest) {
//        if (env.getActiveProfiles().length > 0
//                && "stage".equalsIgnoreCase(env.getActiveProfiles()[0])
//                && LogisticsCompanyConstant.FEDEX.equals(generateOrderRequest.getOrderInfo().getLogisticsCompany())) {
//            GenerateOrderResponse response = new GenerateOrderResponse();
//            GenerateOrderResponse.SuccessEntity successEntity = new GenerateOrderResponse.SuccessEntity();
//            successEntity.setLogisticsNo(String.format("WL%s", DateFormatUtils.format(new Date(), "yyyyMMddHHmmssSSS")));
//            successEntity.setLabelUrl("https://nsy-tms.oss-cn-hangzhou.aliyuncs.com/production/tms-label/SFEXPRESS/SFExpress-SF6143169425817.pdf");
//            response.setSuccessEntity(successEntity);
//            return new ResponseEntity<>(response, HttpStatus.OK);
//        }
        String uri = String.format("%s/new-order", tmsServiceUrl);
        OrderInfo orderInfo = generateOrderRequest.getOrderInfo();
        orderInfo.setLocation(TenantContext.getTenant());
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GET_LABEL, uri,
                JsonMapper.toJson(generateOrderRequest), orderInfo.getBusinessKey(), orderInfo.getLogisticsChannelName());
        ResponseEntity<GenerateOrderResponse> responseEntity;
        try {
            responseEntity = this.restTemplate.postForEntity(uri, generateOrderRequest, GenerateOrderResponse.class);
            GenerateOrderResponse responseEntityBody = responseEntity.getBody();
            if (Objects.isNull(responseEntityBody) || responseEntityBody.getError() != null) {
                String message = Objects.isNull(responseEntityBody) ? "返回错误" : responseEntityBody.getError().getMessage();
                externalApiLogService.updateLog(apiLogEntity, message, ExternalApiLogStatusEnum.FAIL);
            } else {
                externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(responseEntity), ExternalApiLogStatusEnum.SUCCESS);
            }
            return responseEntity;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }

    /**
     * 所有启用物流公司集合
     *
     * @return
     */
    public List<TmsLogisticsCompany> getAllLogisticsCompany() {
//        String url = String.format("%s/logistics-company/all-list", tmsServiceUrl);
        String url = String.format("%s/logistics-channel-config/select-all", tmsServiceUrl);
        TmsLogisticsCompany[] companyArray = this.restTemplate.getForObject(url, TmsLogisticsCompany[].class);
        if (Objects.isNull(companyArray)) {
            return null;
        }
        return Arrays.asList(companyArray);
    }

    public List<TmsLogisticsCompany> getAllLogisticsCompanyWithoutStatus() {
        String url = String.format("%s/logistics-channel-config/all/select-all", tmsServiceUrl);
        TmsLogisticsCompany[] companyArray = this.restTemplate.getForObject(url, TmsLogisticsCompany[].class);
        if (Objects.isNull(companyArray)) {
            return null;
        }
        return Arrays.asList(companyArray);
    }

    public GetLogisticsCompanyResponse getLogisticsCompanyResponse(String logisticsCompany) {
        TmsLogisticsCompany tmsLogisticsCompany = tmsCacheService.getAllLogisticsCompanyList().stream()
                .filter(o -> o.getLogisticsCompany().equals(logisticsCompany)).findFirst().orElse(null);
        GetLogisticsCompanyResponse response = new GetLogisticsCompanyResponse();
        if (tmsLogisticsCompany != null) {
            BeanUtilsEx.copyProperties(tmsLogisticsCompany, response);
        }
        return response;
    }

    /**
     * 国家下拉框、国际物流使用(国家二字码-国家英文名称)
     *
     * @param logisticsCompany
     * @return
     */
    public List<SelectModel> getLogisticsCountrySelect(String logisticsCompany) {
        String url = String.format("%s/logistics-country/name-select/%s", tmsServiceUrl, logisticsCompany);
        SelectModel[] countryArray = this.restTemplate.getForObject(url, SelectModel[].class);
        if (Objects.isNull(countryArray)) {
            return null;
        }
        return Arrays.asList(countryArray);
    }

    /**
     * 根据物流公司获取特殊国家州二字码映射
     *
     * @param logisticsCompany
     * @return
     */
    public List<TmsLogisticsCountryProvinceMapping> getLogisticsCountryProvinceList(String logisticsCompany) {
        String url = String.format("%s/logistics-country-province/list/%s", tmsServiceUrl, logisticsCompany);
        TmsLogisticsCountryProvinceMapping[] countryArray = this.restTemplate.getForObject(url, TmsLogisticsCountryProvinceMapping[].class);
        if (Objects.isNull(countryArray)) {
            return null;
        }
        return Arrays.asList(countryArray);
    }

    /**
     * 根据物流公司获取物流账号信息
     *
     * @param logisticsCompany
     * @return
     */
    public List<LogisticsAccountInfo> getLogisticsAccountInfoList(String logisticsCompany) {
        String url = String.format("%s/logistics-account-info/%s", tmsServiceUrl, logisticsCompany);
        LogisticsAccountInfo[] accountInfoArray = this.restTemplate.getForObject(url, LogisticsAccountInfo[].class);
        if (Objects.isNull(accountInfoArray)) {
            return null;
        }
        return Arrays.asList(accountInfoArray);
    }

    /**
     * 根据物流公司获取物流账号下拉数据源
     *
     * @param logisticsCompany
     * @return
     */
    public List<SelectIntegerModel> getLogisticsAccountSelect(String logisticsCompany) {
        String url = String.format("%s/logistics-account/name-select/%s", tmsServiceUrl, logisticsCompany);
        SelectIntegerModel[] selectArray = this.restTemplate.getForObject(url, SelectIntegerModel[].class);
        if (Objects.isNull(selectArray)) {
            return null;
        }
        return Arrays.asList(selectArray);
    }

    public void replaceSecondaryNumber(List<ReplaceSecondaryNumberRequest> tmsRequest) {
        String uri = String.format("%s/secondary-number/replace", tmsServiceUrl);
        this.restTemplate.postForEntity(uri, tmsRequest, String.class);
    }

    /**
     * ups上传发票或发联接口
     *
     * @param request
     */
    public void upsUploadInvoiceOrFaLian(UpsPaperlessRequest request) {
        String uri = String.format("%s/paperlessPush", tmsServiceUrl);
        this.restTemplate.postForEntity(uri, request, String.class);
    }

    public PrintLabelResponse getNewLabel(LabelRequest request) {
        String uri = String.format("%s/new-label", tmsServiceUrl);
        ResponseEntity<PrintLabelResponse> responseEntity = this.restTemplate.postForEntity(uri, request, PrintLabelResponse.class);
        return responseEntity.getBody();
    }

    public void syncDeliveryInfo(String logisticsNo) {
        String uri = String.format("%s/new-order/sync/delivery-info/%s", tmsServiceUrl, logisticsNo);
        this.restTemplate.getForObject(uri, Void.class);
    }

    public GetSecondaryNumResponse getSecondaryNum(String logisticsNo) {
        String uri = String.format("%s/secondary-number/%s", tmsServiceUrl, logisticsNo);
        return this.restTemplate.getForObject(uri, GetSecondaryNumResponse.class);
    }

    public TmsPackageDetailInfo getPackageInfo(String logisticsNo) {
        String uri = String.format("%s/package-info/%s", tmsServiceUrl, logisticsNo);
        return this.restTemplate.getForObject(uri, TmsPackageDetailInfo.class);
    }

    public void cancelOrderSync(String logisticsNo) {
        String uri = String.format("%s/new-order/cancel-order/%s", tmsServiceUrl, logisticsNo);
        this.restTemplate.put(uri, Void.class);
    }

    public GoodCangStockInOrderNoResponse createGoodCangStockInOrder(WmsCreateStockInOrderRequest request) {
        String uri = String.format("%s/good-cang/create-stock-in-order", tmsServiceUrl);
        ResponseEntity<GoodCangStockInOrderNoResponse> responseEntity = this.restTemplate.postForEntity(uri, request, GoodCangStockInOrderNoResponse.class);
        return responseEntity.getBody();
    }

    public StockTransferTrackingResponse getOverseaStockInOrder(String platformReferenceNo, String space) {
        String uri = String.format("%s/oversea/stock-in-order/info", tmsServiceUrl);
        TmsGetCangSouStockInOrderInfo request = new TmsGetCangSouStockInOrderInfo();
        request.setStockInReferenceNo(platformReferenceNo);
        request.setLocation(TenantContext.getTenant());
        request.setSpace(space);
        ResponseEntity<StockTransferTrackingResponse> responseEntity = this.restTemplate.postForEntity(uri, request, StockTransferTrackingResponse.class);
        return responseEntity.getBody();
    }

    public String createOverseaStockInOrder(WmsCreateStockInOrderRequest request) {
        String uri = String.format("%s/oversea/create-stock-in-order", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, request, String.class);
        return responseEntity.getBody();
    }


    public BaseStringResponse printGoodCangBarcode(String sku, String spaceName) {
        String uri = String.format("%s/good-cang/barcode/%s/%s", tmsServiceUrl, spaceName, sku);
        return this.restTemplate.getForObject(uri, BaseStringResponse.class);
    }

    public ChannelForwarderPriceRecordResponse recentPrice(ChannelForwarderPriceRecordExistRequest request) {
        String uri = String.format("%s/channel-forwarder-price-record/match-price", tmsServiceUrl);
        ResponseEntity<ChannelForwarderPriceRecordResponse> responseEntity = this.restTemplate.postForEntity(uri, request, ChannelForwarderPriceRecordResponse.class);
        return responseEntity.getBody();
    }

    public void syncShipment(SyncTmsStockoutShipmentRequest request) {
        String uri = String.format("%s/shipment/sync-shipped", tmsServiceUrl);
        this.restTemplate.postForEntity(uri, request, String.class);
    }

    public void createZuoHaiStockInOrder(List<ZuoHaiOrderProduct> req) {
        String uri = String.format("%s/zuo-hai/create-stock-in-order", tmsServiceUrl);
        this.restTemplate.postForEntity(uri, req, Void.class);
    }

    public void getSpaceSkuInfo(GetSpaceSkuRequest req) {
        String uri = String.format("%s/space-sku-mapping/info", tmsServiceUrl);
        this.restTemplate.postForEntity(uri, req, Void.class);
    }

    public List<StockinOrderProduct> getZuoHaiTrackingInfo(String platformReferenceNo, String spaceName) {
        String uri = String.format("%s/zuo-hai/stock-in-order/%s/%s", tmsServiceUrl, platformReferenceNo, spaceName);
        StockinOrderProduct[] result = this.restTemplate.getForObject(uri, StockinOrderProduct[].class);
        return Objects.isNull(result) ? Lists.newArrayList() : Arrays.asList(result);
    }

    public String createDianCangStockInOrder(DianCangStockinOrderRequest req) {
        String uri = String.format("%s/dian-cang/create-stock-in-order", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, req, String.class);
        return responseEntity.getBody();
    }

    public String printDianCangStockInOrder(String platformReferenceNo, String logisticsCompany) {
        DianCangStockinOrderSearchRequest request = new DianCangStockinOrderSearchRequest();
        request.setPlatformReferenceNo(platformReferenceNo);
        request.setLocation(TenantContext.getTenant());
        request.setLogisticsCompany(logisticsCompany);
        String uri = String.format("%s/dian-cang/print-label", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, request, String.class);
        return responseEntity.getBody();
    }

    /**
     * 九方云仓系统创建入库单
     *
     * @param req
     * @return
     */
    public String createJiuFangYunCangStockInOrder(JiuFangYunCangCreateStockInOrderRequest req) {
        String uri = String.format("%s/jiu-fang-yun-cang/create-stock-in-order", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, req, String.class);
        return responseEntity.getBody();
    }

    /**
     * 九方云仓系统打印订单
     *
     * @return
     */
    public String printJiuFangYunCangOrder(JiuFangYunCangPrintStockInOrderRequest request) {
        String uri = String.format("%s/jiu-fang-yun-cang/print-label", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, request, String.class);
        return responseEntity.getBody();
    }

    public String printOverseaShipmentLabel(String platformReferenceNo, String logisticsCompany, List<String> boxCodes) {
        OverseaPrintShipmentLabelRequest request = new OverseaPrintShipmentLabelRequest();
        request.setPlatformReferenceNo(platformReferenceNo);
        request.setLocation(TenantContext.getTenant());
        request.setLogisticsCompany(logisticsCompany);
        request.setBoxCodeList(boxCodes);
        String uri = String.format("%s/oversea/print-shipment-label", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, request, String.class);
        return responseEntity.getBody();
    }


    public DmxsmartStockInOrderCreateResponse createDmxsmartStockInOrder(List<DmxsmartStockInOrderDetail> req, String orderNo) {
        String uri = String.format("%s/dmxsmart/create-stock-in-order/%s", tmsServiceUrl, orderNo);
        ResponseEntity<DmxsmartStockInOrderCreateResponse> responseEntity = this.restTemplate.postForEntity(uri, req, DmxsmartStockInOrderCreateResponse.class);
        return responseEntity.getBody();
    }


    public String printDmxsmartLabel(String orderNo, String spaceName) {
        String uri = String.format("%s/dmxsmart/stock-in-order/get-label/%s/%s", tmsServiceUrl, orderNo, spaceName);
        DmxsmartStockInOrderLabelResponse response = this.restTemplate.getForObject(uri, DmxsmartStockInOrderLabelResponse.class);
        if (Objects.isNull(response)) {
            throw new BusinessServiceException("打印箱唛错误返回内容为空");
        }
        return response.getLabel();
    }

    public List<DmxsmartStockInOrderProductInfoResponse> getDmxsmartTrackingInfo(String platformReferenceNo, String spaceName) {
        String uri = String.format("%s/dmxsmart/stock-in-order/info/%s/%s", tmsServiceUrl, platformReferenceNo, spaceName);
        String responseEntity;
        try {
            responseEntity = this.restTemplate.getForObject(uri, String.class);
            return JsonMapper.jsonStringToObjectArray(responseEntity, DmxsmartStockInOrderProductInfoResponse.class);
        } catch (Exception e) {
            throw e;
        }
    }

    public String createWinitStockInOrder(StockInRequest req) {
        String uri = String.format("%s/winit/create-stock-in-order", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, req, String.class);
        return responseEntity.getBody();
    }

    public String[] getChildrenPackageLabel(Integer qty, String space) {
        String uri = String.format("%s/winit/get-children-label/%s/%s/%s", tmsServiceUrl, TenantContext.getTenant(), space, qty);
        return this.restTemplate.getForObject(uri, String[].class);
    }


    public String createCangSouStockInOrder(CangSouStockinOrderRequest req) {
        String uri = String.format("%s/cang-sou/create-stock-in-order", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, req, String.class);
        return responseEntity.getBody();
    }

    public List<CangSouBillInDetails> getCangSouTrackingInfo(String platformReferenceNo, String logisticsCompany) {
        TmsGetCangSouStockInOrderInfo request = new TmsGetCangSouStockInOrderInfo();
        request.setStockInReferenceNo(platformReferenceNo);
        request.setLocation(TenantContext.getTenant());
        request.setSpace(logisticsCompany);
        String uri = String.format("%s/cang-sou/stock-in-order/info", tmsServiceUrl);
        ResponseEntity<String> responseEntity;
        try {
            responseEntity = this.restTemplate.postForEntity(uri, request, String.class);
            return JsonMapper.jsonStringToObjectArray(responseEntity.getBody(), CangSouBillInDetails.class);
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 购买配送取消面单
     *
     * @param request
     */
    public void amazonBuyShippingCancel(AmazonBuyShippingCancelRequest request) {
        String uri = String.format("%s/amazon-buy-shipping/cancel", tmsServiceUrl);

        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.TMS_AMAZON_BUY_SHIPPING_CANCEL, uri,
                JsonMapper.toJson(request), request.getLogisticsNo(), request.getLogisticsNo());
        try {
            ResponseEntity<Void> response = this.restTemplate.postForEntity(uri, request, Void.class);
            if (HttpStatus.OK.value() != response.getStatusCode().value()) {
                throw new BusinessServiceException("网络错误");
            }
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }

    public List<LogisticsAddressResponse> matchAddress(DeliveryAddressMatchRequest request) {
        String uri = String.format("%s/logistics-delivery-address-mapping/match-address", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, request, String.class);
        if (ObjectUtils.isEmpty(responseEntity.getBody())) {
            return Collections.emptyList();
        }
        return JsonMapper.jsonStringToObjectArray(responseEntity.getBody(), LogisticsAddressResponse.class);
    }

    public List<OverseaSpaceSkuDTO> getOverseaSpaceProductStock(OverseaStockRequest request) {
        String uri = String.format("%s/oversea/get-stock", tmsServiceUrl);
        ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(uri, request, String.class);
        if (ObjectUtils.isEmpty(responseEntity.getBody())) {
            return Collections.emptyList();
        }
        return JsonMapper.jsonStringToObjectArray(responseEntity.getBody(), OverseaSpaceSkuDTO.class);
    }
}
