package com.nsy.wms.business.service.download.stockout;

import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class StockoutDepponAirDownloadService implements IDownloadService {
    @Resource
    StockoutShipmentDeclareDownloadService stockoutShipmentDeclareDownloadService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_DEPPON_SHIPMENT_DECLARE_AIR;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        return stockoutShipmentDeclareDownloadService.queryExportData(request);
    }


}
