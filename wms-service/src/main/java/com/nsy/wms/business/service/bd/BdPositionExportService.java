package com.nsy.wms.business.service.bd;

import com.nsy.api.wms.domain.bd.BdPosition;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.bd.BdPositionListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/11/8 13:43
 */
@Service
public class BdPositionExportService implements IDownloadService {

    @Autowired
    private BdPositionService positionService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_POSITION_CODE_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        BdPositionListRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), BdPositionListRequest.class);
        // 设置每次的查询数量
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        PageResponse<BdPosition> pageResponse = positionService.bdPositionByPage(downloadRequest);
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getContent()));
        return response;
    }
}
