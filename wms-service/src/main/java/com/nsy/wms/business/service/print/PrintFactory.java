package com.nsy.wms.business.service.print;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.request.print.PrintDistributionRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 每个打印类型对应一个打印服务
 * 需要的数据系统上需要有对应的打印服务类型
 */
@Component
public class PrintFactory extends ApplicationObjectSupport implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrintFactory.class);

    private final Map<String, IPrintService> printServiceMap = new HashMap<>();

    public List<Map<String, Object>> handle(PrintDistributionRequest request, String typeService) {
        LOGGER.info(typeService);
        return route(typeService).dataAssembly(request);
    }

    public IPrintService route(String serviceName) {
        IPrintService printService = printServiceMap.get(serviceName);
        if (printService == null) {
            throw new BusinessServiceException(String.format("打印服务不存在: %s", serviceName));
        }
        return printService;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, IPrintService> services = Optional.ofNullable(this.getApplicationContext())
                .map(context -> context.getBeansOfType(IPrintService.class))
                .get();
        for (IPrintService printService : services.values()) {
            printServiceMap.put(printService.templateType(), printService);
        }
    }
} 
