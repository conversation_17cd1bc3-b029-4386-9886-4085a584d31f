package com.nsy.wms.business.service.stockout.ready;

import com.nsy.api.wms.enumeration.stockout.StockoutOrderReadyStatusHandleEnum;
import com.nsy.wms.business.service.stockout.StockoutOrderOttoExtendService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderOttoExtendEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/5/5 9:55
 */
@Service
public class StockoutOrderOttoHandleService implements IStockoutOrderReadyStatusHandleService {


    @Autowired
    private StockoutOrderOttoExtendService stockoutOrderOttoExtendService;

    @Override
    public boolean isSupport(StockoutOrderReadyStatusHandleEnum handleEnum) {
        return StockoutOrderReadyStatusHandleEnum.OTTO == handleEnum;
    }

    /**
     * 步骤一：创建ott订单
     * 步骤二：创建tms订单
     * 步骤三：如步骤一、步骤二都创建成功则修改出库单状态
     *
     * @param stockoutOrder
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleOrder(StockoutOrderEntity stockoutOrder) {
        StockoutOrderOttoExtendEntity extendEntity = stockoutOrderOttoExtendService.getByStockoutOrderId(stockoutOrder.getStockoutOrderId());
        //创建订单
        if (Objects.isNull(extendEntity.getOrderCreateDate())) {
            stockoutOrderOttoExtendService.createOrder(stockoutOrder.getStockoutOrderId());
        }
        //创建tms订单
        if (Objects.isNull(extendEntity.getTmsOrderCreateDate())) {
            stockoutOrderOttoExtendService.createTmsOrder(stockoutOrder.getStockoutOrderId());
        }
        //检查订单是否都创建成功，修改成待出库
        stockoutOrderOttoExtendService.checkAndUpdateReadyOutbound(stockoutOrder.getStockoutOrderId());
    }
}