package com.nsy.wms.business.service.stock;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.pms.dto.productspec.ProductSpecPriceDTO;
import com.nsy.api.pms.dto.request.productspec.GetProductSpecPriceInfoRequest;
import com.nsy.api.pms.dto.response.BaseListResponse;
import com.nsy.api.pms.feign.ProductSpecFeignClient;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockLendLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockLendStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockLendStockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.request.stock.StockLendCantReturnRequest;
import com.nsy.api.wms.request.stock.StockLendDelayRequest;
import com.nsy.api.wms.request.stock.StockLendReconciliationRequest;
import com.nsy.wms.business.domain.bo.stock.StockLendStockUpdateBo;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpStockLendCantReturnRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.internal.common.OtherModuleService;
import com.nsy.wms.business.service.stockout.StockoutOrderCancelService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockLendEntity;
import com.nsy.wms.repository.entity.stock.StockLendItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.jpa.mapper.stock.StockLendMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockLendOpService extends ServiceImpl<StockLendMapper, StockLendEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockLendOpService.class);
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockLendLogService lendLogService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockLendService stockLendService;
    @Autowired
    StockLendItemService lendItemService;
    @Autowired
    StockLendStockService stockLendStockService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    ProductSpecFeignClient productSpecFeignClient;
    @Autowired
    OtherModuleService otherModuleService;
    @Autowired
    StockLendPushErpService pushErpService;
    @Autowired
    StockoutOrderCancelService stockoutOrderCancelService;
    @Autowired
    ExternalApiLogService externalApiLogService;

    /**
     * 取消借用
     * step 1: 借用单更新状态，记录日志
     * step 2: 出库单更新状态（准备中，待生成波次=》已取消，除准备中，待生成波次，已取消之外=》取消中），记录日志
     * step 3: 同步erp
     *
     * @param lendEntity
     */
    @Transactional
    public void cancelStockLend(StockLendEntity lendEntity) {
        if (StockLendStatusEnum.WAIT_DEAL.name().equals(lendEntity.getStatus())) {
            // step 1 :借用单更新状态，记录日志
            lendEntity.setStatus(StockLendStatusEnum.CANCELLED.name());
            lendEntity.setUpdateBy(loginInfoService.getName());
            this.updateById(lendEntity);
            // step 2: 取消出库单
            StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(lendEntity.getStockLendCode());
            stockoutOrderCancelService.cancelOutOrder(stockoutOrder.getStockoutOrderId(), 0);
            //记录日志
            lendLogService.addLendLog(lendEntity.getLendId(), StockLendLogTypeEnum.LEND_CANCEL, String.format("借用单%s已取消", lendEntity.getStockLendCode()));

        } else if (StockLendStatusEnum.WAIT_RETURN.name().equals(lendEntity.getStatus())) {
            throw new BusinessServiceException("该借用单已出库，无法取消，请按流程归还后上架");
        } else {
            throw new BusinessServiceException("状态错误，请确认");
        }
    }


    /**
     * 取消借用
     * step 1: 借用单更新状态，记录日志
     * step 2: 出库单更新状态（准备中，待生成波次=》已取消，除准备中，待生成波次，已取消之外=》取消中），记录日志
     * step 3: 同步erp
     *
     * @param lendCode
     */
    @Transactional
    public void erpCancelStockLend(String lendCode) {
        String uri = "/stock-lend/erp-cancel/{lendCode}";
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.ERP_CANCEL_STOCK_LEND, uri,
                lendCode, lendCode, "erp取消借用");
        try {
            StockLendEntity lendEntity = stockLendService.getByStockLendCode(lendCode);
            cancelStockLend(lendEntity);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(String.format("请求失败 : %s", e.getMessage()), e);
        }
    }

    /**
     * 取消借用
     * step 1: 借用单更新状态，记录日志
     * step 2: 出库单更新状态（准备中，待生成波次=》已取消，除准备中，待生成波次，已取消之外=》取消中），记录日志
     * step 3: 同步erp
     *
     * @param lendEntity
     */
    @Transactional
    public void cancelStockLendWithSync(StockLendEntity lendEntity) {
        cancelStockLend(lendEntity);
        // step 3: 同步erp
        ErpStockLendCantReturnRequest request = new ErpStockLendCantReturnRequest();
        request.setStockLendCode(lendEntity.getStockLendCode());
        request.setOperator(loginInfoService.getUserName());
        erpApiService.asyncStockLendCancel(request);
    }

    /**
     * 取消借用(不取消出库单)
     * step 1: 借用单更新状态，记录日志
     * step 2: 同步erp
     *
     * @param stockoutOrderNo
     */
    @Transactional
    public void cancelStockLendWithoutCancelStockoutOrder(String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        if (!StockoutOrderTypeEnum.INNER_BORROW_DELIVERY.name().equals(stockoutOrder.getStockoutType())) return;

        StockLendEntity lendEntity = stockLendService.getByStockLendCode(stockoutOrderNo);
        if (Objects.isNull(lendEntity)) throw new BusinessServiceException("数据有误，找不到借用单");
        if (StockLendStatusEnum.WAIT_DEAL.name().equals(lendEntity.getStatus())) {
            // step 1 :借用单更新状态，记录日志
            lendEntity.setStatus(StockLendStatusEnum.CANCELLED.name());
            lendEntity.setUpdateBy(loginInfoService.getName());
            this.updateById(lendEntity);
            lendLogService.addLendLog(lendEntity.getLendId(), StockLendLogTypeEnum.LEND_CANCEL, String.format("借用单%s已取消", lendEntity.getStockLendCode()));
            // step 2: 同步erp
            ErpStockLendCantReturnRequest request = new ErpStockLendCantReturnRequest();
            request.setStockLendCode(lendEntity.getStockLendCode());
            request.setOperator(loginInfoService.getUserName());
            erpApiService.asyncStockLendCancel(request);
        } else {
            LOGGER.info("借用单已取消 【 {} 】", stockoutOrderNo);
        }
    }

    /**
     * 借用无法归还
     * step 1: 待归还 部分状态 的借用单才可设置无法归还
     * step 2: 扣除借用库存
     * step 3: 记录日志
     * step 4: 同步erp
     *
     * @param request
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void cantReturnStockLend(StockLendCantReturnRequest request) {
        StockLendEntity lendEntity = this.getById(request.getLendId());
        if (Objects.isNull(lendEntity)) throw new BusinessServiceException("数据有误，找不到借用单");
        // step 1: 待归还状态的借用单才可设置无法归还
        if (!StockLendStatusEnum.WAIT_RETURN.name().equals(lendEntity.getStatus())
                && !StockLendStatusEnum.PARTIAL_RETURN.name().equals(lendEntity.getStatus()))
            throw new BusinessServiceException("装填是待归还或者部分归还才可以操作无法归还");
        // step 2: 借用单明细存在归还数量，借用单状态改为部分归还，否则为无法归还
        cancelAndReduceLendStock(lendEntity, StockLendStockChangeLogTypeEnum.CANT_RETURN, StockLendLogTypeEnum.CANT_RETURN, StockLendStatusEnum.CANT_RETURN, request.getRemark());
        //更新erp扫描数
        pushErpService.updateQtyErp(request.getLendId());
        // step 4: 同步erp
        ErpStockLendCantReturnRequest erpRequest = new ErpStockLendCantReturnRequest();
        erpRequest.setStockLendCode(lendEntity.getStockLendCode());
        erpRequest.setOperator(loginInfoService.getUserName());
        erpApiService.asyncStockLendCantReturn(erpRequest);
    }

    /**
     * 扣除借用库存,修改借用单状态
     *
     * @param lendEntity
     * @param lendStockChangeLogType
     * @param stockLendLogType
     */
    private void cancelAndReduceLendStock(StockLendEntity lendEntity, StockLendStockChangeLogTypeEnum lendStockChangeLogType, StockLendLogTypeEnum stockLendLogType, StockLendStatusEnum lendStatus, String remark) {
        List<StockLendItemEntity> lendItemEntityList = lendItemService.listByLendId(lendEntity.getLendId());
        List<StockLendItemEntity> needUpdateItemList = new ArrayList<>(16);
        for (StockLendItemEntity lendItem : lendItemEntityList) {
            int noReturnQty = lendItem.getLendQty() - lendItem.getReturnQty();
            if (noReturnQty <= 0) continue;
            //明细设置备注
            lendItem.setRemark(remark);
            needUpdateItemList.add(lendItem);

            //更新库存
            StockLendStockUpdateBo stockUpdate = new StockLendStockUpdateBo();
            stockUpdate.setLend(lendEntity);
            stockUpdate.setBusinessType(lendEntity.getBusinessType());
            stockUpdate.setSku(lendItem.getSku());
            stockUpdate.setChangeLogType(lendStockChangeLogType);
            stockUpdate.setQty(noReturnQty);
            stockLendStockService.updateStock(stockUpdate);

            lendLogService.addLendLog(lendEntity.getLendId(), stockLendLogType,
                    String.format("%s 借出 %s件，归还 %s件，%s件%s", lendItem.getSku(), lendItem.getLendQty(), lendItem.getReturnQty(), noReturnQty, stockLendLogType.getName()));
        }
        if (!needUpdateItemList.isEmpty())
            lendItemService.updateBatchById(needUpdateItemList);

        lendEntity.setStatus(lendStatus.name());
        if (lendStatus.name().equals(StockLendStatusEnum.OVERDUE_CANCELLED.name())) {
            lendEntity.setOverdueCancelDate(new Date());
        }
        lendEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(lendEntity);

        lendLogService.addLendLog(lendEntity.getLendId(), stockLendLogType, String.format("状态变更 【%s】", stockLendLogType.getName()));
        // 借用无法归还，超期取消 推送api-common
        otherModuleService.pushStockLendToEtl(lendEntity);
    }

    /**
     * 超期取消
     * <p>
     * step 1: 超期 的借用单才可设置超期取消
     * step 2: 扣除借用库存
     * step 3: 记录日志
     * step 4: 同步erp
     *
     * @param request
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void overdueCancel(StockLendCantReturnRequest request) {
        StockLendEntity lendEntity = this.getById(request.getLendId());
        if (Objects.isNull(lendEntity)) throw new BusinessServiceException("数据有误，找不到借用单");
        // step 1: 待归还状态的借用单才可设置无法归还
        if (!StockLendStatusEnum.OVERDUE.name().equals(lendEntity.getStatus()))
            throw new BusinessServiceException("只能选择超期的借用单进行超期取消操作");
        //更新erp扫描数
        pushErpService.updateQtyErp(request.getLendId());
        // step 2: 借用单明细存在归还数量，借用单状态改为部分归还，否则为无法归还
        cancelAndReduceLendStock(lendEntity, StockLendStockChangeLogTypeEnum.OVERDUE_CANCELLED, StockLendLogTypeEnum.OVERDUE_CANCELLED, StockLendStatusEnum.OVERDUE_CANCELLED, request.getRemark());
        // step 3: 同步erp
        ErpStockLendCantReturnRequest erpRequest = new ErpStockLendCantReturnRequest();
        erpRequest.setStockLendCode(lendEntity.getStockLendCode());
        erpRequest.setOperator(loginInfoService.getUserName());
        erpApiService.asyncStockLendCantReturn(erpRequest);
    }

    @Transactional
    public void delay(StockLendDelayRequest request) {
        if (request.getDelayDate().before(new Date()))
            throw new BusinessServiceException("时间选择不能小于今天");
        StockLendEntity lendEntity = this.getById(request.getLendId());
        if (Objects.isNull(lendEntity)) throw new BusinessServiceException("数据有误，找不到借用单");
        if (lendEntity.getDelayTimes() > 0) throw new BusinessServiceException("已经延期过，无法再延期");
        if (request.getDelayDate().before(lendEntity.getExpectReturnDate()))
            throw new BusinessServiceException("时间选择不能小于预计归还时间");
        if (DateUtil.offsetDay(lendEntity.getExpectReturnDate(), 15).isBefore(request.getDelayDate()))
            throw new BusinessServiceException("只能延期最多15天");
        if (!StockLendStatusEnum.WAIT_RETURN.name().equals(lendEntity.getStatus())
                && !StockLendStatusEnum.PARTIAL_RETURN.name().equals(lendEntity.getStatus())
                && !StockLendStatusEnum.OVERDUE.name().equals(lendEntity.getStatus()))
            throw new BusinessServiceException("只能选择（超期，待归还，部分归还）的借用单进行延期操作");
        lendEntity.setExpectReturnDate(request.getDelayDate());
        lendEntity.setDelayReason(request.getDelayReason());
        lendEntity.setUpdateBy(loginInfoService.getName());
        lendEntity.setDelayTimes(lendEntity.getDelayTimes() + 1);
        lendEntity.setDelayDate(request.getDelayDate());

        //是否存在部分归还
        List<StockLendItemEntity> lendItemList = lendItemService.getByLendId(lendEntity.getLendId());
        boolean isPartial = lendItemList.stream().anyMatch(temp -> temp.getReturnQty() > 0);
        if (isPartial) {
            lendEntity.setStatus(StockLendStatusEnum.PARTIAL_RETURN.name());
        } else {
            lendEntity.setStatus(StockLendStatusEnum.WAIT_RETURN.name());
        }
        this.updateById(lendEntity);

        //记录日志
        lendLogService.addLendLog(lendEntity.getLendId(), StockLendLogTypeEnum.DELAY, String.format("借用单%s延期，原因%s", lendEntity.getStockLendCode(), lendEntity.getDelayReason()));
        lendItemService.updateBatchById(lendItemList);
    }

    /**
     * 检查是否可以财务对账
     *
     * @param request
     * @return
     */
    public void checkReconciliation(StockLendReconciliationRequest request) {

        if (request.getIdList().isEmpty()) {
            throw new BusinessServiceException("请选择借用单");
        }
        List<StockLendEntity> lendList = stockLendService.listByIds(request.getIdList());
        for (StockLendEntity lend : lendList) {
            if (StockLendStatusEnum.OVERDUE_CANCELLED.name().equals(lend.getStatus())
                    || StockLendStatusEnum.CANT_RETURN.name().equals(lend.getStatus())) {
                return;
            }

            if (!StockLendStatusEnum.RETURNED.name().equals(lend.getStatus())) {
                throw new BusinessServiceException(String.format("借用单【%s】状态为【%s】，无法对账", lend.getStockLendCode(),
                        enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INSIDE_LEND_DOC_STATUS.getName(), lend.getStatus())));
            }

            //已归还
            List<StockLendItemEntity> lendItemList = lendItemService.getByLendId(lend.getLendId());
            if (lendItemList.isEmpty()) {
                throw new BusinessServiceException(String.format("借用单【%s】明细列表为空", lend.getStockLendCode()));
            }

            boolean canReconciliation = lendItemList.stream().anyMatch(item -> item.getInferiorQty() > 0);
            if (!canReconciliation) {
                throw new BusinessServiceException(String.format("借用单【%s】明细次品数都为0，无法对账", lend.getStockLendCode()));
            }
        }
    }

    /**
     * 财务对账
     *
     * @param request
     */
    @Transactional
    public void reconciliation(StockLendReconciliationRequest request) {
        //检查是否可以财务对账
        checkReconciliation(request);
        //取已归还 无法归还 超期取消
        List<StockLendEntity> lendList = stockLendService.list(new LambdaQueryWrapper<StockLendEntity>().in(StockLendEntity::getStatus,
                        Arrays.asList(StockLendStatusEnum.OVERDUE_CANCELLED.name(), StockLendStatusEnum.CANT_RETURN.name(), StockLendStatusEnum.RETURNED.name()))
                .in(StockLendEntity::getLendId, request.getIdList()));
        if (lendList.isEmpty())
            throw new BusinessServiceException("借用单为空");
        Map<Integer, StockLendEntity> lendMap = lendList.stream().collect(Collectors.toMap(StockLendEntity::getLendId, Function.identity()));
        //获取明细
        List<Integer> lendIdList = lendList.stream().map(StockLendEntity::getLendId).collect(Collectors.toList());
        List<StockLendItemEntity> lendItemList = lendItemService.getByLendIdList(lendIdList);
        //获取吊牌价
        List<StockLendItemEntity> needSettleItemList = lendItemList.stream().filter(item -> item.getInferiorQty() > 0 || item.getLendQty() > item.getReturnQty()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needSettleItemList))
            throw new BusinessServiceException("明细已全部归还");

        List<String> skuList = needSettleItemList.stream().map(StockLendItemEntity::getSku).distinct().collect(Collectors.toList());

        //通过sku列表获取吊牌价
        Map<String, BigDecimal> specPriceMap = fetchPriceBySkuList(skuList);
        //设置计算价格
        //更改借用单状态 - 财务对账

        Map<Integer, List<StockLendItemEntity>> lendItemMap = needSettleItemList.stream().collect(Collectors.groupingBy(StockLendItemEntity::getLendId));
        List<StockLendEntity> needUpdateLendList = new ArrayList<>(lendItemMap.size());
        List<StockLendItemEntity> needUpdateLendItemList = new ArrayList<>();
        for (Map.Entry<Integer, List<StockLendItemEntity>> entry : lendItemMap.entrySet()) {
            BigDecimal total = BigDecimal.ZERO;
            //总价值=（借出数量-归还数量+次品数量）*单价
            for (StockLendItemEntity item : entry.getValue()) {
                BigDecimal price = specPriceMap.get(item.getSku());
                if (Objects.isNull(price)) continue;
                item.setPrice(price);
                item.setTotal(price.multiply(new BigDecimal(item.getLendQty() - item.getReturnQty() + item.getInferiorQty())));
                item.setUpdateBy(loginInfoService.getName());
                total = total.add(item.getTotal());
                needUpdateLendItemList.add(item);
            }
            StockLendEntity lend = lendMap.get(entry.getKey());
            lend.setTotal(total);
            lend.setStatus(StockLendStatusEnum.RECONCILIATION.name());
            lend.setUpdateBy(loginInfoService.getName());
            needUpdateLendList.add(lend);
            //记录日志
            lendLogService.addLendLog(lend.getLendId(), StockLendLogTypeEnum.RECONCILIATION, String.format("借用单 %s 【财务对账】", lend.getStockLendCode()));
        }
        updateBatchById(needUpdateLendList);
        lendItemService.updateBatchById(needUpdateLendItemList);

    }

    /**
     * 通过sku列表获取吊牌价
     *
     * @param skuList
     * @return
     */
    private Map<String, BigDecimal> fetchPriceBySkuList(List<String> skuList) {
        GetProductSpecPriceInfoRequest priceInfoRequest = new GetProductSpecPriceInfoRequest();
        priceInfoRequest.setSpecSkus(skuList);
        BaseListResponse<ProductSpecPriceDTO> specPriceData = productSpecFeignClient.getSpecPriceInfos(priceInfoRequest);
        if (Objects.isNull(specPriceData) || CollectionUtils.isEmpty(specPriceData.getContent())) {
            throw new BusinessServiceException("获取吊牌价失败");
        }
        LOGGER.info("fetchProductLabelTableInfos specPriceData={}", JsonMapper.toJson(specPriceData));
        return specPriceData.getContent().stream().filter(dto -> ObjectUtil.isNotNull(dto.getPrice())).collect(Collectors.toMap(ProductSpecPriceDTO::getSpecSku, ProductSpecPriceDTO::getPrice));
    }

    /**
     * 记录质检不合格原因
     *
     * @param internalBoxItem
     * @param unqualifiedReasonInfo
     */
    @Transactional
    public void recordQcUnqualifiedReasonInfo(StockInternalBoxItemEntity internalBoxItem, String unqualifiedReasonInfo, Integer qty) {
        if (!StringUtils.hasText(internalBoxItem.getStockoutOrderNo()))
            return;
        StockLendEntity stockLend = stockLendService.findByStockLendCode(internalBoxItem.getStockoutOrderNo());
        if (Objects.isNull(stockLend)) {
            LOGGER.error("借用单【 {} 】找不到借用记录", internalBoxItem.getStockoutOrderNo());
            return;
        }

        StockLendItemEntity lendItem = lendItemService.findByStockLendIdAndSku(stockLend.getLendId(), internalBoxItem.getSku());
        if (Objects.isNull(lendItem)) {
            LOGGER.error("借用单【 {} 】找不到sku【 {} 】", internalBoxItem.getStockoutOrderNo(), internalBoxItem.getSku());
            return;
        }
        lendItem.setInferiorRemark(org.apache.commons.lang3.StringUtils.substring(unqualifiedReasonInfo, 0, 200));
        lendItem.setUpdateBy(loginInfoService.getName());
        lendItemService.updateById(lendItem);

        lendLogService.addLendLog(stockLend.getLendId(), StockLendLogTypeEnum.QC_UNQUALIFIED, String.format("不合格 %s 件，原因：%s", qty, unqualifiedReasonInfo));
    }
}
