package com.nsy.wms.business.service.stockout;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutUniversalSortingTaskStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutUniversalSortingTaskItemPageRequest;
import com.nsy.api.wms.request.stockout.StockoutUniversalSortingTaskPageRequest;
import com.nsy.api.wms.request.stockout.StockoutUniversalSortingTaskScanItemRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutUniversalSortingTaskItemPageResponse;
import com.nsy.api.wms.response.stockout.StockoutUniversalSortingTaskPageResponse;
import com.nsy.api.wms.response.stockout.StockoutUniversalSortingTaskStartItemResponse;
import com.nsy.api.wms.response.stockout.StockoutUniversalSortingTaskStartResponse;
import com.nsy.wms.business.manage.user.upload.StockoutUniversalSortingTaskImport;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.stockout.StockoutUniversalSortingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutUniversalSortingTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutUniversalSortingTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutUniversalSortingTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class StockoutUniversalSortingTaskService extends ServiceImpl<StockoutUniversalSortingTaskMapper, StockoutUniversalSortingTaskEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutUniversalSortingTaskService.class);

    @Resource
    LoginInfoService loginInfoService;
    @Resource
    StockoutUniversalSortingTaskItemService itemService;
    @Resource
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Resource
    StockoutUniversalSortingTaskItemMapper itemMapper;

    @Transactional
    @JLock(keyConstant = "generateUniversalSortingTask", lockKey = "#queueId")
    public void generateTask(List<StockoutUniversalSortingTaskImport> importDataList, String remark, Integer queueId) {
        StockoutUniversalSortingTaskEntity sortingTask = findByQueueId(queueId);
        if (Objects.isNull(sortingTask)) {
            StockoutUniversalSortingTaskEntity taskEntity = new StockoutUniversalSortingTaskEntity();
            taskEntity.setStatus(StockoutUniversalSortingTaskStatusEnum.WAIT.name());
            taskEntity.setRemark(remark);
            taskEntity.setQueueId(queueId);
            taskEntity.setCreateBy(loginInfoService.getName());
            this.save(taskEntity);

            generateTaskItem(importDataList, taskEntity);
        } else {
            if (!StockoutUniversalSortingTaskStatusEnum.WAIT.name().equals(sortingTask.getStatus()))
                throw new BusinessServiceException(String.format("该任务已经在分拣 %s", sortingTask.getId()));
            generateTaskItem(importDataList, sortingTask);
        }
    }


    private void generateTaskItem(List<StockoutUniversalSortingTaskImport> importDataList, StockoutUniversalSortingTaskEntity taskEntity) {
        AtomicInteger index = new AtomicInteger(itemService.getBaseMapper().findMaxOutlet(taskEntity.getId()));
        List<StockoutUniversalSortingTaskItemEntity> itemList = importDataList.stream()
                .collect(Collectors.groupingBy(StockoutUniversalSortingTaskImport::getDst))
                .entrySet()
                .stream().map(entry -> {
                    String dst = entry.getKey();
                    List<StockoutUniversalSortingTaskImport> stockoutUniversalSortingTaskImports = entry.getValue();
                    //存在则获取，不存在则自增
                    StockoutUniversalSortingTaskItemEntity firstTaskItem = itemService.findFirstByTaskIdAndDst(taskEntity.getId(), dst);
                    int outlet;
                    if (Objects.isNull(firstTaskItem)) {
                        index.addAndGet(1);
                        outlet = index.get();
                    } else {
                        outlet = firstTaskItem.getOutlet();
                    }
                    return stockoutUniversalSortingTaskImports.stream().map(importData -> {
                        StockoutUniversalSortingTaskItemEntity firstSameItem = itemService.findFirstByTaskIdAndScanCodeAndDst(taskEntity.getId(), importData.getScanCode(), importData.getDst());
                        if (!Objects.isNull(firstSameItem)) {
                            LOGGER.error("明细重复 扫描编码 {} 目的地 {}", importData.getScanCode(), importData.getDst());
                            return null;
                        }
                        StockoutUniversalSortingTaskItemEntity itemEntity = new StockoutUniversalSortingTaskItemEntity();
                        itemEntity.setTaskId(taskEntity.getId());
                        itemEntity.setScanCode(importData.getScanCode());
                        itemEntity.setDst(importData.getDst());
                        itemEntity.setExpectQty(importData.getQty());
                        itemEntity.setCreateBy(loginInfoService.getName());
                        itemEntity.setOutlet(outlet);
                        return itemEntity;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                }).flatMap(Collection::stream).collect(Collectors.toList());
        itemService.saveBatch(itemList);
    }

    /**
     * 列表
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutUniversalSortingTaskPageResponse> page(StockoutUniversalSortingTaskPageRequest request) {
        PageResponse<StockoutUniversalSortingTaskPageResponse> pageResponse = new PageResponse<>();
        IPage<StockoutUniversalSortingTaskPageResponse> pageResult = this.baseMapper.searchPage(new Page<>(request.getPageIndex(), request.getPageSize()), request);

        pageResponse.setTotalCount(pageResult.getTotal());
        pageResponse.setContent(pageResult.getRecords());
        Map<String, String> stockoutEasyScanTasksEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_UNIVERSAL_SORTING_TASK_STATUS.getName());
        pageResult.getRecords().forEach(record -> record.setStatusCn(stockoutEasyScanTasksEnumMap.get(record.getStatus())));
        return pageResponse;
    }

    /**
     * 明细列表
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutUniversalSortingTaskItemPageResponse> itemPage(StockoutUniversalSortingTaskItemPageRequest request) {
        PageResponse<StockoutUniversalSortingTaskItemPageResponse> pageResponse = new PageResponse<>();
        IPage<StockoutUniversalSortingTaskItemPageResponse> pageResult = itemMapper.itemPage(new Page(request.getPageIndex(), request.getPageSize()), request);

        pageResponse.setTotalCount(pageResult.getTotal());
        pageResponse.setContent(pageResult.getRecords());

        return pageResponse;
    }

    /**
     * 开始分拣
     *
     * @param taskId
     * @return
     */
    public StockoutUniversalSortingTaskStartResponse startSorting(Integer taskId) {
        StockoutUniversalSortingTaskEntity task = getById(taskId);
        if (Objects.isNull(task))
            throw new BusinessServiceException(String.format("分拣任务 %s 不存在", taskId));
        if (StockoutUniversalSortingTaskStatusEnum.SORTED.name().equals(task.getStatus()))
            throw new BusinessServiceException(String.format("分拣任务 %s 已分拣", taskId));
        List<StockoutUniversalSortingTaskItemEntity> taskItemList = itemService.listByTaskId(taskId);

        StockoutUniversalSortingTaskStartResponse response = new StockoutUniversalSortingTaskStartResponse();
        response.setId(taskId);
        response.setItemList(taskItemList.stream()
                .map(temp -> {
                    response.setTotalQty(response.getTotalQty() + temp.getExpectQty());
                    response.setScanQty(response.getScanQty() + temp.getScanQty());
                    return BeanUtil.toBean(temp, StockoutUniversalSortingTaskStartItemResponse.class);
                }).collect(Collectors.toList()));

        //更新任务
        if (StockoutUniversalSortingTaskStatusEnum.WAIT.name().equals(task.getStatus())) {
            task.setStatus(StockoutUniversalSortingTaskStatusEnum.SORTING.name());
            task.setOperator(loginInfoService.getName());
            task.setOperateBeginDate(new Date());
            task.setUpdateBy(loginInfoService.getName());
            this.updateById(task);
        }
        return response;
    }

    /**
     * 扫描明细
     *
     * @param request
     * @return
     */
    public StockoutUniversalSortingTaskStartItemResponse scanItem(StockoutUniversalSortingTaskScanItemRequest request) {
        StockoutUniversalSortingTaskItemEntity taskItem = itemService.getById(request.getItemId());
        if (Objects.isNull(taskItem))
            throw new BusinessServiceException(String.format("明细 %s 不存在", request.getItemId()));
        StockoutUniversalSortingTaskEntity task = getById(taskItem.getTaskId());
        if (Objects.isNull(task))
            throw new BusinessServiceException(String.format("分拣任务 %s 不存在", taskItem.getTaskId()));
        if (StockoutUniversalSortingTaskStatusEnum.SORTED.name().equals(task.getStatus()))
            throw new BusinessServiceException(String.format("分拣任务 %s 已分拣", taskItem.getTaskId()));

        if (request.getScanQty() > taskItem.getExpectQty())
            throw new BusinessServiceException("不能超过带分拣数");

        taskItem.setScanQty(request.getScanQty());
        taskItem.setUpdateBy(loginInfoService.getName());
        itemService.updateById(taskItem);
        StockoutUniversalSortingTaskStartItemResponse response = BeanUtil.toBean(taskItem, StockoutUniversalSortingTaskStartItemResponse.class);

        List<StockoutUniversalSortingTaskItemEntity> taskItemList = itemService.listByTaskIdAndOutlet(taskItem.getTaskId(), taskItem.getOutlet());
        response.setFinishOutlet(taskItemList.stream().allMatch(item -> item.getScanQty().equals(item.getExpectQty())));
        buildTotalQty(response, taskItem.getTaskId(), taskItem.getDst());
        return response;
    }

    /**
     * 生成总扫描数 总待扫描数
     *
     * @param response
     * @param taskId
     * @param dst
     */
    private void buildTotalQty(StockoutUniversalSortingTaskStartItemResponse response, Integer taskId, String dst) {
        List<StockoutUniversalSortingTaskItemEntity> taskItemList = itemService.findListByTaskIdAndDst(taskId, dst);
        response.setTotalExpectQty(taskItemList.stream().mapToInt(StockoutUniversalSortingTaskItemEntity::getExpectQty).sum());
        response.setTotalScanQty(taskItemList.stream().mapToInt(StockoutUniversalSortingTaskItemEntity::getScanQty).sum());
    }

    /**
     * 完成分拣
     *
     * @param taskId
     */
    public void finishSorting(Integer taskId) {
        StockoutUniversalSortingTaskEntity task = getById(taskId);
        if (Objects.isNull(task))
            throw new BusinessServiceException(String.format("分拣任务 %s 不存在", taskId));
        if (StockoutUniversalSortingTaskStatusEnum.SORTED.name().equals(task.getStatus()))
            throw new BusinessServiceException(String.format("分拣任务 %s 已分拣", taskId));

        task.setStatus(StockoutUniversalSortingTaskStatusEnum.SORTED.name());
        task.setOperateEndDate(new Date());
        task.setUpdateBy(loginInfoService.getName());
        this.updateById(task);
    }

    public StockoutUniversalSortingTaskEntity findByQueueId(Integer queueId) {
        return getOne(new LambdaQueryWrapper<StockoutUniversalSortingTaskEntity>()
                .eq(StockoutUniversalSortingTaskEntity::getQueueId, queueId));
    }
}
