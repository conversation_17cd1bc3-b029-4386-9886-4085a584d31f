package com.nsy.wms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

public class ErpFeedbackReceivedRequest {
    /**
     * 出库箱码
     */
    @JsonProperty("SupplierDeliveryBoxCode")
    private String supplierDeliveryBoxCode;
    /**
     * 操作人
     */
    @JsonProperty("Operator")
    private String operator;
    /**
     * 区域
     */
    @JsonProperty("DeliveryLocation")
    private String location;

    @JsonProperty("PositionCode")
    private String positionCode;

    @JsonProperty("IsAllot")
    private Integer isAllot;

    @JsonProperty("FeedbackReceivedItemList")
    private List<ErpFeedbackReceivedItem> feedbackReceivedItemList;

    public String getSupplierDeliveryBoxCode() {
        return supplierDeliveryBoxCode;
    }

    public void setSupplierDeliveryBoxCode(String supplierDeliveryBoxCode) {
        this.supplierDeliveryBoxCode = supplierDeliveryBoxCode;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<ErpFeedbackReceivedItem> getFeedbackReceivedItemList() {
        return feedbackReceivedItemList;
    }

    public void setFeedbackReceivedItemList(List<ErpFeedbackReceivedItem> feedbackReceivedItemList) {
        this.feedbackReceivedItemList = feedbackReceivedItemList;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public Integer getIsAllot() {
        return isAllot;
    }

    public void setIsAllot(Integer isAllot) {
        this.isAllot = isAllot;
    }

    public static class ErpFeedbackReceivedItem {
        @JsonProperty("Sku")
        private String sku;
        @JsonProperty("ShelvedQty")
        private Integer shelvedQty;
        @JsonProperty("PurchasePlanNo")
        private String purchasePlanNo;

        @JsonProperty("ShelfTime")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date shelfTime;

        @JsonProperty("ReceiveDate")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Date receiveDate;

        @JsonProperty("StockinOrderItemId")
        private Integer stockinOrderItemId;

        public Integer getStockinOrderItemId() {
            return stockinOrderItemId;
        }

        public void setStockinOrderItemId(Integer stockinOrderItemId) {
            this.stockinOrderItemId = stockinOrderItemId;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public Integer getShelvedQty() {
            return shelvedQty;
        }

        public void setShelvedQty(Integer shelvedQty) {
            this.shelvedQty = shelvedQty;
        }

        public String getPurchasePlanNo() {
            return purchasePlanNo;
        }

        public void setPurchasePlanNo(String purchasePlanNo) {
            this.purchasePlanNo = purchasePlanNo;
        }

        public Date getShelfTime() {
            return shelfTime;
        }

        public void setShelfTime(Date shelfTime) {
            this.shelfTime = shelfTime;
        }

        public Date getReceiveDate() {
            return receiveDate;
        }

        public void setReceiveDate(Date receiveDate) {
            this.receiveDate = receiveDate;
        }
    }
}
