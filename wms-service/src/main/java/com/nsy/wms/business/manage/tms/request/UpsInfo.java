package com.nsy.wms.business.manage.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel("UPS额外信息")
public class UpsInfo {

    @ApiModelProperty(value = "服务类型 65:UPS速快 08:UPS快捷", name = "serviceType")
    private String serviceType;

    @ApiModelProperty(value = "发票编号", name = "invoiceNumber")
    private String invoiceNumber;

    @ApiModelProperty(value = "包裹描述", name = "packageDescription")
    private String packageDescription;

    @ApiModelProperty(value = "包裹数", name = "packageCount")
    private int packageCount;

    @ApiModelProperty(value = "运费", name = "freightCharges")
    private String freightCharges;

    @ApiModelProperty(value = "手续费", name = "handingFee")
    private String handingFee;

    @ApiModelProperty(value = "使用UPS无纸化发票,true ups发票,false 自己的发票", name = "useUpsInvoice")
    private Boolean useUpsInvoice;

    @ApiModelProperty(value = "ups到付账号", name = "freightCollect")
    private String freightCollectAccount;

    @ApiModelProperty(value = "ups到付账号邮编", name = "freightCollectPostalCode")
    private String freightCollectPostalCode;

    public String getFreightCollectAccount() {
        return freightCollectAccount;
    }

    public void setFreightCollectAccount(String freightCollectAccount) {
        this.freightCollectAccount = freightCollectAccount;
    }

    public String getFreightCollectPostalCode() {
        return freightCollectPostalCode;
    }

    public void setFreightCollectPostalCode(String freightCollectPostalCode) {
        this.freightCollectPostalCode = freightCollectPostalCode;
    }

    public Boolean getUseUpsInvoice() {
        return useUpsInvoice;
    }

    public void setUseUpsInvoice(Boolean useUpsInvoice) {
        this.useUpsInvoice = useUpsInvoice;
    }

    public String getFreightCharges() {
        return freightCharges;
    }

    public void setFreightCharges(String freightCharges) {
        this.freightCharges = freightCharges;
    }

    public int getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(int packageCount) {
        this.packageCount = packageCount;
    }

    public String getPackageDescription() {
        return packageDescription;
    }

    public void setPackageDescription(String packageDescription) {
        this.packageDescription = packageDescription;
    }

    public String getHandingFee() {
        return handingFee;
    }

    public void setHandingFee(String handingFee) {
        this.handingFee = handingFee;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }


    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }
}
