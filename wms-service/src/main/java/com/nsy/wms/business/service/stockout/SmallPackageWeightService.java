package com.nsy.wms.business.service.stockout;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.constants.StockoutShipmentMapConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stockin.StockoutPackageCompleteMessage;
import com.nsy.api.wms.domain.stockout.StockoutDeliverProductPrint;
import com.nsy.api.wms.domain.stockout.StockoutPackageBagItemExport;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.common.IsEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPackageBagTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.PackageBagItemDownloadListRequest;
import com.nsy.api.wms.request.stockout.SmallPackageJumpLoadRequest;
import com.nsy.api.wms.request.stockout.SmallPackageWeightListRequest;
import com.nsy.api.wms.request.stockout.StockoutDeliverySignatureFormPrint;
import com.nsy.api.wms.request.stockout.StockoutPackageBagCompleteRequest;
import com.nsy.api.wms.request.stockout.StockoutPackageBagItemDeleteRequest;
import com.nsy.api.wms.request.stockout.StockoutPackageBagListRequest;
import com.nsy.api.wms.request.stockout.StockoutPackagePrintRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentUpdateAndAddRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.SmallPackageWeightResponse;
import com.nsy.api.wms.response.stockout.StockoutPackageBagItemExportResponse;
import com.nsy.api.wms.response.stockout.StockoutPackageBagListResponse;
import com.nsy.api.wms.response.stockout.TmsPackageDetailInfo;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPrematchRemoveService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.stockout.ship.StockoutShipService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.elasticjob.stock.StockTransferCrossSpaceJob;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLogEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPackageBagEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPackageBagItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipperInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.SmallPackageWeightMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutPackageBagItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;
import com.nsy.wms.utils.FreeMarkerTemplateUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.mp.TenantContext;
import freemarker.template.Template;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SmallPackageWeightService extends ServiceImpl<StockoutShipmentMapper, StockoutShipmentEntity> implements IDownloadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SmallPackageWeightService.class);
    @Autowired
    private SmallPackageWeightMapper smallPackageWeightMapper;
    @Autowired
    private StockoutShipmentMapper stockoutShipmentMapper;
    @Autowired
    private StockoutShipmentService stockoutShipmentService;
    @Autowired
    private StockoutPackageBagService stockoutPackageBagService;
    @Autowired
    private StockoutPackageBagItemService stockoutPackageBagItemService;
    @Autowired
    private StockoutPackageBagItemMapper stockoutPackageBagItemMapper;
    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    private StockoutOrderMapper stockoutOrderMapper;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    StockTransferCrossSpaceJob stockTransferCrossSpaceJob;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    StockoutShipmentItemMapper stockoutShipmentItemMapper;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Autowired
    StockoutShipperInfoService stockoutShipperInfoService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutShipmentErpPickingBoxService erpPickingBoxService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutShipmentConfirmService stockoutShipmentConfirmService;
    @Autowired
    StockoutShipService stockoutShipService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockoutShipmentItemService stockoutShipmentItemService;
    @Autowired
    StockoutOrderShipService stockoutOrderShipService;
    @Autowired
    TmsApiService tmsApiService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockPrematchRemoveService stockPrematchRemoveService;


    /**
     * 物流公司中是否有这个物流单号,且唯一
     *
     * @param request
     * @return
     */
    private boolean isExistCompanyAndNoOnly(SmallPackageWeightListRequest request) {
        LambdaQueryWrapper<StockoutShipmentEntity> wrapper = new LambdaQueryWrapper<StockoutShipmentEntity>()
                .eq(StockoutShipmentEntity::getLogisticsCompany, request.getLogisticsCompany())
                .eq(StockoutShipmentEntity::getLogisticsNo, request.getLogisticsNo())
                .eq(StockoutShipmentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED)
                .ne(StockoutShipmentEntity::getStatus, StockoutShipmentStatusEnum.SHIPPED.name());
        StockoutShipmentEntity stockoutShipmentEntity = stockoutShipmentMapper.selectOne(wrapper);
        if (ObjectUtils.isEmpty(stockoutShipmentEntity)) {
            LambdaQueryWrapper<StockoutOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StockoutOrderEntity::getLogisticsCompany, request.getLogisticsCompany()).eq(StockoutOrderEntity::getLogisticsNo, request.getLogisticsNo())
                    .ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.DELIVERED.name()).orderByDesc(StockoutOrderEntity::getStockoutOrderId);
            List<StockoutOrderEntity> list = stockoutOrderService.list(queryWrapper);
            if (!CollectionUtils.isEmpty(list) && Objects.equals(list.get(0).getStatus(), StockoutOrderStatusEnum.CANCELLED.name())) {
                throw new BusinessServiceException("出库单" + list.get(0).getStockoutOrderNo() + "物流单号:" + list.get(0).getLogisticsNo() + "业务已经取消，请核对");
            }
            throw new BusinessServiceException(request.getLogisticsNo() + "此物流单号不存在 或者 已发货，请核对");
        } else {
            return isReady(request);
        }
    }

    /**
     * 查看请求的物流单号的状态是否为装箱完成状态
     *
     * @param request
     * @return
     */
    private boolean isReady(SmallPackageWeightListRequest request) {
        return smallPackageWeightMapper.isReady(request.getLogisticsNo(), request.getLogisticsCompany()) > 0;
    }


    /**
     * 扫描物流单号
     *
     * @param request
     * @return PageResponse
     */
    public PageResponse<SmallPackageWeightResponse> getShipmentInfo(SmallPackageWeightListRequest request) {
        // 查询 是否转运单号，替换成原始单号
        request.setLogisticsNo(getOriginLogistics(request.getLogisticsCompany(), request.getLogisticsNo()));
        // 校验此物流单号 是否被其他包裹占用
        checkUniqueLogisticsNo(request);
        // 这家公司是否有这一条物流单号,且是待发货状态
        if (isExistCompanyAndNoOnly(request)) {
            // 校验出库单
            validateStockoutOrder(request.getLogisticsNo(), request.getLogisticsCompany());
            // 从bag表中获取该公司今天发了几个单，当前日期和公司发单数量 用这个来区别发货编号
            String boxIndex;
            String bagCode;
            DateTime dateTime = DateTime.now();
            if (StringUtils.isNotBlank(request.getBagCode())) {
                bagCode = request.getBagCode();
                boxIndex = bagCode.substring(bagCode.lastIndexOf(StringConstant.HORIZONTAL_BAR) + 1);
            } else {

                String startTime = dateTime.toString(StockConstant.DATE_FORMAT_START);
                String endTime = dateTime.toString(StockConstant.DATE_FORMAT_END);
                String codeDate = dateTime.toString(StockConstant.DATE_FORMAT);
                String initBoxIndex = smallPackageWeightMapper.getBoxIndex(request.getLogisticsCompany(), startTime, endTime);
                // 第一个进行打包，在bag表中增加一条记录为打包中
                boxIndex = StringUtils.isBlank(initBoxIndex) ? "1" : String.valueOf(Integer.parseInt(initBoxIndex) + 1);
                bagCode = request.getLogisticsCompanyCode() + codeDate + StringConstant.HORIZONTAL_BAR + boxIndex;
            }
            PageResponse<SmallPackageWeightResponse> pageResponse = new PageResponse<>();
            Page<SmallPackageWeightResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
            IPage<SmallPackageWeightResponse> pageResult = smallPackageWeightMapper.pageSearchList(page, request);
            List<SmallPackageWeightResponse> resultList = pageResult.getRecords().stream().map(projection -> {
                projection.setBagCode(bagCode);
                projection.setBoxIndex(boxIndex);
                projection.setCreateBy(loginInfoService.getName());
                projection.setCreateDate(dateTime.toString(DateUtils.DATE_FORMAT_DATE4));
                SmallPackageWeightResponse response = new SmallPackageWeightResponse();
                BeanUtils.copyProperties(projection, response);
                return response;
            }).collect(Collectors.toList());
            pageResponse.setTotalCount(page.getTotal());
            pageResponse.setContent(resultList);
            return pageResponse;
        } else {
            return new PageResponse<>();
        }
    }

    // 查询 是否转运单号，替换成原始单号
    public String getOriginLogistics(String logisticsCompany, String logisticsNo) {
        if (StringUtils.isBlank(logisticsNo) || StringUtils.isBlank(logisticsCompany)) {
            return logisticsNo;
        }
        // 4PX单独做转换
        if (!LogisticsCompanyConstant.FOURPX_LOGISTICS_COMPANY.contains(logisticsCompany)) {
            return logisticsNo;
        }
        LambdaQueryWrapper<StockoutOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutOrderEntity::getLogisticsCompany, logisticsCompany).and(wrapper -> wrapper.eq(StockoutOrderEntity::getLogisticsNo, logisticsNo).or().eq(StockoutOrderEntity::getSecondaryNumber, logisticsNo));
        // 校验是否是转运单号
        List<StockoutOrderEntity> list = stockoutOrderService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            // 查tms表
            try {
                TmsPackageDetailInfo packageInfo = tmsApiService.getPackageInfo(logisticsNo);
                if (StringUtils.isBlank(packageInfo.getLogisticsNo())) {
                    return logisticsNo;
                }
                // 更新转运单号
                queryWrapper.clear();
                queryWrapper.eq(StockoutOrderEntity::getLogisticsCompany, logisticsCompany);
                queryWrapper.eq(StockoutOrderEntity::getLogisticsNo, packageInfo.getLogisticsNo());
                List<StockoutOrderEntity> list1 = stockoutOrderService.list(queryWrapper);
                if (StringUtils.isNotBlank(packageInfo.getSecondaryNumber()) && !CollectionUtils.isEmpty(list1)) {
                    list1.get(0).setSecondaryNumber(packageInfo.getSecondaryNumber());
                    stockoutOrderService.updateById(list1.get(0));
                }
                return packageInfo.getLogisticsNo();
            } catch (Exception e) {
                LOGGER.error("获取包裹详情失败：" + logisticsNo, e);
                return logisticsNo;
            }
        }
        // 替换成原始单号
        return list.get(0).getLogisticsNo();
    }

    private void checkUniqueLogisticsNo(SmallPackageWeightListRequest request) {
        LambdaQueryWrapper<StockoutPackageBagItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutPackageBagItemEntity::getLogisticsNo, request.getLogisticsNo());
        List<StockoutPackageBagItemEntity> bagItemList = stockoutPackageBagItemService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(bagItemList)) {
            List<Integer> shipmentIds = bagItemList.stream().map(StockoutPackageBagItemEntity::getShipmentId).distinct().collect(Collectors.toList());
            List<String> orders = stockoutPackageBagItemService.getBaseMapper().getOrderNo(request.getLogisticsNo(), shipmentIds);
            if (!CollectionUtils.isEmpty(orders) && orders.stream().allMatch(it -> it != null && it.startsWith("BH"))) {
                // 同一个物流单号有n个包裹，且都是BH开头的
                return;
            }
            throw new BusinessServiceException("物流单号:" + request.getLogisticsNo() + "存在相同的物流单号包裹，请核对");
        }
    }

    // 获取物流单号下状态为取消中的出库单
    public StockoutOrderEntity getCancellingStockoutOrder(String logisticsNo, String logisticsCompany) {
        LambdaQueryWrapper<StockoutOrderEntity> bindQuery = new LambdaQueryWrapper<>();
        bindQuery.eq(StringUtils.isNotBlank(logisticsCompany), StockoutOrderEntity::getLogisticsCompany, logisticsCompany)
                .eq(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.CANCELLING.name())
                .and(wp -> wp.eq(StringUtils.isNotBlank(logisticsNo), StockoutOrderEntity::getLogisticsNo, logisticsNo)
                        .or().eq(StringUtils.isNotBlank(logisticsNo), StockoutOrderEntity::getSecondaryNumber, logisticsNo));
        return stockoutOrderMapper.selectOne(bindQuery);
    }

    /**
     * 校验出库单是否设置发货，是否需要撤货
     */
    protected void validateStockoutOrder(String logisticsNo, String logisticsCompany) {

        LambdaQueryWrapper<StockoutOrderEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(logisticsNo), StockoutOrderEntity::getLogisticsNo, logisticsNo)
                .eq(StringUtils.isNotBlank(logisticsCompany), StockoutOrderEntity::getLogisticsCompany, logisticsCompany)
                .ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.CANCELLED.name())
                .ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.DELIVERED.name());
        List<StockoutOrderEntity> orderEntityList = stockoutOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orderEntityList)) {
            throw new BusinessServiceException("未找到出库单");
        }
        // 获得出库单号列表
        List<String> stockoutOrderNos = orderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList());
        // 校验
        stockoutShipmentConfirmService.validateStockOutOrder(stockoutOrderNos);
    }

    /**
     * 撤货
     * 1. 修改出库单状态
     * 2. 逻辑删除装箱清单
     * 3. 将商品从发货库位移至撤货箱中
     * 4. 搬动撤货箱
     * 5. 库位扣减
     */
    @Transactional
    public void withdraw(SmallPackageWeightListRequest request) {
        StockoutOrderEntity cancellingOrderEntity = getCancellingStockoutOrder(request.getLogisticsNo(), request.getLogisticsCompany());
        // 1. 修改出库单状态
        LambdaUpdateWrapper<StockoutOrderEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StockoutOrderEntity::getStockoutOrderId, cancellingOrderEntity.getStockoutOrderId()).set(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.CANCELLED.name());
        stockoutOrderService.update(updateWrapper);
        // 2. 逻辑删除装箱清单
        List<StockoutShipmentItemEntity> updateShipmentItemList = stockoutShipmentItemService.list(new QueryWrapper<StockoutShipmentItemEntity>().lambda()
                .in(StockoutShipmentItemEntity::getStockoutOrderNo, Collections.singletonList(cancellingOrderEntity.getStockoutOrderNo())));
        updateShipmentItemList.forEach(o -> o.setIsDeleted(IsEnum.IS.getCode()));
        stockoutShipmentItemService.updateBatchById(updateShipmentItemList);

        List<StockoutShipmentSearchResult> shipmentList = stockoutShipmentMapper.findShipmentConfirmItemList(Collections.singletonList(cancellingOrderEntity.getStockoutOrderNo()), StockoutShipmentStatusEnum.PACKING_END.name(), null);
        stockoutShipmentConfirmService.deleteShipment(shipmentList);

        // 3. 将商品从发货库位移至撤货箱中
        StockInternalBoxEntity stockInternalBoxEntity = stockInternalBoxService.getWithdrawalByWorkspace(cancellingOrderEntity.getWorkspace(), cancellingOrderEntity.getSpaceId());
        if (Objects.isNull(stockInternalBoxEntity))
            throw new BusinessServiceException("查询不到公共工作区！");
        List<StockoutShipmentSearchResult> cancelingShipmentList = shipmentList.stream().filter(s -> IsEnum.IS.getCode().equals(s.getIsDeleted())).collect(Collectors.toList());
        cancelingShipmentList.forEach(s -> {
            // 4. 绑定到工作区域撤货箱
            stockoutShipmentConfirmService.updateWithdrawalByInternalBoxCodeAndSpecId(s.getStockoutOrderItemId(), stockInternalBoxEntity.getInternalBoxCode(), s.getSku(), s.getQty());
        });
        // 5. 发货库位扣减
        stockoutShipService.shippingUpdateStock(cancelingShipmentList, cancellingOrderEntity.getSpaceId());
    }

    /**
     * 更新重量，增加bagitem记录，增加bag记录
     */
    @Transactional
    public void updateAndSaveInfo(StockoutShipmentUpdateAndAddRequest request) {
        request.setLogisticsNo(getOriginLogistics(request.getLogisticsCompany(), request.getLogisticsNo()));
        // 查询bag表，是否有这个发货编号
        LambdaQueryWrapper<StockoutPackageBagEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(request.getBagCode()), StockoutPackageBagEntity::getBagCode, request.getBagCode());
        StockoutPackageBagEntity bagEntity = stockoutPackageBagService.getOne(wrapper);
        Integer bagId;
        if (ObjectUtils.isEmpty(bagEntity)) {
            // bag表中没有记录，说明是第一次打包，新增状态为打包中,返回bagid
            bagId = this.packing(request);
        } else {
            if (StockoutPackageBagTypeEnum.PACKED.name().equals(bagEntity.getStatus())) {
                throw new BusinessServiceException("此包裹已经打包完成！无法继续放入");
            }
            bagId = bagEntity.getBagId();
        }
        // 更新shipment重量
        this.updateWeight(request);
        // bagitem增加记录
        this.addPackageItemInfo(bagId, request.getWeight(), request.getShipmentId(), request.getLogisticsNo(), request.getLogisticsCompany());
        stockoutPackageBagService.sendStockoutWeightCheckMsg(request.getShipmentId(), request.getWeight());
        // bag中更新重量和数量
        SpringUtil.getBean(SmallPackageWeightService.class).updateBagInfo(request.getBagCode());
        List<StockoutOrderEntity> orderLog = stockoutOrderService.list(new LambdaQueryWrapper<StockoutOrderEntity>()
                .eq(StockoutOrderEntity::getLogisticsCompany, request.getLogisticsCompany())
                .in(StockoutOrderEntity::getLogisticsNo, request.getLogisticsNo())
                .ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.CANCELLED.name())
                .ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.DELIVERED.name())
                .ne(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.CANCELLING.name()));
        orderLog.forEach(order -> {
            StockoutOrderLogEntity stockoutOrderLogEntity = new StockoutOrderLogEntity();
            stockoutOrderLogEntity.setStockoutOrderNo(order.getStockoutOrderNo());
            stockoutOrderLogEntity.setOrderLogType(StockoutOrderLogTypeEnum.DELIVERY_ADD_PACKAGE.getType());
            stockoutOrderLogEntity.setContent(String.format("将物流单号【%s】的包裹放入打包编号【%s】里", order.getLogisticsNo(), request.getBagCode()));
            stockoutOrderLogEntity.setLocation(TenantContext.getTenant());
            stockoutOrderLogEntity.setCreateBy(loginInfoService.getName());
            stockoutOrderLogService.save(stockoutOrderLogEntity);
        });
    }


    /**
     * 新增bag表中状态为打包中
     *
     * @return
     */
    @Transactional
    public Integer packing(StockoutShipmentUpdateAndAddRequest request) {
        LambdaQueryWrapper<StockoutShipmentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentEntity::getLogisticsCompany, request.getLogisticsCompany())
                .eq(StockoutShipmentEntity::getLogisticsNo, request.getLogisticsNo())
                .eq(StockoutShipmentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        String customsDeclareType = this.list(wrapper).get(0).getCustomsDeclareType();
        StockoutPackageBagEntity packageBagEntity = new StockoutPackageBagEntity();
        packageBagEntity.setLocation(TenantContext.getTenant());
        packageBagEntity.setBagCode(request.getBagCode());
        //箱号统计为当天物流渠道的发货箱数
        Integer count = stockoutPackageBagService.getBaseMapper().getToDayCount(request.getLogisticsCompany());
        packageBagEntity.setBagIndex(count + 1);
        packageBagEntity.setLogisticsCompany(request.getLogisticsCompany());
        packageBagEntity.setCustomsDeclareType(customsDeclareType);
        packageBagEntity.setPackageQty(1);
        packageBagEntity.setStatus(StockoutPackageBagTypeEnum.PACKING.name());
        packageBagEntity.setCreateBy(loginInfoService.getName());
        stockoutPackageBagService.save(packageBagEntity);
        LOGGER.info("新增发货包裹 {}", packageBagEntity.getBagId());
        return packageBagEntity.getBagId();
    }

    /**
     * 更新对应记录的重量
     */
    @Transactional
    public void updateWeight(StockoutShipmentUpdateAndAddRequest request) {
        LambdaUpdateWrapper<StockoutShipmentEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(StockoutShipmentEntity::getShipmentId, request.getShipmentId())
                .set(StockoutShipmentEntity::getWeight, request.getWeight())
                .set(StockoutShipmentEntity::getUpdateBy, loginInfoService.getName());
        this.update(updateWrapper);
    }

    /**
     * 增加打包信息
     */
    @Transactional
    public void addPackageItemInfo(Integer bagId, BigDecimal weight, Integer shipmentId, String logisticsNo, String logisticsCompany) {
        StockoutPackageBagItemEntity itemEntity = new StockoutPackageBagItemEntity();
        itemEntity.setBagId(bagId);
        itemEntity.setWeight(weight);
        itemEntity.setShipmentId(shipmentId);
        itemEntity.setLogisticsNo(logisticsNo);
        itemEntity.setLogisticsCompany(logisticsCompany);
        itemEntity.setCreateBy(loginInfoService.getName());
        stockoutPackageBagItemService.save(itemEntity);
    }

    /**
     * 更新包裹内容
     *
     * @return
     */
    @Transactional
    @JLock(keyConstant = "updateBagInfo", lockKey = "#bagCode")
    public void updateBagInfo(String bagCode) {
        Map<String, Object> map = statisticalData(bagCode);
        LambdaUpdateWrapper<StockoutPackageBagEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StockoutPackageBagEntity::getBagCode, bagCode)
                .set(StockoutPackageBagEntity::getPackageQty, map.get(StockoutShipmentMapConstant.PACKAGEQTY))
                .set(StockoutPackageBagEntity::getWeight, map.get(StockoutShipmentMapConstant.WEIGHT))
                .set(StockoutPackageBagEntity::getUpdateBy, loginInfoService.getName());
        stockoutPackageBagService.update(updateWrapper);
    }


    /**
     * 删除打包信息
     */
    @Transactional
    public void deleteBagItem(StockoutPackageBagItemDeleteRequest request) {
        // 删除bagitem中的记录
        List<String> logisticsNoList = request.getLogisticsNoList();
        if (logisticsNoList.isEmpty()) {
            throw new BusinessServiceException("请选择信息再进行删除");
        } else {
            stockoutPackageBagItemService.remove(Wrappers.<StockoutPackageBagItemEntity>lambdaQuery()
                    .eq(StockoutPackageBagItemEntity::getLogisticsCompany, request.getLogisticsCompany())
                    .in(StockoutPackageBagItemEntity::getLogisticsNo, logisticsNoList));
            LOGGER.info("删除物流公司 {} 的物流单号 {}", request.getLogisticsCompany(), logisticsNoList);
            List<StockoutOrderEntity> orderLog = stockoutOrderService.list(new LambdaQueryWrapper<StockoutOrderEntity>()
                    .eq(StockoutOrderEntity::getLogisticsCompany, request.getLogisticsCompany())
                    .in(StockoutOrderEntity::getLogisticsNo, logisticsNoList));
            orderLog.forEach(order -> {
                StockoutOrderLogEntity stockoutOrderLogEntity = new StockoutOrderLogEntity();
                stockoutOrderLogEntity.setStockoutOrderNo(order.getStockoutOrderNo());
                stockoutOrderLogEntity.setOrderLogType(StockoutOrderLogTypeEnum.DELIVERY_DELETE_PACKAGE.getType());
                stockoutOrderLogEntity.setContent(String.format("物流单号为【%s】的包裹，从打包编号【%s】中被删除", order.getLogisticsNo(), request.getBagCode()));
                stockoutOrderLogEntity.setLocation(TenantContext.getTenant());
                stockoutOrderLogEntity.setCreateBy(loginInfoService.getName());
                stockoutOrderLogService.save(stockoutOrderLogEntity);
            });
        }
        SpringUtil.getBean(SmallPackageWeightService.class).updateBagInfo(request.getBagCode());
    }

    /**
     * 打包完成
     * step 1: 统计数据(总重量，总件数，bagId)
     * step 2: 通过bagId拿到shipmentId列表（用于更新状态，扣减库存，更新库位）
     * step 3: 更新packageBag表的数据
     * <p>
     * step 4: 更新出库单状态
     * step 5: 生成入库单
     */
    @Transactional(rollbackFor = Exception.class)
    public void packageComplete(StockoutPackageBagCompleteRequest request) {
        // step 1: 统计数据(总重量，总件数，bagId)
        Map<String, Object> map = statisticalData(request.getBagCode());
        // step 2: 通过bagId拿到对应的shipmentItem的Id
        LambdaQueryWrapper<StockoutPackageBagItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutPackageBagItemEntity::getBagId, map.get(StockoutShipmentMapConstant.BAGID));
        List<StockoutPackageBagItemEntity> bagItemEntities = stockoutPackageBagItemMapper.selectList(wrapper);
        ArrayList<Integer> shipmentIds = new ArrayList<>();
        ArrayList<String> logisticsNoList = new ArrayList<>();
        ArrayList<String> logisticsCompanyList = new ArrayList<>();
        Map<Integer, String> shipmentIdAndLogisticsNo = new HashMap<>(32);
        for (StockoutPackageBagItemEntity bagItem : bagItemEntities) {
            logisticsNoList.add(bagItem.getLogisticsNo());
            logisticsCompanyList.add(bagItem.getLogisticsCompany());
            shipmentIds.add(bagItem.getShipmentId());
            shipmentIdAndLogisticsNo.put(bagItem.getShipmentId(), bagItem.getLogisticsNo());
        }
        Set<Integer> shipmentIdSet = stockoutShipmentService.findByShipmentIdsListReturnId(shipmentIds);
        validShipment(shipmentIds, shipmentIdSet, shipmentIdAndLogisticsNo);
        // step 3: 更新packageBag表的数据
        LambdaUpdateWrapper<StockoutPackageBagEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(StockoutPackageBagEntity::getBagCode, request.getBagCode())
                .set(StockoutPackageBagEntity::getPackageQty, map.get(StockoutShipmentMapConstant.PACKAGEQTY))
                .set(StockoutPackageBagEntity::getWeight, map.get(StockoutShipmentMapConstant.WEIGHT))
                .set(StockoutPackageBagEntity::getStatus, StockoutPackageBagTypeEnum.PACKED);
        stockoutPackageBagService.update(updateWrapper);
        LOGGER.info("发货包裹发货编号 {} 更新状态为 {}", request.getBagCode(), StockoutPackageBagTypeEnum.PACKED);
        // 更新装箱清单状态
        this.update(Wrappers.<StockoutShipmentEntity>lambdaUpdate()
                .in(StockoutShipmentEntity::getShipmentId, shipmentIds)
                .set(StockoutShipmentEntity::getDeliveryDate, DateTime.now().toString(DateUtils.DATE_FORMAT_DATE4))
                .set(StockoutShipmentEntity::getOperateDeliveryDate, new Date())
                .set(StockoutShipmentEntity::getStatus, StockoutShipmentStatusEnum.SHIPPED.name())
                .set(StockoutShipmentEntity::getUpdateBy, loginInfoService.getName())
        );
        LOGGER.info("装箱清单Id: {} 更新状态为 {}", shipmentIds, StockoutShipmentStatusEnum.SHIPPED.name());
        if (!CollectionUtils.isEmpty(shipmentIds)) {
            StockoutPackageCompleteMessage message = new StockoutPackageCompleteMessage(shipmentIds, logisticsCompanyList, logisticsNoList, request.getBagCode(), map);
            message.setUserName(loginInfoService.getName());
            messageProducer.sendMessage(KafkaConstant.SYNC_STOCKOUT_PACKAGE_COMPLETE_TOPIC_NAME, KafkaConstant.SYNC_STOCKOUT_PACKAGE_COMPLETE_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), message));
            //processStockoutShippedByShipmentIds(shipmentIds, logisticsCompanyList, logisticsNoList, request.getBagCode(), map);
        }
    }

    private void validShipment(List<Integer> shipmentIds, Set<Integer> shipmentIdSet, Map<Integer, String> shipmentIdAndLogisticsNo) {
        if (shipmentIds.size() == shipmentIdSet.size()) {
            return;
        }
        shipmentIds.removeAll(shipmentIdSet);
        List<String> logisticsNos = new ArrayList<>();
        shipmentIds.forEach(shipmentId -> {
            String logisticsNo = shipmentIdAndLogisticsNo.get(shipmentId);
            if (StringUtils.isEmpty(logisticsNo)) {
                throw new BusinessServiceException(shipmentId + "未找到装箱id对应的物流单号");
            }
            logisticsNos.add(logisticsNo);
        });
        if (!CollectionUtils.isEmpty(logisticsNos)) {
            throw new BusinessServiceException("以下出库单/装箱清单被取消，请拿出包裹并删除, 把货物放入撤货箱：" + String.join(";", logisticsNos));
        }
    }

    @Transactional
    public void processStockoutShippedByShipmentIds(List<Integer> shipmentIds, List<String> logisticsCompanyList, List<String> logisticsNoList, String bagCode, Map<String, Object> map) {
        // step 4: 更新出库单状态
        List<Integer> orderIds = stockoutOrderMapper.selectIds(logisticsCompanyList, logisticsNoList);
        List<StockoutOrderEntity> stockoutOrderEntities = stockoutOrderService.listByIds(orderIds).stream().filter(o -> !StockoutOrderStatusEnum.DELIVERED.name().equals(o.getStatus())
                && !StockoutOrderStatusEnum.CANCELLED.name().equals(o.getStatus())).collect(Collectors.toList());
        if (stockoutOrderEntities.isEmpty()) {
            return;
        }
        // 获得出库单号列表
        List<String> stockoutOrderNos = stockoutOrderEntities.stream().map(StockoutOrderEntity::getStockoutOrderNo).collect(Collectors.toList());
        // 校验
        stockoutShipmentConfirmService.validateStockOutOrder(stockoutOrderNos);

        stockoutOrderShipService.changeStockoutOrderShippedBy(stockoutOrderEntities);
        // step 5: 生成入库单
        stockTransferCrossSpaceJob.generateStockinOrderById(orderIds);
        // 记录操作日志
        List<StockoutOrderLogEntity> stockoutOrderLogList = stockoutOrderEntities.stream().map(item -> {
            StockoutOrderLogEntity stockoutOrderLogEntity = new StockoutOrderLogEntity();
            stockoutOrderLogEntity.setStockoutOrderNo(item.getStockoutOrderNo());
            stockoutOrderLogEntity.setOrderLogType(StockoutOrderLogTypeEnum.DELIVERY_PACKAGE.getType());
            stockoutOrderLogEntity.setContent(String.format("物流单号【%s】完成发货打包，打包编号【%s】重量【%s】kg", item.getLogisticsNo(), bagCode, map.get(StockoutShipmentMapConstant.WEIGHT)));
            stockoutOrderLogEntity.setLocation(TenantContext.getTenant());
            stockoutOrderLogEntity.setCreateBy(loginInfoService.getName());
            return stockoutOrderLogEntity;
        }).collect(Collectors.toList());
        stockoutOrderLogService.saveBatch(stockoutOrderLogList);
        // 扣减库存更新库位
        List<StockoutShipmentSearchResult> deductionStockList = stockoutShipmentItemMapper.getDeductionStockList(shipmentIds);
        stockoutShipService.shippingUpdateStock(deductionStockList, stockoutOrderEntities.get(0).getSpaceId());
        //清除预占
        stockPrematchRemoveService.removeByStockoutOrder(stockoutOrderEntities.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()), StockoutOrderLogTypeEnum.DELIVERY_PACKAGE);
        // 同步.net装箱清单，已发货
        erpPickingBoxService.sendErpPickingBoxShippedSyncByShipmentIdList(shipmentIds);
    }

    /**
     * 统计bagitem表对应的bagid的数据
     *
     * @param bagCode
     * @return
     */
    public Map<String, Object> statisticalData(String bagCode) {
        LambdaQueryWrapper<StockoutPackageBagEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutPackageBagEntity::getBagCode, bagCode);
        StockoutPackageBagEntity entity = stockoutPackageBagService.getOne(wrapper);
        if (entity == null) {
            throw new BusinessServiceException(bagCode + "该包裹已经被删除，请刷新后重试");
        }
        BigDecimal sumWeight = stockoutPackageBagItemMapper.getSumWeight(entity.getBagId());

        LambdaQueryWrapper<StockoutPackageBagItemEntity> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(StockoutPackageBagItemEntity::getBagId, entity.getBagId());
        Integer packageQty = stockoutPackageBagItemMapper.selectCount(countWrapper);

        HashMap<String, Object> map = new HashMap<>();
        map.put(StockoutShipmentMapConstant.WEIGHT, sumWeight);
        map.put(StockoutShipmentMapConstant.PACKAGEQTY, packageQty);
        map.put(StockoutShipmentMapConstant.BAGID, entity.getBagId());
        return map;
    }


    /**
     * 从发货包裹列表跳转
     *
     * @param
     * @return
     */
    public PageResponse<SmallPackageWeightResponse> jumpToLoad(SmallPackageJumpLoadRequest request) {
        StockoutPackageBagEntity bag = stockoutPackageBagService.getById(request.getBagId());
        if (bag == null || StockoutPackageBagTypeEnum.PACKED.name().equals(bag.getStatus())) {
            throw new BusinessServiceException("此包裹已经打包完成！无法继续放入");
        }
        PageResponse<SmallPackageWeightResponse> pageResponse = new PageResponse<>();
        List<SmallPackageWeightResponse> pageResult = smallPackageWeightMapper.jumpLoadData(request);
        pageResponse.setContent(pageResult);
        return pageResponse;
    }

    // 根据发货包裹 bagIds查询明细
    public PageResponse<StockoutPackageBagItemExport> getPackageItemListByBagIds(PackageBagItemDownloadListRequest request) {
        PageResponse<StockoutPackageBagItemExport> pageResponse = new PageResponse<>();
        Page<StockoutPackageBagItemExport> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutPackageBagItemExport> pageResult = smallPackageWeightMapper.pageSearchListByBagIds(page, request);
        List<StockoutPackageBagItemExport> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pageResult.getRecords())) {
            Map<String, List<StockoutPackageBagItemExport>> collect = pageResult.getRecords().stream().collect(Collectors.groupingBy(StockoutPackageBagItemExport::getLogisticsNo));
            collect.forEach((key, value) -> {
                if (!CollectionUtils.isEmpty(value)) {
                    Integer qty = value.stream().filter(item -> item.getQty() != null).mapToInt(StockoutPackageBagItemExport::getQty).sum();
                    StockoutPackageBagItemExport response = new StockoutPackageBagItemExport();
                    BeanUtilsEx.copyProperties(value.get(0), response);
                    response.setQty(qty);
                    resultList.add(response);
                }
            });
        }
        pageResponse.setTotalCount(resultList.stream().count());
        pageResponse.setContent(resultList);
        return pageResponse;
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKOUT_PACKAGE_BAG_ITEM;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        PackageBagItemDownloadListRequest downloadRequest = JSONObject.parseObject(request.getRequestContent(), PackageBagItemDownloadListRequest.class);
        if (CollectionUtils.isEmpty(downloadRequest.getBagIds())) {
            downloadRequest.setBagIds(new ArrayList<>());
            StockoutPackageBagListRequest pageReq = JsonMapper.fromJson(request.getRequestContent(), StockoutPackageBagListRequest.class);
            pageReq.setPageSize(request.getPageSize());
            pageReq.setPageIndex(request.getPageIndex());
            PageResponse<StockoutPackageBagListResponse> stockoutPackageBagList = stockoutPackageBagService.getStockoutPackageBagList(pageReq);
            if (stockoutPackageBagList == null || stockoutPackageBagList.getTotalCount() == 0L) {
                return response;
            }
            stockoutPackageBagList.getContent().forEach(it -> downloadRequest.getBagIds().add(it.getBagId()));
        }
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        PageResponse<StockoutPackageBagItemExport> pageResponse = getPackageItemListByBagIds(downloadRequest);
        if (!CollectionUtils.isEmpty(pageResponse.getContent())) {
            List<StockoutPackageBagItemExportResponse> resultList = pageResponse.getContent().stream().map(item -> {
                StockoutPackageBagItemExportResponse export = new StockoutPackageBagItemExportResponse();
                BeanUtilsEx.copyProperties(item, export, "createDate");
                SimpleDateFormat dateFormat = new SimpleDateFormat(DateUtils.DATE_FORMAT_DATE4);
                export.setCreateDate(item.getCreateDate() == null ? null : dateFormat.format(item.getCreateDate()));
                return export;
            }).collect(Collectors.toList());
            response.setTotalCount(pageResponse.getTotalCount());
            response.setDataJsonStr(JsonMapper.toJson(resultList));
        }
        return response;
    }

    public PrintListResponse printShipmentOrder(StockoutPackagePrintRequest request) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.STOCKOUT_SMALL_PACKAGE.getTemplateName());
        StockoutPackageBagEntity bagEntity = stockoutPackageBagService.getOne(new LambdaQueryWrapper<StockoutPackageBagEntity>().eq(StockoutPackageBagEntity::getBagCode, request.getBagCode()));
        String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), bagEntity);
        response.setHtmlList(Collections.singletonList(transfer));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    public PrintListResponse printShipmentPrintSigntureForm(IdListRequest idListRequest) {
        List<StockoutShipmentItemEntity> allShipmentItemEntityList = shipmentItemService.findByShipmentIdList(idListRequest.getIdList());
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.STOCKOUT_DELIVERY_SIGNATURE_FORM.getTemplateName());
        if (CollectionUtils.isEmpty(allShipmentItemEntityList)) throw new BusinessServiceException("装箱记录不存在");
        PrintListResponse response = new PrintListResponse();

        List<String> result = new ArrayList<>();
        Map<String, List<StockoutShipmentItemEntity>> mapGroup = allShipmentItemEntityList.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getStockoutOrderNo));
        mapGroup.forEach((stockoutOrderNo, groupList) -> {
            Map<String, Object> map = new HashMap<>(32);
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
            if (Objects.isNull(stockoutOrderEntity)) {
                throw new BusinessServiceException(String.format("根据出库单号【%s】,找不到出库单信息", stockoutOrderNo));
            }
            map.put("printDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
            map.put("storeName", stockoutOrderEntity.getStoreName());

            // 收货人信息
            StockoutReceiverInfo receiverInfo = stockoutReceiverInfoService.getReceiveInfoByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            StockoutDeliverySignatureFormPrint print = new StockoutDeliverySignatureFormPrint();
            BeanUtilsEx.copyProperties(receiverInfo, print);

            //商品信息
            List<Integer> specIdList = groupList.stream().map(StockoutShipmentItemEntity::getSpecId).collect(Collectors.toList());
            List<ProductSpecInfoEntity> specInfoList = productSpecInfoService.findAllBySpecIdIn(specIdList);
            if (CollectionUtils.isEmpty(specInfoList)) {
                throw new BusinessServiceException(String.format("根据装箱清单号【%s】,找不到商品信息", groupList.get(0).getShipmentId()));
            }
            List<StockoutDeliverProductPrint> deliverProductPrintList = StockoutBuilding.buildDeliverProductPrintList(specInfoList, groupList);
            map.put("deliverProductList", deliverProductPrintList);

            // 发货人信息
            StockoutShipperInfoEntity stockoutShipperInfoEntity = stockoutShipperInfoService.getByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            if (Objects.isNull(stockoutShipperInfoEntity)) {
                throw new BusinessServiceException("找不到发货人信息");
            }
            BeanUtilsEx.copyProperties(stockoutShipperInfoEntity, print);
            map.put("printInfo", print);

            Template template = FreeMarkerTemplateUtils.getTemplate("StockoutDeliverSignatureFormPage.ftl");
            String html = FreeMarkerTemplateUtils.renderTemplate(template, map);
            result.add(html);
        });
        response.setHtmlList(result);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

}
