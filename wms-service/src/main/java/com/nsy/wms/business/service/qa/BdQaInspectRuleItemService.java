package com.nsy.wms.business.service.qa;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.enumeration.qa.BdQaInspectRuleEnum;
import com.nsy.api.wms.request.qa.BdQaInspectRuleItemSaveRequest;
import com.nsy.api.wms.response.qa.BdQaInspectRuleItemInfoResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.BdQaInspectRuleItemEntity;
import com.nsy.wms.repository.jpa.mapper.qa.BdQaInspectRuleItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检稽查规则详情业务实现
 * @date: 2025-02-24 10:34
 */
@Service
public class BdQaInspectRuleItemService extends ServiceImpl<BdQaInspectRuleItemMapper, BdQaInspectRuleItemEntity> {

    @Autowired
    private LoginInfoService loginInfoService;


    /**
     * 保存明细信息
     *
     * @param itemInfo
     * @param ruleId
     */
    public void saveItemInfo(BdQaInspectRuleItemSaveRequest itemInfo, Integer ruleId) {
        List<BdQaInspectRuleItemEntity> ruleItemEntityList = new ArrayList<>();
        this.buildItemListInfo(BdQaInspectRuleEnum.SKU_TYPE, itemInfo.getSkuTypeList(), ruleId, ruleItemEntityList);
        this.buildItemListInfo(BdQaInspectRuleEnum.SUPPLIER, itemInfo.getSupplierIdList(), ruleId, ruleItemEntityList);
        //质检员
        this.buildItemListInfo(BdQaInspectRuleEnum.QC_USER_NAME, itemInfo.getQcUserName(), ruleId, ruleItemEntityList);
        //是否返工退货
        this.buildItemInfo(BdQaInspectRuleEnum.RETURN_APPLY, itemInfo.getIsReturnApply(), ruleId, ruleItemEntityList);
        //批次到货数
        this.buildItemInfo(BdQaInspectRuleEnum.ARRIVAL_COUNT, itemInfo.getArrivalCount(), ruleId, ruleItemEntityList);
        //退货率
        this.buildItemInfo(BdQaInspectRuleEnum.RETURN_RATE, itemInfo.getReturnRate(), ruleId, ruleItemEntityList);
        if (CollectionUtils.isEmpty(ruleItemEntityList)) {
            return;
        }
        this.saveBatch(ruleItemEntityList);
    }

    /**
     * 更新信息，全删在全增
     *
     * @param itemInfo
     * @param ruleId
     */
    public void updateItemInfo(BdQaInspectRuleItemSaveRequest itemInfo, Integer ruleId) {
        LambdaQueryWrapper<BdQaInspectRuleItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdQaInspectRuleItemEntity::getBdQaInspectRuleId, ruleId);
        // 执行删除操作
        this.remove(wrapper);
        this.saveItemInfo(itemInfo, ruleId);
    }

    /**
     * 赋值明细信息
     *
     * @param ruleEnum
     * @param paramList
     * @param ruleId
     * @param ruleItemEntityList
     */
    private void buildItemListInfo(BdQaInspectRuleEnum ruleEnum, List<String> paramList, Integer ruleId, List<BdQaInspectRuleItemEntity> ruleItemEntityList) {
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        for (String param : paramList) {
            this.buildItemInfo(ruleEnum, param, ruleId, ruleItemEntityList);
        }
    }

    /**
     * 获取明细信息
     *
     * @param ruleId
     * @return
     */
    public BdQaInspectRuleItemInfoResponse getItemInfo(Integer ruleId) {
        LambdaQueryWrapper<BdQaInspectRuleItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BdQaInspectRuleItemEntity::getBdQaInspectRuleId, ruleId);
        List<BdQaInspectRuleItemEntity> ruleItemEntityList = this.list(wrapper);
        BdQaInspectRuleItemInfoResponse response = new BdQaInspectRuleItemInfoResponse();
        if (CollectionUtils.isEmpty(ruleItemEntityList)) {
            return response;
        }
        Map<String, List<BdQaInspectRuleItemEntity>> itemMap = ruleItemEntityList.stream().collect(Collectors.groupingBy(BdQaInspectRuleItemEntity::getItemName));
        for (Map.Entry<String, List<BdQaInspectRuleItemEntity>> entry : itemMap.entrySet()) {
            BdQaInspectRuleEnum ruleEnum = BdQaInspectRuleEnum.getValueByName(entry.getKey());
            if (ObjectUtils.isEmpty(ruleEnum)) {
                continue;
            }
            this.buildDetailInfo(ruleEnum, entry.getValue(), response);
        }
        return response;
    }

    /**
     * 赋值明细信息
     *
     * @param ruleEnum
     * @param ruleItemEntityList
     * @param response
     */
    public void buildDetailInfo(BdQaInspectRuleEnum ruleEnum, List<BdQaInspectRuleItemEntity> ruleItemEntityList, BdQaInspectRuleItemInfoResponse response) {
        if (CollectionUtils.isEmpty(ruleItemEntityList)) {
            return;
        }
        List<String> paramList = ruleItemEntityList.stream().map(BdQaInspectRuleItemEntity::getItemValue).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paramList)) {
            return;
        }
        switch (ruleEnum) {
            case ARRIVAL_COUNT:
                response.setArrivalCount(paramList.get(0));
                break;
            case QC_USER_NAME:
                response.setQcUserName(paramList);
                break;
            case RETURN_APPLY:
                response.setIsReturnApply(paramList.get(0));
                break;
            case RETURN_RATE:
                response.setReturnRate(paramList.get(0));
                break;
            case SKU_TYPE:
                response.setSkuTypeList(paramList);
                break;
            case SUPPLIER:
                response.setSupplierIdList(paramList);
                break;
            default:
                // 处理其他枚举值的情况
                break;
        }
    }


    /**
     * 赋值明细信息
     *
     * @param ruleEnum
     * @param param
     * @param ruleId
     * @param ruleItemEntityList
     */
    private void buildItemInfo(BdQaInspectRuleEnum ruleEnum, String param, Integer ruleId, List<BdQaInspectRuleItemEntity> ruleItemEntityList) {
        if (!StringUtils.hasText(param) || BdQaInspectRuleEnum.RETURN_APPLY != ruleEnum && "0".equalsIgnoreCase(param)) {
            return;
        }
        BdQaInspectRuleItemEntity itemEntity = new BdQaInspectRuleItemEntity();
        itemEntity.setItemName(ruleEnum.name());
        itemEntity.setItemValue(param);
        itemEntity.setBdQaInspectRuleId(ruleId);
        itemEntity.setCreateBy(loginInfoService.getName());
        itemEntity.setUpdateBy(loginInfoService.getName());
        ruleItemEntityList.add(itemEntity);
    }
}
