package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.domain.stockout.StockoutOrderScanTask;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskStatus;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderLackSourceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingTypeEnum;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLackEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskItemEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class StockoutBatchSplitTaskCheckService {

    @Autowired
    StockoutBatchSplitTaskService splitTaskService;
    @Autowired
    StockoutBatchSplitTaskItemService splitItemService;
    @Autowired
    StockoutOrderScanTaskItemService scanTaskItemService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;

    @Autowired
    StockoutOrderLackService stockoutOrderLackService;

    /**
     * 校验是否拼单完成
     */
    public StockoutOrderScanTask checkSpiltTask(String stockoutOrderNo) {
        StockoutOrderScanTask result = new StockoutOrderScanTask();
        result.setShowNotice(Boolean.FALSE);
        List<StockoutBatchSplitTaskItemEntity> splitTaskItemEntityList = splitItemService.list(new QueryWrapper<StockoutBatchSplitTaskItemEntity>().lambda().eq(StockoutBatchSplitTaskItemEntity::getStockoutOrderNo, stockoutOrderNo));
        if (splitTaskItemEntityList.isEmpty())
            return result;
        List<Integer> taskIds = splitTaskItemEntityList.stream().map(StockoutBatchSplitTaskItemEntity::getTaskId).distinct().collect(Collectors.toList());
        StockoutBatchSplitTaskEntity splitTask = splitTaskService.listByIds(taskIds).stream().max(Comparator.comparing(StockoutBatchSplitTaskEntity::getTaskIndex)).orElse(null);
        if (splitTask == null)
            return result;
        splitTaskItemEntityList = splitTaskItemEntityList.stream().filter(o -> o.getTaskId().equals(splitTask.getTaskId())).collect(Collectors.toList());
        // 窄带分拣
        if (splitTask.getBatchSplitType().equals(StockoutSortingTypeEnum.AUTO_MACHINE_SORT.name())) {
            if (!splitTask.getStatus().equals(StockoutBatchSplitTaskStatus.SUSPEND_SORT.name()) && !splitTask.getStatus().equals(StockoutBatchSplitTaskStatus.SORTED.name())) {
                result.setShowNotice(Boolean.TRUE);
                result.setNotice(String.format("请先操作完成窄带分拣任务【%s】", splitTask.getTaskId()));
                return result;
            }
        } else {
            if (splitTask.getStatus().equals(StockoutBatchSplitTaskStatus.SORTED.name()))
                return result;
            if (checkScanQty(stockoutOrderNo, splitTaskItemEntityList))
                return result;
            return checkLack(stockoutOrderNo, splitTask.getTaskId());
        }
        return result;
    }

    /**
     * 校验复核任务明细的待复核 与 分拣的扫描数是否相等
     */
    private Boolean checkScanQty(String stockoutOrderNo, List<StockoutBatchSplitTaskItemEntity> splitTaskItemEntityList) {
        List<StockoutOrderScanTaskItemEntity> taskItemEntityList = scanTaskItemService.getByStockoutOrderNo(stockoutOrderNo);
        List<Integer> stockoutOrderItemIds = splitTaskItemEntityList.stream().map(StockoutBatchSplitTaskItemEntity::getStockoutOrderItemId).collect(Collectors.toList());
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByIds(stockoutOrderItemIds);
        for (StockoutOrderScanTaskItemEntity scanTaskItemEntity : taskItemEntityList) {
            StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemEntityList.stream().filter(o -> o.getOrderItemId().equals(scanTaskItemEntity.getOrderItemId())).findFirst().orElse(null);
            if (stockoutOrderItemEntity == null)
                return Boolean.FALSE;
            StockoutBatchSplitTaskItemEntity splitTaskItemEntity = splitTaskItemEntityList.stream().filter(o -> o.getStockoutOrderItemId().equals(stockoutOrderItemEntity.getStockoutOrderItemId())).findFirst().orElse(null);
            if (splitTaskItemEntity == null)
                return Boolean.FALSE;
            if (scanTaskItemEntity.getExpectedQty() > splitTaskItemEntity.getScanQty() || scanTaskItemEntity.getExpectedQty().equals(0))
                return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * 校验缺货出库单是否处理完成
     */
    private StockoutOrderScanTask checkLack(String stockoutOrderNo, Integer taskId) {
        StockoutOrderScanTask result = new StockoutOrderScanTask();
        result.setShowNotice(Boolean.FALSE);
        StockoutOrderLackEntity lackEntity = stockoutOrderLackService.getOne(new QueryWrapper<StockoutOrderLackEntity>().lambda()
                .eq(StockoutOrderLackEntity::getStockoutOrderNo, stockoutOrderNo)
                .eq(StockoutOrderLackEntity::getLackSource, StockoutOrderLackSourceEnum.SECOND_SORT.name())
                .last(MybatisQueryConstant.QUERY_FIRST));
        if (lackEntity == null) {
            result.setShowNotice(Boolean.TRUE);
            result.setNotice(String.format("请先操作完成分拣任务【%s】", taskId));
        }
        return result;
    }
}
