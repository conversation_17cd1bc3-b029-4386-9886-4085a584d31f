package com.nsy.wms.business.service.stockout;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormItemExport;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormLogResult;
import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormResult;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareContractStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormSystemMarkEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormItemConfirmRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormLogRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormSearchRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormUpdateRequest;
import com.nsy.api.wms.request.stockout.SupplierSyncInvoiceItemRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StatusCountResponse;
import com.nsy.wms.business.domain.dto.stockout.CustomsDeclareFormItemMessage;
import com.nsy.wms.business.domain.dto.stockout.CustomsDeclareFormMessage;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsAuditOneDetailRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsAuditOneRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareFormWmsAuditRequest;
import com.nsy.wms.business.service.bd.BdExportPortService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductWmsCategoryService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.product.ProductWmsCategoryEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareContractEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormLogEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareFormLogMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareFormMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 关单(StockoutCustomsDeclareForm)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-14 17:13:59
 */
@Service
public class StockoutCustomsDeclareFormService extends ServiceImpl<StockoutCustomsDeclareFormMapper, StockoutCustomsDeclareFormEntity> {

    @Autowired
    StockoutCustomsDeclareFormLogMapper logMapper;
    @Autowired
    StockoutCustomsDeclareFormLogService logService;
    @Autowired
    StockoutCustomsDeclareFormItemService formItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    SupplierApiService supplierApiService;
    @Autowired
    BdExportPortService exportPortService;
    @Autowired
    StockoutCustomsDeclareContractService contractService;
    @Autowired
    ProductWmsCategoryService productWmsCategoryService;
    @Autowired
    MessageProducer messageProducer;


    public PageResponse<StockoutCustomsDeclareFormResult> pageSearchList(StockoutCustomsDeclareFormSearchRequest request) {
        PageResponse<StockoutCustomsDeclareFormResult> pageResponse = new PageResponse<>();
        Page page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutCustomsDeclareFormResult> pageResult = this.baseMapper.pageForm(page, request);
        List<StockoutCustomsDeclareFormResult> collect = pageResult.getRecords().stream().map(entity -> {
            StockoutCustomsDeclareFormResult result = new StockoutCustomsDeclareFormResult();
            BeanUtilsEx.copyProperties(entity, result);
            result.setStatusStr(StockoutCustomsDeclareFormStatusEnum.getByName(result.getStatus()));
            result.setExportPortCode(entity.getExportPort());
            result.setExportPort(exportPortService.getByCode(entity.getExportPort()));
            result.setUnitCode(entity.getgUnit());
            result.setgUnit(StockoutBuilding.getDeclareUnitMap().get(entity.getgUnit()));
            result.setSystemMarkStr(StockoutCustomsDeclareFormSystemMarkEnum.getByName(result.getSystemMark()));
            result.setAllWmsCategory(productWmsCategoryService.findAllParentList(result.getWmsCategoryId()).stream()
                    .map(ProductWmsCategoryEntity::getName)
                    .collect(Collectors.collectingAndThen(Collectors.toList(), list -> {
                        Collections.reverse(list);
                        return list.stream();
                    })).collect(Collectors.joining("/")));
            return result;
        }).collect(Collectors.toList());
        pageResponse.setContent(collect);
        pageResponse.setTotalCount(pageResult.getTotal());
        return pageResponse;
    }

    public PageResponse<StockoutCustomsDeclareFormItemExport> exportFormItem(StockoutCustomsDeclareFormSearchRequest request) {
        PageResponse<StockoutCustomsDeclareFormItemExport> pageResponse = new PageResponse<>();
        Page<StockoutCustomsDeclareFormItemExport> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutCustomsDeclareFormItemExport> pageResult = this.getBaseMapper().exportFormItem(page, request);
        pageResult.getRecords().forEach(item -> {
            item.setOutTaxInclusivePrice(item.getTaxInclusivePrice().subtract(item.getTaxPrice()));
            if (!StringUtils.hasText(item.getDeclareContractNo()))
                item.setDeclareContractNo(item.getMainDeclareContractNo());
        });
        pageResponse.setContent(pageResult.getRecords());
        pageResponse.setTotalCount(this.getBaseMapper().countExportFormItem(request));
        return pageResponse;
    }

    public List<StatusCountResponse> tabCount() {
        Map<String, List<StatusCountResponse>> collect = this.getBaseMapper().countByStatus().stream().collect(Collectors.groupingBy(StatusCountResponse::getStatus));
        List<StatusCountResponse> list = Arrays.stream(StockoutCustomsDeclareFormStatusEnum.values()).map(statusEnum -> {
            StatusCountResponse response = new StatusCountResponse();
            List<StatusCountResponse> responses = collect.get(statusEnum.name());
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getName());
            return response;
        }).collect(Collectors.toList());
        StatusCountResponse response = new StatusCountResponse();
        response.setQty(list.stream().mapToInt(StatusCountResponse::getQty).sum());
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }

    public StockoutCustomsDeclareFormResult detailInfo(Integer declareFormId) {
        StockoutCustomsDeclareFormEntity declareFormEntity = getById(declareFormId);
        if (declareFormEntity == null) {
            throw new BusinessServiceException(declareFormId + "无相关的关单信息");
        }

        // TODO: 2022/12/15
        StockoutCustomsDeclareFormResult result = new StockoutCustomsDeclareFormResult();
        BeanUtilsEx.copyProperties(declareFormEntity, result);
        result.setStatusStr(StockoutCustomsDeclareFormStatusEnum.getByName(result.getStatus()));
        result.setExportPortCode(declareFormEntity.getExportPort());
        result.setExportPort(exportPortService.getByCode(declareFormEntity.getExportPort()));
        result.setUnitCode(declareFormEntity.getgUnit());
        result.setgUnit(StockoutBuilding.getDeclareUnitMap().get(declareFormEntity.getgUnit()));
        return result;
    }

    @Transactional
    public void updateInfo(StockoutCustomsDeclareFormUpdateRequest request) {
        StockoutCustomsDeclareFormEntity declareFormEntity = getById(request.getDeclareFormId());
        if (declareFormEntity == null)
            throw new BusinessServiceException(request.getDeclareFormId() + "无相关的关单信息");
        if (!StockoutCustomsDeclareFormSystemMarkEnum.MANUAL_SPLIT.name().equals(declareFormEntity.getSystemMark())
                && !StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name().equals(declareFormEntity.getStatus())) {
            throw new BusinessServiceException("非手动冲库存也不是非待处理，无法修改");
        }
        StringBuilder content = new StringBuilder("更新关单内容：");
        if (!Objects.isNull(request.getExchangeRate()) && request.getExchangeRate().compareTo(declareFormEntity.getExchangeRate()) != 0) {
            content.append(String.format(" 汇率【%s】->【%s】", declareFormEntity.getExchangeRate().toString(), request.getExchangeRate().toString()));
            declareFormEntity.setExchangeRate(request.getExchangeRate());
        }
        if (!Objects.isNull(request.getApportionedFreight()) && request.getApportionedFreight().compareTo(declareFormEntity.getApportionedFreight()) != 0) {
            content.append(String.format(" 运费【%s】->【%s】", declareFormEntity.getApportionedFreight().toString(), request.getApportionedFreight().toString()));
            declareFormEntity.setApportionedFreight(request.getApportionedFreight());
        }
        if (!Objects.isNull(request.getFobPrice()) && request.getFobPrice().compareTo(declareFormEntity.getFobPrice()) != 0) {
            content.append(String.format(" fob总价【%s】->【%s】", declareFormEntity.getFobPrice().toString(), request.getFobPrice().toString()));
            declareFormEntity.setFobPrice(request.getFobPrice());
        }
        if (!Objects.isNull(request.getFobPriceCny()) && request.getFobPriceCny().compareTo(declareFormEntity.getFobPriceCny()) != 0) {
            content.append(String.format(" fob总价(人民币)【%s】->【%s】", declareFormEntity.getFobPriceCny().toString(), request.getFobPriceCny().toString()));
            declareFormEntity.setFobPriceCny(request.getFobPriceCny());
        }
        if (!Objects.isNull(request.getBoxQty()) && request.getBoxQty().compareTo(declareFormEntity.getBoxQty()) != 0) {
            content.append(String.format(" 箱数【%s】->【%s】", declareFormEntity.getBoxQty().toString(), request.getBoxQty().toString()));
            declareFormEntity.setBoxQty(request.getBoxQty());
        }
        if (!Objects.isNull(request.getRoughWeight()) && request.getRoughWeight().compareTo(declareFormEntity.getRoughWeight()) != 0) {
            content.append(String.format(" 毛重【%s】->【%s】", declareFormEntity.getRoughWeight().toString(), request.getRoughWeight().toString()));
            declareFormEntity.setRoughWeight(request.getRoughWeight());
        }
        if (!Objects.isNull(request.getTaxInclusiveUnitPrice()) && request.getTaxInclusiveUnitPrice().compareTo(declareFormEntity.getTaxInclusiveUnitPrice()) != 0) {
            content.append(String.format(" 含税单价【%s】->【%s】", declareFormEntity.getTaxInclusiveUnitPrice().toString(), request.getTaxInclusiveUnitPrice().toString()));
            declareFormEntity.setTaxInclusiveUnitPrice(request.getTaxInclusiveUnitPrice());
        }
        if (!Objects.isNull(request.getTaxInclusivePrice()) && request.getTaxInclusivePrice().compareTo(declareFormEntity.getTaxInclusivePrice()) != 0) {
            content.append(String.format(" 含税金额【%s】->【%s】", declareFormEntity.getTaxInclusivePrice().toString(), request.getTaxInclusivePrice().toString()));
            declareFormEntity.setTaxInclusivePrice(request.getTaxInclusivePrice());
        }
        if (StrUtil.isNotEmpty(request.getDeclareBatch()) && !request.getDeclareBatch().equals(declareFormEntity.getDeclareBatch())) {
            content.append(String.format(" 申报批次【%s】->【%s】", declareFormEntity.getDeclareBatch(), request.getDeclareBatch()));
            declareFormEntity.setDeclareBatch(request.getDeclareBatch());
        }
        if (StrUtil.isNotEmpty(request.getExportInvoiceNo()) && !request.getExportInvoiceNo().equals(declareFormEntity.getExportInvoiceNo())) {
            content.append(String.format(" 申报批次【%s】->【%s】", declareFormEntity.getExportInvoiceNo(), request.getExportInvoiceNo()));
            declareFormEntity.setExportInvoiceNo(request.getExportInvoiceNo());
        }
        if (!Objects.isNull(request.getExportInvoiceDate()) && !request.getExportInvoiceDate().equals(declareFormEntity.getExportInvoiceDate())) {
            content.append(String.format(" 出口开票日期【%s】->【%s】", declareFormEntity.getExportInvoiceDate(), request.getExportInvoiceDate()));
            declareFormEntity.setExportInvoiceDate(request.getExportInvoiceDate());
        }
        if (!Objects.isNull(request.getInputRate()) && request.getInputRate().compareTo(declareFormEntity.getInputRate()) != 0) {
            content.append(String.format(" 进项比例【%s】->【%s】", declareFormEntity.getInputRate().toString(), request.getInputRate().toString()));
            declareFormEntity.setInputRate(request.getInputRate());
            //通过进项比例和税率计算含税金额/含税单价/fob总价
            computeByInputRateAndExchangeRate(declareFormEntity, declareFormEntity.getExchangeRate(), declareFormEntity.getInputRate());
        }
        if (StrUtil.isNotBlank(request.getLogisticsInvoiceNumber()) && !request.getLogisticsInvoiceNumber().equals(declareFormEntity.getLogisticsInvoiceNumber())) {
            content.append(String.format(" 物流发票号【%s】->【%s】", declareFormEntity.getLogisticsInvoiceNumber(), request.getLogisticsInvoiceNumber()));
            declareFormEntity.setLogisticsInvoiceNumber(request.getLogisticsInvoiceNumber());
        }
        if (!Objects.isNull(request.getLogisticsInvoiceDate()) && (declareFormEntity.getLogisticsInvoiceDate() == null || !request.getLogisticsInvoiceDate().equals(declareFormEntity.getLogisticsInvoiceDate()))) {
            content.append(String.format(" 物流发票开票时间【%s】->【%s】", declareFormEntity.getLogisticsInvoiceDate() == null ? "" : declareFormEntity.getLogisticsInvoiceDate().toString(), request.getLogisticsInvoiceDate().toString()));
            declareFormEntity.setLogisticsInvoiceDate(request.getLogisticsInvoiceDate());
        }
        if (StrUtil.isNotBlank(request.getLogisticsCompany()) && !request.getLogisticsCompany().equals(declareFormEntity.getLogisticsCompany())) {
            content.append(String.format(" 物流商【%s】->【%s】", declareFormEntity.getLogisticsCompany(), request.getLogisticsCompany()));
            declareFormEntity.setLogisticsCompany(request.getLogisticsCompany());
        }
        updateById(declareFormEntity);
        logService.addLog(request.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.UPDATE_INFO, content.toString());
    }

    /**
     * 审核明细
     * <p>
     * 1. 判断关单是否【工厂已反馈】
     * 2. 审核通过
     * * * a.关单是否需要审核明细-否 是否审核通过明细-是
     * * * b.判断关单是否【已完成】
     * * * c.通知supplier
     * 3. 审核不通过
     * * * * a. 填写不通过原因
     * * * * b. 日志记录
     * * * * c. 通知supplier
     *
     * @param request
     */
    @Transactional
    public void confirmItem(StockoutCustomsDeclareFormItemConfirmRequest request) {
        StockoutCustomsDeclareFormEntity declareFormEntity = getById(request.getDeclareFormId());
        if (declareFormEntity == null) {
            throw new BusinessServiceException(request.getDeclareFormId() + "无相关的关单信息");
        }
        // 1. 判断关单是否【工厂已反馈】
        if (!StockoutCustomsDeclareFormStatusEnum.DEALT.name().equals(declareFormEntity.getStatus())
                && !StockoutCustomsDeclareFormStatusEnum.GENERATE_ORDER.name().equals(declareFormEntity.getStatus())
                && !StockoutCustomsDeclareFormStatusEnum.SUPPLIER_DONE.name().equals(declareFormEntity.getStatus())) {
            throw new BusinessServiceException("当前状态非 【已处理】【已生成合同】【工厂已反馈】，无法审批");
        }
        if (!declareFormEntity.getShouldAuditItem()) {
            throw new BusinessServiceException("该关单不需要审批");
        }
        //2. 通过 关单 【已完成】
        if (1 == request.getResult()) {
            //a.关单是否需要审核明细-否 是否审核通过明细-是
            declareFormEntity.setShouldAuditItem(Boolean.FALSE);
            declareFormEntity.setHasAuditPassItem(Boolean.TRUE);
            updateById(declareFormEntity);
            logService.addLog(declareFormEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.CONFIRM_ITEM, "审核通过");
            //b.判断关单是否【已完成】
            checkComplete(declareFormEntity.getDeclareFormId());
            //c.通知supplier
            syncAuditStatusToSupplier(declareFormEntity, "COMPANY_PASS", request.getReason());
        } else if (0 == request.getResult()) { //3. 审核不通过
            declareFormEntity.setShouldAuditItem(Boolean.FALSE);
            updateById(declareFormEntity);
            //b. 日志记录
            logService.addLog(declareFormEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.CONFIRM_ITEM, String.format("审核不通过，原因%s", request.getReason()));
            //c. 通知supplier
            syncAuditStatusToSupplier(declareFormEntity, "COMPANY_NO_PASS", request.getReason());
        }
    }


    /**
     * 同步关单审核状态给supplier
     *
     * @param declareFormEntity
     * @param status
     * @param reason
     */
    private void syncAuditStatusToSupplier(StockoutCustomsDeclareFormEntity declareFormEntity, String status, String reason) {
        StockoutCustomsDeclareFormWmsAuditRequest auditRequest = new StockoutCustomsDeclareFormWmsAuditRequest();
        StockoutCustomsDeclareFormWmsAuditOneRequest oneRequest = new StockoutCustomsDeclareFormWmsAuditOneRequest();
        List<StockoutCustomsDeclareFormItemEntity> itemList = formItemService.itemListNoManual(declareFormEntity.getDeclareFormId());
        List<StockoutCustomsDeclareFormWmsAuditOneDetailRequest> detailList = itemList.stream().map(temp -> {
            StockoutCustomsDeclareFormWmsAuditOneDetailRequest detailRequest = new StockoutCustomsDeclareFormWmsAuditOneDetailRequest();
            detailRequest.setDeclareFormItemId(temp.getDeclareFormItemId());
            return detailRequest;
        }).collect(Collectors.toList());
        oneRequest.setDeclareFormItem(detailList);
        oneRequest.setDeclareFormId(declareFormEntity.getDeclareFormId());
        auditRequest.setDeclareForm(Collections.singletonList(oneRequest));
        auditRequest.setStatus(status);
        auditRequest.setAuditNoPassReason(reason);
        auditRequest.setOperator(loginInfoService.getName());
        supplierApiService.syncCustomsFormItemAudit(auditRequest);
    }


    public PageResponse<StockoutCustomsDeclareFormLogResult> pageSearchLogList(StockoutCustomsDeclareFormLogRequest request) {
        LambdaQueryWrapper<StockoutCustomsDeclareFormLogEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutCustomsDeclareFormLogEntity::getDeclareFormId, request.getDeclareFormId());
        PageResponse<StockoutCustomsDeclareFormLogResult> pageResponse = new PageResponse<>();
        Page<StockoutCustomsDeclareFormLogEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutCustomsDeclareFormLogEntity> pageResult = logMapper.selectPage(page, wrapper);
        List<StockoutCustomsDeclareFormLogResult> collect = pageResult.getRecords().stream().map(entity -> {
            StockoutCustomsDeclareFormLogResult result = new StockoutCustomsDeclareFormLogResult();
            BeanUtilsEx.copyProperties(entity, result);
            result.setType(StockoutCustomsDeclareFormLogTypeEnum.getByName(result.getType()));
            return result;
        }).collect(Collectors.toList());
        pageResponse.setContent(collect);
        pageResponse.setTotalCount(pageResult.getTotal());
        return pageResponse;
    }


    /**
     * 供方签署并检查是否工厂已反馈
     *
     * @param formId
     */
    public void supplierSignAndCheckSupplierDone(Integer formId) {
        StockoutCustomsDeclareFormEntity formEntity = findById(formId);
        formEntity.setHasSupplierSignContract(Boolean.TRUE);
        this.updateById(formEntity);
        logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.SUPPLIER_SIGN, "供方已签署");
        checkSupplierDone(formId);
    }

    /**
     * 检查是否工厂已反馈
     * <p>
     * 供应商合同签署完后返回给我司 且 对应的报关条目已填写了进项发票号码+代码+开票日期后更新成工厂已反馈
     *
     * @param formId
     */
    private void checkSupplierDone(Integer formId) {
        StockoutCustomsDeclareFormEntity formEntity = findById(formId);
        if (formEntity.getHasSupplierSignContract() && formEntity.getHasInvoiceFill()) {
            formEntity.setStatus(StockoutCustomsDeclareFormStatusEnum.SUPPLIER_DONE.name());
            this.updateById(formEntity);
            logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.SUPPLIER_DONE, "工厂已反馈");
        }
    }

    /**
     * 判断关单变成【已完成】
     * 合同已完成 进项明细审核通过 -> 关单【已完成】
     *
     * @param formId
     */
    public void checkComplete(Integer formId) {
        StockoutCustomsDeclareFormEntity formEntity = findById(formId);
        StockoutCustomsDeclareContractEntity contract = contractService.findById(formEntity.getDeclareContractId());
        if (StockoutCustomsDeclareContractStatusEnum.COMPLETE.name().equals(contract.getStatus()) && formEntity.getHasAuditPassItem()) {
            formEntity.setStatus(StockoutCustomsDeclareFormStatusEnum.COMPLETE.name());
            formEntity.setUpdateBy(loginInfoService.getName());
            this.updateById(formEntity);
            logService.addLog(formEntity.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.DONE, "关单已完成");
            // 已完成则更新一期 发票/合同 及其对应文件
            formItemService.updateDocumentData(formEntity);
        }
    }

    /**
     * 检查是否所有发票上传完毕
     *
     * @param formId
     */
    private Boolean checkAllInvoiceSync(Integer formId) {
        List<StockoutCustomsDeclareFormItemEntity> formItemList = formItemService.itemList(formId);
        return formItemList.stream().allMatch(item ->
                StringUtils.hasText(item.getInputInvoiceNo())
                        && !Objects.isNull(item.getInvoiceDate())
                        && StringUtils.hasText(item.getInvoiceUrl())
                        && StringUtils.hasText(item.getInputInvoiceCode()));
    }

    /**
     * 需方已签署
     *
     * @param formId
     */
    public void companySign(Integer formId) {
        logService.addLog(formId, StockoutCustomsDeclareFormLogTypeEnum.COMPANY_SIGN, "需方已签署");
    }

    /**
     * 上传合同反馈
     * <p>
     * 修改关单状态 回填合同号
     *
     * @param form
     * @param contract
     */
    public void uploadContractFeedback(StockoutCustomsDeclareFormEntity form, StockoutCustomsDeclareContractEntity contract, Date stockinDate) {
        this.update(new LambdaUpdateWrapper<StockoutCustomsDeclareFormEntity>()
                .eq(StockoutCustomsDeclareFormEntity::getDeclareFormId, form.getDeclareFormId())
                .set(StockoutCustomsDeclareFormEntity::getStatus, StockoutCustomsDeclareFormStatusEnum.GENERATE_ORDER.name())
                .set(StockoutCustomsDeclareFormEntity::getDeclareContractId, contract.getDeclareContractId())
                .set(StockoutCustomsDeclareFormEntity::getDeclareContractNo, contract.getDeclareContractNo())
                .set(StockoutCustomsDeclareFormEntity::getStockinDate, stockinDate)
                .set(StockoutCustomsDeclareFormEntity::getDeliveryDate, contract.getDeliveryDate())
                .set(StockoutCustomsDeclareFormEntity::getUpdateBy, loginInfoService.getName())
                .set(StockoutCustomsDeclareFormEntity::getContractGenerateDate, new Date()));
        logService.addLog(form.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.GENERATE_CONTRACT, String.format("已生成合同【%s】", contract.getDeclareContractNo()));
        //回填进项明细
        List<StockoutCustomsDeclareFormItemEntity> formItemList = formItemService.itemListNoManual(form.getDeclareFormId());
        formItemList.forEach(formItem -> formItem.setDeclareContractNo(contract.getDeclareContractNo()));
        formItemService.updateBatchById(formItemList);
    }

    /**
     * 更新签订日期
     *
     * @param formIdList
     * @param signedDate
     */
    public void updateSignedDate(List<Integer> formIdList, Date signedDate) {
        if (formIdList.isEmpty()) return;
        this.update(new LambdaUpdateWrapper<StockoutCustomsDeclareFormEntity>()
                .in(StockoutCustomsDeclareFormEntity::getDeclareFormId, formIdList)
                .set(StockoutCustomsDeclareFormEntity::getContractSignedDate, signedDate)
                .set(StockoutCustomsDeclareFormEntity::getUpdateBy, loginInfoService.getName()));
    }

    /**
     * supplier同步发票
     * <p>
     * 1.批量赋值关单明细
     * 2.判断关单明细是否全部上传完，更改是否上传关单明细状态
     * 3.判断关单状态 更改为【工厂已反馈】
     *
     * @param requestList
     */
    @Transactional
    public void supplierSyncInvoice(Integer formId, List<SupplierSyncInvoiceItemRequest> requestList) {
        if (CollectionUtils.isEmpty(requestList)) {
            throw new BusinessServiceException("请求参数关单明细为空");
        }
        List<Integer> formItemIdList = requestList.stream()
                .map(SupplierSyncInvoiceItemRequest::getDeclareFormItemId)
                .filter(temp -> !Objects.isNull(temp))
                .collect(Collectors.toList());
        if (formItemIdList.isEmpty()) {
            throw new BusinessServiceException("过滤后关单明细为空");
        }
        List<StockoutCustomsDeclareFormItemEntity> formItemList = formItemService.itemListNoManual(formId);
        if (formItemList.isEmpty()) {
            throw new BusinessServiceException("关单明细为空");
        }
        //1.批量赋值关单明细
        Map<Integer, StockoutCustomsDeclareFormItemEntity> formItemMap = formItemList.stream().collect(Collectors.toMap(StockoutCustomsDeclareFormItemEntity::getDeclareFormItemId, Function.identity()));
        requestList.forEach(request -> {
            StockoutCustomsDeclareFormItemEntity formItem = formItemMap.get(request.getDeclareFormItemId());
            if (Objects.isNull(formItem)) {
                throw new BusinessServiceException(String.format("找不到id为【%s】关单明细为空", request.getDeclareFormItemId()));
            }
            formItem.setInputInvoiceNo(request.getInputInvoiceNo());
            formItem.setInvoiceDate(request.getInvoiceDate());
            formItem.setInvoiceUrl(request.getInvoiceUrl());
            formItem.setInputInvoiceCode(request.getInputInvoiceCode());
            formItem.setUpdateBy(loginInfoService.getName());
            formItem.setInvoiceFilename(request.getInvoiceFilename());
        });
        formItemService.updateBatchById(formItemList);

        StockoutCustomsDeclareFormEntity formEntity = this.getById(formId);
        //2.判断关单明细是否全部上传完，更改是否上传关单明细状态
        logService.addLog(formId, StockoutCustomsDeclareFormLogTypeEnum.UPDATE_ITEM, "供应商上传发票同步");
        Boolean isAllSync = checkAllInvoiceSync(formId);
        if (isAllSync && !formEntity.getShouldAuditItem()) {
            formEntity.setHasInvoiceFill(Boolean.TRUE);
            formEntity.setShouldAuditItem(Boolean.TRUE);
            formEntity.setUpdateBy(loginInfoService.getName());
            this.updateById(formEntity);
            logService.addLog(formId, StockoutCustomsDeclareFormLogTypeEnum.UPDATE_ITEM, "所有发票上传完成，待审核");
        }
        //3.判断关单状态 更改为【工厂已反馈】
        checkSupplierDone(formId);
    }


    /**
     * 通过id查找
     *
     * @param formId
     * @return
     */
    public StockoutCustomsDeclareFormEntity findById(Integer formId) {
        StockoutCustomsDeclareFormEntity formEntity = getById(formId);
        if (Objects.isNull(formEntity)) {
            throw new BusinessServiceException(String.format("找不到关单 %s", formId));
        }
        return formEntity;
    }

    /**
     * 计算进项金额
     *
     * @param inputQty              进项数量
     * @param taxInclusiveUnitPrice 含税单价
     * @return
     */
    public static BigDecimal calInputPrice(Integer inputQty, BigDecimal taxInclusiveUnitPrice) {
        return taxInclusiveUnitPrice.multiply(BigDecimal.valueOf(inputQty)).divide(BigDecimal.valueOf(1.13f), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算含税金额
     *
     * @param inputQty              进项数量
     * @param taxInclusiveUnitPrice 含税单价
     * @return
     */
    public static BigDecimal calTaxInclusivePrice(Integer inputQty, BigDecimal taxInclusiveUnitPrice) {
        return taxInclusiveUnitPrice.multiply(BigDecimal.valueOf(inputQty)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取税率
     *
     * @return
     */
    public static BigDecimal getTaxRate() {
        return new BigDecimal("0.13");
    }


    /**
     * 根据合同号获取关单列表
     *
     * @param contractNo
     * @return
     */
    public List<StockoutCustomsDeclareFormEntity> findListByContractNo(String contractNo) {
        return this.list(new LambdaQueryWrapper<StockoutCustomsDeclareFormEntity>().eq(StockoutCustomsDeclareFormEntity::getDeclareContractNo, contractNo));
    }

    /**
     * 根据合同号获取关单列表
     *
     * @param contractNoList
     * @return
     */
    public List<StockoutCustomsDeclareFormEntity> findListByContractNoList(List contractNoList) {
        return this.list(new LambdaQueryWrapper<StockoutCustomsDeclareFormEntity>().in(StockoutCustomsDeclareFormEntity::getDeclareContractNo, contractNoList));
    }

    /**
     * 根据合同id获取关单列表
     *
     * @param contractId
     * @return
     */
    public List<StockoutCustomsDeclareFormEntity> findListByContractId(Integer contractId) {
        return this.list(new LambdaQueryWrapper<StockoutCustomsDeclareFormEntity>().eq(StockoutCustomsDeclareFormEntity::getDeclareContractId, contractId));
    }


    /**
     * 通过进项比例和税率计算含税金额/含税单价/fob总价
     * 含税单价 = fob总价*当月汇率*1.13*进项比例/出口数量(结果保留整数、四舍五入)
     * 含税金额 = 含税单价*出口数量
     * fob总价(人民币) = fob总价*汇率
     *
     * <AUTHOR>
     */
    public void computeByInputRateAndExchangeRate(StockoutCustomsDeclareFormEntity declareFormEntity, BigDecimal exchangeRate, BigDecimal inputRate) {
        if (exchangeRate == null || inputRate == null) {
            declareFormEntity.setTaxInclusiveUnitPrice(BigDecimal.ZERO);
            declareFormEntity.setTaxInclusivePrice(BigDecimal.ZERO);
            declareFormEntity.setFobPriceCny(BigDecimal.ZERO);
            return;
        }
        declareFormEntity.setTaxInclusiveUnitPrice(declareFormEntity.getFobPrice()
                .multiply(exchangeRate).multiply(new BigDecimal("1.13"))
                .multiply(inputRate).divide(BigDecimal.valueOf(declareFormEntity.getgQty()), 1, BigDecimal.ROUND_HALF_UP));
        declareFormEntity.setTaxInclusivePrice(declareFormEntity.getTaxInclusiveUnitPrice().multiply(BigDecimal.valueOf(declareFormEntity.getgQty())));
        declareFormEntity.setFobPriceCny(declareFormEntity.getFobPrice().multiply(exchangeRate).setScale(2, RoundingMode.HALF_UP));
    }

    public StockoutCustomsDeclareFormEntity findByDeclareDocumentNoAndProtocolNoAndGNo(String declareDocumentNo, String protocolNo, String gNo) {
        LambdaQueryWrapper<StockoutCustomsDeclareFormEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutCustomsDeclareFormEntity::getDeclareDocumentNo, declareDocumentNo);
        wrapper.eq(StockoutCustomsDeclareFormEntity::getProtocolNo, protocolNo);
        wrapper.eq(StockoutCustomsDeclareFormEntity::getgNo, gNo);
        List<StockoutCustomsDeclareFormEntity> list = list(wrapper);
        if (list.size() > 1) {
            throw new BusinessServiceException(StrUtil.format("根据报关单号:{}, 协议号:{}, 项号{}在关单列表中找到多条数据，请确认", declareDocumentNo, protocolNo, gNo));
        }
        return CollectionUtil.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 通过协议号 和 项号获取
     *
     * @param protocolNo
     * @param gNo
     * @return
     */
    public StockoutCustomsDeclareFormEntity getByProtocolNoAndGNo(String protocolNo, String gNo) {
        StockoutCustomsDeclareFormEntity formEntity = findByProtocolNoAndGNo(protocolNo, gNo);
        if (Objects.isNull(formEntity))
            throw new BusinessServiceException(String.format("关单不存在 协议号:%s, 项号%s", protocolNo, gNo));
        return formEntity;
    }

    /**
     * 通过协议号 和 项号获取
     *
     * @param protocolNo
     * @param gNo
     * @return
     */
    public StockoutCustomsDeclareFormEntity findByProtocolNoAndGNo(String protocolNo, String gNo) {
        return getOne(new LambdaQueryWrapper<StockoutCustomsDeclareFormEntity>()
                .eq(StockoutCustomsDeclareFormEntity::getProtocolNo, protocolNo)
                .eq(StockoutCustomsDeclareFormEntity::getgNo, gNo)
                .last("limit 1"));
    }

    /**
     * 通过海关报关单号 和 项号获取
     *
     * @param declareDocumentNo
     * @param gNo
     * @return
     */
    public StockoutCustomsDeclareFormEntity getByDeclareDocumentNoAndGNo(String declareDocumentNo, String gNo) {
        StockoutCustomsDeclareFormEntity formEntity = getOne(new LambdaQueryWrapper<StockoutCustomsDeclareFormEntity>()
                .eq(StockoutCustomsDeclareFormEntity::getDeclareDocumentNo, declareDocumentNo)
                .eq(StockoutCustomsDeclareFormEntity::getgNo, gNo)
                .last("limit 1"));

        if (Objects.isNull(formEntity))
            throw new BusinessServiceException(String.format("关单不存在 海关报关单号:%s, 项号%s", declareDocumentNo, gNo));
        return formEntity;
    }

    public void delete(IdListRequest request) {
        if (CollectionUtil.isEmpty(request.getIdList()))
            throw new BusinessServiceException("请选择数据");
        listByIds(request.getIdList()).forEach(form -> {
            if (!StockoutCustomsDeclareFormStatusEnum.WAIT_DEAL.name().equals(form.getStatus())) {
                throw new BusinessServiceException(String.format("协议号 %s 项号 %s，非【待处理】", form.getProtocolNo(), form.getgNo()));
            }
        });

        removeByIds(request.getIdList());
    }

    @Transactional
    public void audit(IdListRequest request) {
        CollectionUtil.split(request.getIdList(), 50).forEach(splitFormIdList -> {
            List<StockoutCustomsDeclareFormEntity> splitFormList = listByIds(splitFormIdList).stream().peek(form -> {
                if (!StockoutCustomsDeclareFormStatusEnum.WAIT_AUDIT.name().equals(form.getStatus())) {
                    throw new BusinessServiceException(String.format("%s 第%s项 非待审核", form.getDeclareDocumentNo(), form.getgNo()));
                }

                form.setStatus(StockoutCustomsDeclareFormStatusEnum.DEALT.name());
                form.setCreateBy(loginInfoService.getName());

                logService.addLog(form.getDeclareFormId(), StockoutCustomsDeclareFormLogTypeEnum.AUDIT, "关单审核，推送供应商");
            }).collect(Collectors.toList());
            updateBatchById(splitFormList);

            //c. 发送supplier同步关单与供应商匹配数据
            syncSupplierMatchInfo(splitFormList);
        });

    }

    /**
     * 发送supplier同步关单与供应商匹配数据
     */
    private void syncSupplierMatchInfo(List<StockoutCustomsDeclareFormEntity> formList) {
        List<CustomsDeclareFormMessage> messageList = formList.stream().map(form -> {
            CustomsDeclareFormMessage message = new CustomsDeclareFormMessage();
            BeanUtilsEx.copyProperties(form, message);
            message.setgUnitName(StockoutBuilding.getDeclareUnitMap().get(message.getgUnit()));

            List<StockoutCustomsDeclareFormItemEntity> itemList = formItemService.itemList(form.getDeclareFormId());

            List<CustomsDeclareFormItemMessage> messageItemList = itemList.stream().map(item -> {
                CustomsDeclareFormItemMessage itemMessage = new CustomsDeclareFormItemMessage();
                BeanUtilsEx.copyProperties(item, itemMessage);
                return itemMessage;
            }).collect(Collectors.toList());
            message.setItemList(messageItemList);
            return message;
        }).collect(Collectors.toList());
        messageProducer.sendMessage(KafkaConstant.CUSTOMS_DECLARE_CONTRACT_SYNC_MARK, KafkaConstant.CUSTOMS_DECLARE_CONTRACT_SYNC_TOPIC, messageList);
    }


    /**
     * 匹配类型
     */
    public enum MatchTypeResultEnum {
        MANUAL("手动匹配"),
        AUTO("自动匹配");

        MatchTypeResultEnum(String desc) {
            this.desc = desc;
        }

        private String desc;

        public String getDesc() {
            return desc;
        }

    }
}

