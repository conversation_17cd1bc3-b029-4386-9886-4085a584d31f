package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemSourcePositionBo;
import com.nsy.api.wms.domain.stockout.StockoutOrderInfo;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.common.IsEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaLabelStatusEnum;
import com.nsy.api.wms.enumeration.stockout.FbaReplenishTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutDeliveryNoticeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stockout.ErpSyncLogisticsRequest;
import com.nsy.api.wms.request.stockout.FactoryDirectShippingRequest;
import com.nsy.api.wms.request.stockout.StockoutOrderLogisticsCompanyRequest;
import com.nsy.api.wms.request.stockout.StockoutUpdateLogisticsNoRequest;
import com.nsy.api.wms.request.stockout.SyncCancelPickRequest;
import com.nsy.api.wms.request.stockout.SyncIsUrgentRequest;
import com.nsy.api.wms.request.stockout.SyncShipmentNoticeRequest;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.erp.request.ErpSyncIossRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stock.StockPrematchService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentErpPickingBoxEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import com.nsy.wms.utils.randomid.FormNoGenerateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * HXD
 * 2022/3/31
 **/
@Service
public class StockoutOrderOperateService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderOperateService.class);
    @Autowired
    private StockoutOrderService orderService;
    @Autowired
    StockoutOrderLogService logService;
    @Autowired
    StockoutOrderItemService orderItemService;
    @Autowired
    ExternalApiLogService externalApiLogService;
    @Autowired
    StockoutShipmentItemService stockoutShipmentItemService;
    @Autowired
    StockoutShipmentConfirmService shipmentConfirmService;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    StockoutPickingTaskService stockoutPickingTaskService;
    @Autowired
    StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Autowired
    StockoutBatchLogService batchLogService;
    @Autowired
    StockoutBatchService batchService;
    @Autowired
    StockPrematchInfoService prematchInfoService;
    @Autowired
    StockInternalBoxItemService internalBoxItemService;
    @Autowired
    StockInternalBoxService internalBoxService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutShipmentAmazonRelationService amazonRelationService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentCustomsService shipmentCustomsService;
    @Autowired
    StockoutShipmentErpPickingBoxService shipmentErpPickingBoxService;
    @Autowired
    StockoutReceiverInfoService receiverInfoService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderCancelService stockoutOrderCancelService;
    @Autowired
    StockoutBatchSplitCommonService stockoutBatchSortCommonService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutOrderScanCheckService scanCheckService;
    @Autowired
    StockPrematchService stockPrematchService;

    /**
     * 校验是否能取消拣货单,出库中的出库单不能取消
     */
    public boolean checkCancelPick(SyncCancelPickRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.CHECK_CANCEL_PICK, "/stockout-order/check-cancel-pick",
                JsonMapper.toJson(request), request.getErpPickId().toString(), "校验拣货单是否能取消");
        apiLogEntity.setCreateBy(request.getOperator());
        try {
            StockoutOrderEntity stockoutOrderEntity = orderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getErpPickId, request.getErpPickId()).last("limit 1"));
            if (stockoutOrderEntity == null)
                throw new BusinessServiceException(String.format("ERP拣货单id【%s】在wms找不到出库单,请确认是否同步", request.getErpPickId()));
            if (stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.DELIVERED.name()))
                throw new BusinessServiceException("WMS出库单已发货，出库单无法取消，请确定");
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
            if (!stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.OUTBOUNDING.name())) return true;
        } catch (RuntimeException e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
        return false;
    }

    /**
     * 校验是否能回退订单
     * 已生成波次的出库单不让回退
     */
    public boolean checkBackTrade(SyncCancelPickRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.CHECK_BACK_TRADE, "/stockout-order/check-back-trade",
                JsonMapper.toJson(request), request.getErpPickId().toString(), "校验订单是否能回退");
        apiLogEntity.setCreateBy(request.getOperator());
        boolean result;
        try {
            if (request.getErpPickIds().isEmpty())
                throw new BusinessServiceException("拣货单id不能为空");
            result = getCheckResult(request.getErpPickIds());
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (RuntimeException e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
        return result;
    }

    private boolean getCheckResult(List<Integer> erpPickIds) {
        List<StockoutOrderEntity> stockoutOrderEntityList = orderService.list(new QueryWrapper<StockoutOrderEntity>().lambda().in(StockoutOrderEntity::getErpPickId, erpPickIds));
        if (stockoutOrderEntityList.isEmpty())
            throw new BusinessServiceException("ERP拣货单在wms找不到出库单,请确认是否同步");
        List<String> statusList = stockoutOrderEntityList.stream().map(StockoutOrderEntity::getStatus).distinct().collect(Collectors.toList());
        for (String status : statusList) {
            if (StockoutOrderStatusEnum.valueOf(status).getIndex() >= StockoutOrderStatusEnum.READY_PICK.getIndex())
                return false;
        }
        return true;
    }

    /**
     * 商通设置急单同步
     */
    public void syncIsUrgent(SyncIsUrgentRequest request) {
        if (CollectionUtils.isEmpty(request.getOrderNoList())) throw new BusinessServiceException("订单集合不能为空");
        if (Objects.isNull(request.getUrgent())) throw new BusinessServiceException("是否急单参数有误");
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SET_URGENT, "/stockout-order/sync-is-urgent",
                JsonMapper.toJson(request), String.join(",", request.getOrderNoList()), String.format("%s同步", request.getUrgent() ? "设置急单" : "取消急单"));
        apiLogEntity.setCreateBy(request.getOperator());
        try {
            List<StockoutOrderItemEntity> list = orderItemService.list(new LambdaQueryWrapper<StockoutOrderItemEntity>().in(StockoutOrderItemEntity::getOrderNo, request.getOrderNoList()));
            if (CollectionUtils.isEmpty(list))
                throw new BusinessServiceException(String.format("订单号【%s】在wms均找不到出库单", String.join(",", request.getOrderNoList())));
            List<Integer> stockoutOrderIdList = list.stream().map(StockoutOrderItemEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
            List<StockoutOrderEntity> orderEntityList = orderService.list(new LambdaQueryWrapper<StockoutOrderEntity>().in(StockoutOrderEntity::getStockoutOrderId, stockoutOrderIdList));
            orderService.updateBatchById(orderEntityList.stream().peek(entity -> {
                entity.setUrgent(request.getUrgent());
                entity.setUpdateBy(request.getOperator());
            }).collect(Collectors.toList()));
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (RuntimeException e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }

    /**
     * 同步发货通知修改
     */
    public void syncShipmentNotice(SyncShipmentNoticeRequest request) {
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_SHIPMENT_NOTICE, "/stockout-order/sync-shipment-notice",
                JsonMapper.toJson(request), request.getErpPickId().toString(), "发货通知修改同步");
        apiLogEntity.setCreateBy(request.getOperator());
        try {
            StockoutOrderEntity stockoutOrderEntity = orderService.getOne(new QueryWrapper<StockoutOrderEntity>().lambda().eq(StockoutOrderEntity::getErpPickId, request.getErpPickId()));
            String oldNotifyShipStatus = StockoutDeliveryNoticeEnum.of(stockoutOrderEntity.getNotifyShipStatus());
            orderService.update(new LambdaUpdateWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getErpPickId, request.getErpPickId())
                    .set(StockoutOrderEntity::getNotifyShipStatus, StockoutDeliveryNoticeEnum.of(request.getNotifyShip()))
                    .set(StockoutOrderEntity::getUpdateBy, request.getOperator()));
            String content = String.format("单号：%s，发货通知【%s】修改为【%s】", stockoutOrderEntity.getStockoutOrderNo(), oldNotifyShipStatus, StockoutDeliveryNoticeEnum.of(request.getNotifyShip()));
            logService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.SHIPPING_NOTICE, content, request.getOperator());
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (RuntimeException e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }

    public void erpChangeLogistics(ErpSyncLogisticsRequest request) {
        LoginInfoService.setName(request.getOperator());
        if (CollectionUtils.isEmpty(request.getErpPickIds())) {
            LoginInfoService.removeName();
            return;
        }
        List<StockoutOrderEntity> list = orderService.list(new LambdaQueryWrapper<StockoutOrderEntity>().in(StockoutOrderEntity::getErpPickId, request.getErpPickIds()));
        if (CollectionUtils.isEmpty(list)) {
            LoginInfoService.removeName();
            return;
        }
        StockoutOrderLogisticsCompanyRequest updateRequest = new StockoutOrderLogisticsCompanyRequest();
        updateRequest.setSyncErp(false);
        updateRequest.setStockoutOrderIds(list.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()));
        updateRequest.setLogisticsCompany(request.getLogisticsCompany());
        orderService.updateOutOrderLogisticsCompany(updateRequest);
        LoginInfoService.removeName();
    }

    /**
     * 取消装箱数据
     *
     * @param stockoutOrderEntity 出库单
     */
    public void cancelShipment(StockoutOrderEntity stockoutOrderEntity) {
        List<StockoutShipmentItemEntity> updateShipmentItemList = stockoutShipmentItemService.list(new QueryWrapper<StockoutShipmentItemEntity>().lambda()
                .eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderEntity.getStockoutOrderNo())
                .eq(StockoutShipmentItemEntity::getIsDeleted, IsEnum.IS_NOT));
        if (!updateShipmentItemList.isEmpty()) {
            // 出库单 - 删除装箱清单
            shipmentConfirmService.cancelShipment(Collections.singletonList(stockoutOrderEntity.getStockoutOrderNo()), stockoutOrderEntity.getSpaceId());
            // 不是按单拣货 && 取消中  直接return
            if (!StockoutPickingTypeEnum.WHOLE_PICK.name().equals(stockoutOrderEntity.getPickingType())
                    && StockoutOrderStatusEnum.CANCELLING.name().equals(stockoutOrderEntity.getStatus()))
                return;

            // 拣货箱数据撤货
            scanCheckService.updateWithdrawalBoxByStockoutOrder(stockoutOrderEntity);
            // 更新出库单状态 -> 已取消
            stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.CANCELLED.name());
            orderService.updateById(stockoutOrderEntity);
            messageProducer.sendMessage(KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC_MARK, KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), stockoutOrderEntity.getStockoutOrderId()));
        }
    }

    /**
     * 取消拣货任务
     *
     * @param stockoutOrderEntity      出库单
     * @param stockoutBatchOrderEntity 波次出库单
     * @return 提示信息
     */
    public String cancelPickTask(StockoutOrderEntity stockoutOrderEntity, StockoutBatchOrderEntity stockoutBatchOrderEntity) {
        String notice = "";
        List<StockoutBatchOrderEntity> batchOrderEntityList = batchOrderService.findAllByBatchId(stockoutBatchOrderEntity.getBatchId()).stream()
                .filter(o -> !o.getStatus().equals(StockoutWaveTaskStatusEnum.CANCELLED.name())).collect(Collectors.toList());
        if (batchOrderEntityList.size() != 1) {
            return notice;
        }
        List<StockoutPickingTaskEntity> taskEntityList = stockoutPickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .eq(StockoutPickingTaskEntity::getBatchId, stockoutBatchOrderEntity.getBatchId()));
        if (stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.PICKING.name()) || stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.READY_OUTBOUND.name())) {
            notice = "当前出库单已开始拣货，请撤货";
            // 已拣货的 sku恢复到拣货库位
            backPickItem(taskEntityList);
        }
        if (stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.PICKING.name())
                || stockoutOrderEntity.getStatus().equals(StockoutOrderStatusEnum.READY_PICK.name())) {
            // 取消拣货任务
            if (!taskEntityList.isEmpty()) {
                List<Integer> taskIds = taskEntityList.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList());
                stockoutPickingTaskService.removeByIds(taskIds);
                stockoutPickingTaskItemService.remove(new LambdaQueryWrapper<StockoutPickingTaskItemEntity>().in(StockoutPickingTaskItemEntity::getTaskId, taskIds));
                StringBuilder sb = new StringBuilder(String.format("波次【%s】下拣货任务", stockoutBatchOrderEntity.getBatchId()));
                taskEntityList.forEach(taskEntity -> sb.append(String.format("【%s】", taskEntity.getTaskId())));
                sb.append("全部取消");
                batchLogService.addLog(stockoutBatchOrderEntity.getBatchId(), StockoutBatchLogTypeEnum.CANCEL_PICKING_TASK.getStockoutBatchLogType(), sb.toString());
            }
            // 取消波次
            StockoutBatchEntity batchEntity = batchService.getById(stockoutBatchOrderEntity.getBatchId());
            batchService.updateBatchStatus(batchEntity, StockoutWaveTaskStatusEnum.CANCELLED);
            batchLogService.addLog(stockoutBatchOrderEntity.getBatchId(), StockoutBatchLogTypeEnum.CANCEL_BATCH.getStockoutBatchLogType(), String.format("波次【%s】已取消", stockoutBatchOrderEntity.getBatchId()));
        }

        stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.CANCELLED.name());
        messageProducer.sendMessage(KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC_MARK, KafkaConstant.STOCKOUT_ORDER_CANCELLED_TODO_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), stockoutOrderEntity.getStockoutOrderId()));
        return notice;
    }

    /**
     * 已拣货的 sku恢复到拣货库位
     */
    private void backPickItem(List<StockoutPickingTaskEntity> taskEntityList) {
        if (taskEntityList.isEmpty()) {
            return;
        }
        List<Integer> taskIds = taskEntityList.stream().map(StockoutPickingTaskEntity::getTaskId).collect(Collectors.toList());
        // 胚款不撤货
        List<StockoutPickingTaskItemEntity> taskItemEntityList = stockoutPickingTaskItemService.list(new QueryWrapper<StockoutPickingTaskItemEntity>().lambda().in(StockoutPickingTaskItemEntity::getTaskId, taskIds))
                .stream().filter(o -> o.getPickedQty() != null && o.getPickedQty() > 0 && o.getProcessCompleteQty() != null && o.getProcessCompleteQty() <= 0).collect(Collectors.toList());
        if (taskItemEntityList.isEmpty()) {
            return;
        }
        StockoutBatchEntity batchEntity = batchService.getStockoutBatchById(taskEntityList.get(0).getBatchId());
        List<StockoutPickingTaskEntity> pickingTaskEntityList = stockoutPickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .in(StockoutPickingTaskEntity::getTaskId, taskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getTaskId).distinct().collect(Collectors.toList())));
        List<StockInternalBoxItemEntity> boxItemEntityList = internalBoxItemService.list(new QueryWrapper<StockInternalBoxItemEntity>().lambda()
                .in(StockInternalBoxItemEntity::getInternalBoxCode, pickingTaskEntityList.stream().map(StockoutPickingTaskEntity::getPickingBoxCode).collect(Collectors.toList()))
                .in(StockInternalBoxItemEntity::getSku, taskItemEntityList.stream().map(StockoutPickingTaskItemEntity::getSku).distinct().collect(Collectors.toList())));
        // 拣货箱明细扣减
        StockInternalBoxEntity stockInternalBoxEntity = internalBoxService.getWithdrawalByWorkspace(batchEntity.getWorkspace(), taskEntityList.get(0).getSpaceId());
        for (StockInternalBoxItemEntity boxItemEntity : boxItemEntityList) {
            StockInternalBoxItemSourcePositionBo stockInternalBoxItemSourcePositionBo = internalBoxItemService.minusStockInternalBoxItemQty(boxItemEntity, boxItemEntity.getQty(), StockChangeLogTypeEnum.STOCKOUT_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
            // 撤货
            ProductSpecInfoEntity topBySku = productSpecInfoService.findTopBySku(boxItemEntity.getSku());
            stockoutBatchSortCommonService.saveOrUpdateWithdrawalBox(stockInternalBoxEntity, topBySku, taskEntityList.get(0).getBatchId(), stockInternalBoxItemSourcePositionBo.getQty(), Collections.singletonList(stockInternalBoxItemSourcePositionBo));
        }
        pickingTaskEntityList.forEach(o -> {
            internalBoxService.changeStockInternalBoxStatus(o.getPickingBoxCode(), StockInternalBoxStatusEnum.EMPTY.name());
        });
    }

    /**
     * 已拣货数撤货
     * 加工库位
     * 普通库位 -> 普通撤货箱
     */
   /* private void withdrawalStock(Map<String, List<StockoutPickingTaskItemEntity>> withdrawalMap, Integer batchId, Integer spaceId) {
        if (withdrawalMap.isEmpty()) {
            return;
        }
        StockInternalBoxEntity stockInternalBoxEntity = internalBoxService.getWithdrawalByWorkspace("", spaceId);
        // 撤货箱状态改为装箱中
        if (!stockInternalBoxEntity.getStatus().equals(StockInternalBoxStatusEnum.PACKING.name())) {
            internalBoxService.changeStockInternalBoxStatus(stockInternalBoxEntity.getInternalBoxCode(), StockInternalBoxStatusEnum.PACKING.name());
        }
        LambdaQueryWrapper<StockInternalBoxItemEntity> stockInternalBoxItemEntityLambdaQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, stockInternalBoxEntity.getInternalBoxCode())
                .eq(StockInternalBoxItemEntity::getBatchId, batchId);
        List<StockInternalBoxItemEntity> internalBoxItemList = internalBoxItemService.list(stockInternalBoxItemEntityLambdaQueryWrapper);
        for (Map.Entry<String, List<StockoutPickingTaskItemEntity>> entry : withdrawalMap.entrySet()) {
            StockInternalBoxItemEntity curBoxItem = internalBoxItemList.stream().filter(o -> o.getSku().equals(entry.getKey())).findFirst().orElse(null);
            if (Objects.isNull(curBoxItem)) {
                curBoxItem = new StockInternalBoxItemEntity();
                curBoxItem.setSpaceId(stockInternalBoxEntity.getSpaceId());
                curBoxItem.setInternalBoxId(stockInternalBoxEntity.getInternalBoxId());
                curBoxItem.setInternalBoxCode(stockInternalBoxEntity.getInternalBoxCode());
                curBoxItem.setProductId(entry.getValue().get(0).getProductId());
                curBoxItem.setSpecId(entry.getValue().get(0).getSpecId());
                curBoxItem.setSku(entry.getValue().get(0).getSku());
                curBoxItem.setBatchId(batchId);
                curBoxItem.setIsProcess(0);
                curBoxItem.setQty(0);
            }
            internalBoxItemService.addStockInternalBoxItemQty(curBoxItem, entry.getValue().stream().mapToInt(StockoutPickingTaskItemEntity::getPickedQty).sum(), StockChangeLogTypeEnum.STOCKOUT_WITHDRAWAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
        }
    }*/

    /**
     * 复核完成扫描，出库单状态变更为【待发货】
     *
     * @param stockoutOrderInfo 出库单信息
     * @param scanSum           扫描数
     * @param lackSum           缺货数
     */
    public void changeStockoutOrderStatusToReadyDelivery(StockoutOrderInfo stockoutOrderInfo, Integer scanSum, int lackSum) {
        StockoutOrderEntity stockoutOrderEntity = orderService.getByStockoutOrderNo(stockoutOrderInfo.getStockoutOrderNo());
        orderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.READY_DELIVERY.name());
        // 扫描结束日志
        logService.addLog(stockoutOrderInfo.getStockoutOrderNo(), StockoutOrderLogTypeEnum.READY_DELIVERY, String.format("扫描结束，共扫描【%s】件，缺货【%s】件", scanSum, lackSum));
        // 装箱确认日志
        List<Integer> shipmentIdList = shipmentItemService.list(new QueryWrapper<StockoutShipmentItemEntity>().lambda()
                .eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderInfo.getStockoutOrderNo())).stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        if (shipmentIdList.isEmpty())
            throw new BusinessServiceException(String.format("当前出库单【%s】未装箱，请先装箱", stockoutOrderInfo.getStockoutOrderNo()));
        List<StockoutShipmentEntity> shipmentEntityList = shipmentService.list(new QueryWrapper<StockoutShipmentEntity>().lambda()
                .in(StockoutShipmentEntity::getShipmentId, shipmentIdList)
                .ne(StockoutShipmentEntity::getIsDeleted, IsDeletedConstant.DELETED));
        if (!shipmentEntityList.isEmpty()) {
            LOGGER.debug(JsonMapper.toJson(shipmentEntityList));
            // 同步ERP装箱清单数据
            shipmentErpPickingBoxService.sendErpPickingBoxSyncRequestByShipmentList(shipmentEntityList);
            List<StockoutShipmentEntity> unShippedList = shipmentEntityList.stream().filter(o -> !StockoutShipmentStatusEnum.SHIPPED.name().equals(o.getStatus())).collect(Collectors.toList());
            if (!unShippedList.isEmpty()) {
                List<String> shipmentCodeList = unShippedList.stream().map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.toList());
                BigDecimal weightSum = unShippedList.stream().map(StockoutShipmentEntity::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                logService.addLog(stockoutOrderInfo.getStockoutOrderNo(), StockoutOrderLogTypeEnum.PACKING_COMFIRM, String.format("完成装箱，装箱单号【%s】，共【%s】箱子，总重量【%s】kg", String.join("、", shipmentCodeList), shipmentEntityList.size(), weightSum));
                // 通知厦门业务员
//                confirmBoxNotifyReceiver(stockoutOrderInfo, shipmentIdList);
            }
        }
    }

    /**
     * 金多厦装箱完成后要通知对应的业务员
     */
//    private void confirmBoxNotifyReceiver(StockoutOrderInfo stockoutOrderInfo, List<Integer> shipmentIdList) {
//        if (!LocationEnum.XIAMEN.name().equals(TenantContext.getTenant())) {
//            return;
//        }
//        if (!StockoutOrderTypeEnum.FIRST_LEG_DELIVERY.name().equals(stockoutOrderInfo.getStockoutType()) || !StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(stockoutOrderInfo.getWorkspace())) {
//            return;
//        }
//        StockoutReceiverInfoEntity receiverInfoEntity = receiverInfoService.getByStockoutOrderId(stockoutOrderInfo.getStockoutOrderId());
//        if (receiverInfoEntity == null || !com.nsy.api.core.apicore.util.StringUtils.hasText(receiverInfoEntity.getReceiverUserName())) {
//            return;
//        }
//        List<String> orderNos = stockoutShipmentItemService.getOrderNosByShipmentIds(shipmentIdList);
//        SendDingDingMessage sendDingDingMessage = new SendDingDingMessage();
//        sendDingDingMessage.setText(String.format("订单号：【%s】，已装箱完成", String.join(",", orderNos)));
//        sendDingDingMessage.setUserNameList(Collections.singletonList(receiverInfoEntity.getReceiverUserName()));
//        //发送kafka消息
//        String businessMark = KafkaConstant.WMS_SEND_DINGDING_MESSAGE_NAME;
//        messageProducer.sendMessage(businessMark, KafkaConstant.WMS_SEND_DINGDING_MESSAGE_TOPIC, sendDingDingMessage);
//    }
    public void changeLogisticsNoBatch(StockoutUpdateLogisticsNoRequest request) {
        List<StockoutOrderEntity> orderEntityList = orderService.listByIds(request.getIdList());
        if (CollectionUtils.isEmpty(orderEntityList)) {
            throw new BusinessServiceException("无法找到对应出库单，请核对");
        }
        List<String> orderNos = new ArrayList<>();
        orderEntityList.forEach(item -> {
            String originNos = StringUtils.hasText(item.getLogisticsNo()) ? item.getLogisticsNo() : "空";
            item.setLogisticsNo(request.getLogisticsNo());
            item.setUpdateBy(loginInfoService.getName());
            orderNos.add(item.getStockoutOrderNo());
            logService.addLog(item.getStockoutOrderNo(), StockoutOrderLogTypeEnum.STOCKOUT_ORDER_CHANGE_LOGISTICS_NO, "出库单修改物流单号，由" + originNos + "更改为" + request.getLogisticsNo());
        });
        orderService.updateBatchById(orderEntityList);
        List<StockoutShipmentEntity> shipments = shipmentItemService.findByStockoutOrderNo(orderNos);
        if (!CollectionUtils.isEmpty(shipments)) {
            shipments.forEach(shipment -> {
                shipment.setLogisticsNo(request.getLogisticsNo());
                shipment.setUpdateBy(loginInfoService.getName());
            });
            shipmentService.updateBatchById(shipments);
            shipmentErpPickingBoxService.sendErpPickingBoxSyncRequestByShipmentList(shipments);
        }

    }

    public void syncIOSS(ErpSyncIossRequest request) {
        if (CollectionUtils.isEmpty(request.getTidList()) || !StringUtils.hasText(request.getIoss())) {
            return;
        }
        List<StockoutOrderItemEntity> itemList = orderItemService.getListByOrderNos(request.getTidList());
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        Set<Integer> orderIds = new HashSet<>();
        itemList.forEach(item -> {
            item.setIossNumber(request.getIoss());
            item.setUpdateBy(request.getUserName());
            orderIds.add(item.getStockoutOrderId());
        });
        orderItemService.updateBatchById(itemList);
        List<StockoutOrderEntity> orderEntityList = orderService.listByIds(orderIds);
        orderEntityList.forEach(order -> logService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.SYNC_IOSS, "设置ioss号：" + request.getIoss()));
    }

    /**
     * 工厂直发FBA 单据补充回填
     *
     * <AUTHOR>
     * 2024-11-18
     */
    public void generateFactoryShippingData(FactoryDirectShippingRequest request) {
        // validAndRemove(request);
        StockoutOrderEntity orderEntity = new StockoutOrderEntity();
        buildOrderEntity(request, orderEntity);
        orderService.save(orderEntity);
        Map<String, StockoutOrderItemEntity> orderItemMap = new HashMap<>();
        request.getBoxInfoList().forEach(boxInfo -> {
            StockoutShipmentEntity shipmentEntity = buildShipmentEntity(request, boxInfo);
            StockoutShipmentErpPickingBoxEntity pickingBoxEntity = new StockoutShipmentErpPickingBoxEntity();
            pickingBoxEntity.setErpPickingBoxId(boxInfo.getPickingBoxId());
            pickingBoxEntity.setErpPickId(0);
            pickingBoxEntity.setShipmentId(shipmentEntity.getShipmentId());
            pickingBoxEntity.setCreateBy(request.getUserName());
            shipmentErpPickingBoxService.save(pickingBoxEntity);
            StockoutShipmentAmazonRelationEntity amazonRelationEntity = new StockoutShipmentAmazonRelationEntity();
            amazonRelationEntity.setShipmentId(shipmentEntity.getShipmentId());
            amazonRelationEntity.setDestinationFulfillmentCenterId(boxInfo.getDestinationFulfillmentCenterId());
            amazonRelationEntity.setAmazonReferenceId(boxInfo.getAmazonReferenceId());
            amazonRelationEntity.setFbaShipmentId(boxInfo.getFbaShipmentId());
            amazonRelationEntity.setErpPickingBoxId(boxInfo.getPickingBoxId());
            amazonRelationEntity.setErpTid(request.getOrderNo());
            amazonRelationService.save(amazonRelationEntity);
            boxInfo.getSkuInfoList().forEach(skuInfo -> {
                StockoutOrderItemEntity orderItemEntity = orderItemMap.get(skuInfo.getSku());
                if (orderItemEntity == null) {
                    orderItemEntity = new StockoutOrderItemEntity();
                    BeanUtilsEx.copyProperties(skuInfo, orderItemEntity);
                    orderItemEntity.setStockoutOrderId(orderEntity.getStockoutOrderId());
                    orderItemEntity.setOrderNo(request.getOrderNo());
                    orderItemEntity.setScanQty(orderItemEntity.getQty());
                    orderItemEntity.setShipmentQty(orderItemEntity.getQty());
                    orderItemEntity.setCreateBy(loginInfoService.getName());
                } else {
                    orderItemEntity.setQty(orderItemEntity.getQty() + skuInfo.getQty());
                }
                orderItemService.saveOrUpdate(orderItemEntity);
                orderItemMap.put(skuInfo.getSku(), orderItemEntity);
                StockoutShipmentItemEntity itemEntity = new StockoutShipmentItemEntity();
                BeanUtilsEx.copyProperties(skuInfo, itemEntity);
                itemEntity.setOrderItemId(orderItemEntity.getOrderItemId());
                itemEntity.setShipmentId(shipmentEntity.getShipmentId());
                itemEntity.setStockoutOrderNo(orderEntity.getStockoutOrderNo());
                itemEntity.setOrderNo(request.getOrderNo());
                itemEntity.setStockoutOrderItemId(orderItemEntity.getStockoutOrderItemId());
                shipmentItemService.save(itemEntity);
            });
            shipmentCustomsService.generateCustomsOrder(amazonRelationEntity);
        });
    }

    private StockoutShipmentEntity buildShipmentEntity(FactoryDirectShippingRequest request, com.nsy.api.wms.request.stockout.FactoryDirectShippingBoxInfo boxInfo) {
        StockoutShipmentEntity shipmentEntity = new StockoutShipmentEntity();
        BeanUtilsEx.copyProperties(boxInfo, shipmentEntity);
        shipmentEntity.setStockoutType(StockoutOrderTypeEnum.FBA_FACTORY_DELIVERY.name());
        shipmentEntity.setPlatformName(StockoutOrderPlatformEnum.AMAZON.getName());
        shipmentEntity.setShipmentDate(new Date());
        shipmentEntity.setStatus(StockoutShipmentStatusEnum.PACKING_END.name());
        shipmentEntity.setFbaLabelStatus(FbaLabelStatusEnum.COMPLETE.name());
        shipmentEntity.setFbaReplenishType(request.getFbaReplenishType());
        shipmentEntity.setReplenishOrder(request.getFbaReplenishOrder());
        shipmentService.save(shipmentEntity);
        return shipmentEntity;
    }

    private void buildOrderEntity(FactoryDirectShippingRequest request, StockoutOrderEntity orderEntity) {
        orderEntity.setSpaceId(1);
        orderEntity.setAreaName("工厂直发区域");
        orderEntity.setStockoutType(StockoutOrderTypeEnum.FBA_FACTORY_DELIVERY.name());
        orderEntity.setStockoutOrderNo(FormNoGenerateUtil.generateFormNo(orderService.getFormNoTypeEnumByStockoutOrderType(StockoutOrderTypeEnum.getBy(orderEntity.getStockoutType()))));
        orderEntity.setStatus(StockoutOrderStatusEnum.READY_DELIVERY.name());
        orderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
        orderEntity.setWorkspace(StockoutOrderWorkSpaceEnum.FBA_AREA.name());
        orderEntity.setStoreId(request.getStoreId());
        orderEntity.setBusinessType(request.getBusinessType());
        orderEntity.setReplenishOrder(request.getFbaReplenishOrder());
        orderEntity.setFbaReplenishType(request.getFbaReplenishType());
        orderEntity.setStoreName(request.getStoreName());
        orderEntity.setFbaLabelStatus(FbaLabelStatusEnum.COMPLETE.name());
        orderEntity.setCreateBy(loginInfoService.getName());
        orderEntity.setFbaReplenishType(FbaReplenishTypeEnum.NORMAL.getName());
    }

    public void validAndRemove(FactoryDirectShippingRequest request) {
        // 先判断之前是否有订单信息，如果有先删掉
        List<StockoutShipmentItemEntity> byOrderNo = shipmentItemService.findByOrderNo(request.getOrderNo());
        if (CollectionUtil.isNotEmpty(byOrderNo)) {
            List<String> outOrderNos = byOrderNo.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
            List<Integer> shipmentIds = byOrderNo.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
            List<StockoutOrderEntity> stockoutOrderList = orderService.getByStockoutOrderNoList(outOrderNos);
            if (stockoutOrderList.stream().anyMatch(it -> StrUtil.equals(it.getStatus(), StockoutOrderStatusEnum.DELIVERED.name()))) {
                throw new BusinessServiceException("出库单已发货，无法重新生成数据！");
            }
            List<Integer> orderIds = stockoutOrderList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList());
            // 删除出库单、出库单明细、装箱清单、装箱明细、亚马逊装箱
            orderService.removeByIds(orderIds);
            LambdaQueryWrapper<StockoutOrderItemEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(StockoutOrderItemEntity::getStockoutOrderId, orderIds);
            orderItemService.remove(wrapper);
            shipmentService.removeByIds(shipmentIds);
            shipmentItemService.removeByIds(byOrderNo.stream().map(StockoutShipmentItemEntity::getShipmentItemId).collect(Collectors.toList()));
            LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper1 = new LambdaQueryWrapper<>();
            wrapper1.in(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentIds);
            amazonRelationService.remove(wrapper1);
        }
    }
}
