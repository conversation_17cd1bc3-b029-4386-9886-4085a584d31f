package com.nsy.wms.business.service.download.stockout;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.repository.jpa.mapper.stock.StockInternalBoxItemMapper;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stock.StockInternalBoxItemListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockInternalBoxItemResponse;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockTransferBoxItemDownloadService extends ServiceImpl<StockInternalBoxItemMapper, StockInternalBoxItemEntity> implements IDownloadService {

    @Autowired
    private StockInternalBoxItemService stockInternalBoxItemService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_TRANSFER_BOX_ITEM;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockInternalBoxItemListRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockInternalBoxItemListRequest.class);
        downloadRequest.setPageSize(request.getPageSize());
        downloadRequest.setPageIndex(request.getPageIndex());
        PageResponse<StockInternalBoxItemResponse> pageResponse = stockInternalBoxItemService.getStockInternalBoxItemListByRequeryList(downloadRequest);
        List<StockInternalBoxItemResponse> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(pageResponse.getContent())) {
            resultList = pageResponse.getContent();
        }
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(resultList));
        return response;
    }
}
