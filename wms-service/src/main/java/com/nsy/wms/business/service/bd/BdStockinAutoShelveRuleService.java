package com.nsy.wms.business.service.bd;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxMaterialSizeEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.request.bd.BdStockinAutoShelveRuleAddRequest;
import com.nsy.api.wms.request.bd.BdStockinAutoShelveRuleImportRequest;
import com.nsy.api.wms.request.bd.BdStockinAutoShelveRuleListRequest;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.bd.BdStockinAutoShelveRuleListResponse;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdStockinAutoShelveRuleEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdStockinAutoShelveRuleMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 自动上架规则业务实现
 * @date: 2024-03-21 10:39
 */
@Service
public class BdStockinAutoShelveRuleService extends ServiceImpl<BdStockinAutoShelveRuleMapper, BdStockinAutoShelveRuleEntity> implements IProcessUploadDataService {

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private BdPositionService positionService;
    @Autowired
    private StockInternalBoxService internalBoxService;

    /**
     * 新增规则
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void addRule(BdStockinAutoShelveRuleAddRequest request) {
        this.validData(request);
        BdStockinAutoShelveRuleEntity entity = new BdStockinAutoShelveRuleEntity();
        BeanUtils.copyProperties(request, entity);
        entity.setLocation(TenantContext.getTenant());
        entity.setCreateBy(loginInfoService.getName());
        this.save(entity);
    }

    /**
     * 修改规则
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void editRule(BdStockinAutoShelveRuleAddRequest request) {
        this.validData(request);
        BdStockinAutoShelveRuleEntity entity = this.getById(request.getId());
        if (entity == null) {
            throw new BusinessServiceException("规则不存在");
        }
        BeanUtils.copyProperties(request, entity);
        entity.setUpdateBy(loginInfoService.getName());
        this.updateById(entity);
    }

    /**
     * 分页查询
     *
     * @param request
     * @return
     */
    public PageResponse<BdStockinAutoShelveRuleListResponse> getRuleList(BdStockinAutoShelveRuleListRequest request) {
        Page page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<BdStockinAutoShelveRuleListResponse> pageInfo = this.baseMapper.getRuleList(page, request);

        PageResponse<BdStockinAutoShelveRuleListResponse> pageResponse = new PageResponse<>();
        if (CollectionUtils.isEmpty(pageInfo.getRecords())) {
            pageResponse.setTotalCount(0L);
            return pageResponse;
        }
        Map<String, String> internalBoxTypeMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_INTERNAL_BOX_TYPE.getName());
        pageInfo.getRecords().forEach(detail -> detail.setInternalBoxType(internalBoxTypeMap.get(detail.getInternalBoxType())));
        pageResponse.setTotalCount(pageInfo.getTotal());
        pageResponse.setContent(pageInfo.getRecords());
        return pageResponse;
    }

    /**
     * 批量删除规则
     *
     * @param ids 规则ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessServiceException("请选择要删除的规则");
        }
        this.removeByIds(ids);
    }

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_BD_STOCKIN_AUTO_SHELVE_RULE_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<BdStockinAutoShelveRuleImportRequest> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), BdStockinAutoShelveRuleImportRequest.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        List<BdStockinAutoShelveRuleImportRequest> errorList = new ArrayList<>();
        List<BdStockinAutoShelveRuleEntity> saveList = new LinkedList<>();
        List<String> existList = new ArrayList<>();
        importList.forEach(detail -> {
            //校验不通过
            if (!this.importValidData(detail, errorList, existList)) {
                return;
            }
            BdStockinAutoShelveRuleEntity entity = new BdStockinAutoShelveRuleEntity();
            BeanUtils.copyProperties(detail, entity);
            entity.setCreateBy(loginInfoService.getName());
            entity.setCreateDate(new Date());
            saveList.add(entity);
        });
        if (!CollectionUtils.isEmpty(saveList)) {
            this.saveBatch(saveList);
        }
        if (!errorList.isEmpty()) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return response;
    }

    /**
     * 校验数据
     *
     * @param request
     */
    private void validData(BdStockinAutoShelveRuleAddRequest request) {
        //新增的时候要校验内部箱下是否存在对应的规则,修改的时候由于内部箱不让修改所以校验
        if (Objects.isNull(request.getId())) {
            LambdaQueryWrapper<BdStockinAutoShelveRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BdStockinAutoShelveRuleEntity::getInternalBoxCode, request.getInternalBoxCode());
            if (this.count(queryWrapper) > 0) {
                throw new BusinessServiceException("该内部箱规则已存在!");
            }
        }
        BdPositionEntity positionInfo = positionService.getByPositionCode(request.getPositionCode());
        if (Objects.isNull(positionInfo)) {
            throw new BusinessServiceException("查询不到对应的库位信息请检查!");
        }
        if (!positionInfo.getSpaceId().equals(request.getSpaceId())) {
            throw new BusinessServiceException("仓库和库位信息不一致请检查!");
        }
        if (!Lists.newArrayList(BdPositionTypeEnum.STOCK_POSITION.name(), BdPositionTypeEnum.CROSS_POSITION.name(), BdPositionTypeEnum.ACTIVITY_POSITION.name()).contains(positionInfo.getPositionType())) {
            throw new BusinessServiceException("目标库位仅支持存储库位、活动库位、越库库位请检查!");
        }
        StockInternalBoxEntity internalBox = internalBoxService.findByInternalBoxCode(request.getInternalBoxCode());
        if (Objects.isNull(internalBox)) {
            throw new BusinessServiceException("内部箱不存在!");
        }
        if (!Lists.newArrayList(StockInternalBoxTypeEnum.QA_BOX.name(), StockInternalBoxTypeEnum.RECEIVE_BOX.name()).contains(internalBox.getInternalBoxType())
                || !StockInternalBoxMaterialSizeEnum.LOOP_BOX.name().equals(internalBox.getInternalBoxMaterialSize())) {
            throw new BusinessServiceException("内部箱类型或规则错误,只支持质检箱和收货箱且是循环箱!");
        }
        if (!internalBox.getSpaceId().equals(positionInfo.getSpaceId())) {
            throw new BusinessServiceException("内部箱与目标库位对应仓库不一致请检查!");
        }
        request.setInternalBoxType(internalBox.getInternalBoxType());
    }

    /**
     * 校验数据
     *
     * @param detail
     * @param errorList
     * @return
     */
    public boolean importValidData(BdStockinAutoShelveRuleImportRequest detail, List<BdStockinAutoShelveRuleImportRequest> errorList, List<String> existList) {
        try {
            if (existList.contains(detail.getInternalBoxCode())) {
                detail.setErrorMsg("当前规则已在文件中存在!");
                errorList.add(detail);
                return false;
            }
            LambdaQueryWrapper<BdStockinAutoShelveRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BdStockinAutoShelveRuleEntity::getInternalBoxCode, detail.getInternalBoxCode());
            if (this.count(queryWrapper) > 0) {
                detail.setErrorMsg("该内部箱规则已存在!");
                errorList.add(detail);
                return false;
            }
            BdPositionEntity positionInfo = positionService.getByPositionCode(detail.getPositionCode());
            if (Objects.isNull(positionInfo)) {
                detail.setErrorMsg("查询不到对应的库位信息请检查!");
                errorList.add(detail);
                return false;
            }
            if (!positionInfo.getSpaceName().equals(detail.getSpaceName())) {
                detail.setErrorMsg("仓库和库位信息不一致请检查!");
                errorList.add(detail);
                return false;
            }
            if (!Lists.newArrayList(BdPositionTypeEnum.STOCK_POSITION.name(), BdPositionTypeEnum.CROSS_POSITION.name(), BdPositionTypeEnum.ACTIVITY_POSITION.name()).contains(positionInfo.getPositionType())) {
                detail.setErrorMsg("目标库位仅支持存储库位、活动库位、越库库位请检查!");
                errorList.add(detail);
                return false;
            }
            StockInternalBoxEntity internalBox = internalBoxService.findByInternalBoxCode(detail.getInternalBoxCode());
            if (Objects.isNull(internalBox)) {
                detail.setErrorMsg("内部箱不存在!");
                errorList.add(detail);
                return false;
            }
            if (!Lists.newArrayList(StockInternalBoxTypeEnum.QA_BOX.name(), StockInternalBoxTypeEnum.RECEIVE_BOX.name()).contains(internalBox.getInternalBoxType())
                    || !StockInternalBoxMaterialSizeEnum.LOOP_BOX.name().equals(internalBox.getInternalBoxMaterialSize())) {
                detail.setErrorMsg("内部箱类型或规则错误,只支持质检箱和收货箱且是循环箱!");
                errorList.add(detail);
                return false;
            }
            if (!internalBox.getSpaceId().equals(positionInfo.getSpaceId())) {
                detail.setErrorMsg("内部箱与目标库位对应仓库不一致请检查!");
                errorList.add(detail);
                return false;
            }
            detail.setInternalBoxType(internalBox.getInternalBoxType());
            detail.setSpaceId(positionInfo.getSpaceId());
        } catch (Exception e) {
            detail.setErrorMsg(e.getMessage());
            errorList.add(detail);
            return false;
        }
        existList.add(detail.getInternalBoxCode());
        return true;
    }
} 