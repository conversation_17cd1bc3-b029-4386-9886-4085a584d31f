package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.pms.dto.response.BaseStringResponse;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.domain.stockout.PrintSkuDTO;
import com.nsy.api.wms.domain.stockout.ShipmentBoxSkuInfo;
import com.nsy.api.wms.domain.stockout.ShipmentSkuInfo;
import com.nsy.api.wms.domain.stockout.ShipperAndReceiverDTO;
import com.nsy.api.wms.domain.stockout.StockoutOrderFtlDTO;
import com.nsy.api.wms.domain.stockout.StockoutOrderSkuInfo;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentPrintOrderInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentPrintShipmentList;
import com.nsy.api.wms.domain.stockout.YuZhenPrintDTO;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.bd.NoListRequest;
import com.nsy.api.wms.request.stockout.AmazonOrderNoRequest;
import com.nsy.api.wms.request.stockout.FbaShipmentLabelRequest;
import com.nsy.api.wms.request.stockout.OrderShipmentRequest;
import com.nsy.api.wms.request.stockout.PrintFaireOrderNoRequest;
import com.nsy.api.wms.request.stockout.ScanPrintRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentPrintRequest;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.CNPrintResponse;
import com.nsy.api.wms.response.stockout.FbaPrintRequest;
import com.nsy.api.wms.response.stockout.StockoutOrderLabelResponse;
import com.nsy.wms.business.manage.amazon.AmazonApiService;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLabelEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuExtendItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipperInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;
import com.nsy.wms.utils.FreeMarkerTemplateUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import com.nsy.wms.utils.encryption.AesEncryptUtil;
import freemarker.template.Template;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 装箱清单-打印
 */
@Service
public class StockoutShipmentPrintService {
    @Autowired
    StockoutShipmentMapper shipmentMapper;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    PrintTemplateService printTemplateService;
    @Autowired
    StockoutShipmentItemService itemService;
    @Autowired
    StockoutReceiverInfoService receiverInfoService;
    @Autowired
    StockoutOrderService orderService;
    @Autowired
    BdSpaceService spaceService;
    @Autowired
    StockoutOrderItemService orderItemService;
    @Autowired
    StockoutOrderTemuExtendItemService temuExtendItemService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutOrderLabelService labelService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    AmazonApiService amazonApiService;
    @Autowired
    StockoutShipmentAmazonRelationService amazonRelationService;
    @Autowired
    StockoutShipperInfoService shipperInfoService;
    @Autowired
    StockoutOrderScanShipmentDetailService shipmentDetailService;
    @Autowired
    StockoutOrderPrintService orderPrintService;
    @Autowired
    private TmsApiService tmsApiService;
    @Autowired
    StockoutTransparencyCodeService transparencyCodeService;
    @Resource
    private LoginInfoService loginInfoService;
    @Autowired
    private StockoutOrderTikTokExtendService stockoutOrderTikTokExtendService;

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutShipmentPrintService.class);

    /**
     * 打印装箱
     *
     * <AUTHOR>
     * 2022-01-05
     */
    public PrintListResponse printShipmentList(StockoutShipmentPrintRequest request) {
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.SHIPMENT_LIST.getTemplateName());
        List<ShipmentSkuInfo> skuInfoList = shipmentMapper.getPrintShipmentList(request.getIdList());
        Map<String, List<ShipmentSkuInfo>> map = skuInfoList.stream().collect(Collectors.groupingBy(ShipmentSkuInfo::getOrderNo));
        List<String> htmlList = new ArrayList<>(16);
        Map<String, Object> dataMap = new HashMap<>(16);
        for (Map.Entry<String, List<ShipmentSkuInfo>> entry : map.entrySet()) {
            StockoutShipmentPrintShipmentList shipmentList = new StockoutShipmentPrintShipmentList();
            Set<Integer> boxSet = new HashSet<>(16);
            for (ShipmentSkuInfo skuInfo : entry.getValue()) {
                if (skuInfo.getBoxIndex() == null) {
                    continue;
                }
                if (boxSet.contains(skuInfo.getBoxIndex())) {
                    skuInfo.setBoxIndex(null);
                } else {
                    boxSet.add(skuInfo.getBoxIndex());
                }
            }
            shipmentList.setOrderNo(entry.getKey());
            shipmentList.setSkuInfoList(entry.getValue());
            shipmentList.setSkuQty(entry.getValue().stream().map(ShipmentSkuInfo::getSku).distinct().count());
            shipmentList.setDescription(entry.getValue().get(0).getRemark());
            shipmentList.setLogisticsCompany(entry.getValue().get(0).getLogisticsCompany());
            shipmentList.setTotalQty(entry.getValue().stream().mapToInt(ShipmentSkuInfo::getQty).sum());
            shipmentList.setPrintDate(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            shipmentList.setPlatformReferenceNo(entry.getValue().get(0).getPlatformReferenceNo());
            if (!Objects.isNull(entry.getValue().get(0).getReceiverInfo())) {
                String receiverInfoOriginalText = Objects.requireNonNull(AesEncryptUtil.desEncrypt(entry.getValue().get(0).getReceiverInfo())).trim();
                StockoutReceiverInfo receiverInfo = JsonMapper.fromJson(receiverInfoOriginalText, StockoutReceiverInfo.class);
                if (!Objects.isNull(receiverInfo)) {
                    shipmentList.setTo(receiverInfo.getReceiverName());
                    shipmentList.setReceiverZip(receiverInfo.getReceiverZip());
                    shipmentList.setReceiverMobile(receiverInfo.getReceiverMobile());
                    shipmentList.setReceiverAddress(String.format("%s %s %s %s %s", receiverInfo.getReceiverAddress(), receiverInfo.getReceiverDistrict(),
                            receiverInfo.getReceiverCity(), receiverInfo.getReceiverState(), receiverInfo.getReceiverCountry()));
                }
            }
            dataMap.put("data", shipmentList);
            Template template = FreeMarkerTemplateUtils.getTemplate(request.getEnFlag() ? "ShipmentEnList.ftl" : "ShipmentList.ftl");
            String html = FreeMarkerTemplateUtils.renderTemplate(template, dataMap);
            htmlList.add(html);
        }
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(htmlList);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    /**
     * 打印装箱-订单
     *
     * <AUTHOR>
     * 2022-01-05
     */
    public PrintListResponse printOrder(NoListRequest request) {
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.SHIPMENT_ORDER.getTemplateName());
        List<ShipmentSkuInfo> skuInfoList = shipmentMapper.getPrintShipmentOrder(request.getNoList());
        Map<String, List<ShipmentSkuInfo>> map = skuInfoList.stream().collect(Collectors.groupingBy(ShipmentSkuInfo::getStockoutOrderNo));
        List<String> htmlList = new ArrayList<>(16);
        Map<String, Object> dataMap = new HashMap<>(16);
        for (Map.Entry<String, List<ShipmentSkuInfo>> entry : map.entrySet()) {
            StockoutShipmentPrintOrderInfo orderInfo = new StockoutShipmentPrintOrderInfo();
            orderInfo.setStockoutOrderNo(entry.getKey());
            orderInfo.setSkuQty(entry.getValue().size());
            orderInfo.setDescription(entry.getValue().get(0).getRemark());
            orderInfo.setTotalQty(entry.getValue().stream().mapToInt(ShipmentSkuInfo::getQty).sum());
            orderInfo.setPrintDate(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            if (!Objects.isNull(entry.getValue().get(0).getReceiverInfo())) {
                String receiverInfoOriginalText = Objects.requireNonNull(AesEncryptUtil.desEncrypt(entry.getValue().get(0).getReceiverInfo())).trim();
                StockoutReceiverInfo receiverInfo = JsonMapper.fromJson(receiverInfoOriginalText, StockoutReceiverInfo.class);
                if (!Objects.isNull(receiverInfo)) {
                    orderInfo.setTo(receiverInfo.getReceiverName());
                    orderInfo.setReceiverZip(receiverInfo.getReceiverZip());
                    orderInfo.setReceiverMobile(receiverInfo.getReceiverMobile());
                    orderInfo.setReceiverAddress(String.format("%s %s %s %s %s", receiverInfo.getReceiverAddress(), receiverInfo.getReceiverDistrict(),
                            receiverInfo.getReceiverCity(), receiverInfo.getReceiverState(), receiverInfo.getReceiverCountry()));
                }
            }
            List<ShipmentBoxSkuInfo> boxSkuInfoList = new ArrayList<>(16);
            Map<Integer, List<ShipmentSkuInfo>> boxMap = entry.getValue().stream().collect(Collectors.groupingBy(ShipmentSkuInfo::getBoxIndex));
            for (Map.Entry<Integer, List<ShipmentSkuInfo>> boxEntry : boxMap.entrySet()) {
                ShipmentBoxSkuInfo boxSkuInfo = new ShipmentBoxSkuInfo();
                boxSkuInfo.setBoxIndex(boxEntry.getKey());
                boxSkuInfo.setSkuInfoList(boxEntry.getValue());
                boxSkuInfo.setTotalQty(boxEntry.getValue().stream().mapToInt(ShipmentSkuInfo::getQty).sum());
                boxSkuInfoList.add(boxSkuInfo);
            }
            orderInfo.setBoxSkuInfoList(boxSkuInfoList.stream().sorted(Comparator.comparing(ShipmentBoxSkuInfo::getBoxIndex)).collect(Collectors.toList()));
            dataMap.put("data", orderInfo);
            Template template = FreeMarkerTemplateUtils.getTemplate("ShipmentOrder.ftl");
            String html = FreeMarkerTemplateUtils.renderTemplate(template, dataMap);
            htmlList.add(html);
        }
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(htmlList);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    /**
     * 打印装箱-出库单
     *
     * <AUTHOR>
     * 2022-01-05
     */
    public PrintListResponse printStockoutOrderByShipment(OrderShipmentRequest request) {
        PrintTemplateEntity templateEntity = printTemplateService.getByName(request.getTemplateName());
        LambdaQueryWrapper<StockoutShipmentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentEntity::getShipmentBoxCode, request.getNoList());
        List<Integer> shipmentIds = shipmentService.list(wrapper).stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shipmentIds))
            throw new BusinessServiceException("无法找到装箱明细信息，请核对后输入");
        List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentIdList(shipmentIds);
        // 一个订单一个打印页面
        Map<String, List<StockoutShipmentItemEntity>> orders = itemList.stream()
                .filter(item -> item.getOrderNo() != null && item.getStockoutOrderNo() != null).collect(Collectors.groupingBy(StockoutShipmentItemEntity::getOrderNo));
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        orders.forEach((k, v) -> {
            Map<String, Object> map = new HashMap<>(32);
            List<StockoutOrderSkuInfo> skuInfoList = new ArrayList<>();
            StockoutOrderEntity stockoutOrder = orderService.getByStockoutOrderNo(v.get(0).getStockoutOrderNo());
            StockoutReceiverInfo reveiveInfo = receiverInfoService.getReceiveInfoByStockoutOrderId(stockoutOrder.getStockoutOrderId());
            // 一个订单对应多个出库单
            Map<String, StockoutOrderSkuInfo> skuInfoMap = new HashMap<>();
            v.forEach(iiv -> buildSkuInfoMap(skuInfoMap, iiv));
            StockoutOrderFtlDTO dto = new StockoutOrderFtlDTO();
            dto.setOrderNo(k);
            StockoutShipmentEntity shipment = shipmentService.getById(v.get(0).getShipmentId());
            if (shipment.getShipmentDate() != null)
                dto.setShipDate(DateFormatUtils.format(shipment.getShipmentDate(), "yyyy年MM月dd日"));
            dto.setReceiver(reveiveInfo.getReceiverName());
            dto.setSpaceMemo(orderPrintService.getOrderSpaceMemo(stockoutOrder.getStockoutOrderNo()));
            dto.setPhoneNumber(reveiveInfo.getReceiverMobile() == null ? reveiveInfo.getReceiverPhone() : reveiveInfo.getReceiverMobile());
            dto.setStoreName(stockoutOrder.getStoreName());
            dto.setTotalCount(0);
            skuInfoMap.forEach((ik, iv) -> {
                iv.setIndex(skuInfoList.size() + 1);
                skuInfoList.add(iv);
                dto.setTotalCount(dto.getTotalCount() + iv.getQty());
            });
            dto.setSkuInfoList(skuInfoList);
            map.put("data", dto);
            Template template;
            if (request.getTemplateName().equals(PrintTemplateNameEnum.STOCKOUT_DELIVERY_SIGNATURE_FORM_SMALL.getTemplateName())) {
                template = FreeMarkerTemplateUtils.getTemplate("StockoutOrderShipmentPage.ftl");
            } else {
                template = FreeMarkerTemplateUtils.getTemplate("StockoutOrderPage.ftl");
            }
            String html = FreeMarkerTemplateUtils.renderTemplate(template, map);
            response.getHtmlList().add(html);
        });
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    private void buildSkuInfoMap(Map<String, StockoutOrderSkuInfo> skuInfoMap, StockoutShipmentItemEntity iiv) {
        StockoutOrderItemEntity itemEntity = orderItemService.getById(iiv.getStockoutOrderItemId());
        StockoutOrderSkuInfo skuInfo = new StockoutOrderSkuInfo();
        skuInfo.setSku(itemEntity.getSku());
        skuInfo.setQty(iiv.getQty());
        skuInfo.setPositionCode(itemEntity.getPositionCode());
        ProductSpecInfoEntity spec = productSpecInfoService.findTopBySku(itemEntity.getSku());
        skuInfo.setColor(spec.getColor());
        skuInfo.setSize(spec.getSize());
        skuInfoMap.merge(skuInfo.getSku() + skuInfo.getPositionCode(), skuInfo, (pre, now) -> {
            StockoutOrderSkuInfo orderSkuInfo = new StockoutOrderSkuInfo();
            BeanUtils.copyProperties(pre, orderSkuInfo);
            orderSkuInfo.setQty(pre.getQty() + now.getQty());
            return orderSkuInfo;
        });
    }

    public StockoutOrderLabelResponse printShipmentLabel(NoListRequest request) {
        LambdaQueryWrapper<StockoutShipmentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentEntity::getShipmentBoxCode, request.getNoList());
        List<StockoutShipmentEntity> list = shipmentService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("未找到对应的装箱清单");
        }
        List<String> collect = list.stream().map(StockoutShipmentEntity::getLogisticsNo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            throw new BusinessServiceException("装箱清单未获取到物流单号，请先获取物流单号");
        }
        labelService.updatePrintStatus(collect.get(0));
        return labelService.getLogisticsDetail(collect.get(0));
    }

    /**
     * 装箱详情-打印业务条码
     *
     * <AUTHOR>
     * 2022-01-05
     */
    public PrintListResponse printShipmentFba(FbaPrintRequest request) {
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        PrintTemplateEntity template = printService.getByName(request.getTemplateName());
        if (CollectionUtils.isEmpty(request.getSkuList())) {
            for (String boxCode : request.getShipmentBoxCodeList()) {
                if (StrUtil.contains(request.getTemplateName(), "透明计划T标")) {
                    shipmentDetailService.printByShipment(boxCode, template, response, false);
                } else {
                    printBarcodeByShipment(boxCode, response, template, request.getPrintTransferSku());
                }
            }
        } else {
            // 根据箱内sku打印
            StockoutShipmentEntity shipment = shipmentService.getEntityByShipmentBoxCode(request.getShipmentBoxCodeList().get(0));
            if (shipment == null)
                throw new BusinessServiceException(request.getShipmentBoxCodeList().get(0) + "此箱码找不到箱子，请核对");
            List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentId(shipment.getShipmentId());
            Map<String, StockoutShipmentItemEntity> collect = itemList.stream().collect(Collectors.toMap(StockoutShipmentItemEntity::getSku, Function.identity(), (key1, key2) -> key2));
            request.getSkuList().forEach(skuDTO -> {
                StockoutShipmentItemEntity itemEntity = collect.get(skuDTO.getSku());
                if (itemEntity == null)
                    throw new BusinessServiceException(skuDTO.getSku() + "此sku未找到装箱明细");
                StockoutOrderItemEntity itemById = orderItemService.getById(itemEntity.getStockoutOrderItemId());
                if (!StringUtils.hasText(itemById.getSellerBarcode()) && PrintTemplateNameEnum.SHIPMENT_FBA_BARCODE.getTemplateName().equalsIgnoreCase(request.getTemplateName())) {
                    response.setErrorPrintMsg(response.getErrorPrintMsg() + itemById.getSku() + "此sku未导入业务条码;");
                } else {
                    String transfer = StockoutBuilding.buildFbaHtml(itemById, template);
                    for (int i = 0; i < skuDTO.getQty(); i++) {
                        response.getHtmlList().add(transfer);
                    }
                }
            });
        }
        List<String> list = PrintTransferUtils.doubleTransfer(request.getSinglePrint(), response.getHtmlList(), template);
        response.setHtmlList(list);
        response.setSpec(template.getSpec());
        response.setTemplateName(template.getName());
        return response;
    }

    // 按箱打印
    public void printBarcodeByShipment(String boxCode, PrintListResponse response, PrintTemplateEntity template, Boolean printTransferSku) {
        StockoutShipmentEntity shipment = shipmentService.getEntityByShipmentBoxCode(boxCode);
        if (shipment == null)
            throw new BusinessServiceException(boxCode + "此箱码找不到箱子，请核对");
        List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentIdOrderByCreateTime(shipment.getShipmentId());
        itemList.forEach(item -> {
            StockoutOrderItemEntity itemById = orderItemService.getById(item.getStockoutOrderItemId());
            if (!StringUtils.hasText(itemById.getSellerBarcode()) && !PrintTemplateNameEnum.DIAN_CANG_BARCODE.getTemplateName().equalsIgnoreCase(template.getName())
                    && !PrintTemplateNameEnum.OVERSEA_SPACE_BARCODE.getTemplateName().equalsIgnoreCase(template.getName())) {
                response.setErrorPrintMsg(response.getErrorPrintMsg() + itemById.getSku() + "此sku未导入业务条码;");
            } else {
                String transfer = StockoutBuilding.buildFbaHtml(itemById, template);
                for (int i = 0; i < item.getQty(); i++) {
                    response.getHtmlList().add(transfer);
                }
            }
        });
        shipmentDetailService.printTransferSku(response, printTransferSku, itemList);
    }

    /**
     * 打印钰真条码
     *
     * <AUTHOR>
     * 2022-01-05
     */
    public PrintListResponse printYuZhen(IdListRequest request) {
        PrintTemplateEntity template = printService.getByName(PrintTemplateNameEnum.YUZHEN_BARCODE.getTemplateName());
        List<StockoutShipmentEntity> shipmentEntityList = shipmentService.listByIds(request.getIdList());
        if (CollectionUtils.isEmpty(shipmentEntityList)) {
            throw new BusinessServiceException("请选择正确的箱子进行打印");
        }
        List<StockoutShipmentItemEntity> shipmentItemList = itemService.findByShipmentIdsOrderByCreateTime(request.getIdList());
        if (CollectionUtils.isEmpty(shipmentItemList)) {
            throw new BusinessServiceException("箱子没有sku，无法打印");
        }
        List<String> htmlList = new ArrayList<>();
        shipmentItemList.forEach(item -> {
            StockoutOrderItemEntity orderItem = orderItemService.getById(item.getStockoutOrderItemId());
            if (!StringUtils.hasText(orderItem.getSellerBarcode())) {
                throw new BusinessServiceException(orderItem.getSku() + "业务员未导入业务条形码，请先导入");
            }
            YuZhenPrintDTO dto = new YuZhenPrintDTO();
            // yyMMdd格式
            if (orderItem.getOrderPayTime() == null) {
                throw new BusinessServiceException(orderItem.getOrderNo() + "订单付款时间不能为空");
            }
            dto.setOrderPayTime(DateFormatUtils.format(orderItem.getOrderPayTime(), "yyMMdd"));
            ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopBySku(item.getSku());
            dto.setSkc(specInfoEntity == null ? null : specInfoEntity.getSkc());
            dto.setSellerBarcode(orderItem.getSellerBarcode());
            if (orderItem.getSellerBarcode().contains("-")) {
                int i = orderItem.getSellerBarcode().lastIndexOf('-');
                dto.setSellerBarcodeBefore(orderItem.getSellerBarcode().substring(0, i));
                dto.setSellerBarcodeSize(orderItem.getSellerBarcode().substring(i + 1));
            } else {
                dto.setSellerBarcodeSize(orderItem.getSellerBarcode());
                dto.setSellerBarcodeBefore(orderItem.getSellerBarcode());
            }
            String transfer = PrintTransferUtils.transfer(template.getContent(), dto);
            for (int i = 0; i < item.getQty(); i++) {
                htmlList.add(transfer);
            }
        });
        List<String> list = PrintTransferUtils.doubleTransfer(Boolean.FALSE, htmlList, template);
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(list);
        response.setSpec(template.getSpec());
        response.setTemplateName(template.getName());
        return response;
    }

    /**
     * 通过物流单号获取面单路径
     *
     * @param request
     * @return
     */
    public Map<String, List<String>> getLogisticsLabelUrl(NoListRequest request) {
        Map<String, List<String>> map = new HashMap<>();
        List<StockoutOrderLabelEntity> labelEntityList = labelService.listByLogisticsNo(request.getNoList());
        labelService.updatePrintStatus(request.getNoList());
        List<String> cnPrintList = labelEntityList.stream().filter(i -> StringUtils.hasText(i.getPrintContent())).map(label -> {
            CNPrintResponse cnPrintResponse = new CNPrintResponse();
            cnPrintResponse.setLogisticsNo(label.getLogisticsNo());
            cnPrintResponse.setPrintContent(label.getPrintContent());
            return JsonMapper.toJson(cnPrintResponse);
        }).collect(Collectors.toList());
        List<String> allLabelUrlList = labelEntityList.stream().map(StockoutOrderLabelEntity::getLabelUrl).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        List<String> imageLabelList = allLabelUrlList.stream().filter(t -> t.contains(".png") || t.contains(".jpeg") || t.contains(".jpg")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(imageLabelList)) {
            map.put("image", imageLabelList);
        }
        List<String> pdfLabelList = allLabelUrlList.stream().filter(t -> t.contains(".pdf")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pdfLabelList)) {
            map.put("pdf", pdfLabelList);
        }
        if (!CollectionUtils.isEmpty(cnPrintList)) {
            // 菜鸟打印
            map.put("cn", cnPrintList);
        }
        return map;
    }

    /**
     * 根据物流公司打印 外部面单
     *
     * <AUTHOR>
     * 2022-01-05
     */
    public PrintListResponse printOutLabelByLogisticsCompany(IdListRequest request, String logisticsCompany) {
        List<StockoutShipmentEntity> shipmentList = shipmentService.listByIds(request.getIdList());
        if (CollectionUtils.isEmpty(shipmentList)) {
            throw new BusinessServiceException("未找到任何的装箱数据，请核对");
        }
        List<String> result = new ArrayList<>();
        String date = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        shipmentList.forEach(item -> {
            if (!StringUtils.hasText(item.getLogisticsCompany())) {
                throw new BusinessServiceException(item.getShipmentBoxCode() + "此装箱清单没有物流公司，请核对");
            }
            PrintTemplateEntity template = printService.getByName(StringUtils.hasText(logisticsCompany) ? logisticsCompany : item.getLogisticsCompany());
            List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentId(item.getShipmentId());
            if (CollectionUtils.isEmpty(itemList)) {
                throw new BusinessServiceException(item.getShipmentBoxCode() + "此装箱没有装箱数据，请核对");
            }
            StockoutOrderEntity order = orderService.getByStockoutOrderNo(itemList.get(0).getStockoutOrderNo());
            // 寄件人 和 发件人的信息
            StockoutReceiverInfo receiverInfo = receiverInfoService.getReceiveInfoByStockoutOrderId(order.getStockoutOrderId());
            StockoutShipperInfoEntity shipperInfo = shipperInfoService.getByStockoutOrderId(order.getStockoutOrderId());
            if (receiverInfo == null || shipperInfo == null) {
                throw new BusinessServiceException("收货人和发件人不能为空");
            }
            ShipperAndReceiverDTO dto = new ShipperAndReceiverDTO();
            String tid = itemList.stream().map(StockoutShipmentItemEntity::getOrderNo).distinct().collect(Collectors.joining(","));
            dto.setTid(tid);
            dto.setDate(date);
            BeanUtils.copyProperties(receiverInfo, dto);
            BeanUtils.copyProperties(shipperInfo, dto);
            result.add(PrintTransferUtils.transfer(template.getContent(), dto));
        });
        PrintTemplateEntity template = printService.getByName(StringUtils.hasText(logisticsCompany) ? logisticsCompany : shipmentList.get(0).getLogisticsCompany());
        PrintListResponse response = new PrintListResponse();
        response.setTemplateName(template.getName());
        response.setSpec(template.getSpec());
        response.setHtmlList(result);
        return response;
    }

    public PrintListResponse print6040Barcode(List<Integer> shipmentIdList) {
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.SHIPMENT_60_40_BARCODE.getTemplateName());
        List<StockoutShipmentItemEntity> allShipmentItemEntityList = itemService.findByShipmentIdList(shipmentIdList);
        if (CollectionUtils.isEmpty(allShipmentItemEntityList)) throw new BusinessServiceException("装箱记录不存在");
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        List<StockoutShipmentEntity> stockoutShipmentEntities = shipmentService.listByIds(shipmentIdList);
        stockoutShipmentEntities.forEach(shipment -> shipmentDetailService.printByShipment(shipment.getShipmentBoxCode(), templateEntity, response, false));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }


    public PrintListResponse print8040Barcode(List<String> boxCodes) {
        PrintListResponse resp = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.SHIPMENT_80_40_BARCODE.getTemplateName());
        List<String> htmlList = new ArrayList<>();
        boxCodes.forEach(boxCode -> {
            Map<String, String> map = new HashMap<>();
            map.put("boxCode", boxCode);
            htmlList.add(PrintTransferUtils.transfer(templateEntity.getContent(), map));
        });
        resp.setHtmlList(htmlList);
        resp.setSpec(templateEntity.getSpec());
        resp.setTemplateName(templateEntity.getName());
        return resp;
    }

    public PrintListResponse printCustomerCode(ScanPrintRequest request) {
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        response.setHtmlList(new ArrayList<>());
        if (CollectionUtils.isEmpty(request.getPrintSkuDTO())) {
            shipmentDetailService.printByShipment(request.getShipmentBoxCode(), templateEntity, response, request.getPrintTransferSku());
        } else {
            printBySkuList(request, templateEntity, response);
        }
        response.setHtmlList(PrintTransferUtils.doubleTransfer(request.getSinglePrint(), response.getHtmlList(), templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    /**
     * 打印客户定制条码  如（FBA条码、内贸条码、拼多多专属条码）
     *
     * <AUTHOR>
     * 2023-03-13
     */
    public PrintListResponse printCustomer(ScanPrintRequest request) {
        if (Objects.equals("FBA条码(新)", request.getTemplateName()) || !StringUtils.hasText(request.getTemplateName())) {
            request.setTemplateName(PrintTemplateNameEnum.PRODUCT_CUSTOMER_SPEC_DOUBLE_BARCODE.getTemplateName());
        }
        LOGGER.info("复核界面打印业务条码: {}", request.getShipmentBoxCode());
        PrintListResponse response = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        response.setHtmlList(new ArrayList<>());
        if ("Temu条码".equalsIgnoreCase(request.getTemplateName())) {
            // 目前条码种类较少，先用if，后续用case区分
            printCustomerTemu(request, templateEntity, response);
            return response;
        }
        if ("TIKTOK条码".equalsIgnoreCase(request.getTemplateName())) {
            if (StringUtils.hasText(request.getShipmentBoxCode())) {
                return stockoutOrderTikTokExtendService.printLabelByShipment(request.getShipmentBoxCode());
            } else if (StringUtils.hasText(request.getStockoutOrderNo())) {
                return stockoutOrderTikTokExtendService.printLabelByStockoutOrderNo(request.getStockoutOrderNo());
            }
        }
        if (CollectionUtils.isEmpty(request.getPrintSkuDTO())) {
            LOGGER.info("{}打印条码-根据sku扫描顺序进行打印{}", loginInfoService.getName(), request.getShipmentBoxCode());
            shipmentDetailService.printByShipment(request.getShipmentBoxCode(), templateEntity, response, request.getPrintTransferSku());
        } else {
            LOGGER.info("复核-选择sku进行打印: {}", JsonMapper.toJson(request.getPrintSkuDTO()));
            printBySkuList(request, templateEntity, response);
        }
        response.setHtmlList(PrintTransferUtils.doubleTransfer(request.getSinglePrint(), response.getHtmlList(), templateEntity));
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }


    // Temu条码
    private void printCustomerTemu(ScanPrintRequest request, PrintTemplateEntity templateEntity, PrintListResponse response) {
        // 打印箱子内容
        if (CollectionUtils.isEmpty(request.getPrintSkuDTO())) {
            PrintTemplateEntity temp = printService.getByName("Temu条码(分隔)");
            StockoutShipmentEntity shipment = shipmentService.getEntityByShipmentBoxCode(request.getShipmentBoxCode());
            if (shipment == null)
                throw new BusinessServiceException(request.getShipmentBoxCode() + "此箱码找不到箱子，请核对");
            List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentIdOrderByCreateTime(shipment.getShipmentId());
            Long productSkuId = null;
            for (StockoutShipmentItemEntity item : itemList) {
                StockoutOrderTemuExtendItemEntity temuItem = temuExtendItemService.getOneByStockoutOrderItemId(item.getStockoutOrderItemId());
                StockoutOrderItemEntity orderItem = orderItemService.getById(item.getStockoutOrderItemId());
                if (temuItem == null || orderItem == null)
                    throw new BusinessServiceException(item.getSku() + "未找到TEMU明细或出库单明细，请核对");
                if (productSkuId != null && !productSkuId.equals(temuItem.getProductSkuId())) {
                    response.getHtmlList().add(temp.getContent());
                }
                productSkuId = temuItem.getProductSkuId();
                String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), temuItem);
                Map<String, String> map = new HashMap<>();
                map.put("orderNo", item.getOrderNo());
                map.put("sellerSku", orderItem.getSellerSku());
                String result = PrintTransferUtils.transfer(transfer, map);
                for (int i = 0; i < item.getQty(); i++)
                    response.getHtmlList().add(result);
            }
        } else {
            StockoutOrderEntity byStockoutOrderNo = orderService.getByStockoutOrderNo(request.getStockoutOrderNo());
            List<StockoutOrderItemEntity> itemEntities = orderItemService.listByStockoutOrderId(byStockoutOrderNo.getStockoutOrderId());
            Map<String, Integer> collect = request.getPrintSkuDTO().stream().collect(Collectors.toMap(PrintSkuDTO::getSku, PrintSkuDTO::getQty, (v1, v2) -> v1));
            itemEntities.forEach(item -> {
                Integer qty = collect.get(item.getSku());
                printBySku(templateEntity, response, collect, item, qty);
            });
        }
        response.setTemplateName(templateEntity.getName());
        response.setSpec(templateEntity.getSpec());
    }

    // 按照勾选的打条码
    private void printBySku(PrintTemplateEntity templateEntity, PrintListResponse response, Map<String, Integer> collect, StockoutOrderItemEntity item, Integer qty) {
        if (qty != null) {
            StockoutOrderTemuExtendItemEntity temuItem = temuExtendItemService.getOneByStockoutOrderItemId(item.getStockoutOrderItemId());
            if (temuItem == null)
                throw new BusinessServiceException("未找到Temu明细");
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), temuItem);
            Map<String, String> map = new HashMap<>();
            map.put("orderNo", item.getOrderNo());
            map.put("sellerSku", item.getSellerSku());
            String result = PrintTransferUtils.transfer(transfer, map);
            for (int i = 0; i < qty; i++)
                response.getHtmlList().add(result);
            collect.remove(item.getSku());
        }
    }

    private void printBySkuList(ScanPrintRequest request, PrintTemplateEntity templateEntity, PrintListResponse response) {
        for (PrintSkuDTO item : request.getPrintSkuDTO()) {
            StockoutOrderEntity order = orderService.getByStockoutOrderNo(request.getStockoutOrderNo());
            List<StockoutOrderItemEntity> sellerSku = orderItemService.getByStockoutOrderIdAndSku(order.getStockoutOrderId(), item.getSku(), item.getStockOutOrderItemId());
            if (CollectionUtils.isEmpty(sellerSku)) {
                throw new BusinessServiceException(item.getSku() + "此款找不到出库单明细等信息");
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(sellerSku.get(0).getSellerBarcode()) && !PrintTemplateNameEnum.DIAN_CANG_BARCODE.getTemplateName().equalsIgnoreCase(templateEntity.getName())
                    && !PrintTemplateNameEnum.OVERSEA_SPACE_BARCODE.getTemplateName().equalsIgnoreCase(templateEntity.getName())) {
                response.setErrorPrintMsg(response.getErrorPrintMsg() + sellerSku.get(0).getSku() + "此sku未导入业务条码;");
                continue;
            }
            if (templateEntity.getName().contains("透明计划T标")) {
                List<String> list = transparencyCodeService.printByScanTaskOrderBy(new HashSet<>(8), null, sellerSku.get(0), item.getQty(), templateEntity);
                response.getHtmlList().addAll(list);
            } else {
                String fbaHtml = StockoutBuilding.buildFbaWithSupplierNameHtml(sellerSku.get(0), request.getSupplierName(), templateEntity);
                for (int i = 0; i < item.getQty(); i++) {
                    response.getHtmlList().add(fbaHtml);
                }
            }
        }
    }

    public PrintListResponse printGoodCangBarcode(ScanPrintRequest request) {
        PrintListResponse resp = new PrintListResponse();
        resp.setHtmlList(new ArrayList<>());
        if (CollectionUtils.isEmpty(request.getPrintSkuDTO())) {
            // 按箱打印
            StockoutShipmentEntity shipment = shipmentService.getEntityByShipmentBoxCode(request.getShipmentBoxCode());
            if (shipment == null)
                throw new BusinessServiceException(request.getShipmentBoxCode() + "此箱码找不到箱子，请核对");
            List<StockoutShipmentItemEntity> shipmentItems = itemService.findByShipmentId(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(shipmentItems))
                throw new BusinessServiceException("此箱子为空箱，请确认");
            List<String> collect = shipmentItems.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).collect(Collectors.toList());
            List<StockoutOrderEntity> orderEntityList = orderService.getByStockoutOrderNoList(collect);
            if (orderEntityList.stream().anyMatch(StockoutOrderEntity::getHasPack))
                throw new BusinessServiceException("请选择谷仓海外仓补货普通商品打印条码");
            BdSpaceEntity spaceEntity = spaceService.getSpaceByIdValid(orderEntityList.get(0).getDesSpaceId());
            for (StockoutShipmentItemEntity item : shipmentItems) {
                BaseStringResponse tmsResp = tmsApiService.printGoodCangBarcode(item.getSku(), spaceEntity.getSpaceName());
                if (!com.nsy.api.core.apicore.util.StringUtils.hasText(tmsResp.getValue())) {
                    resp.setErrorPrintMsg(resp.getErrorPrintMsg() + item.getSku() + "该sku无法打印;");
                    continue;
                }
                for (int i = 0; i < item.getQty(); i++) {
                    resp.getHtmlList().add(tmsResp.getValue());
                }
            }
        } else {
            String stockoutOrderNo = request.getStockoutOrderNo();
            if (StrUtil.isBlank(stockoutOrderNo)) {
                throw new BusinessServiceException("出库单必填！");
            }
            StockoutOrderEntity orderEntity = orderService.getByStockoutOrderNo(stockoutOrderNo);
            BdSpaceEntity spaceEntity = spaceService.getSpaceByIdValid(orderEntity.getSpaceId());
            for (PrintSkuDTO item : request.getPrintSkuDTO()) {
                BaseStringResponse tmsResp = tmsApiService.printGoodCangBarcode(item.getSku(), spaceEntity.getSpaceName());
                if (!com.nsy.api.core.apicore.util.StringUtils.hasText(tmsResp.getValue())) {
                    resp.setErrorPrintMsg(resp.getErrorPrintMsg() + item.getSku() + "该sku无法打印;");
                    continue;
                }
                for (int i = 0; i < item.getQty(); i++) {
                    resp.getHtmlList().add(tmsResp.getValue());
                }
            }
        }
        return resp;
    }

    public PrintListResponse amazonOrderNoPrint(AmazonOrderNoRequest request) {
        PrintListResponse resp = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.AMAZON_ORDER_NO.getTemplateName());
        String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), request);
        List<String> htmlList = new ArrayList<>();
        htmlList.add(transfer);
        resp.setHtmlList(htmlList);
        resp.setSpec(templateEntity.getSpec());
        resp.setTemplateName(templateEntity.getName());
        return resp;
    }

    public PrintListResponse printShipmentBoxCode(List<String> boxCodes) {
        PrintListResponse resp = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.SHIPMENT_BOX_CODE.getTemplateName());
        String location = templateEntity.getLocation();
        List<String> htmlList = new ArrayList<>();
        boxCodes.forEach(boxCode -> {
            Map<String, String> map = new HashMap<>();
            map.put("boxCode", boxCode);
            htmlList.add(PrintTransferUtils.transfer(templateEntity.getContent(), map));
            // 查询是否打印其他内容
            buildOverseaBoxCode(location, htmlList, boxCode);
        });
        resp.setHtmlList(htmlList);
        resp.setSpec(templateEntity.getSpec());
        resp.setTemplateName(templateEntity.getName());
        return resp;
    }

    // 打印万邑通海外仓子箱贴
    private void buildOverseaBoxCode(String location, List<String> htmlList, String boxCode) {
        if (!StrUtil.equalsIgnoreCase(location, LocationEnum.MISI.name())) {
            return;
        }
        PrintTemplateEntity boxTemp = printService.getByNameWithoutValid(PrintTemplateNameEnum.OVERSEA_BOX_CODE.getTemplateName());
        if (boxTemp == null) {
            return;
        }
        StockoutShipmentEntity shipmentEntity = shipmentService.findTopByShipmentBoxCode(boxCode);
        List<StockoutShipmentItemEntity> itemServiceByShipmentId = itemService.findByShipmentId(shipmentEntity.getShipmentId());
        if (CollectionUtils.isEmpty(itemServiceByShipmentId)) {
            return;
        }
        StockoutOrderEntity orderEntity = orderService.findByStockoutOrderNo(itemServiceByShipmentId.get(0).getStockoutOrderNo());
        if (!StockoutOrderTypeEnum.SPACE_TRANSFER_DELIVERY.name().equals(orderEntity.getStockoutType()))
            return;
        Integer desSpaceId = orderEntity.getDesSpaceId();
        if (desSpaceId == null) {
            return;
        }
        BdSpaceEntity space = spaceService.getById(desSpaceId);
        if (space == null) {
            return;
        }
        if (!StrUtil.startWithAnyIgnoreCase(space.getSpaceName(), SpaceAreaMapConstant.WmsSpace.WINIT_OVERSEA_SPACE)) {
            return;
        }
        List<String> skuList = itemServiceByShipmentId.stream().map(StockoutShipmentItemEntity::getSku).distinct().collect(Collectors.toList());
        if (skuList.size() > 1) {
            String[] childrenPackageLabel = tmsApiService.getChildrenPackageLabel(skuList.size(), space.getSpaceName());
            for (String barcode : childrenPackageLabel) {
                Map<String, String> mapBox = new HashMap<>();
                mapBox.put("boxCode", barcode);
                htmlList.add(PrintTransferUtils.transfer(boxTemp.getContent(), mapBox));
            }
        }
    }

    public PrintListResponse printOrderNo(PrintFaireOrderNoRequest request) {
        if (!StringUtils.hasText(request.getStockoutOrderNo())) {
            throw new BusinessServiceException("请选择正确的订单号!");
        }
        PrintListResponse resp = new PrintListResponse();
        PrintTemplateEntity templateEntity = printService.getByName(PrintTemplateNameEnum.ORDER_NO_BY_STOCKOUT_ORDER.getTemplateName());
        StockoutOrderEntity orderEntity = orderService.getByStockoutOrderNo(request.getStockoutOrderNo());
        List<StockoutOrderItemEntity> stockoutOrderItems = orderItemService.listByStockoutOrderId(orderEntity.getStockoutOrderId());
        List<String> collect = stockoutOrderItems.stream().map(StockoutOrderItemEntity::getOrderNo).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        List<String> htmlList = new ArrayList<>();
        String logisticsCompany = orderEntity.getLogisticsCompany();
        if (StringUtils.hasText(logisticsCompany) && logisticsCompany.contains("(")) {
            logisticsCompany = logisticsCompany.substring(0, logisticsCompany.indexOf('('));
        }
        for (String orderNo : collect) {
            Map<String, String> map = new HashMap<>();
            map.put("orderNo", orderNo);
            map.put("logisticsCompany", logisticsCompany);
            map.put("storeName", orderEntity.getStoreName());
            map.put("indexNo", StringUtils.hasText(request.getIndexNo()) ? String.format("%s号", request.getIndexNo()) : "");
            String transfer = PrintTransferUtils.transfer(templateEntity.getContent(), map);
            //打印几张则添加几次
            for (int i = 0; i < request.getPrintCount(); i++) {
                htmlList.add(transfer);
            }
        }
        resp.setHtmlList(htmlList);
        resp.setSpec(templateEntity.getSpec());
        resp.setTemplateName(templateEntity.getName());
        return resp;
    }

    public PrintListResponse printFbaLabel(FbaShipmentLabelRequest request) {
        List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = amazonRelationService.listByFbaShipmentId(Collections.singletonList(request.getShipmentId()));
        if (CollectionUtils.isEmpty(amazonRelationEntities)) {
            throw new BusinessServiceException("暂无数据打印");
        }
        StockoutShipmentEntity shipment = shipmentService.getByShipmentId(amazonRelationEntities.get(0).getShipmentId());
        List<String> fbaShipmentLabelBase64;
        if (StockoutOrderPlatformEnum.TIKTOK.getName().equalsIgnoreCase(shipment.getPlatformName())) {
            List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentId(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(itemList)) {
                throw new BusinessServiceException("暂无数据打印");
            }
            List<String> orderNoList = itemList.stream().map(StockoutShipmentItemEntity::getOrderNo).distinct().collect(Collectors.toList());
            List<StockoutShipmentItemEntity> orderEntityList = itemService.findByOrderNos(orderNoList);
            List<Integer> shipmentIdList = orderEntityList.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
            List<StockoutShipmentAmazonRelationEntity> printRelationList = amazonRelationService.listByShipmentId(shipmentIdList);
            if (CollectionUtils.isEmpty(printRelationList)) {
                throw new BusinessServiceException("暂无数据打印");
            }
            request.setTemplateName("FBT箱贴");
            fbaShipmentLabelBase64 = printRelationList.stream().map(StockoutShipmentAmazonRelationEntity::getLabelUrl).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        } else {
            fbaShipmentLabelBase64 = amazonApiService.getFbaShipmentLabelBase64(request);
        }
        if (CollectionUtils.isEmpty(fbaShipmentLabelBase64)) {
            throw new BusinessServiceException("暂无箱贴打印");
        }
        PrintListResponse resp = new PrintListResponse();
        List<String> htmlList = new ArrayList<>();
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        fbaShipmentLabelBase64.forEach(it -> {
            Map<String, Object> map = new HashMap<>();
            map.put("fbaShipmentLabelBase64", it);
            String transfer = PrintTransferUtils.dynamicHtml(templateEntity, map);
            htmlList.add(transfer);
        });
        resp.setHtmlList(htmlList);
        resp.setSpec(templateEntity.getSpec());
        resp.setTemplateName(templateEntity.getName());
        return resp;
    }

    public PrintListResponse printFbaLabelForGc(FbaShipmentLabelRequest request) {
        List<String> fbaShipmentLabelBase64 = amazonApiService.getFbaShipmentLabelBase64(request);
        if (CollectionUtils.isEmpty(fbaShipmentLabelBase64)) {
            throw new BusinessServiceException("暂无数据打印");
        }
        PrintListResponse resp = new PrintListResponse();
        List<String> htmlList = new ArrayList<>();
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        fbaShipmentLabelBase64.forEach(it -> {
            Map<String, Object> map = new HashMap<>();
            map.put("fbaShipmentLabelBase64", it);
            String transfer = PrintTransferUtils.dynamicHtml(templateEntity, map);
            htmlList.add(transfer);
        });
        resp.setHtmlList(htmlList);
        resp.setSpec(templateEntity.getSpec());
        resp.setTemplateName(templateEntity.getName());
        return resp;
    }

    public PrintListResponse printSingleShipmentFbaLabel(FbaShipmentLabelRequest request) {
        if (StrUtil.isBlank(request.getShipmentId())) {
            throw new BusinessServiceException("请选择正确ShipmentID");
        }
        List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = amazonRelationService.listByFbaShipmentId(Collections.singletonList(request.getShipmentId()));
        if (CollectionUtils.isEmpty(amazonRelationEntities)) {
            throw new BusinessServiceException("暂无数据打印");
        }
        request.setLabelCount(amazonRelationEntities.size());
        List<String> fbaShipmentLabelBase64 = amazonApiService.getSingleFbaShipmentLabelBase64(request);
        if (CollectionUtils.isEmpty(fbaShipmentLabelBase64)) {
            throw new BusinessServiceException("暂无数据打印");
        }
        PrintListResponse resp = new PrintListResponse();
        List<String> htmlList = new ArrayList<>();
        PrintTemplateEntity templateEntity = printService.getByName(request.getTemplateName());
        fbaShipmentLabelBase64.forEach(it -> {
            Map<String, Object> map = new HashMap<>();
            map.put("fbaShipmentLabelBase64", it);
            String transfer = PrintTransferUtils.dynamicHtml(templateEntity, map);
            htmlList.add(transfer);
        });
        resp.setHtmlList(htmlList);
        resp.setSpec(templateEntity.getSpec());
        resp.setTemplateName(templateEntity.getName());
        return resp;
    }

    public List<String> downloadFbaLabel(FbaShipmentLabelRequest request) {
        List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = amazonRelationService.listByFbaShipmentId(Collections.singletonList(request.getShipmentId()));
        if (CollectionUtils.isEmpty(amazonRelationEntities)) {
            throw new BusinessServiceException("暂无数据打印");
        }
        StockoutShipmentEntity shipment = shipmentService.getByShipmentId(amazonRelationEntities.get(0).getShipmentId());
        if (StockoutOrderPlatformEnum.TIKTOK.getName().equalsIgnoreCase(shipment.getPlatformName())) {
            List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentId(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(itemList)) {
                throw new BusinessServiceException("暂无数据打印");
            }
            List<String> orderNoList = itemList.stream().map(StockoutShipmentItemEntity::getOrderNo).distinct().collect(Collectors.toList());
            List<StockoutShipmentItemEntity> orderEntityList = itemService.findByOrderNos(orderNoList);
            List<Integer> shipmentIdList = orderEntityList.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
            List<StockoutShipmentAmazonRelationEntity> printRelationList = amazonRelationService.listByShipmentId(shipmentIdList);
            if (CollectionUtils.isEmpty(printRelationList)) {
                throw new BusinessServiceException("暂无数据打印");
            }
            return printRelationList.stream().map(StockoutShipmentAmazonRelationEntity::getLabelUrl).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        } else {
            throw new BusinessServiceException("暂不支持FBT以外的订单下载PDF");
        }
    }

    /**
     * 打印空加派箱唛
     * 根据shipmentId和boxTitle打印箱子标签，每个箱子打印两张
     *
     * @param shipmentIds 装箱ID列表
     * @param printNum    打印数量
     * @param boxTitle    箱子标题
     * @return 打印结果
     */
    public PrintListResponse printKjpBoxLabel(List<Integer> shipmentIds, Integer printNum, String boxTitle, Integer isShowOrder) {
        if (CollectionUtils.isEmpty(shipmentIds)) {
            throw new BusinessServiceException("请选择要打印的箱子");
        }
        // 获取打印模板
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.SHIPMENT_KJP_BOX.getTemplateName());
        // 获取装箱信息
        List<StockoutShipmentEntity> shipmentList = shipmentService.listByIds(shipmentIds);
        if (CollectionUtils.isEmpty(shipmentList)) {
            throw new BusinessServiceException("未找到装箱信息");
        }
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());

        // 遍历每个装箱，获取订单和收货人信息
        for (StockoutShipmentEntity shipment : shipmentList) {
            // 获取装箱明细
            List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentId(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            // 获取订单信息
            StockoutOrderEntity order = orderService.getByStockoutOrderNo(itemList.get(0).getStockoutOrderNo());
            if (order == null) {
                continue;
            }
            // 获取收货人信息
            StockoutReceiverInfo receiverInfo = receiverInfoService.getReceiveInfoByStockoutOrderId(order.getStockoutOrderId());
            if (receiverInfo == null) {
                continue;
            }
            // 构建打印数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("boxCode", shipment.getShipmentBoxCode());
            dataMap.put("boxTitle", boxTitle);
            dataMap.put("stockoutOrderNo", order.getStockoutOrderNo());
            dataMap.put("orderNo", itemList.get(0).getOrderNo());
            dataMap.put("isShowOrder", isShowOrder);
            dataMap.put("receiverZip", StringUtils.hasText(receiverInfo.getReceiverZip()) ? receiverInfo.getReceiverZip().substring(0, Math.min(5, receiverInfo.getReceiverZip().length())) : null);
            // 渲染模板
            String html = PrintTransferUtils.dynamicHtml(templateEntity, dataMap);
            for (int i = 0; i < printNum; i++) {
                response.getHtmlList().add(html);
            }
        }
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }
}
