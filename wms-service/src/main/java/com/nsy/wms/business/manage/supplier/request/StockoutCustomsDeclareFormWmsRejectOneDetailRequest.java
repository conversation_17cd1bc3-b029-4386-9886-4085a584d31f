package com.nsy.wms.business.manage.supplier.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * 供应商系统 - 关单 - WMS驳回 - 请求体"
 *
 * <AUTHOR>
 * @since 2022-12-25
 */
@ApiModel(value = "StockoutCustomsDeclareFormWmsRejectOneDetailRequest", description = "供应商系统 - 关单 - WMS驳回 - 请求体")
public class StockoutCustomsDeclareFormWmsRejectOneDetailRequest {

    @ApiModelProperty(value = "关单明细IDS", name = "declareFormIds")
    @NotNull(message = "关单明细IDS不能为空")
    private Integer declareFormItemId;

    @ApiModelProperty(value = "开票明细ID", name = "taxItemId")
    @NotNull(message = "开票明细ID不能为空")
    private Integer taxItemId;

    public Integer getDeclareFormItemId() {
        return declareFormItemId;
    }

    public void setDeclareFormItemId(Integer declareFormItemId) {
        this.declareFormItemId = declareFormItemId;
    }

    public Integer getTaxItemId() {
        return taxItemId;
    }

    public void setTaxItemId(Integer taxItemId) {
        this.taxItemId = taxItemId;
    }
}
