package com.nsy.wms.business.service.qa;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.ProductInfoConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.product.ProductShopifyMarketing;
import com.nsy.api.wms.domain.qa.ProductBomAttachmentDto;
import com.nsy.api.wms.domain.qa.StockinQaOrderProcessDto;
import com.nsy.api.wms.domain.qa.StockinQaProductSampleRecordDto;
import com.nsy.api.wms.domain.qc.ProductCategoryDetail;
import com.nsy.api.wms.domain.qc.QcInboundsItem;
import com.nsy.api.wms.domain.stockin.QcInboundsPriceItem;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdQaSopEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.qa.PurchaseOrderItemTypeEnum;
import com.nsy.api.wms.enumeration.qa.QaProcessEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaOrderProcessStatusEnum;
import com.nsy.api.wms.enumeration.stock.PurchaseOrderTypeEnum;
import com.nsy.api.wms.request.bd.DateRangeRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.product.ProductTryOnValidRequest;
import com.nsy.api.wms.request.qa.QcInboundsConcessionApplyFileRequest;
import com.nsy.api.wms.request.qa.StockinQaBatchAuditRequest;
import com.nsy.api.wms.request.qa.StockinQaOrderHistoryRequest;
import com.nsy.api.wms.request.qa.StockinQaOrderPageRequest;
import com.nsy.api.wms.request.qa.StockinQaSpuReturnHistoryRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.StatusTabResponse;
import com.nsy.api.wms.response.qa.AttachInfoResponse;
import com.nsy.api.wms.response.qa.QcInboundsConcessionApplyFileResponse;
import com.nsy.api.wms.response.qa.StockinQaHistoryResponse;
import com.nsy.api.wms.response.qa.StockinQaInfoResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderCountQtyResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderDetailResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderPageExport;
import com.nsy.api.wms.response.qa.StockinQaOrderPageResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderProcess;
import com.nsy.api.wms.response.qa.StockinQaOrderSkcResponse;
import com.nsy.api.wms.response.qa.StockinQaOrderUnqualifiedResponse;
import com.nsy.api.wms.response.qa.StockinQaSopConfigResponse;
import com.nsy.api.wms.response.qa.StockinQaSpuReturnHistoryResponse;
import com.nsy.wms.business.manage.product.ProductApiService;
import com.nsy.wms.business.manage.product.response.QcRuleResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierPurchasePriceRequest;
import com.nsy.wms.business.manage.scm.response.ProductMainMarkTagResponse;
import com.nsy.wms.business.manage.scm.response.PurchaseOrderConcessionApplyResponse;
import com.nsy.wms.business.manage.scm.response.PurchaseOrderWorkmanshipInfoResponse;
import com.nsy.wms.business.manage.scm.response.SchedulingOrderPlanBomDetailResponse;
import com.nsy.wms.business.manage.scm.response.SupplierPurchasePriceResponse;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.product.ProductTryOnTaskService;
import com.nsy.wms.business.service.stockin.StockinOrderItemService;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderDetailEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderImgEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderItemEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderProcessEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderSkuTypeInfoEntity;
import com.nsy.wms.repository.entity.qa.StockinQaOrderUnqualifiedEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.utils.ConvertUtils;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.UnitUtils;
import com.nsy.wms.utils.WmsDateUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: caishaohui
 * @time: 2024/11/27 10:50
 */
@Service
public class StockinQaOrderSearchService implements IDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQaOrderSearchService.class);

    @Autowired
    private StockinQaTaskService stockinQaTaskService;
    @Autowired
    private StockinQaOrderService stockinQaOrderService;
    @Autowired
    private StockinQaOrderItemService stockinQaOrderItemService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private ProductTryOnTaskService productTryOnTaskService;
    @Autowired
    private StockinQaOrderDetailService stockinQaOrderDetailService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private StockinQaProductSampleRecordService stockinQaProductSampleRecordService;
    @Autowired
    private StockinQaOrderProcessService stockinQaOrderProcessService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private StockinQaOrderUnqualifiedService stockinQaOrderUnqualifiedService;
    @Autowired
    private StockinQaOrderImgService stockinQaOrderImgService;
    @Autowired
    private BdSystemParameterService bdSystemParameterService;
    @Autowired
    private ProductApiService productApiService;
    @Autowired
    private StockinQaOrderSkuTypeInfoService stockinQaOrderSkuTypeInfoService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockinOrderItemService stockinOrderItemService;
    @Autowired
    private BdQaInspectRuleService bdQaInspectRuleService;
    @Autowired
    private BdQaFullInspectRuleService bdqaFullInspectRuleService;

    /**
     * 操作质检页面-获取信息区数据
     *
     * @param stockinQaOrderId
     * @return
     */
    public StockinQaInfoResponse getQaInfo(Integer stockinQaOrderId) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(stockinQaOrderId);
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        List<StockinQaOrderItemEntity> itemEntityList = stockinQaOrderItemService.findByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());

        StockinQaInfoResponse response = new StockinQaInfoResponse();
        ProductSpecInfoEntity specInfoEntity = productSpecInfoService.findTopBySku(stockinQaOrderEntity.getSku());
        StockinQaInfoResponse.ProductBaseInfo productBaseInfo = new StockinQaInfoResponse.ProductBaseInfo();
        BeanUtils.copyProperties(stockinQaOrderEntity, productBaseInfo);
        productBaseInfo.setLabel(buildBaseInfoLabel(stockinQaOrderEntity, itemEntityList));
        productBaseInfo.setBrandName(itemEntityList.stream().map(StockinQaOrderItemEntity::getBrandName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        productBaseInfo.setPurchasePlanNo(itemEntityList.stream().map(StockinQaOrderItemEntity::getPurchasePlanNo).distinct().collect(Collectors.joining(",")));
        productBaseInfo.setBoxQty(itemEntityList.stream().mapToInt(StockinQaOrderItemEntity::getQty).sum());
        productBaseInfo.setColor(specInfoEntity.getColor());
        productBaseInfo.setSize(specInfoEntity.getSize());
        productBaseInfo.setSupplierDeliveryNo(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));
        ProductTryOnValidRequest productTryOnValidRequest = new ProductTryOnValidRequest();
        productTryOnValidRequest.setSku(stockinQaOrderEntity.getSku());
        productBaseInfo.setTryOn(productTryOnTaskService.validIsTryOn(productTryOnValidRequest));

        productBaseInfo.setArrivalCount(stockinOrderItemService.getBaseMapper()
                .sumArrivalCount(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList()), stockinQaOrderEntity.getSku()));

        StockinQaTaskEntity qaTaskEntity = stockinQaTaskService.getById(stockinQaOrderEntity.getTaskId());
        response.setStockinType(qaTaskEntity.getStockinType());

        productBaseInfo.setQaQty(qaTaskEntity.getQaQty());
        productBaseInfo.setIsClash(specInfoEntity.getIsClash());
        Map<String, List<StockinQaOrderItemEntity>> collect = itemEntityList.stream().filter(item -> StringUtils.hasText(item.getPackageName())).collect(Collectors.groupingBy(StockinQaOrderItemEntity::getPackageName));
        productBaseInfo.setPackageName(collect.entrySet().stream().map(entry -> String.format("%s(%s)", entry.getKey(), entry.getValue().stream().mapToInt(StockinQaOrderItemEntity::getQty).sum())).collect(Collectors.joining(",")));
        productBaseInfo.setIsLeadGeneration(StrUtil.nullToDefault(qaTaskEntity.getLabelAttributeNames(), "").contains("引流款"));
        //构建工艺信息
        StockinQaInfoResponse.DesignInfo designInfo = getDesignInfo(itemEntityList, stockinQaOrderEntity);

        StockinQaProductSampleRecordDto lastRecordBySkc = stockinQaProductSampleRecordService.getLastRecordBySkc(stockinQaOrderEntity.getSkc());

        //退货返工 需要赋值上一次退货的原因
        if (itemEntityList.stream().anyMatch(item -> PurchaseOrderTypeEnum.RETURN_APPLY.getIntValue().equals(item.getPurchasingApplyType()))
                || stockinQaOrderItemService.getBaseMapper().isReturnDeliveryType(stockinQaOrderEntity.getStockinQaOrderId())) {
            bulidPreReturnReason(response, itemEntityList, stockinQaOrderEntity);
        }

        //没有bom信息则不处理
        SchedulingOrderPlanBomDetailResponse bomDetail = scmApiService.getSchedulingOrderPlanBomDetail(stockinQaOrderEntity.getProductId(), stockinQaOrderEntity.getSkc());
        if (!Objects.isNull(bomDetail)) {
            List<ProductBomAttachmentDto> productBomAttachmentDtoList = bomDetail.getProductBomAttachmentDtoList();
            designInfo.setProductBomAttachmentDtoList(productBomAttachmentDtoList);
            //审版意见单
            List<ProductBomAttachmentDto> reviewFeedbackAttachList = productBomAttachmentDtoList
                    .stream().filter(dto -> "审版意见单".equals(dto.getAttachmentType())).collect(Collectors.toList());
            designInfo.setReviewFeedbackAttachList(reviewFeedbackAttachList);

            bomDetail.getProductSkcBomMaterialOfferingDtoList().forEach(item -> {
                item.setPreviewImageUrl(item.getImgUrl() + ProductInfoConstant.PRODUCT_INFO_PREVIEW_IMAGE_URL);
                item.setThumbnailImageUrl(item.getImgUrl() + ProductInfoConstant.PRODUCT_INFO_THUMBNAIL_IMAGE_URL);
            });
            response.setProductSkcBomMaterialOfferingDtoList(bomDetail.getProductSkcBomMaterialOfferingDtoList());
        }

        ProductShopifyMarketing platformMarketing = productApiService.getPlatformMarketing(stockinQaOrderEntity.getProductId());
        response.setProductShopifyMarketing(platformMarketing);

        response.setSampleRecordDto(lastRecordBySkc);
        response.setBaseInfo(productBaseInfo);
        response.setDesignInfo(designInfo);
        return response;
    }

    @NotNull
    private StockinQaInfoResponse.DesignInfo getDesignInfo(List<StockinQaOrderItemEntity> itemEntityList, StockinQaOrderEntity stockinQaOrderEntity) {
        StockinQaInfoResponse.DesignInfo designInfo = new StockinQaInfoResponse.DesignInfo();
        designInfo.setPackageName(itemEntityList.stream().map(StockinQaOrderItemEntity::getPackageName).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        PurchaseOrderWorkmanshipInfoResponse workmanshipInfoResponse = scmApiService.purchaseOrderWorkmanshipInfoForQa(itemEntityList.get(0).getPurchasePlanNo(), stockinQaOrderEntity.getSku());
        if (Objects.nonNull(workmanshipInfoResponse)) {
            designInfo.setWorkmanshipVersion(workmanshipInfoResponse.getVersionNo());
            designInfo.setFabricType(workmanshipInfoResponse.getFabricType());
            designInfo.setFabricTypeEn(workmanshipInfoResponse.getFabricTypeEn());
            designInfo.setWashLabel(workmanshipInfoResponse.getWashLabel());
            designInfo.setBdTagDto(workmanshipInfoResponse.getBdTagDto());
            designInfo.setWorkmanshipSheet(workmanshipInfoResponse.getWorkmanshipSheet());
            designInfo.setPackedSize(String.format("%s*%s*%s", ConvertUtils.convertPackageSize(workmanshipInfoResponse.getPackageLong()),
                    ConvertUtils.convertPackageSize(workmanshipInfoResponse.getPackageWidth()),
                    ConvertUtils.convertPackageSize(workmanshipInfoResponse.getPackageHeight())));
            designInfo.setChargedWeight(UnitUtils.poundsToKilograms(workmanshipInfoResponse.getChargedWeight()));
            designInfo.setVolumeWeight(UnitUtils.poundsToKilograms(workmanshipInfoResponse.getVolumeWeightPound()));
            designInfo.setWeight(workmanshipInfoResponse.getWeight());
            designInfo.setFbaCost(workmanshipInfoResponse.getFbaCost());
            designInfo.setPackageName(workmanshipInfoResponse.getPackageName());
            designInfo.setPackageSize(workmanshipInfoResponse.getPackageSize());
        } else {
            StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderEntity.getStockinQaOrderId());
            ProductInfoEntity productInfoEntity = productInfoService.findTopByProductId(stockinQaOrderEntity.getProductId());
            designInfo.setWorkmanshipVersion(detailEntity.getWorkmanshipVersion());
            designInfo.setFabricType(productInfoEntity.getFabricType());
            designInfo.setFabricTypeEn(productInfoEntity.getFabricTypeEn());
            designInfo.setWashLabel(productInfoEntity.getWashLabel());
            List<ProductMainMarkTagResponse> productMainMarkTagResponses = scmApiService.queryProductMainMarkTagByProductId(stockinQaOrderEntity.getProductId());
            if (!CollectionUtils.isEmpty(productMainMarkTagResponses))
                designInfo.setBdTagDto(productMainMarkTagResponses.get(0).getBdTagDto());
        }
        return designInfo;
    }

    private void bulidPreReturnReason(StockinQaInfoResponse response, List<StockinQaOrderItemEntity> itemEntityList, StockinQaOrderEntity stockinQaOrderEntity) {
        List<String> purchasePlanNos = itemEntityList.stream().map(StockinQaOrderItemEntity::getPurchasePlanNo).collect(Collectors.toList());
        Integer stockinPreOrderId = this.stockinQaOrderService.getBaseMapper().findPreQaOrder(stockinQaOrderEntity.getStockinQaOrderId(), stockinQaOrderEntity.getSku(), purchasePlanNos);
        if (Objects.nonNull(stockinPreOrderId)) {
            List<StockinQaOrderUnqualifiedEntity> unqualifiedEntities = stockinQaOrderUnqualifiedService.findByStockinQaOrderId(stockinPreOrderId);
            if (CollectionUtils.isEmpty(unqualifiedEntities))
                return;

            StockinQaOrderUnqualifiedEntity orderUnqualifiedEntity = unqualifiedEntities.get(0);
            response.setIsNotice(1);
            response.setUnqualifiedQuestion(orderUnqualifiedEntity.getUnqualifiedQuestion());
            response.setUnqualifiedReason(orderUnqualifiedEntity.getUnqualifiedReason());
        }
    }

    private String buildBaseInfoLabel(StockinQaOrderEntity stockinQaOrderEntity, List<StockinQaOrderItemEntity> itemEntityList) {
        List<String> labels = new LinkedList<>();
        StockinQaTaskEntity taskEntity = stockinQaTaskService.getById(stockinQaOrderEntity.getTaskId());
        Integer isNew = taskEntity.getIsNew();
        if (isNew.equals(1))
            labels.add("新");

        if (itemEntityList.stream().anyMatch(e -> PurchaseOrderTypeEnum.RETURN_APPLY.getIntValue().equals(e.getPurchasingApplyType()))
                || stockinQaOrderItemService.getBaseMapper().isReturnDeliveryType(stockinQaOrderEntity.getStockinQaOrderId())) {
            labels.add("返");
        }

        if (StringUtils.hasText(taskEntity.getLabelAttributeNames()) && itemEntityList.stream().anyMatch(e -> taskEntity.getLabelAttributeNames().contains(PurchaseOrderItemTypeEnum.SUPPLIER_REDESIGN_FIRST_ORDER.getValue()))) {
            labels.add("改版");
        }
        return labels.stream().collect(Collectors.joining(","));

    }

    //操作质检页面-历史同sku质检结果
    public PageResponse<StockinQaHistoryResponse> pageQaHistory(StockinQaOrderHistoryRequest request) {
        PageResponse<StockinQaHistoryResponse> response = new PageResponse<>();

        Page<StockinQaHistoryResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        IPage<StockinQaHistoryResponse> pageList = stockinQaOrderService.getBaseMapper().pageQaHistory(page, request);
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_STATUS.getName());

        pageList.getRecords().forEach(item -> {
            item.setResult(statusMap.get(item.getResult()));
        });
        response.setContent(pageList.getRecords());
        response.setTotalCount(page.getTotal());
        return response;
    }

    public PageResponse<StockinQaSpuReturnHistoryResponse> pageQaSpuReturnHistory(StockinQaSpuReturnHistoryRequest request) {
        PageResponse<StockinQaSpuReturnHistoryResponse> response = new PageResponse<>();

        Page<StockinQaSpuReturnHistoryResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinQaSpuReturnHistoryResponse> pageList = stockinQaOrderService.getBaseMapper().pageQaSpuReturnHistory(page, request);
        response.setContent(pageList.getRecords());
        response.setTotalCount(page.getTotal());
        return response;
    }

    public StockinQaSopConfigResponse getQaSopConfig(Integer stockinQaOrderId) {
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getById(stockinQaOrderId);
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单");
        List<StockinQaOrderProcessEntity> processEntityList = stockinQaOrderProcessService.listByStockinQaOrderId(stockinQaOrderId);
        StockinQaProductSampleRecordDto lastRecordBySkc = stockinQaProductSampleRecordService.getLastRecordBySkc(stockinQaOrderEntity.getSkc());
        if (!LocationEnum.MISI.name().equals(stockinQaOrderEntity.getLocation())) {
            //判断不是新款 || 做过新款质检   需要去掉样衣核对
            StockinQaTaskEntity qaTaskEntity = stockinQaTaskService.getById(stockinQaOrderEntity.getTaskId());
            if (qaTaskEntity.getIsNew().equals(0) || Objects.nonNull(lastRecordBySkc))
                processEntityList = processEntityList.stream().filter(item ->
                        !BdQaSopEnum.QC_SAMPLE.getProcessName().equals(item.getProcessName())).collect(Collectors.toList());
        }

        //如果新款质检的色卡核对结果不是无色卡的话 需要去掉色卡核对
        if (Objects.nonNull(lastRecordBySkc)
                && lastRecordBySkc.getQaProcessList().stream().anyMatch(item ->
                BdQaSopEnum.QC_COLOR.getProcessName().equals(item.getProcessName())
                        && !QaProcessEnum.NO_COLOR_CARD.name().equals(item.getStatus()))) {
            processEntityList = processEntityList.stream().filter(item ->
                    !BdQaSopEnum.QC_COLOR.getProcessName().equals(item.getProcessName())).collect(Collectors.toList());
        } else {
            //同个工厂出库单，新款，同个颜色其中一个尺码验过色卡(包括新款检验和大货检验)，同颜色的其他尺码就不再显示色卡核对环节
            List<String> supplierDeliveryNoList = stockinQaOrderItemService.list(new LambdaQueryWrapper<StockinQaOrderItemEntity>()
                            .select(StockinQaOrderItemEntity::getSupplierDeliveryNo)
                            .eq(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderEntity.getStockinQaOrderId()))
                    .stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
            int count = stockinQaOrderService.getBaseMapper().countQcColorOrderBySupplierDeliveryNoAndSkc(supplierDeliveryNoList, stockinQaOrderEntity.getSkc());
            if (count > 0)
                processEntityList = processEntityList.stream().filter(item ->
                        !BdQaSopEnum.QC_COLOR.getProcessName().equals(item.getProcessName())).collect(Collectors.toList());
        }


        List<StockinQaOrderImgEntity> stockinQaOrderImage = stockinQaOrderImgService.getStockinQaOrderImage(stockinQaOrderId);

        List<StockinQaOrderProcessDto> processDtoList = processEntityList.stream().filter(item ->
                        !StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus().equals(item.getProcessName())
                                && !StockinQaOrderProcessStatusEnum.SECOND_AUDIT.getStatus().equals(item.getProcessName())
                                && !StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.getStatus().equals(item.getProcessName())
                                && !BdQaSopEnum.QC_RESULT.getProcessName().equals(item.getProcessName())
                                && !StockinQaOrderProcessStatusEnum.FULL_INSPECT.getStatus().equals(item.getProcessName()))
                .map(item -> {
                    StockinQaOrderProcessDto dto = new StockinQaOrderProcessDto();
                    BeanUtils.copyProperties(item, dto);
                    //每个流程需要赋值特有的质检参数 （对色图、质检要点等等）
                    buildProcessParam(dto, stockinQaOrderEntity, stockinQaOrderImage);
                    dto.setStatusStr(QaProcessEnum.getValueByName(item.getStatus()));
                    dto.setIsComplete(Objects.nonNull(item.getOperateDate()) ? 1 : 0);
                    return dto;
                }).collect(Collectors.toList());

        StockinQaSopConfigResponse response = new StockinQaSopConfigResponse();
        response.setProcessList(processDtoList);
        //判断处于哪个流程
        processDtoList.stream().filter(item -> Objects.isNull(item.getOperateDate())).findFirst().ifPresent(item -> response.setProcessName(item.getProcessName()));
        return response;
    }

    /**
     * 构造sop质检参数
     *
     * @param dto
     * @param stockinQaOrderEntity
     * @param stockinQaOrderImage
     */
    private void buildProcessParam(StockinQaOrderProcessDto dto, StockinQaOrderEntity stockinQaOrderEntity, List<StockinQaOrderImgEntity> stockinQaOrderImage) {
        BdQaSopEnum sopEnum = BdQaSopEnum.getEnumByStatusOrName(dto.getProcessName());
        dto.setImgList(stockinQaOrderImage.stream().filter(item -> item.getProcessName().equals(sopEnum.getProcessName())).map(StockinQaOrderImgEntity::getImgUrl).collect(Collectors.toList()));

        switch (sopEnum) {
            case PRODUCT_CHECK:
                dto.setProductSpecInfo(productInfoService.findInboundsInfoFromProduct(stockinQaOrderEntity.getSku()));
                break;
            case QC_ITEMS:
                QcRuleResponse qcRuleResponse = productApiService.findQcRuleFromProduct(stockinQaOrderEntity.getProductId());
                if (Objects.nonNull(qcRuleResponse) && !CollectionUtils.isEmpty(qcRuleResponse.getQcRuleList())) {
                    List<QcInboundsItem> collect = qcRuleResponse.getQcRuleList().stream().map(rule -> {
                        QcInboundsItem qcInboundsItem = new QcInboundsItem();
                        qcInboundsItem.setPart(rule.getPart());
                        qcInboundsItem.setDescription(rule.getDescription());
                        return qcInboundsItem;
                    }).collect(Collectors.toList());
                    dto.setQcInboundsItemList(collect);
                }
                break;
            default:
                break;

        }

    }

    public PageResponse<StockinQaOrderPageResponse> pageList(StockinQaOrderPageRequest request) {
        PageResponse<StockinQaOrderPageResponse> response = new PageResponse<>();

        Page<StockinQaOrderPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        //分组人员信息查询条件
        this.buildGroupUserInfo(request);
        IPage<StockinQaOrderPageResponse> pageList = stockinQaOrderService.getBaseMapper().pageList(page, request);
        List<StockinQaOrderPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setContent(pageList.getRecords());
            response.setTotalCount(page.getTotal());
            return response;
        }
        List<Integer> stockinQaOrderIds = records.stream().map(StockinQaOrderPageResponse::getStockinQaOrderId).collect(Collectors.toList());

        Map<Integer, List<StockinQaOrderItemEntity>> itemMap = stockinQaOrderItemService.list(new LambdaQueryWrapper<StockinQaOrderItemEntity>().select(StockinQaOrderItemEntity::getStockinQaOrderId, StockinQaOrderItemEntity::getSupplierDeliveryNo).in(StockinQaOrderItemEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderItemEntity::getStockinQaOrderId));

        Map<Integer, List<StockinQaOrderProcessEntity>> processMap = stockinQaOrderProcessService.list(new LambdaQueryWrapper<StockinQaOrderProcessEntity>().select(StockinQaOrderProcessEntity::getStockinQaOrderId, StockinQaOrderProcessEntity::getProcessName, StockinQaOrderProcessEntity::getOperator, StockinQaOrderProcessEntity::getUnqualifiedCount, StockinQaOrderProcessEntity::getReturnCount, StockinQaOrderProcessEntity::getConcessionsCount).in(StockinQaOrderProcessEntity::getStockinQaOrderId, stockinQaOrderIds).in(StockinQaOrderProcessEntity::getProcessName, Lists.newArrayList(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus(), StockinQaOrderProcessStatusEnum.SECOND_AUDIT.getStatus()))).stream().collect(Collectors.groupingBy(StockinQaOrderProcessEntity::getStockinQaOrderId));

        Map<Integer, List<StockinQaOrderSkuTypeInfoEntity>> skuTypeMap = stockinQaOrderSkuTypeInfoService.list(new LambdaQueryWrapper<StockinQaOrderSkuTypeInfoEntity>().select(StockinQaOrderSkuTypeInfoEntity::getSkuType, StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId).in(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId, stockinQaOrderIds)).stream().collect(Collectors.groupingBy(StockinQaOrderSkuTypeInfoEntity::getStockinQaOrderId));

        Map<Integer, List<StockinQaOrderUnqualifiedResponse>> unqualifiedInfoMap = stockinQaOrderUnqualifiedService.getUnqualifiedList(stockinQaOrderIds);
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_STATUS.getName());
        Map<String, String> processStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName());

        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(records.stream().map(StockinQaOrderPageResponse::getSku).collect(Collectors.toList())).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));

        records.stream().forEach(entity -> {

            //如果质检单未完成，退货数填入直接退货数
            if (!StockinQaOrderProcessStatusEnum.COMPLETED.name().equals(entity.getProcessStatus()))
                entity.setReturnCount(entity.getDirectReturnCount());

            List<StockinQaOrderItemEntity> taskItemEntityList = itemMap.get(entity.getStockinQaOrderId());

            List<String> supplierDeliveryNoList = taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
            entity.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, entity.getSku()));

            entity.setSupplierDeliveryNo(taskItemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));
            entity.setProcessStatusStr(processStatusMap.get(entity.getProcessStatus()));
            entity.setResultStr(statusMap.get(entity.getResult()));
            this.buildPageUnqualifiedReasonInfo(unqualifiedInfoMap, entity);
            ProductSpecInfoEntity productSpecInfoEntity = specMap.get(entity.getSku());
            entity.setImageUrl(productSpecInfoEntity.getImageUrl());
            entity.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
            entity.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());

            List<StockinQaOrderProcessEntity> processEntityList = processMap.get(entity.getStockinQaOrderId());
            if (!CollectionUtils.isEmpty(processEntityList)) {
                processEntityList.stream().filter(process -> StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus().equals(process.getProcessName())).findAny().ifPresent(process -> {
                    entity.setFirstAuditUserName(process.getOperator());

                });
                processEntityList.stream().filter(process -> StockinQaOrderProcessStatusEnum.SECOND_AUDIT.getStatus().equals(process.getProcessName())).findAny().ifPresent(process -> entity.setSecondAuditUserName(process.getOperator()));
            }

            List<StockinQaOrderSkuTypeInfoEntity> skuTypeInfoEntities = skuTypeMap.get(entity.getStockinQaOrderId());
            if (!CollectionUtils.isEmpty(skuTypeInfoEntities)) {
                entity.setSkuTypeList(skuTypeInfoEntities.stream().map(StockinQaOrderSkuTypeInfoEntity::getSkuType).collect(Collectors.toList()));
            }

        });

        response.setContent(pageList.getRecords());
        response.setTotalCount(stockinQaOrderService.getBaseMapper().pageCount(request));
        return response;
    }

    private void buildPageUnqualifiedReasonInfo(Map<Integer, List<StockinQaOrderUnqualifiedResponse>> unqualifiedInfoMap, StockinQaOrderPageResponse detail) {
        if (CollectionUtils.isEmpty(unqualifiedInfoMap) || CollectionUtils.isEmpty(unqualifiedInfoMap.get(detail.getStockinQaOrderId()))) {
            return;
        }
        detail.setUnqualifiedReason(unqualifiedInfoMap.get(detail.getStockinQaOrderId()).get(0).getUnqualifiedReason());
        detail.setUnqualifiedQuestion(unqualifiedInfoMap.get(detail.getStockinQaOrderId()).get(0).getUnqualifiedQuestion());
    }

    public List<StatusTabResponse> getProcessTabList() {
   /*     List<StatusTabResponse> processTabList = stockinQaOrderService.getBaseMapper().countByProcessStatus();
        Map<String, StatusTabResponse> collect = processTabList.stream().collect(Collectors.toMap(StatusTabResponse::getStatus, Function.identity()));
*/
        //是否存在稽查规则
        List<StockinQaOrderProcessStatusEnum> statusEnumList = Arrays.stream(StockinQaOrderProcessStatusEnum.values())
                .filter(item -> !StockinQaOrderProcessStatusEnum.CANCELED.name().equals(item.name())
                        && !StockinQaOrderProcessStatusEnum.COMPLETED.name().equals(item.name())).collect(Collectors.toList());
        if (!bdqaFullInspectRuleService.existInspect())
            statusEnumList = statusEnumList.stream().filter(item -> !StockinQaOrderProcessStatusEnum.FULL_INSPECT.name().equals(item.name())).collect(Collectors.toList());
        if (!bdQaInspectRuleService.existInspect())
            statusEnumList = statusEnumList.stream().filter(item -> !StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.name().equals(item.name())).collect(Collectors.toList());

        List<StatusTabResponse> resultList = statusEnumList.stream().map(item -> {
            /*StatusTabResponse statusTabResponse = collect.get(item.name());
            if (Objects.isNull(statusTabResponse)) {
                statusTabResponse = new StatusTabResponse();
                statusTabResponse.setStatus(item.name());
                statusTabResponse.setNum(0);
            }*/
            StatusTabResponse statusTabResponse = new StatusTabResponse();
            statusTabResponse.setLabel(item.getStatus());
            statusTabResponse.setName(item.getStatus());
            statusTabResponse.setStatus(item.name());
            return statusTabResponse;
        }).collect(Collectors.toList());

        StatusTabResponse statusTabResponse1 = new StatusTabResponse();
        statusTabResponse1.setStatus(StockinQaOrderProcessStatusEnum.COMPLETED.name());
        statusTabResponse1.setLabel(StockinQaOrderProcessStatusEnum.COMPLETED.getStatus());
        statusTabResponse1.setName(StockinQaOrderProcessStatusEnum.COMPLETED.getStatus());
        resultList.add(statusTabResponse1);

        StatusTabResponse statusTabResponse = new StatusTabResponse();
        statusTabResponse.setStatus("ALL");
        statusTabResponse.setLabel("所有");
        statusTabResponse.setName("所有");
        resultList.add(statusTabResponse);
        return resultList;
    }

    /**
     * 获取质检单详情
     *
     * @param stockinQaOrderId
     * @return
     */
    public StockinQaOrderDetailResponse getDetail(Integer stockinQaOrderId) {
        //校验用户查看的权利
        StockinQaOrderEntity stockinQaOrderEntity = stockinQaOrderService.getBaseMapper().getByIdPermission(stockinQaOrderId);
        if (Objects.isNull(stockinQaOrderEntity))
            throw new BusinessServiceException("未找到质检单或无权查看");
        StockinQaOrderDetailResponse response = new StockinQaOrderDetailResponse();
        StockinQaOrderDetailEntity detailEntity = stockinQaOrderDetailService.findTopByStockinQaOrderId(stockinQaOrderId);
        BeanUtils.copyProperties(stockinQaOrderEntity, response);
        BeanUtils.copyProperties(detailEntity, response);

        StockinQaTaskEntity taskEntity = stockinQaTaskService.getById(stockinQaOrderEntity.getTaskId());
        response.setQaQty(taskEntity.getQaQty());

        response.setProcessStatusStr(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName(), stockinQaOrderEntity.getProcessStatus()));
        response.setResultStr(enumConversionChineseUtils.baseConversionByTypeAndValue(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_STATUS.getName(), stockinQaOrderEntity.getResult()));
        response.setQcStartDate(stockinQaOrderEntity.getCreateDate());
        response.setQcCompleteDate(stockinQaOrderEntity.getCompleteDate());

        List<StockinQaOrderProcessEntity> processEntityList = stockinQaOrderProcessService.listByStockinQaOrderId(stockinQaOrderId);
        List<StockinQaOrderItemEntity> itemEntityList = stockinQaOrderItemService.findByStockinQaOrderId(stockinQaOrderId);

        response.setPurchasePlanNo(itemEntityList.stream().map(StockinQaOrderItemEntity::getPurchasePlanNo).distinct().collect(Collectors.joining(",")));
        response.setSupplierDeliveryBoxCode(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryBoxCode).distinct().collect(Collectors.joining(",")));
        response.setSupplierDeliveryNo(itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.joining(",")));

        List<String> supplierDeliveryNoList = itemEntityList.stream().map(StockinQaOrderItemEntity::getSupplierDeliveryNo).distinct().collect(Collectors.toList());
        response.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(supplierDeliveryNoList, stockinQaOrderEntity.getSku()));

        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySku(stockinQaOrderEntity.getSku());

        response.setImageUrl(productSpecInfoEntity.getImageUrl());
        response.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
        response.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());

        buildUnqualifiedReason(stockinQaOrderId, response);

        Map<String, List<StockinQaOrderImgEntity>> imageMap = stockinQaOrderImgService.getStockinQaOrderImage(stockinQaOrderId).stream().collect(Collectors.groupingBy(StockinQaOrderImgEntity::getProcessName));

        List<StockinQaOrderProcess> processList = new LinkedList<>();

        processEntityList.forEach(item -> {
            StockinQaOrderProcess process = new StockinQaOrderProcess();
            BeanUtils.copyProperties(item, process);

            process.setStatusStr(QaProcessEnum.getValueByName(item.getStatus()));
            process.setIsComplete(Objects.nonNull(item.getOperateDate()) ? 1 : 0);
            //流程图片信息
            this.buildProcessImgInfo(imageMap, process);
            if (!StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus().equals(item.getProcessName())
                    && !StockinQaOrderProcessStatusEnum.SECOND_AUDIT.getStatus().equals(item.getProcessName())
                    && !StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.getStatus().equals(item.getProcessName())
                    && !StockinQaOrderProcessStatusEnum.FULL_INSPECT.getStatus().equals(item.getProcessName())
                    && !BdQaSopEnum.QC_RESULT.getProcessName().equals(item.getProcessName())
                    && !(BdQaSopEnum.QC_COLOR.getProcessName().equals(item.getProcessName()) && Objects.isNull(item.getStatus()))) {
                processList.add(process);
            }
            if (BdQaSopEnum.QC_RESULT.getProcessName().equals(item.getProcessName()))
                response.setSopProcessResult(process);
            if (StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus().equals(item.getProcessName()))
                response.setFirstAuditResult(process);
            if (StockinQaOrderProcessStatusEnum.SECOND_AUDIT.getStatus().equals(item.getProcessName()))
                response.setSecondAuditResult(process);
            if (StockinQaOrderProcessStatusEnum.INSPECT_AUDIT.getStatus().equals(item.getProcessName())) {
                buildProcessUnqualifiedReason(stockinQaOrderId, process);

                process.setInspectTotalCount(detailEntity.getInspectTotalCount());
                response.setQaInspectResult(process);
            }
            if (StockinQaOrderProcessStatusEnum.FULL_INSPECT.getStatus().equals(item.getProcessName())) {
                if (QaProcessEnum.WAIT_INSPECT.name().equalsIgnoreCase(item.getStatus())) {
                    process.setStatusStr("待全检");
                }
                buildProcessUnqualifiedReason(stockinQaOrderId, process);
                process.setFullInspectCount(item.getFullInspectCount());
                response.setQaFullInspectResult(process);
            }

        });
        response.setProcessList(processList);


        return response;
    }

    /**
     * 避免多次返回，如果有图片表里根据流程id找的找数据就找对应数据,没有就找全部
     *
     * @param imageMap
     * @param process
     */
    private void buildProcessImgInfo(Map<String, List<StockinQaOrderImgEntity>> imageMap, StockinQaOrderProcess process) {
        List<StockinQaOrderImgEntity> stockinQaOrderImgEntities = imageMap.get(process.getProcessName());
        if (CollectionUtils.isEmpty(stockinQaOrderImgEntities)) {
            return;
        }
        List<String> filterImgList = new ArrayList<>();
        if (Objects.nonNull(process.getStockinQaOrderProcessId())) {
            filterImgList = stockinQaOrderImgEntities.stream().filter(item -> item.getStockinQaOrderProcessId().equals(process.getStockinQaOrderProcessId())).map(StockinQaOrderImgEntity::getImgUrl).collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(filterImgList)) {
            process.setImageList(filterImgList);
            return;
        }
        process.setImageList(stockinQaOrderImgEntities.stream().map(StockinQaOrderImgEntity::getImgUrl).collect(Collectors.toList()));
    }

    public void buildProcessUnqualifiedReason(Integer stockinQaOrderId, StockinQaOrderProcess process) {
        List<StockinQaOrderUnqualifiedEntity> unqualifiedEntityList = stockinQaOrderUnqualifiedService.findByStockinQaOrderIdAndProcess(stockinQaOrderId, process);
        if (CollectionUtils.isEmpty(unqualifiedEntityList))
            return;
        StockinQaOrderUnqualifiedEntity unqualifiedEntity = unqualifiedEntityList.get(0);
        process.setUnqualifiedCategory(unqualifiedEntity.getUnqualifiedCategory());
        process.setUnqualifiedQuestion(unqualifiedEntity.getUnqualifiedQuestion());
        process.setUnqualifiedReason(unqualifiedEntity.getUnqualifiedReason());

        StockinQaOrderUnqualifiedEntity secondaryUnqualifiedEntity = null;
        if (unqualifiedEntityList.size() > 1)
            secondaryUnqualifiedEntity = unqualifiedEntityList.get(1);
        if (Objects.nonNull(secondaryUnqualifiedEntity) && secondaryUnqualifiedEntity.getProcessName().equals(unqualifiedEntity.getProcessName())) {
            process.setUnqualifiedCategorySecondary(secondaryUnqualifiedEntity.getUnqualifiedCategory());
            process.setUnqualifiedQuestionSecondary(secondaryUnqualifiedEntity.getUnqualifiedQuestion());
            process.setUnqualifiedReasonSecondary(secondaryUnqualifiedEntity.getUnqualifiedReason());
        }
    }

    /**
     * 构建不合格原因参数
     *
     * @param stockinQaOrderId
     * @param response
     */
    private void buildUnqualifiedReason(Integer stockinQaOrderId, StockinQaOrderDetailResponse response) {
        List<StockinQaOrderUnqualifiedEntity> entityList = stockinQaOrderUnqualifiedService.findByStockinQaOrderId(stockinQaOrderId);
        if (CollectionUtils.isEmpty(entityList))
            return;
        Map<String, List<StockinQaOrderUnqualifiedEntity>> collect = entityList.stream().collect(Collectors.groupingBy(StockinQaOrderUnqualifiedEntity::getProcessName));

        //质检完成和质检初审都可能存在不合格原因
        List<StockinQaOrderUnqualifiedEntity> unqualifiedEntityList = collect.size() == 1
                ? entityList : collect.get(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus());
        if (CollectionUtils.isEmpty(unqualifiedEntityList))
            unqualifiedEntityList = entityList;

        StockinQaOrderUnqualifiedEntity unqualifiedEntity = unqualifiedEntityList.get(0);
        response.setUnqualifiedCategory(unqualifiedEntity.getUnqualifiedCategory());
        response.setUnqualifiedQuestion(unqualifiedEntity.getUnqualifiedQuestion());
        response.setUnqualifiedReason(unqualifiedEntity.getUnqualifiedReason());

        StockinQaOrderUnqualifiedEntity secondaryUnqualifiedEntity = null;
        if (unqualifiedEntityList.size() > 1)
            secondaryUnqualifiedEntity = unqualifiedEntityList.get(1);
        if (Objects.nonNull(secondaryUnqualifiedEntity) && secondaryUnqualifiedEntity.getProcessName().equals(unqualifiedEntity.getProcessName())) {
            response.setUnqualifiedCategorySecondary(secondaryUnqualifiedEntity.getUnqualifiedCategory());
            response.setUnqualifiedQuestionSecondary(secondaryUnqualifiedEntity.getUnqualifiedQuestion());
            response.setUnqualifiedReasonSecondary(secondaryUnqualifiedEntity.getUnqualifiedReason());
        }
    }

    public List<QcInboundsPriceItem> buildPriceItemList(StockinQaOrderEntity stockinQaOrderEntity, List<String> purchasePlanNos) {
        //todo 权限控制 只有主管级别能看到采购价
        SupplierPurchasePriceRequest request = new SupplierPurchasePriceRequest();
        request.setLocation(stockinQaOrderEntity.getLocation());
        request.setSku(stockinQaOrderEntity.getSku());
        request.setOrderNoList(purchasePlanNos);
        LOGGER.info("请求采购价:{}", JsonMapper.toJson(request));
        List<SupplierPurchasePriceResponse> supplierProductSpecPurchasePrice = scmApiService.getSupplierProductSpecPurchasePrice(request);
        LOGGER.info("请求采购价返回:{}", JsonMapper.toJson(supplierProductSpecPurchasePrice));

        if (CollectionUtils.isEmpty(supplierProductSpecPurchasePrice))
            throw new BusinessServiceException("未查询到采购价!");

        BdSystemParameterEntity parameterEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_STOCKIN_QA_CONCESSION_RECEIVE_DISCOUNT.getKey());
        BigDecimal discount = StockConstant.DISCOUNT;
        if (Objects.nonNull(parameterEntity))
            discount = new BigDecimal(parameterEntity.getConfigValue());
        BigDecimal finalDiscount = discount;
        return supplierProductSpecPurchasePrice.stream().map(purchasePrice -> {
            QcInboundsPriceItem qcInboundsPriceItem = new QcInboundsPriceItem();
            qcInboundsPriceItem.setPurchasePlanNo(purchasePrice.getOrderNo());
            qcInboundsPriceItem.setDiscount(finalDiscount);
            qcInboundsPriceItem.setPurchasePrice(purchasePrice.getPurchasePrice());
            return qcInboundsPriceItem;

        }).collect(Collectors.toList());

    }

    public List<StockinQaOrderSkcResponse> getAuditList(StockinQaBatchAuditRequest request, StockinQaOrderProcessStatusEnum processStatusEnum) {
        if (!StringUtils.hasText(request.getSupplierDeliveryNo())) {
            throw new BusinessServiceException("工厂出库单不能为空!");
        }
        List<StockinQaOrderSkcResponse> skcList = stockinQaOrderService.getBaseMapper().getAuditList(request.getSpu(), request.getSkc(), Arrays.asList(request.getSupplierDeliveryNo().split(",")), processStatusEnum.name(), loginInfoService.getName());
        if (!CollectionUtils.isEmpty(skcList)) {
            //赋值不合格原因
            List<Integer> stockinQaOrderIds = skcList.stream().map(StockinQaOrderSkcResponse::getStockinQaOrderId).collect(Collectors.toList());
            Map<Integer, List<StockinQaOrderUnqualifiedResponse>> unqualifiedInfoMap = stockinQaOrderUnqualifiedService.getUnqualifiedList(stockinQaOrderIds);
            skcList.forEach(item -> {
                List<StockinQaOrderUnqualifiedResponse> unqualifiedResponseList = unqualifiedInfoMap.get(item.getStockinQaOrderId());
                if (!CollectionUtils.isEmpty(unqualifiedResponseList) && unqualifiedResponseList.size() == 2) {
                    item.setUnqualifiedCategory(unqualifiedResponseList.get(0).getUnqualifiedCategory());
                    item.setUnqualifiedReason(unqualifiedResponseList.get(0).getUnqualifiedReason());
                    item.setUnqualifiedQuestion(unqualifiedResponseList.get(0).getUnqualifiedQuestion());
                    item.setUnqualifiedCategorySecondary(unqualifiedResponseList.get(1).getUnqualifiedCategory());
                    item.setUnqualifiedReasonSecondary(unqualifiedResponseList.get(1).getUnqualifiedReason());
                    item.setUnqualifiedQuestionSecondary(unqualifiedResponseList.get(1).getUnqualifiedQuestion());
                }
                if (!CollectionUtils.isEmpty(unqualifiedResponseList) && unqualifiedResponseList.size() == 1) {
                    item.setUnqualifiedCategory(unqualifiedResponseList.get(0).getUnqualifiedCategory());
                    item.setUnqualifiedReason(unqualifiedResponseList.get(0).getUnqualifiedReason());
                    item.setUnqualifiedQuestion(unqualifiedResponseList.get(0).getUnqualifiedQuestion());
                }
            });
        }
        if (!CollectionUtils.isEmpty(skcList) && StockinQaOrderProcessStatusEnum.SECOND_AUDIT.name().equals(processStatusEnum.name())) {
            //赋值初审的结果
            List<Integer> stockinQaOrderIds = skcList.stream().map(StockinQaOrderSkcResponse::getStockinQaOrderId).collect(Collectors.toList());
            List<StockinQaOrderProcessEntity> processEntityList = stockinQaOrderProcessService.listByStockinQaOrderIdsAndProcessName(stockinQaOrderIds, StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus());
            Map<Integer, StockinQaOrderProcessEntity> processEntityMap = processEntityList.stream().collect(Collectors.toMap(StockinQaOrderProcessEntity::getStockinQaOrderId, Function.identity()));
            skcList.forEach(item -> {
                StockinQaOrderProcessDto processDto = new StockinQaOrderProcessDto();
                StockinQaOrderProcessEntity processEntity = processEntityMap.get(item.getStockinQaOrderId());
                BeanUtils.copyProperties(processEntity, processDto);
                processDto.setQualifiedCount(item.getArrivalCount() - processEntity.getUnqualifiedCount());
                item.setFirstAuditProcess(processDto);
            });
        }
        return skcList;

    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_QA_ORDER_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest downloadRequest) {
        DownloadResponse response = new DownloadResponse();

        Page<StockinQaOrderPageResponse> page = new Page<>(downloadRequest.getPageIndex(), downloadRequest.getPageSize());
        page.setSearchCount(false);
        StockinQaOrderPageRequest request = JsonMapper.fromJson(downloadRequest.getRequestContent(), StockinQaOrderPageRequest.class);
        //分组人员信息查询条件
        this.buildGroupUserInfo(request);
        IPage<StockinQaOrderPageResponse> pageList = stockinQaOrderService.getBaseMapper().pageList(page, request);
        List<StockinQaOrderPageResponse> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            response.setDataJsonStr(JsonMapper.toJson(records));
            response.setTotalCount(page.getTotal());
            return response;
        }
        List<Integer> idList = records.stream().map(StockinQaOrderPageResponse::getStockinQaOrderId).collect(Collectors.toList());

        List<StockinQaOrderProcessEntity> processEntities = new LinkedList<>();
        List<StockinQaOrderPageExport> supplierDeliveryNoList = new LinkedList<>();
        List<StockinQaOrderPageExport> skuTypeList = new LinkedList<>();

        buildExportData(idList, processEntities, supplierDeliveryNoList, skuTypeList);
        Map<Integer, List<StockinQaOrderProcessEntity>> processMap = processEntities.stream().collect(Collectors.groupingBy(StockinQaOrderProcessEntity::getStockinQaOrderId));
        Map<Integer, StockinQaOrderPageExport> itemExportMap = supplierDeliveryNoList.stream().collect(Collectors.toMap(StockinQaOrderPageExport::getStockinQaOrderId, Function.identity()));
        Map<Integer, String> skuTyppeMap = skuTypeList.stream().collect(Collectors.toMap(StockinQaOrderPageExport::getStockinQaOrderId, StockinQaOrderPageExport::getSkuType, (v1, v2) -> v1));

        Map<Integer, List<StockinQaOrderUnqualifiedResponse>> unqualifiedInfoMap = stockinQaOrderUnqualifiedService.getUnqualifiedList(idList);
        Map<String, String> statusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_STATUS.getName());
        Map<String, String> processStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_QA_ORDER_PROCESS_STATUS.getName());
        Map<Integer, Date> lastProcessDateInfo = stockinQaOrderProcessService.getLastProcessDateInfo(idList, StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus());
        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(records.stream().map(StockinQaOrderPageResponse::getSku).collect(Collectors.toList())).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));
        Map<Integer, ProductCategoryDetail> productCategoryMap = productApiService.findCategoryDetailByProductIds(records.stream().map(StockinQaOrderPageResponse::getProductId).collect(Collectors.toSet()));
        List<StockinQaOrderPageExport> exportList = records.stream().map(entity -> {
            StockinQaOrderPageExport export = new StockinQaOrderPageExport();
            BeanUtils.copyProperties(entity, export);
            //赋值不合格信息
            this.buildUnqualifiedReasonInfo(unqualifiedInfoMap, export);
            export.setSupplierDeliveryNo(itemExportMap.get(entity.getStockinQaOrderId()).getSupplierDeliveryNo());
            export.setArrivalCount(itemExportMap.get(entity.getStockinQaOrderId()).getArrivalCount());
            export.setProcessStatusStr(processStatusMap.get(entity.getProcessStatus()));
            export.setResultStr(statusMap.get(entity.getResult()));
            export.setFirstAuditEndDate(lastProcessDateInfo.get(entity.getStockinQaOrderId()));
            ProductSpecInfoEntity productSpecInfoEntity = specMap.get(entity.getSku());

            export.setImageUrl(productSpecInfoEntity.getImageUrl());
            export.setPreviewImageUrl(productSpecInfoEntity.getPreviewImageUrl());
            export.setThumbnailImageUrl(productSpecInfoEntity.getThumbnailImageUrl());
            List<StockinQaOrderProcessEntity> processEntityList = processMap.get(entity.getStockinQaOrderId());
            if (!CollectionUtils.isEmpty(processEntityList)) {
                processEntityList.stream().filter(process -> StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus().equals(process.getProcessName())).findAny().ifPresent(process -> {
                    export.setFirstAuditUserName(process.getOperator());
                });
                processEntityList.stream().filter(process -> StockinQaOrderProcessStatusEnum.SECOND_AUDIT.getStatus().equals(process.getProcessName())).findAny().ifPresent(process -> export.setSecondAuditUserName(process.getOperator()));
            }

            export.setSkuType(skuTyppeMap.get(entity.getStockinQaOrderId()));
            if (Objects.nonNull(productCategoryMap.get(entity.getProductId()))) {
                export.setCategoryOne(productCategoryMap.get(entity.getProductId()).getCategoryOneName());
                export.setCategoryTwo(productCategoryMap.get(entity.getProductId()).getCategoryTwoName());
                export.setCategoryThree(productCategoryMap.get(entity.getProductId()).getCategoryThreeName());
            }
            return export;
        }).collect(Collectors.toList());

        response.setTotalCount((long) (records.size() >= downloadRequest.getPageSize() ? (downloadRequest.getPageIndex() + 1) * downloadRequest.getPageSize() : (downloadRequest.getPageIndex() - 1) * downloadRequest.getPageSize() + records.size()));
        response.setDataJsonStr(JsonMapper.toJson(exportList));
        return response;
    }

    private void buildUnqualifiedReasonInfo(Map<Integer, List<StockinQaOrderUnqualifiedResponse>> unqualifiedInfoMap, StockinQaOrderPageExport export) {
        List<StockinQaOrderUnqualifiedResponse> qaOrderUnqualifiedList = unqualifiedInfoMap.get(export.getStockinQaOrderId());
        //没有不合格原因
        if (CollectionUtils.isEmpty(qaOrderUnqualifiedList)) {
            return;
        }
        // 检查列表中是否存在具有 FIRST_AUDIT 状态的元素
        boolean hasFirstAudit = qaOrderUnqualifiedList.stream()
                .anyMatch(response -> StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus().equals(response.getProcessName()));

        // 根据检查结果过滤列表
        List<StockinQaOrderUnqualifiedResponse> filteredList = hasFirstAudit
                ? qaOrderUnqualifiedList.stream()
                .filter(response -> StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus().equals(response.getProcessName())).collect(Collectors.toList()) : qaOrderUnqualifiedList;
        //质检完成和质检初审都可能存在不合格原因
        StockinQaOrderUnqualifiedResponse unqualifiedEntity = filteredList.get(0);
        export.setUnqualifiedCategory(unqualifiedEntity.getUnqualifiedCategory());
        export.setUnqualifiedQuestion(unqualifiedEntity.getUnqualifiedQuestion());
        export.setUnqualifiedReason(unqualifiedEntity.getUnqualifiedReason());

        StockinQaOrderUnqualifiedResponse secondaryUnqualifiedEntity = null;
        if (filteredList.size() > 1)
            secondaryUnqualifiedEntity = filteredList.get(1);
        if (Objects.nonNull(secondaryUnqualifiedEntity)) {
            export.setUnqualifiedCategorySecondary(secondaryUnqualifiedEntity.getUnqualifiedCategory());
            export.setUnqualifiedQuestionSecondary(secondaryUnqualifiedEntity.getUnqualifiedQuestion());
            export.setUnqualifiedReasonSecondary(secondaryUnqualifiedEntity.getUnqualifiedReason());
        }
    }

    private void buildExportData(List<Integer> idList, List<StockinQaOrderProcessEntity> processEntities, List<StockinQaOrderPageExport> supplierDeliveryNoList, List<StockinQaOrderPageExport> skuTypeList) {
        LambdaQueryWrapper<StockinQaOrderProcessEntity> processEntityQueryWrapper = new LambdaQueryWrapper<>();

        List<String> processName = Lists.newArrayList(StockinQaOrderProcessStatusEnum.FIRST_AUDIT.getStatus(), StockinQaOrderProcessStatusEnum.SECOND_AUDIT.getStatus());

        Lists.partition(idList, 100).forEach(stockinQaOrderIds -> {
            List<StockinQaOrderPageExport> stockinQaOrderPageExports = stockinQaOrderItemService.getBaseMapper().listSupplierDeliveryNo(stockinQaOrderIds);
            stockinQaOrderPageExports.forEach(item -> {
                item.setArrivalCount(stockinOrderItemService.getBaseMapper().sumArrivalCount(Arrays.stream(item.getSupplierDeliveryNo().split(",")).collect(Collectors.toList()), item.getSku()));
            });
            supplierDeliveryNoList.addAll(stockinQaOrderPageExports);
            skuTypeList.addAll(stockinQaOrderSkuTypeInfoService.getBaseMapper().listSkuType(stockinQaOrderIds));

            processEntityQueryWrapper.select(StockinQaOrderProcessEntity::getStockinQaOrderId, StockinQaOrderProcessEntity::getProcessName, StockinQaOrderProcessEntity::getOperator);
            processEntityQueryWrapper.in(StockinQaOrderProcessEntity::getProcessName, processName);
            processEntityQueryWrapper.in(StockinQaOrderProcessEntity::getStockinQaOrderId, stockinQaOrderIds);
            processEntities.addAll(stockinQaOrderProcessService.list(processEntityQueryWrapper));

            processEntityQueryWrapper.clear();
        });
    }

    /**
     * 统计数量
     *
     * @param request
     * @return
     */
    public StockinQaOrderCountQtyResponse countQty(StockinQaOrderPageRequest request) {
        this.buildGroupUserInfo(request);
        StockinQaOrderCountQtyResponse response = new StockinQaOrderCountQtyResponse();
        if (Objects.nonNull(request.getStartDate()) && Objects.nonNull(request.getEndDate())) {
            //按七天一次查询
            List<DateRangeRequest> dateRangeRequests = WmsDateUtils.splitDateRange(request.getStartDate(), request.getEndDate(), 7);
            Integer boxQty = 0;
            Integer arrivalCount = 0;
            Integer testTotalCount = 0;
            Integer unqualifiedCount = 0;
            Integer directReturnCount = 0;
            Integer returnCount = 0;
            Integer concessionsCount = 0;
            for (DateRangeRequest dateRangeRequest : dateRangeRequests) {
                request.setStartDate(dateRangeRequest.getStartDate());
                request.setEndDate(dateRangeRequest.getEndDate());
                StockinQaOrderCountQtyResponse countQty = stockinQaOrderService.getBaseMapper().countQty(request);
                boxQty += countQty.getBoxQty();
                arrivalCount += countQty.getArrivalCount();
                testTotalCount += countQty.getTestTotalCount();
                unqualifiedCount += countQty.getUnqualifiedCount();
                directReturnCount += countQty.getDirectReturnCount();
                returnCount += countQty.getReturnCount();
                concessionsCount += countQty.getConcessionsCount();
            }
            response.setBoxQty(boxQty);
            response.setArrivalCount(arrivalCount);
            response.setTestTotalCount(testTotalCount);
            response.setUnqualifiedCount(unqualifiedCount);
            response.setDirectReturnCount(directReturnCount);
            response.setReturnCount(returnCount);
            response.setConcessionsCount(concessionsCount);
            return response;
        } else {
            response = stockinQaOrderService.getBaseMapper().countQty(request);
        }

        return response;
    }

    /**
     * 组装请求的分组人员
     */
    private void buildGroupUserInfo(StockinQaOrderPageRequest request) {
        if (Objects.isNull(request)) {
            return;
        }
        List<Integer> groupIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(request.getGroupIdList())) {
            groupIdList.addAll(request.getGroupIdList());
        }
        if (!Objects.isNull(request.getGroupId())) {
            groupIdList.add(request.getGroupId());
        }
        //没有分组则不处理
        if (CollectionUtils.isEmpty(groupIdList)) {
            return;
        }
        List<SysUserInfo> userInfoList = userApiService.getUserByDepartmentId(groupIdList);
        //分组下没有人员不处理
        if (CollectionUtils.isEmpty(userInfoList)) {
            return;
        }
        request.setGroupUserIdList(userInfoList.stream().map(SysUserInfo::getUserId).distinct().collect(Collectors.toList()));
    }

    public QcInboundsConcessionApplyFileResponse getConcessionApplyFile(QcInboundsConcessionApplyFileRequest request) {
        if (!StringUtils.hasText(request.getPurchaseNumber())) {
            return null;
        }
        QcInboundsConcessionApplyFileResponse response = new QcInboundsConcessionApplyFileResponse();
        List<AttachInfoResponse> attachInfoResponseList = new ArrayList<>();
        List<String> purchaseNumber = Lists.newArrayList(request.getPurchaseNumber().split(","));
        purchaseNumber.forEach(detail -> {
            PurchaseOrderConcessionApplyResponse concessionApplyFile = scmApiService.queryConcessionApplyOrder(detail, request.getSkc());
            if (Objects.isNull(concessionApplyFile) || CollectionUtils.isEmpty(concessionApplyFile.getAttachmentList())) {
                return;
            }
            attachInfoResponseList.addAll(concessionApplyFile.getAttachmentList());
        });
        response.setFileUrlList(attachInfoResponseList);
        return response;
    }
}
