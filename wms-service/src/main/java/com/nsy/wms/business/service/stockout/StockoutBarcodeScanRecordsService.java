package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutBarcodeScanRecordsPageRequest;
import com.nsy.api.wms.request.stockout.StockoutBarcodeScanRecordsSaveRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutBarcodeScanRecordsPageResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.repository.entity.stockout.StockoutBarcodeScanRecordsEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutBarcodeScanRecordsMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/27 10:15
 */
@Service
public class StockoutBarcodeScanRecordsService extends ServiceImpl<StockoutBarcodeScanRecordsMapper, StockoutBarcodeScanRecordsEntity> implements IDownloadService {

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    private StockoutShipmentService stockoutShipmentService;
    @Autowired
    private StockoutShipmentItemService stockoutShipmentItemService;

    /**
     * 列表
     *
     * @param request
     * @return
     */
    public PageResponse<StockoutBarcodeScanRecordsPageResponse> searchPage(StockoutBarcodeScanRecordsPageRequest request) {
        PageResponse<StockoutBarcodeScanRecordsPageResponse> pageResponse = new PageResponse<>();
        IPage<StockoutBarcodeScanRecordsPageResponse> pageResult = this.baseMapper.searchPage(new Page<>(request.getPageIndex(), request.getPageSize()), request);
        if (CollUtil.isEmpty(pageResult.getRecords())) {
            pageResponse.setTotalCount(0L);
            return pageResponse;
        }
        pageResult.getRecords().forEach(detail -> {
            if (Objects.nonNull(detail.getDeliveryQty()) && detail.getDeliveryQty() > 0) {
                return;
            }
            detail.setDeliveryQty(stockoutOrderService.getStockoutOrderQty(Arrays.asList(detail.getStockoutOrderNo().split(","))));
        });
        pageResponse.setTotalCount(pageResult.getTotal());
        pageResponse.setContent(pageResult.getRecords());
        return pageResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveRecords(StockoutBarcodeScanRecordsSaveRequest request) {
        if (!StringUtils.hasText(request.getBusinessNo())) {
            throw new BusinessServiceException("请扫描条码!");
        }
        //扫描的为装箱清单
        StockoutShipmentEntity shipmentEntity = stockoutShipmentService.getEntityByShipmentBoxCode(request.getBusinessNo());
        if (Objects.nonNull(shipmentEntity)) {
            this.saveShipmentBoxCodeScanRecords(shipmentEntity, request.getBusinessType());
            return;
        }
        //扫描订单号
        List<StockoutShipmentItemEntity> shipmentItemEntityList = stockoutShipmentItemService.findByOrderNo(request.getBusinessNo());
        if (!CollUtil.isEmpty(shipmentItemEntityList)) {
            List<Integer> shipmentBoxIdList = shipmentItemEntityList.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
            for (Integer shipmentId : shipmentBoxIdList) {
                this.saveShipmentBoxCodeScanRecords(stockoutShipmentService.getByShipmentId(shipmentId), request.getBusinessType());
            }
        } else {
            //扫描物流单号,截取第一个-前面的数据
            String logisticNo = request.getBusinessNo().split("-")[0];
            List<StockoutShipmentEntity> shipmentList = stockoutShipmentService.getByLogisticNo(logisticNo);
            if (CollUtil.isEmpty(shipmentList)) {
                throw new BusinessServiceException("根据订单号/物流单号/装箱清单查询不到订单信息!");
            }
            for (StockoutShipmentEntity stockoutShipment : shipmentList) {
                this.saveShipmentBoxCodeScanRecords(stockoutShipment, request.getBusinessType());
            }
        }
    }

    private void saveShipmentBoxCodeScanRecords(StockoutShipmentEntity shipmentEntity, String businessType) {
        if (Objects.isNull(shipmentEntity)) {
            throw new BusinessServiceException("查询不到数据请检查!");
        }
        LambdaQueryWrapper<StockoutBarcodeScanRecordsEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutBarcodeScanRecordsEntity::getShipmentBoxCode, shipmentEntity.getShipmentBoxCode());
        queryWrapper.last("limit 1");
        StockoutBarcodeScanRecordsEntity entity = this.getOne(queryWrapper);
        if (!ObjectUtil.isEmpty(entity)) {
            throw new BusinessServiceException(String.format("当前条码已被%s扫描,请检查", entity.getOperateName()));
        }

        List<StockoutShipmentItemEntity> shipmentItemList = stockoutShipmentItemService.findByShipmentId(shipmentEntity.getShipmentId());
        if (CollectionUtils.isEmpty(shipmentItemList)) {
            return;
        }
        StockoutBarcodeScanRecordsEntity recordsEntity = new StockoutBarcodeScanRecordsEntity();
        recordsEntity.setLogisticsNo(shipmentEntity.getLogisticsNo());
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(shipmentItemList.get(0).getStockoutOrderNo());
        if (Objects.nonNull(stockoutOrderEntity)) {
            recordsEntity.setPlatformName(stockoutOrderEntity.getPlatformName());

        }
        recordsEntity.setStockoutOrderNo(shipmentItemList.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.joining(StringConstant.COMMA)));
        recordsEntity.setOrderNo(shipmentItemList.stream().map(StockoutShipmentItemEntity::getOrderNo).distinct().collect(Collectors.joining(StringConstant.COMMA)));
        recordsEntity.setShipmentBoxCode(shipmentEntity.getShipmentBoxCode());
        recordsEntity.setOperateName(loginInfoService.getName());
        recordsEntity.setBusinessType(businessType);
        recordsEntity.setOperateTime(new Date());
        recordsEntity.setLocation(TenantContext.getTenant());
        this.save(recordsEntity);
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_BARCODE_SCAN_RECORDS_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        StockoutBarcodeScanRecordsPageRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockoutBarcodeScanRecordsPageRequest.class);
        // 设置每次的查询数量
        downloadRequest.setPageIndex(request.getPageIndex());
        downloadRequest.setPageSize(request.getPageSize());
        PageResponse<StockoutBarcodeScanRecordsPageResponse> pageResponse = this.searchPage(downloadRequest);
        pageResponse.getContent().forEach(detail -> {
            if (Objects.nonNull(detail.getDeliveryQty()) && detail.getDeliveryQty() > 0) {
                return;
            }
            detail.setDeliveryQty(stockoutOrderService.getStockoutOrderQty(Arrays.asList(detail.getStockoutOrderNo().split(","))));
        });
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getContent()));
        return response;
    }
}
