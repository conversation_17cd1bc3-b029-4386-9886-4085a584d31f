package com.nsy.wms.business.service.logistics.base;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.CountryCodeConstant;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.StockoutSpaceTrackConstant;
import com.nsy.api.wms.domain.stockout.StockoutReceiverInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentTmsModel;
import com.nsy.api.wms.enumeration.stockout.LogisticsTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.WaybillSourceEnum;
import com.nsy.wms.business.manage.tms.LogisticsCompanySetting;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.manage.tms.request.Address;
import com.nsy.wms.business.manage.tms.request.GenerateOrderRequest;
import com.nsy.wms.business.manage.tms.request.OrderInfo;
import com.nsy.wms.business.manage.tms.request.OrderItemInfo;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.manage.tms.response.GenerateOrderResponse;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.product.ProductCategoryCustomsDeclareItemService;
import com.nsy.wms.business.service.product.ProductCategoryCustomsDeclareService;
import com.nsy.wms.business.service.product.ProductCustomsDeclareService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.product.ProductWmsCategoryService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLabelService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutReceiverInfoService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.business.service.stockout.StockoutShipperInfoService;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.product.ProductCategoryCustomsDeclareEntity;
import com.nsy.wms.repository.entity.product.ProductCategoryCustomsDeclareItemEntity;
import com.nsy.wms.repository.entity.product.ProductCustomsDeclareEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.product.ProductWmsCategoryEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLabelEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipperInfoEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class BaseLogisticsService {

    @Autowired
    StockoutShipperInfoService stockoutShipperInfoService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;
    @Resource
    BdSpaceService spaceService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutOrderLabelService stockoutOrderLabelService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    TmsApiService tmsApiService;
    @Autowired
    StockoutShipmentItemService stockoutShipmentItemService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    LogisticsCompanySetting logisticsCompanySetting;
    @Autowired
    ProductCategoryCustomsDeclareService productCategoryCustomsDeclareService;
    @Autowired
    ProductCategoryCustomsDeclareItemService productCategoryCustomsDeclareItemService;
    @Autowired
    PrintService printService;
    @Autowired
    ProductCustomsDeclareService productDeclareService;
    @Autowired
    StockoutOrderLabelService labelService;
    @Autowired
    ProductWmsCategoryService wmsCategoryService;
    @Resource
    StockPrematchInfoService stockPrematchInfoService;

    /**
     * 根据 出库单/装箱清单明细  获取面单,并保存面单
     * stockoutOrderEntity -> 主要用于获取 地址、报关等信息，如果要更新，需通过stockoutOrderItems的id更新
     * stockoutOrderItems -> 要发货的sku集合 【装箱清单/出库单】
     */
    @Transactional
    public BaseGetLogisticsNoResponse getLogisticsNo(StockoutOrderEntity stockoutOrderEntity, List<StockoutOrderItemEntity> stockoutOrderItems, String userName) {
        BaseGetLogisticsNoResponse response = new BaseGetLogisticsNoResponse();
        if (StringUtils.hasText(stockoutOrderEntity.getLogisticsNo())
                && (!StockoutOrderTypeEnum.OVERSEA_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())
                || !StrUtil.containsAny(spaceService.getSpaceByIdValid(stockoutOrderEntity.getSpaceId()).getDescription(),
                StockoutSpaceTrackConstant.SPACE_DESC_ZUOHAI, StockoutSpaceTrackConstant.SPACE_DESC_ZHIYUN, StockoutSpaceTrackConstant.SPACE_DESC_GUCANG))) {
            response.setLogisticsNo(stockoutOrderEntity.getLogisticsNo());
            return response;
        }
        BaseGetLogisticsNoResponse tempResponse = specialCheck(stockoutOrderEntity, userName);
        if (Objects.nonNull(tempResponse)) return tempResponse;
        // 构建tms基本request
        GenerateOrderRequest generateOrderRequest = buildTmsOrderRequest(stockoutOrderEntity, stockoutOrderItems, userName);
        // tms请求
        GenerateOrderResponse.SuccessEntity successEntity = postTmsOrder(generateOrderRequest, stockoutOrderEntity, userName);
        // 面单保存
        saveTmsLabel(successEntity, stockoutOrderItems, stockoutOrderEntity, userName);
        response.setLogisticsNo(successEntity.getLogisticsNo());
        response.setSecondaryNumber(successEntity.getSecondaryNumber());
        response.setLogisticsTid(successEntity.getLogisticsTid());
        response.setAmazonBuyShippingFlag(successEntity.getAmazonBuyShippingFlag());
        return response;
    }

    private void saveTmsLabel(GenerateOrderResponse.SuccessEntity successEntity, List<StockoutOrderItemEntity> stockoutOrderItems, StockoutOrderEntity stockoutOrderEntity, String userName) {
        if (!CollectionUtils.isEmpty(successEntity.getLabelUrlList()) || StringUtils.hasText(successEntity.getPrintData())) {
            // 面单保存
            List<Integer> collect = stockoutOrderItems.stream().map(StockoutOrderItemEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
            addStockoutOrderLabel(collect, successEntity, userName);
        } else {
            if (StockoutOrderTypeEnum.OVERSEA_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())) {
                // 海外仓不获取面单
                return;
            }
            // 再次请求面单
            List<Integer> collect = stockoutOrderItems.stream().map(StockoutOrderItemEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
            collect.forEach(it -> {
                StockoutOrderEntity orderById = stockoutOrderService.getById(it);
                printService.getTmsLabel(orderById.getStockoutOrderNo(), successEntity.getLogisticsNo());
            });
        }
    }

    // 请求并处理tms的返回结果
    public GenerateOrderResponse.SuccessEntity postTmsOrder(GenerateOrderRequest generateOrderRequest, StockoutOrderEntity stockoutOrderEntity, String userName) {
        ResponseEntity<GenerateOrderResponse> responseEntity = tmsApiService.printOrder(generateOrderRequest);
        GenerateOrderResponse generateOrderResponse = responseEntity.getBody();
        if (generateOrderResponse == null) throw new BusinessServiceException("返回错误");
        if (generateOrderResponse.getError() != null) {
            processDeliver(generateOrderResponse, stockoutOrderEntity, userName);
            GenerateOrderResponse.Error error = generateOrderResponse.getError();
            throw new InvalidRequestException(String.format("TMS物流错误：%s【%s】", error.getCode(), error.getMessage()));
        }
        return generateOrderResponse.getSuccessEntity();
    }

    /**
     * 保存面单
     */
    public void addStockoutOrderLabel(List<Integer> stockoutOrderIds, GenerateOrderResponse.SuccessEntity successEntity, String userName) {
        stockoutOrderIds.forEach(stockoutOrderId -> {
            if (!CollectionUtils.isEmpty(successEntity.getLabelUrlList())) {
                successEntity.getLabelUrlList().forEach(labelUrl -> {
                    List<StockoutOrderLabelEntity> orderLabelEntityList = stockoutOrderLabelService.listByStockoutOrderId(stockoutOrderId);
                    Optional<StockoutOrderLabelEntity> optional = orderLabelEntityList.stream().filter(t -> Objects.equals(t.getLabelUrl(), labelUrl)).findAny();
                    if (optional.isPresent()) return;
                    StockoutOrderLabelEntity stockoutOrderLabelEntity = new StockoutOrderLabelEntity();
                    stockoutOrderLabelEntity.setLabelUrl(labelUrl);
                    stockoutOrderLabelEntity.setStockoutOrderId(stockoutOrderId);
                    stockoutOrderLabelEntity.setCreateBy(userName);
                    stockoutOrderLabelEntity.setLogisticsNo(successEntity.getLogisticsNo());
                    stockoutOrderLabelEntity.setSource(WaybillSourceEnum.SYSTEM_PRINTING.name());
                    stockoutOrderLabelService.save(stockoutOrderLabelEntity);
                });
            }
            if (StringUtils.hasText(successEntity.getPrintData())) {
                StockoutOrderLabelEntity stockoutOrderLabelEntity = new StockoutOrderLabelEntity();
                stockoutOrderLabelEntity.setPrintContent(successEntity.getPrintData());
                stockoutOrderLabelEntity.setStockoutOrderId(stockoutOrderId);
                stockoutOrderLabelEntity.setCreateBy(userName);
                stockoutOrderLabelEntity.setLogisticsNo(successEntity.getLogisticsNo());
                stockoutOrderLabelEntity.setSource(WaybillSourceEnum.SYSTEM_PRINTING.name());
                stockoutOrderLabelService.save(stockoutOrderLabelEntity);
            }
        });
    }

    // 构建tms/order的request
    public GenerateOrderRequest buildTmsOrderRequest(StockoutOrderEntity stockoutOrderEntity, List<StockoutOrderItemEntity> stockoutOrderItems, String userName) {
        GenerateOrderRequest generateOrderRequest = new GenerateOrderRequest();
        generateOrderRequest.setOrderInfo(buildOrderInfo(stockoutOrderEntity, stockoutOrderItems));
        setLogisticsInfo(generateOrderRequest);
        setCaiNiaoInfo(generateOrderRequest, stockoutOrderItems);
        setIossNumber(generateOrderRequest, stockoutOrderItems);
        checkRequestInfo(generateOrderRequest, stockoutOrderEntity, userName);
        checkCategoryName(generateOrderRequest);
        return generateOrderRequest;
    }

    /**
     * 构建获取面单基本request
     *
     * @param stockoutOrderEntity
     * @param stockoutOrderItemEntityList
     * @return
     */
    public OrderInfo buildOrderInfo(StockoutOrderEntity stockoutOrderEntity, List<StockoutOrderItemEntity> stockoutOrderItemEntityList) {
        StockoutShipperInfoEntity shipperInfoEntity = stockoutShipperInfoService.getByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        StockoutReceiverInfo receiverInfo = stockoutReceiverInfoService.getReceiveInfoByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        List<OrderItemInfo> orderItemInfoList = buildOrderItemInfoList(stockoutOrderEntity.getCustomsDeclareType(), stockoutOrderItemEntityList, receiverInfo.getCountryCode(), stockoutOrderEntity.getLogisticsCompany());
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setLogisticsChannelName(stockoutOrderEntity.getLogisticsCompany());
        orderInfo.setReceiveCountryCode(receiverInfo.getCountryCode());
        orderInfo.setPlatform(stockoutOrderEntity.getPlatformName());
        orderInfo.setDeptName(stockoutOrderEntity.getBusinessType());
        orderInfo.setStoreId(stockoutOrderEntity.getStoreId());
        orderInfo.setStoreName(stockoutOrderEntity.getStoreName());
        orderInfo.setTid(stockoutOrderItemEntityList.get(0).getOrderNo());
        orderInfo.setBusinessKey(stockoutOrderEntity.getStockoutOrderNo());
        orderInfo.setLength(35.0);
        orderInfo.setHeight(5.0);
        orderInfo.setWidth(25.0);
        // 设置重量，控制小数点
        BigDecimal weightDc = BigDecimal.valueOf(orderItemInfoList.stream().mapToDouble(OrderItemInfo::getWeight).sum())
                .setScale(3, BigDecimal.ROUND_HALF_UP);
        orderInfo.setWeight(weightDc.doubleValue());
        orderInfo.setErpPickId(stockoutOrderEntity.getErpPickId());
        orderInfo.setCustomsValueAmount(orderItemInfoList.stream().mapToDouble(OrderItemInfo::getCustomsPrice).sum());
        orderInfo.setOrderItemInfoList(orderItemInfoList);
        orderInfo.setSender(buildSender(shipperInfoEntity));
        orderInfo.setReceiver(buildReceiver(receiverInfo));
        buildShipmentInfo(stockoutOrderEntity, orderInfo);
        orderInfo.setBoxCount(buildBoxCount(stockoutOrderItemEntityList));
        orderInfo.setAmazonBuyShippingInfo(stockoutOrderEntity.getAmazonBuyShippingInfo());
        if (StrUtil.isNotBlank(stockoutOrderEntity.getLogisticsNo())) {
            orderInfo.setMappingLogisticsNo(stockoutOrderEntity.getLogisticsNo());
            List<StockoutOrderLabelEntity> labels = labelService.getStockoutOrderLabelEntities(Collections.singletonList(stockoutOrderEntity.getLogisticsNo()));
            if (!CollectionUtils.isEmpty(labels)) {
                orderInfo.setMappingLogisticsNoLabel(labels.get(0).getLabelUrl());
            }
        }
        if (StrUtil.equals(stockoutOrderEntity.getStockoutType(), StockoutOrderTypeEnum.OVERSEA_DELIVERY.name())) {
            BdSpaceEntity spaceServiceById = spaceService.getSpaceByIdValid(stockoutOrderEntity.getSpaceId());
            orderInfo.setSpaceName(spaceServiceById.getSpaceName());
            orderInfo.setOverseaSpace(Boolean.TRUE);
        }
        return orderInfo;
    }

    private void buildShipmentInfo(StockoutOrderEntity stockoutOrderEntity, OrderInfo orderInfo) {
        List<StockoutShipmentEntity> shipments = stockoutShipmentItemService.findByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        if (!CollectionUtils.isEmpty(shipments)) {
            orderInfo.setShipmentTmsModelList(shipments.stream().map(shipment -> {
                StockoutShipmentTmsModel stockoutShipmentTmsModel = new StockoutShipmentTmsModel();
                BeanUtilsEx.copyProperties(shipment, stockoutShipmentTmsModel);
                return stockoutShipmentTmsModel;
            }).collect(Collectors.toList()));
        }
    }

    private long buildBoxCount(List<StockoutOrderItemEntity> list) {
        List<Integer> orderItemIds = list.stream().map(StockoutOrderItemEntity::getStockoutOrderItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemIds))
            return 0L;
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentItemEntity::getStockoutOrderItemId, orderItemIds);
        wrapper.in(StockoutShipmentItemEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        List<StockoutShipmentItemEntity> list1 = stockoutShipmentItemService.list(wrapper);
        if (CollectionUtils.isEmpty(list1))
            return 0L;
        return list1.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().count();
    }

    public List<OrderItemInfo> buildOrderItemInfoList(String customsDeclareType, List<StockoutOrderItemEntity> stockoutOrderItemEntityList, String receiveCountryCode, String logisticsCompany) {
        List<OrderItemInfo> orderItemInfoList = buildOrderItemInfo(stockoutOrderItemEntityList, logisticsCompany);
        reSetUserPrice(orderItemInfoList, receiveCountryCode);
        // todo 是否报关处理
        if ("9610".equals(customsDeclareType)) {
            for (OrderItemInfo orderItemInfo : orderItemInfoList) {
                setCatePriceByWeight(orderItemInfo);
            }
        }
        setWeightByZero(orderItemInfoList, logisticsCompany);
//        List<OrderItemInfo> collect = orderItemInfoList.stream().filter(item -> item.getUnitPrice() == null || item.getUnitPrice() == 0).collect(Collectors.toList());
//        String skus = collect.stream().map(OrderItemInfo::getSku).filter(StringUtils::hasText).distinct().collect(Collectors.joining(","));
//        if (StringUtils.hasText(skus)) {
//            throw new BusinessServiceException(skus + "商品未配置吊牌价");
//        }
        return orderItemInfoList;
    }

    private void setCatePriceByWeight(OrderItemInfo orderItemInfo) {
        Double declarePrice = getDeclarePriceByWeight(orderItemInfo.getCategoryId(), orderItemInfo.getWeight()).doubleValue();
        if (declarePrice > 0) {
            orderItemInfo.setUnitPrice(declarePrice * 0.1);
            orderItemInfo.setCustomsPrice(declarePrice * 0.1);
            orderItemInfo.setCustomsUnitPrice(declarePrice * 0.1);
        }
    }

    public BigDecimal getDeclarePriceByWeight(Integer categoryId, Double weight) {
        QueryWrapper<ProductCategoryCustomsDeclareItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProductCategoryCustomsDeclareItemEntity::getWmsCategoryId, categoryId);
        List<ProductCategoryCustomsDeclareItemEntity> categoryCustomsDeclareItemEntityList = productCategoryCustomsDeclareItemService.list(queryWrapper).stream()
                .filter(t -> t.getMinWeight().doubleValue() <= weight && t.getMaxWeight().doubleValue() >= weight)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(categoryCustomsDeclareItemEntityList)) return BigDecimal.ZERO;
        return categoryCustomsDeclareItemEntityList.get(0).getPrice();
    }

    public List<OrderItemInfo> buildOrderItemInfo(List<StockoutOrderItemEntity> stockoutOrderItemEntityList, String logisticsCompany) {
        List<OrderItemInfo> orderItemInfoList = new ArrayList<>();
        List<Integer> productIdList = stockoutOrderItemEntityList.stream().map(StockoutOrderItemEntity::getProductId).distinct().collect(Collectors.toList());
        List<Integer> specIdList = stockoutOrderItemEntityList.stream().map(StockoutOrderItemEntity::getSpecId).distinct().collect(Collectors.toList());
        List<ProductSpecInfoEntity> productSpecInfoEntityList = productSpecInfoService.findAllBySpecIdIn(specIdList);
        List<ProductInfoEntity> productInfoEntityList = productInfoService.listByProductIdList(productIdList);
        List<String> companyList = logisticsCompanySetting.getLogisticsCompanyListByLogisticsType(LogisticsTypeEnum.DOMESTIC_EXPRESS);
        stockoutOrderItemEntityList.forEach(itemEntity -> {
            OrderItemInfo orderItemInfo = new OrderItemInfo();
            ProductInfoEntity productInfoEntity = productInfoEntityList.stream().filter(t -> t.getProductId().equals(itemEntity.getProductId())).findAny().orElseThrow(() -> new BusinessServiceException(String.format("sku【%s】在商品表不存在", itemEntity.getSku())));
            ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoEntityList.stream().filter(t -> t.getSpecId().equals(itemEntity.getSpecId())).findAny().orElseThrow(() -> new BusinessServiceException(String.format("sku【%s】不存在", itemEntity.getSku())));
            if (!companyList.contains(logisticsCompany)) {
                buildDeclareInfo(orderItemInfo, itemEntity, productInfoEntity, productSpecInfoEntity);
            } else {
                // 内贸不需要有报关等要素
                if (StringUtils.hasText(productInfoEntity.getProductName())) {
                    orderItemInfo.setCnName(productInfoEntity.getProductName());
                } else if (StringUtils.hasText(productInfoEntity.getCategoryName())) {
                    orderItemInfo.setCnName(productInfoEntity.getCategoryName());
                } else if (StringUtils.hasText(productInfoEntity.getWmsCategoryName())) {
                    orderItemInfo.setCnName(productInfoEntity.getWmsCategoryName());
                } else {
                    throw new BusinessServiceException(productInfoEntity.getSpu() + "此商品名称/分类名称不能为空，请维护");
                }
                orderItemInfo.setCustomsPrice(0.1d);
                orderItemInfo.setUnitPrice(0.1d);
                orderItemInfo.setCustomsUnitPrice(0.1d);
            }
            productSpecInfoEntity.setWeight(productSpecInfoEntity.getActualWeight() == null || productSpecInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0
                    ? productSpecInfoEntity.getWeight() : productSpecInfoEntity.getActualWeight());
            if (productSpecInfoEntity.getWeight() == null || productSpecInfoEntity.getWeight().compareTo(BigDecimal.ZERO) == 0) {
                throw new BusinessServiceException(productSpecInfoEntity.getSku() + "此商品重量为空，请先维护");
            }
            BigDecimal totalWeight = productSpecInfoEntity.getWeight().multiply(new BigDecimal(itemEntity.getQty()));
            orderItemInfo.setWeight(totalWeight.divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_DOWN).doubleValue());
            orderItemInfo.setBarcode(itemEntity.getBarcode());
            orderItemInfo.setCount(itemEntity.getQty());
            orderItemInfo.setSku(itemEntity.getSku());
            orderItemInfo.setCategoryName(productInfoEntity.getWmsCategoryName());
            orderItemInfo.setFabricTypeEn(productInfoEntity.getFabricTypeEn());
            orderItemInfo.setFabricType(productInfoEntity.getFabricType());
            ProductWmsCategoryEntity wmsCategory = wmsCategoryService.getByCategoryId(productInfoEntity.getCategoryId());
            orderItemInfo.setCategoryId(wmsCategory.getWmsCategoryId());
            orderItemInfo.setSellerMemo(itemEntity.getSellerMemo());
            orderItemInfo.setSpaceMemo(itemEntity.getSpaceMemo());
            orderItemInfo.setInvoicePrice(itemEntity.getInvoicePrice());
            orderItemInfo.setOid(itemEntity.getOrderItemId());
            orderItemInfo.setStock(stockPrematchInfoService.getAvailableStock(itemEntity));
            orderItemInfoList.add(orderItemInfo);
        });
        return orderItemInfoList;
    }


    public void buildDeclareInfo(OrderItemInfo orderItemInfo, StockoutOrderItemEntity itemEntity, ProductInfoEntity productInfoEntity, ProductSpecInfoEntity productSpecInfoEntity) {
        ProductCustomsDeclareEntity spuDeclare = productDeclareService.findBySpu(productInfoEntity.getSpu());
        ProductCategoryCustomsDeclareEntity categoryCustomsDeclareEntity = productCategoryCustomsDeclareService.getByCategoryId(productInfoEntity.getCategoryId());
        ProductWmsCategoryEntity entity = wmsCategoryService.getById(categoryCustomsDeclareEntity.getWmsCategoryId());
        double userPrice = productSpecInfoEntity.getUserPrice() == null ? 0d : productSpecInfoEntity.getUserPrice().doubleValue() * 0.1;
        double price = productSpecInfoEntity.getPrice() == null ? 0d : productSpecInfoEntity.getPrice().doubleValue() * 0.1;
        double unitPrice = userPrice == 0 ? price : userPrice;
        unitPrice = buildUnitPrice(unitPrice, itemEntity);
        if (unitPrice == 0) {
            unitPrice = 10;
        }
        if (spuDeclare == null) {
            // 可能没有配置商品报关
            spuDeclare = new ProductCustomsDeclareEntity();
        }
        orderItemInfo.setUnitPrice(unitPrice);
        orderItemInfo.setCustomsUnitPrice(unitPrice);
        if (entity != null) {
            buildGender(orderItemInfo, entity);
        }
        orderItemInfo.setCustomsPrice(unitPrice * itemEntity.getQty());
        orderItemInfo.setDescription(entity != null && StringUtils.hasText(entity.getEnglishName()) ? entity.getEnglishName() : StringUtils.hasText(spuDeclare.getCustomsDeclareEn()) ? spuDeclare.getCustomsDeclareEn() : categoryCustomsDeclareEntity.getCustomsDeclareEn());
        orderItemInfo.setHsCode(StringUtils.hasText(spuDeclare.getHsCode()) ? spuDeclare.getHsCode() : categoryCustomsDeclareEntity.getHsCode());
        if (StrUtil.contains(productInfoEntity.getSpinType(), "针织")) {
            orderItemInfo.setEnName("Knitting " + orderItemInfo.getDescription());
        } else if (StrUtil.contains(productInfoEntity.getSpinType(), "梭织")) {
            orderItemInfo.setEnName("Weaving  " + orderItemInfo.getDescription());
        } else {
            orderItemInfo.setEnName(orderItemInfo.getDescription());
        }
        orderItemInfo.setDeclareType(StringUtils.hasText(spuDeclare.getDeclareType()) ? spuDeclare.getDeclareType() : categoryCustomsDeclareEntity.getDeclareType());
        String cnName = StringUtils.hasText(spuDeclare.getCustomsDeclareCn()) ? spuDeclare.getCustomsDeclareCn() : entity == null || !StringUtils.hasText(entity.getName()) ? categoryCustomsDeclareEntity.getCustomsDeclareCn() : entity.getName();
        orderItemInfo.setCnName(StrUtil.isNotBlank(productInfoEntity.getSpinType()) ? productInfoEntity.getSpinType() + cnName : cnName);
        if (!StringUtils.hasText(orderItemInfo.getEnName()) || !StringUtils.hasText(orderItemInfo.getCnName())) {
            throw new BusinessServiceException(String.format("spu: %s,报关英文或中文品名不能为空,请先维护商品报关信息", productInfoEntity.getSpu()));
        }
        if (entity != null && Objects.equals("其他", entity.getEnglishName())) {
            throw new BusinessServiceException(String.format("spu: %s的商品分类【其他】, 请重新指定商品分类", productInfoEntity.getSpu()));
        }
    }

    private void buildGender(OrderItemInfo orderItemInfo, ProductWmsCategoryEntity entity) {
        if (StrUtil.contains(entity.getName(), "男")) {
            orderItemInfo.setUserGenderCn("男士");
            orderItemInfo.setUserGenderEn("Man");
        } else if (StrUtil.contains(entity.getName(), "女")) {
            orderItemInfo.setUserGenderCn("女士");
            orderItemInfo.setUserGenderEn("Woman");
            return;
        }
        List<ProductWmsCategoryEntity> allParentList = wmsCategoryService.findAllParentList(entity.getWmsCategoryId());
        for (ProductWmsCategoryEntity eachParent : allParentList) {
            if (StrUtil.contains(eachParent.getName(), "男")) {
                orderItemInfo.setUserGenderCn("男士");
                orderItemInfo.setUserGenderEn("Man");
                break;
            } else if (StrUtil.contains(eachParent.getName(), "女")) {
                orderItemInfo.setUserGenderCn("女士");
                orderItemInfo.setUserGenderEn("Woman");
                break;
            }
        }
    }

    public double buildUnitPrice(double unitPrice, StockoutOrderItemEntity itemEntity) {
        return unitPrice;
    }

    /**
     * 分拣热敏配货单
     * <p>
     * 构建收件人信息
     *
     * @param receiverInfo
     * @return
     */
    public Address buildReceiver(StockoutReceiverInfo receiverInfo) {
        Address address = new Address();
        address.setName(receiverInfo.getReceiverName());
        address.setPostCode(receiverInfo.getReceiverZip());
        address.setPhone(receiverInfo.getReceiverPhone());
        address.setMobile(receiverInfo.getReceiverMobile());
        address.setCountry(receiverInfo.getReceiverCountry());
        address.setProvince(receiverInfo.getReceiverState());
        address.setCity(receiverInfo.getReceiverCity());
        address.setCounty(receiverInfo.getReceiverDistrict());
        address.setStreet(receiverInfo.getReceiverAddress());
        address.setEmail(receiverInfo.getMail());
        address.setCompany(receiverInfo.getCompanyName());
        if (!StringUtils.hasText(address.getMobile()) && !StringUtils.hasText(address.getPhone())) {
            address.setMobile("1234567890");
            address.setPhone("1234567890");
        }
        if (StrUtil.isNotBlank(receiverInfo.getTaxId())) {
            address.setTaxNumber(receiverInfo.getTaxId());
        }
        processReceiver(address);
        return address;
    }

    /**
     * 构建发件人信息
     *
     * @param shipperInfoEntity
     * @return
     */
    public Address buildSender(StockoutShipperInfoEntity shipperInfoEntity) {
        Address address = new Address();
        address.setName(shipperInfoEntity.getShipperName());
        address.setPostCode(shipperInfoEntity.getShipperZip());
        address.setPhone(shipperInfoEntity.getShipperPhone());
        address.setMobile(shipperInfoEntity.getShipperMobile());
        address.setCountry(shipperInfoEntity.getShipperCountry());
        address.setProvince(shipperInfoEntity.getShipperState());
        address.setCity(shipperInfoEntity.getShipperCity());
        address.setCounty(shipperInfoEntity.getShipperDistrict());
        address.setStreet(shipperInfoEntity.getShipperAddress());
        address.setEmail(shipperInfoEntity.getShipperEmail());
        address.setCompany(shipperInfoEntity.getShipperCompanyName());
        return address;
    }

    /**
     * 个别物流根据件数调整报关价格
     *
     * @param orderItemList
     * @param receiveCountryCode
     */
    public void reSetUserPrice(List<OrderItemInfo> orderItemList, String receiveCountryCode) {
    }

    /**
     * 小包物流重量为0的重新赋值
     *
     * @param orderItemInfoList
     * @param logisticsCompany
     */
    private void setWeightByZero(List<OrderItemInfo> orderItemInfoList, String logisticsCompany) {
        List<OrderItemInfo> tempOrderItemInfoList = orderItemInfoList.stream().filter(t -> t.getWeight() == 0).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(tempOrderItemInfoList)
                && logisticsCompanySetting.getLogisticsCompanyListByLogisticsType(LogisticsTypeEnum.INTERNATIONAL_PACKAGE).contains(logisticsCompany)) {
            tempOrderItemInfoList.forEach(orderItemInfo -> orderItemInfo.setWeight(orderItemInfo.getCount() * 0.2));
        }
    }

    public void reSetUserPriceByTotalNum(List<OrderItemInfo> orderItemInfoList) {
        BigDecimal total;
        if (orderItemInfoList.isEmpty()) {
            return;
        }
        int num = orderItemInfoList.stream().mapToInt(OrderItemInfo::getCount).sum();
        if (num <= 3) {
            total = BigDecimal.valueOf(6);
        } else if (num <= 6) {
            total = BigDecimal.valueOf(14);
        } else {
            total = BigDecimal.valueOf(16);
        }
        Double price = total.divide(BigDecimal.valueOf(num), 2, BigDecimal.ROUND_HALF_UP).doubleValue();
        for (OrderItemInfo orderItemInfo : orderItemInfoList) {
            orderItemInfo.setCustomsUnitPrice(price);
            orderItemInfo.setCustomsPrice(price * orderItemInfo.getCount());
            orderItemInfo.setUnitPrice(price);
        }
    }

    protected BaseGetLogisticsNoResponse specialCheck(StockoutOrderEntity stockoutOrderEntity, String userName) {
        return null;
    }

    public void setLogisticsInfo(GenerateOrderRequest generateOrderRequest) {
    }

    public void checkCategoryName(GenerateOrderRequest generateOrderRequest) {
    }

    public void checkRequestInfo(GenerateOrderRequest generateOrderRequest, StockoutOrderEntity stockoutOrderEntity, String userName) {
    }

    public void processReceiver(Address receiver) {
    }

    public void setCaiNiaoInfo(GenerateOrderRequest generateOrderRequest, List<StockoutOrderItemEntity> stockoutOrderItems) {
    }

    public void processSender(Address sender, String logisticsCompany) {
    }

    public void setIossNumber(GenerateOrderRequest generateOrderRequest, List<StockoutOrderItemEntity> stockoutOrderItemEntityList) {
        String receiveCountryCode = generateOrderRequest.getOrderInfo().getReceiveCountryCode();
        if (CountryCodeConstant.EUROPEAN_UNION_COUNTRY_CODE.contains(receiveCountryCode)) {
            List<String> iossNumberList = stockoutOrderItemEntityList.stream().map(StockoutOrderItemEntity::getIossNumber).distinct().collect(Collectors.toList());
            generateOrderRequest.getOrderInfo().setIossNumber(iossNumberList.isEmpty() ? "" : iossNumberList.get(0));
        }
    }

    public void processDeliver(GenerateOrderResponse generateOrderResponse, StockoutOrderEntity stockoutOrderEntity, String userName) {
    }
}
