package com.nsy.wms.business.service.stockin;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stock.StockInternalBoxOrderInfo;
import com.nsy.api.wms.domain.stockin.StockinReturnProductOrderItem;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnProductOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnProductOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockinReturnProductOrderLogTypeEnum;
import com.nsy.api.wms.request.stockin.StockinReturnProductOrderItemListRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductOrderListRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductRegisterRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductRegisterScanRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductWaitConfirmRequest;
import com.nsy.api.wms.request.stockin.TabCountRequest;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductOrderItemListResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductOrderListResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductOrderRequest;
import com.nsy.api.wms.response.stockin.StockinReturnProductRegisterResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductRegisterSaveRequest;
import com.nsy.api.wms.response.stockin.StockinReturnProductRepeatedRegistResponse;
import com.nsy.api.wms.response.stockin.TaskListCountResponse;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.domain.bo.stock.StockInternalBoxSumQtyByCodeBo;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpReturnProductInfo;
import com.nsy.wms.business.manage.erp.request.ErpReturnProductItemInfo;
import com.nsy.wms.business.manage.erp.request.ErpReturnProductRequest;
import com.nsy.wms.business.manage.erp.response.ErpReturnApplyInfo;
import com.nsy.wms.business.manage.erp.response.ErpReturnApplyInfoResponse;
import com.nsy.wms.business.manage.user.upload.ProductReturnOrderImport;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.GenerateCodeService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stockin.valid.StockinReturnProductOrderValid;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductOrderItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductOrderItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductOrderMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockinReturnProductOrderService extends ServiceImpl<StockinReturnProductOrderMapper, StockinReturnProductOrderEntity> implements IProcessUploadDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockinReturnProductOrderService.class);
    @Autowired
    private StockinReturnProductOrderItemMapper orderItemMapper;
    @Autowired
    private StockinReturnProductOrderItemService orderItemService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockInternalBoxService internalBoxService;
    @Autowired
    private StockInternalBoxItemService internalBoxItemService;
    @Autowired
    private GenerateCodeService generateCodeService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private ErpApiService erpApiService;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    private ExternalApiLogService externalApiLogService;
    @Autowired
    private StockinReturnProductOrderImportService productOrderImportService;
    @Autowired
    StockinReturnProductOrderOriginBackInfoService orderOriginBackInfoService;
    @Autowired
    MessageProducer messageProducer;
    @Resource
    BdSpaceService spaceService;
    @Resource
    StockinReturnProductOrderLogService returnProductOrderLogService;
    @Autowired
    BdErpSpaceMappingService bdErpSpaceMappingService;


    /**
     * 查询退货单列表
     */
    public PageResponse<StockinReturnProductOrderListResponse> orderList(StockinReturnProductOrderListRequest request) {
        Page<StockinReturnProductOrderListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinReturnProductOrderListResponse> pageList = this.baseMapper.getOrderList(page, request);
        PageResponse<StockinReturnProductOrderListResponse> pageResponse = new PageResponse<>();
        Set<String> internalBoxCodeSet = new HashSet<>();
        Map<String, String> stockinSaleReturnStatusMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_SALE_RETURN_STATUS.getName());
        Map<String, String> stockinReturnTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_RETURN_TYPE.getName());
        Map<String, String> stockoutWorkLocationEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName());

        pageList.getRecords().forEach(m -> {
            m.setStatusStr(stockinSaleReturnStatusMap.get(m.getStatus()));
            m.setTypeStr(stockinReturnTypeEnumMap.get(m.getType()));
            if (StringUtils.isEmpty(m.getInternalBoxCode())) {
                // 旧数据没有箱子号
                StockinReturnProductOrderItemEntity itemEntity = orderItemService.getOne(Wrappers.<StockinReturnProductOrderItemEntity>lambdaQuery()
                        .eq(StockinReturnProductOrderItemEntity::getReturnOrderId, m.getReturnOrderId())
                        .last(MybatisQueryConstant.QUERY_FIRST)
                );
                if (!ObjectUtils.isEmpty(itemEntity)) {
                    m.setInternalBoxCode(itemEntity.getInternalBoxCode());
                }
            }

            if (!StringUtils.isEmpty(m.getInternalBoxCode())) {
                // 获取内部箱区域
                StockInternalBoxEntity internalBox = internalBoxService.getOne(Wrappers.<StockInternalBoxEntity>lambdaQuery()
                        .eq(StockInternalBoxEntity::getInternalBoxCode, m.getInternalBoxCode())
                        .last(MybatisQueryConstant.QUERY_FIRST)
                );
                if (!ObjectUtils.isEmpty(internalBox)) {
                    m.setWorkspace(stockoutWorkLocationEnumMap.get(internalBox.getWorkspace()));
                }
            }
            internalBoxCodeSet.add(m.getInternalBoxCode());
        });
        //设置箱内数量
        if (!internalBoxCodeSet.isEmpty()) {
            Map<String, StockInternalBoxSumQtyByCodeBo> sumBoMap = internalBoxItemService.sumQtyByInternalBoxCodeList(new ArrayList<>(internalBoxCodeSet)).stream().collect(Collectors.toMap(StockInternalBoxSumQtyByCodeBo::getInternalBoxCode, Function.identity()));
            pageList.getRecords().forEach(temp -> {
                if (StringUtils.isEmpty(temp.getInternalBoxCode())) return;
                StockInternalBoxSumQtyByCodeBo sumBo = sumBoMap.get(temp.getInternalBoxCode());
                if (Objects.isNull(sumBo)) return;
                temp.setSkuQty(sumBo.getQty());
            });
        }

        pageResponse.setContent(pageList.getRecords());
        pageResponse.setTotalCount(pageList.getTotal());
        return pageResponse;
    }

    public List<TaskListCountResponse> getTaskListCount(TabCountRequest request) {
        Map<String, List<TaskListCountResponse>> collect = this.getBaseMapper().countByStatus(request).stream().collect(Collectors.groupingBy(TaskListCountResponse::getStatus));
        List<TaskListCountResponse> list = Arrays.stream(ReturnProductOrderStatusEnum.values()).map(statusEnum -> {
            TaskListCountResponse response = new TaskListCountResponse();
            List<TaskListCountResponse> responses = collect.get(statusEnum.name());
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.getCode());
            response.setLabel(statusEnum.getValue());
            return response;
        }).collect(Collectors.toList());
        TaskListCountResponse response = new TaskListCountResponse();
        response.setQty(list.stream().mapToInt(TaskListCountResponse::getQty).sum());
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }


    /**
     * 查询退货单信息
     */
    public StockinReturnProductOrderListResponse orderInfo(Integer returnMenuId) {
        StockinReturnProductOrderEntity entity = getById(returnMenuId);
        StockinReturnProductOrderListResponse response = new StockinReturnProductOrderListResponse();
        BeanUtils.copyProperties(entity, response);
        response.setStatus(ReturnProductOrderStatusEnum.getValueByCode(response.getStatus()));
        response.setType(ReturnProductOrderTypeEnum.getValueByCode(response.getType()));
        return response;
    }

    /**
     * 查询退货单详情
     */
    public PageResponse<StockinReturnProductOrderItemListResponse> orderItemList(StockinReturnProductOrderItemListRequest request) {
        Page<StockinReturnProductOrderItemListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinReturnProductOrderItemListResponse> pageList = orderItemMapper.orderItemPage(page, request);
        if (!CollectionUtils.isEmpty(pageList.getRecords())) {
            pageList.getRecords().forEach(item -> item.setSpu(getSpu(item.getSkc())));
        }
        PageResponse<StockinReturnProductOrderItemListResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(pageList.getRecords());
        pageResponse.setTotalCount(pageList.getTotal());
        return pageResponse;
    }


    /**
     * 通过扫描的单号，查询是否有退货单
     *
     * @param scanNumber
     * @return StockinReturnProductMenuEntity
     */
    public List<StockinReturnProductOrderEntity> getReturnProductOrderByScanNumber(String scanNumber) {
        if (StringUtils.isBlank(scanNumber)) return null;
        return list(Wrappers.<StockinReturnProductOrderEntity>lambdaQuery()
                .and(wrapper -> wrapper.eq(StockinReturnProductOrderEntity::getOrderNo, scanNumber).or().eq(StockinReturnProductOrderEntity::getLogisticsNo, scanNumber))
        );
    }

    /**
     * 通过分割skc，取到spu
     */
    public String getSpu(String skc) {
        if (StringUtils.isBlank(skc)) return "";
        int index = skc.indexOf(StringConstant.HORIZONTAL_BAR);
        if (index == -1) return skc;
        return skc.substring(BigDecimal.ZERO.intValue(), index);
    }

    /**
     * 新增
     *
     * @param requestList
     */
    public void save(List<StockinReturnProductRegisterResponse> requestList, Integer spaceId, String spaceName, Boolean shouldSaveItem) {
        requestList.forEach(request -> {
            StockinReturnProductOrderEntity order = new StockinReturnProductOrderEntity();
            request.setStatus(ReturnProductOrderStatusEnum.WAIT_REGISTER.getCode());
            request.setReturnOrderNo(generateCodeService.getReturnOrderNo());
            BeanUtils.copyProperties(request, order);
            BdSpaceEntity spaceEntity = spaceService.getById(order.getSpaceId());
            if (!Objects.isNull(spaceEntity)) {
                order.setSpaceName(spaceEntity.getSpaceName());
            }
            order.setCreateDate(null);
            order.setRegisterBy(loginInfoService.getName());
            order.setCreateBy(loginInfoService.getName());
            order.setSpaceId(spaceId);
            order.setSpaceName(spaceName);
            order.setReturnTypeDesc(request.getReturnTypeDesc());
            save(order);
            request.setReturnOrderId(order.getReturnOrderId());

            if (shouldSaveItem) {
                request.getItemList().forEach(item -> {
                    StockinReturnProductOrderItemEntity itemEntity = new StockinReturnProductOrderItemEntity();
                    BeanUtils.copyProperties(item, itemEntity);
                    itemEntity.setReturnOrderId(order.getReturnOrderId());
                    itemEntity.setId(null);
                    itemEntity.setCreateDate(null);
                    itemEntity.setCreateBy(loginInfoService.getName());
                    orderItemService.save(itemEntity);
                    item.setReturnOrderId(itemEntity.getReturnOrderId());
                    item.setId(itemEntity.getId());
                });
            }

            returnProductOrderLogService.addLog(order.getReturnOrderId(), StockinReturnProductOrderLogTypeEnum.NEW, "创建退货单");
        });
    }

    /**
     * 更新
     *
     * @param saveRequestList
     */
    @Transactional
    public void update(List<StockinReturnProductRegisterSaveRequest> saveRequestList) {
        saveRequestList.forEach(request -> {
            checkLogisticNo(request);
            checkInternalBoxCode(request.getInternalBoxCode());
            StockinReturnProductOrderEntity oldOrder = this.getById(request.getReturnOrderId());
            if (ReturnProductOrderStatusEnum.ALREADY_REGISTER.name().equals(oldOrder.getStatus()))
                throw new BusinessServiceException(String.format("存在已登记退货单 id 【%s】", request.getReturnOrderId()));

            StockinReturnProductOrderEntity order = new StockinReturnProductOrderEntity();
            BeanUtils.copyProperties(request, order, "type");
            order.setStatus(oldOrder.getStatus());
            updateById(order);

            returnProductOrderLogService.addLog(oldOrder.getReturnOrderId(), StockinReturnProductOrderLogTypeEnum.UPDATE, String.format("更新信息，当前状态 %s", ReturnProductOrderStatusEnum.getValueByCode(oldOrder.getStatus())));
        });
    }


    /**
     * 待业务确认
     * <p>
     * 1. 原件退货
     * 2. 待退货
     * 3. 改状态待业务确认
     * 4. 通知商通
     *
     * @param request
     */
    @Transactional
    public void waitForConfirm(StockinReturnProductWaitConfirmRequest request) {
        List<StockinReturnProductOrderEntity> returnOrderList = new ArrayList<>(request.getReturnOrderIdList().size());
        request.getReturnOrderIdList().forEach(returnOrderId -> {
            StockinReturnProductOrderEntity returnOrder = this.getById(returnOrderId);
            if (Objects.isNull(returnOrder))
                throw new BusinessServiceException("退货单不存在");
            if (!ReturnProductOrderTypeEnum.ORIGIN_BACK.getCode().equals(returnOrder.getType()))
                throw new BusinessServiceException("退货单非【原件退回】");
            if (!ReturnProductOrderStatusEnum.WAIT_REGISTER.getCode().equals(returnOrder.getStatus()))
                throw new BusinessServiceException("退货单非【待登记】");

            updateStatus(returnOrder.getReturnOrderId(), ReturnProductOrderStatusEnum.WAIT_CONFIRM);
            returnOrderList.add(this.getById(returnOrderId));
        });

        pushOmsByReturnProduct(returnOrderList, Boolean.FALSE);
    }


    private void checkLogisticNo(StockinReturnProductRegisterSaveRequest request) {
        if (StrUtil.isEmpty(request.getLogisticsNo())) {
            throw new BusinessServiceException("物流单号不能为空");
        }
        // 如果是仓库退货，原件退回，客户申请
        // 则判断物流单号是否修改
        // 如果修改则判断物流单号是否重复
        StockinReturnProductOrderEntity returnProductOrder = this.getById(request.getReturnOrderId());
        if (!ReturnProductOrderTypeEnum.EXCEPTION_RETURN.name().equals(returnProductOrder.getType())
                && request.getLogisticsNo().equals(returnProductOrder.getLogisticsNo())) {
            return;
        }

        LambdaQueryWrapper<StockinReturnProductOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockinReturnProductOrderEntity::getLogisticsNo, request.getLogisticsNo());
        if (!ObjectUtils.isEmpty(request.getReturnOrderId()))
            wrapper.ne(StockinReturnProductOrderEntity::getReturnOrderId, request.getReturnOrderId());

        if (this.count(wrapper) > 0) throw new BusinessServiceException("物流单号不能重复");
    }


    private void checkInternalBoxCode(String internalBoxCode) {
        StockInternalBoxEntity internalBox = internalBoxService.getOne(Wrappers.<StockInternalBoxEntity>lambdaQuery()
                .eq(StockInternalBoxEntity::getInternalBoxCode, internalBoxCode)
                .eq(StockInternalBoxEntity::getInternalBoxType, StockInternalBoxTypeEnum.RETURN_BOX.name())
                .last(MybatisQueryConstant.QUERY_FIRST)
        );
        if (ObjectUtils.isEmpty(internalBox)) throw new BusinessServiceException("查询不到指定的销售退货箱！");
        if (IsDeletedConstant.DELETED.equals(internalBox.getIsDeleted()))
            throw new BusinessServiceException("销售退货箱已经被删除！");
    }

    /**
     * 扫描完成:
     * 原件退回需区分是否业务确认完成
     * step 1: 绑定退货箱 状态-> 装箱中
     * step 2: 退货登记单状态 -> 已登记
     * step 3: 更新退货明细，退货箱编码
     * step 4: 推送OMS系统，进行审核，审核通过财务结算
     * step 5: 推送质检系统，触发箱子中的货物上架
     */
    @Transactional(rollbackFor = Exception.class)
    public void scanFinish(StockinReturnProductRegisterScanRequest request) {
        if (CollectionUtils.isEmpty(request.getSaveRequestList()))
            throw new BusinessServiceException("请求参数不能为空");
        //保存信息
        update(request.getSaveRequestList());
        List<Integer> returnOrderIdList = request.getSaveRequestList().stream().map(StockinReturnProductRegisterSaveRequest::getReturnOrderId).collect(Collectors.toList());

        if (returnOrderIdList.isEmpty()) {
            throw new BusinessServiceException("退货单id不能为空");
        }

        List<StockinReturnProductOrderEntity> oldMenuList = this.listByIds(returnOrderIdList);
        if (oldMenuList.isEmpty())
            throw new BusinessServiceException("查询不到数据，请检查数据是否保存？");

        if (oldMenuList.stream().map(StockinReturnProductOrderEntity::getType).distinct().count() > 1)
            throw new BusinessServiceException("不同退货类型的退货单不能一起登记");

        oldMenuList.forEach(oldMenu -> {
            if (oldMenu.getType().equals(ReturnProductOrderTypeEnum.ORIGIN_BACK.getCode())
                    && !ReturnProductOrderStatusEnum.CONFIRMED.name().equals(oldMenu.getStatus()))
                throw new BusinessServiceException("【原件退回】必须【待入库登记】才可以进行登记");

            if (!oldMenu.getType().equals(ReturnProductOrderTypeEnum.ORIGIN_BACK.getCode())
                    && !ReturnProductOrderStatusEnum.WAIT_REGISTER.name().equals(oldMenu.getStatus())
                    && !ReturnProductOrderStatusEnum.REGISTERING.name().equals(oldMenu.getStatus()))
                throw new BusinessServiceException("必须【待登记】或者【登记中】才可以进行登记");

            // step 1: 完成退货单
            finishReturnOrder(oldMenu);
        });


        List<StockinReturnProductOrderEntity> stockinReturnProductOrderList = listByIds(returnOrderIdList);

        //不推送erp
        if (!request.getPushErp() && loginInfoService.isAdmin())
            return;

        boolean isFinishOriginBack = ReturnProductOrderTypeEnum.ORIGIN_BACK.getCode().equals(stockinReturnProductOrderList.get(0).getType());
        pushOmsByReturnProduct(stockinReturnProductOrderList, isFinishOriginBack);
        if (isFinishOriginBack) {
            LOGGER.info("扫描完成: 推送OMS系统，进行审核，审核通过财务结算： {}", JsonMapper.toJson(stockinReturnProductOrderList));
        } else {
            LOGGER.info("扫描完成: 推送OMS系统，退货登记 加临时仓库存： {}", JsonMapper.toJson(stockinReturnProductOrderList));
        }

    }


    /**
     * 完成退货单
     * step 1: 绑定退货箱 状态-> 装箱中
     * step 2: 退货登记单状态 -> 已登记
     * step 3: 更新退货明细，退货箱编码
     *
     * @param oldMenu 退货单
     */
    private void finishReturnOrder(StockinReturnProductOrderEntity oldMenu) {
        // step 1: 绑定退货箱 状态-> 装箱中
        StockInternalBoxEntity internalBox = bindInternalBox(oldMenu);
        //step 2: 退货登记单状态 -> 已登记
        updateStatus(oldMenu.getReturnOrderId(), ReturnProductOrderStatusEnum.ALREADY_REGISTER);

        // step 3: 更新退货登记单退货箱编码
        orderItemService.update(Wrappers.<StockinReturnProductOrderItemEntity>lambdaUpdate()
                .eq(StockinReturnProductOrderItemEntity::getReturnOrderId, oldMenu.getReturnOrderId())
                .set(StockinReturnProductOrderItemEntity::getInternalBoxCode, internalBox.getInternalBoxCode())
        );
        LOGGER.info("扫描完成：退货登记单状态变为：已登记- {}，绑定退货箱号: {}", ReturnProductOrderStatusEnum.ALREADY_REGISTER.getCode(), internalBox.getInternalBoxCode());
    }

    /**
     * 构建erp退货请求参数
     *
     * @param entityList
     * @param isFinishOriginBack
     * @return
     */
    public ErpReturnProductRequest buildErpReturnProductRequest(List<StockinReturnProductOrderEntity> entityList, Boolean isFinishOriginBack) {
        StockinReturnProductOrderEntity entity = entityList.get(0);
        ErpReturnProductRequest request = new ErpReturnProductRequest();
        request.setUserName(loginInfoService.getName());
        request.setDeliveryLocation(entity.getLocation());
        ErpReturnProductInfo erpReturnProductInfo = buildErpReturnProductInfo(entityList);
        List<ErpReturnProductItemInfo> erpReturnProductItemInfoList = new ArrayList<>();
        List<String> orderNoList = new ArrayList<>(entityList.size());
        List<ErpReturnApplyInfo> erpReturnApplyInfoList = new ArrayList<>();
        for (StockinReturnProductOrderEntity order : entityList) {
            erpReturnProductItemInfoList.addAll(orderItemService.buildErpReturnProductInfoItem(order, order.getReturnOrderId()));
            orderNoList.add(order.getOrderNo());
            if (isFinishOriginBack) {
                erpReturnApplyInfoList.addAll(orderOriginBackInfoService.getByReturnOrderId(order.getReturnOrderId()));
            }
        }
        erpReturnProductInfo.setTid(String.join(",", orderNoList));
        erpReturnProductInfo.setDetails(erpReturnProductItemInfoList);
        request.setReturnBookInInfo(erpReturnProductInfo);
        request.setUserHostAddress(loginInfoService.getIpAddress());
        request.setIsFinishOriginBack(isFinishOriginBack ? 1 : 0);
        request.setApplyInfoList(erpReturnApplyInfoList);

        return request;
    }

    /**
     * 推送退货登记到订单系统
     */
    public void pushOmsByReturnProduct(List<StockinReturnProductOrderEntity> entityList, Boolean isFinishOriginBack) {
        if (entityList.isEmpty()) return;

        ErpReturnProductRequest request = buildErpReturnProductRequest(entityList, isFinishOriginBack);

        ErpReturnApplyInfoResponse response = erpApiService.completeReturnProduct(request);
        if (response != null && !CollectionUtils.isEmpty(response.getApplyInfoList())) {
            // 原件退回 退货申请单id
            orderOriginBackInfoService.addBy(response, entityList);
        }
    }

    private ErpReturnProductInfo buildErpReturnProductInfo(List<StockinReturnProductOrderEntity> orderList) {
        StockinReturnProductOrderEntity entity = orderList.get(0);
        ErpReturnProductInfo returnProductInfo = new ErpReturnProductInfo();
        returnProductInfo.setReturnOrderId(entity.getReturnOrderId());
        returnProductInfo.setLogisticsCompany(entity.getLogisticsCompanyName());
        returnProductInfo.setLogisticsNo(entity.getLogisticsNo());
        returnProductInfo.setStoreId(entity.getStoreId());
        returnProductInfo.setStoreName(entity.getStoreName());
        returnProductInfo.setReturnAddress(entity.getAddress());
        returnProductInfo.setReceiverName(entity.getSender());
        returnProductInfo.setReceiverPhone(entity.getTel());
        returnProductInfo.setPlatTradeRefundId(orderList.stream().filter(temp -> !Objects.isNull(temp.getReturnApplyId())).map(temp -> Integer.toString(temp.getReturnApplyId())).collect(Collectors.joining(",")));
//        returnProductInfo.setSpaceId(Objects.isNull(entity.getSpaceId()) ? Integer.valueOf(0) : entity.getSpaceId());
        returnProductInfo.setSpaceId(bdErpSpaceMappingService.getDefaultEntityBySpaceId(entity.getSpaceId()).getErpSpaceId());
        returnProductInfo.setPlatTradeRefundType(entity.getType());
        returnProductInfo.setIsCheckDeliveryFee(entity.getIsCollected());
        returnProductInfo.setResendPostFee(entity.getBackFee());
        returnProductInfo.setCustomsClearanceFee(entity.getCustomsClearanceFee());
        returnProductInfo.setNote(entity.getRemark());
        return returnProductInfo;
    }

    /**
     * 退货登记完成绑定退货箱
     */
    protected StockInternalBoxEntity bindInternalBox(StockinReturnProductOrderEntity oldMenu) {
        StockInternalBoxEntity internalBox = internalBoxService.getOne(Wrappers.<StockInternalBoxEntity>lambdaQuery()
                .eq(StockInternalBoxEntity::getInternalBoxCode, oldMenu.getInternalBoxCode())
                .eq(StockInternalBoxEntity::getInternalBoxType, StockInternalBoxTypeEnum.RETURN_BOX.name())
                .last(MybatisQueryConstant.QUERY_FIRST)
        );
        if (ObjectUtils.isEmpty(internalBox)) throw new BusinessServiceException("查询不到指定的退货箱！");
        //更新明细内部箱号
        updateItemInternalBoxCode(oldMenu.getReturnOrderId(), oldMenu.getInternalBoxCode());
        // 更新退货箱子状态 -> 装箱中
        internalBoxService.changeStockInternalBoxStatus(internalBox, StockInternalBoxStatusEnum.WAIT_QC.name());

        List<StockInternalBoxItemEntity> boxItemList = internalBoxItemService.list(Wrappers.<StockInternalBoxItemEntity>lambdaQuery().eq(StockInternalBoxItemEntity::getInternalBoxId, internalBox.getInternalBoxId()));
        List<StockinReturnProductOrderItemListResponse> list = orderItemMapper.orderItemList(Collections.singletonList(oldMenu.getReturnOrderId()));
        list.forEach(orderItem -> {
            Optional<StockInternalBoxItemEntity> existBoxItem = boxItemList.stream().filter(boxItem ->
                    com.nsy.api.core.apicore.util.StringUtils.hasText(boxItem.getSku())
                            && com.nsy.api.core.apicore.util.StringUtils.hasText(boxItem.getReturnOrderNo())
                            && boxItem.getSku().equals(orderItem.getSku())
                            && boxItem.getReturnOrderNo().equals(oldMenu.getReturnOrderNo())).findFirst();
            StockInternalBoxItemEntity boxItemEntity;
            // 存在更新
            if (existBoxItem.isPresent()) {
                boxItemEntity = existBoxItem.get();
            } else {
                // 不存在新增
                boxItemEntity = new StockInternalBoxItemEntity();
                boxItemEntity.setReturnOrderNo(oldMenu.getReturnOrderNo());
                boxItemEntity.setProductId(orderItem.getProductId());
                boxItemEntity.setQty(0);
                boxItemEntity.setSku(orderItem.getSku());
                boxItemEntity.setInternalBoxCode(internalBox.getInternalBoxCode());
                boxItemEntity.setInternalBoxId(internalBox.getInternalBoxId());
                boxItemEntity.setLocation(internalBox.getLocation());
                boxItemEntity.setSpaceId(internalBox.getSpaceId());
                boxItemEntity.setSpecId(orderItem.getSpecId());
                boxItemEntity.setCreateBy(loginInfoService.getName());
                boxItemEntity.setStatus(StockInternalBoxStatusEnum.WAIT_QC.name());
            }
            StockInternalBoxOrderInfo orderInfo = new StockInternalBoxOrderInfo();
            orderInfo.setOrderNo(oldMenu.getOrderNo());
            if (orderItem.getRegisterQty() > 0) {
                internalBoxItemService.addStockInternalBoxItemQty(boxItemEntity, orderItem.getRegisterQty(), StockChangeLogTypeEnum.RETURN_PRODUCT_RECEIVE, StockChangeLogTypeModuleEnum.STOCK_IN, orderInfo);
            }
        });
        return internalBox;
    }

    /**
     * 更新明细内部箱号
     *
     * @param returnOrderId
     * @param internalBoxCode
     */
    private void updateItemInternalBoxCode(Integer returnOrderId, String internalBoxCode) {
        if (StrUtil.isEmpty(internalBoxCode)) return;
        orderItemService.update(new LambdaUpdateWrapper<StockinReturnProductOrderItemEntity>()
                .eq(StockinReturnProductOrderItemEntity::getReturnOrderId, returnOrderId)
                .set(StockinReturnProductOrderItemEntity::getInternalBoxCode, internalBoxCode));

    }


    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_RETURN_PRODUCT_ORDER_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (StringUtils.isBlank(request.getDataJsonStr())) return response;
        List<ProductReturnOrderImport> dataList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), ProductReturnOrderImport.class);
        if (CollectionUtils.isEmpty(dataList)) return response;
        productOrderImportService.process(dataList, response, request.getCreateBy(), request.getLocation());
        return response;
    }

    /**
     * 客户申请退货
     */
    @Transactional
    public void saveCustomerApply(StockinReturnProductOrderRequest request) {
        StockinReturnProductOrderValid.validateStockinReturnProductOrderRequest(request);
        StockinReturnProductOrderEntity one = this.getOne(new LambdaQueryWrapper<StockinReturnProductOrderEntity>()
                .eq(StockinReturnProductOrderEntity::getReturnOrderNo, request.getReturnOrderNo()).last("limit 1"));
        if (Objects.nonNull(one)) throw new BusinessServiceException("该退货单号已存在！");
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SYNC_RETURN_APPLY, "/return-product-order/customer-apply",
                JsonMapper.toJson(request), request.getReturnOrderNo(), String.format("OMS退货申请【%s】同步WMS", request.getReturnOrderNo()));
        try {
            StockinReturnProductOrderEntity orderEntity = new StockinReturnProductOrderEntity();
            BeanUtilsEx.copyProperties(request, orderEntity);
            orderEntity.setType(ReturnProductOrderTypeEnum.USER_APPLY.getCode());
            orderEntity.setStatus(ReturnProductOrderStatusEnum.WAIT_REGISTER.getCode());
            this.save(orderEntity);
            List<StockinReturnProductOrderItem> stockinReturnProductOrderItemList = request.getStockinReturnProductOrderItemList();
            List<String> skuList = stockinReturnProductOrderItemList.stream().map(StockinReturnProductOrderItem::getSku).distinct().collect(Collectors.toList());
            List<ProductSpecInfoEntity> specModelList = productSpecInfoService.findAllBySkuIn(skuList);
            List<StockinReturnProductOrderItemEntity> collect = stockinReturnProductOrderItemList.stream().map(item -> {
                ProductSpecInfoEntity specInfoEntity = specModelList.stream().filter(t -> t.getSku().equals(item.getSku())).findFirst().orElseThrow(() -> new BusinessServiceException(String.format("规格编码【%s】未找到", item.getSku())));
                StockinReturnProductOrderItemEntity itemEntity = new StockinReturnProductOrderItemEntity();
                BeanUtilsEx.copyProperties(item, itemEntity);
                itemEntity.setProductId(specInfoEntity.getProductId());
                itemEntity.setSpecId(specInfoEntity.getSpecId());
                itemEntity.setLocation(orderEntity.getLocation());
                itemEntity.setReturnOrderId(orderEntity.getReturnOrderId());
                return itemEntity;
            }).collect(Collectors.toList());
            orderItemService.saveBatch(collect);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(e.getMessage()), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }

    /**
     * 是否允许重复登记
     * 1. 仓库退货
     * 2. 是否存在已登记退货单
     *
     * @param request
     * @return
     */
    public StockinReturnProductRepeatedRegistResponse checkRepeatedRegiste(StockinReturnProductRegisterRequest request) {
        StockinReturnProductRepeatedRegistResponse response = new StockinReturnProductRepeatedRegistResponse();
        if (StringUtils.isEmpty(request.getScanNumber()))
            return response;

        List<StockinReturnProductOrderEntity> orderList = getReturnProductOrderByScanNumber(request.getScanNumber());
        if (Objects.isNull(orderList) || orderList.isEmpty())
            return response;

        List<StockinReturnProductOrderEntity> whsOrderList = orderList.stream().filter(order -> ReturnProductOrderTypeEnum.WHS_RETURN.name().equals(order.getType())).collect(Collectors.toList());
        if (whsOrderList.isEmpty())
            return response;

        boolean allRegisted = whsOrderList.stream().allMatch(order -> ReturnProductOrderStatusEnum.ALREADY_REGISTER.name().equals(order.getStatus()));
        if (!allRegisted)
            return response;

        response.setCanRepeated(Boolean.TRUE);
        return response;
    }

    /**
     * 更新状态
     *
     * @param returnOrderId
     * @param changeStatus
     */
    public void updateStatus(Integer returnOrderId, ReturnProductOrderStatusEnum changeStatus) {

        StockinReturnProductOrderEntity orderEntity = getById(returnOrderId);

        // 退货单 【登记中】
        if (ReturnProductOrderStatusEnum.WAIT_REGISTER.getCode().equals(orderEntity.getStatus())
                && ReturnProductOrderStatusEnum.REGISTERING.getCode().equals(changeStatus.getCode())) {
            orderEntity.setStatus(ReturnProductOrderStatusEnum.REGISTERING.getCode());
            updateById(orderEntity);
            //日志
            returnProductOrderLogService.addLog(orderEntity.getReturnOrderId(), StockinReturnProductOrderLogTypeEnum.STATUS, "【待登记】=》【登记中】");
        } else if (ReturnProductOrderStatusEnum.WAIT_REGISTER.getCode().equals(orderEntity.getStatus())
                && ReturnProductOrderStatusEnum.WAIT_CONFIRM.getCode().equals(changeStatus.getCode())) {
            // step 1: 退货登记单状态 -> 待确认
            orderEntity.setStatus(ReturnProductOrderStatusEnum.WAIT_CONFIRM.getCode());
            orderEntity.setRegisterBy(loginInfoService.getName());
            orderEntity.setRegisterDate(new Date());
            updateById(orderEntity);
            //日志
            returnProductOrderLogService.addLog(orderEntity.getReturnOrderId(), StockinReturnProductOrderLogTypeEnum.STATUS, "【待登记】=》【待业务确认】");
        } else if (ReturnProductOrderTypeEnum.ORIGIN_BACK.getCode().equals(orderEntity.getType())
                && ReturnProductOrderStatusEnum.CONFIRMED.name().equals(orderEntity.getStatus())
                && ReturnProductOrderStatusEnum.ALREADY_REGISTER.getCode().equals(changeStatus.getCode())) {

            // step 2: 退货登记单状态 -> 已登记
            orderEntity.setStatus(ReturnProductOrderStatusEnum.ALREADY_REGISTER.getCode());
            orderEntity.setRegisterBy(loginInfoService.getName());
            orderEntity.setRegisterDate(new Date());
            updateById(orderEntity);
            //日志
            returnProductOrderLogService.addLog(orderEntity.getReturnOrderId(), StockinReturnProductOrderLogTypeEnum.STATUS, "【待入库登记】=》【已登记】");
        } else if (!ReturnProductOrderTypeEnum.ORIGIN_BACK.getCode().equals(orderEntity.getType())
                && (ReturnProductOrderStatusEnum.WAIT_REGISTER.name().equals(orderEntity.getStatus())
                || ReturnProductOrderStatusEnum.REGISTERING.name().equals(orderEntity.getStatus()))
                && ReturnProductOrderStatusEnum.ALREADY_REGISTER.getCode().equals(changeStatus.getCode())) {
            String status = ReturnProductOrderStatusEnum.getValueByCode(orderEntity.getStatus());
            // step 2: 退货登记单状态 -> 已登记
            orderEntity.setStatus(ReturnProductOrderStatusEnum.ALREADY_REGISTER.getCode());
            orderEntity.setRegisterBy(loginInfoService.getName());
            orderEntity.setRegisterDate(new Date());
            updateById(orderEntity);
            //日志
            returnProductOrderLogService.addLog(orderEntity.getReturnOrderId(), StockinReturnProductOrderLogTypeEnum.STATUS, String.format("【%s】=》【已登记】", status));
        }
    }
}
