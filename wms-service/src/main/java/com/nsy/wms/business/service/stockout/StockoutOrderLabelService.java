package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.AmazonBuyShippingInfoEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutDeliveryNoticeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.enumeration.stockout.WaybillSourceEnum;
import com.nsy.api.wms.request.stockout.ForwarderChannelDeliveryRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentUploadLabelRequest;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.api.wms.response.stockout.StockoutOrderLabelResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.service.async.AsyncProcessFlowService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.logistics.base.PrintService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLabelEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderLabelMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockoutOrderLabelService extends ServiceImpl<StockoutOrderLabelMapper, StockoutOrderLabelEntity> {

    @Autowired
    TmsApiService tmsApiService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderLogService logService;
    @Autowired
    PrintService printService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockoutOrderItemService orderItemService;
    @Autowired
    StockoutShipmentItemService itemService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutShipmentConfirmService confirmService;
    @Autowired
    StockoutShipmentErpPickingBoxService erpPickingBoxService;
    @Autowired
    AsyncProcessFlowService asyncProcessFlowService;


    public List<StockoutOrderLabelEntity> listByStockoutOrderId(Integer stockOutOrderId) {
        QueryWrapper<StockoutOrderLabelEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StockoutOrderLabelEntity::getStockoutOrderId, stockOutOrderId).orderByDesc(StockoutOrderLabelEntity::getLabelId);
        return list(queryWrapper);
    }

//    public List<StockoutOrderLabelEntity> listByStockoutOrderIds(List<Integer> stockOutOrderId) {
//        QueryWrapper<StockoutOrderLabelEntity> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().in(StockoutOrderLabelEntity::getStockoutOrderId, stockOutOrderId);
//        return list(queryWrapper);
//    }

    /**
     * 更新打印状态
     *
     * <AUTHOR>
     * 2021-11-04
     */
    public void updatePrintStatus(String logisticsNo) {
        QueryWrapper<StockoutOrderLabelEntity> queryWrapper = new QueryWrapper<>();
        List<StockoutOrderLabelEntity> labelEntityList = list(queryWrapper.lambda().eq(StockoutOrderLabelEntity::getLogisticsNo, logisticsNo)
                .eq(StockoutOrderLabelEntity::getIsPrint, 0));
        if (CollectionUtils.isEmpty(labelEntityList)) {
            return;
        }
        updateBatchById(labelEntityList.stream().peek(labelEntity -> {
            labelEntity.setIsPrint(1);
            labelEntity.setUpdateBy(loginInfoService.getName());
        }).collect(Collectors.toList()));
    }

    /**
     * 更新打印状态
     *
     * <AUTHOR>
     * 2021-11-04
     */
    public void updatePrintStatus(List<String> logisticsNoList) {
        QueryWrapper<StockoutOrderLabelEntity> queryWrapper = new QueryWrapper<>();
        List<StockoutOrderLabelEntity> labelEntityList = list(queryWrapper.lambda().in(StockoutOrderLabelEntity::getLogisticsNo, logisticsNoList)
                .eq(StockoutOrderLabelEntity::getIsPrint, 0));
        if (CollectionUtils.isEmpty(labelEntityList)) {
            return;
        }
        updateBatchById(labelEntityList.stream().peek(labelEntity -> {
            labelEntity.setIsPrint(1);
            labelEntity.setUpdateBy(loginInfoService.getName());
        }).collect(Collectors.toList()));
    }

    // 统一先查label表，然后再去tms查
    public List<StockoutOrderLabelEntity> listByLogisticsNo(List<String> logisticsNo) {
        LambdaQueryWrapper<StockoutOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutOrderEntity::getLogisticsNo, logisticsNo);
        List<StockoutOrderEntity> list = stockoutOrderService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            // 找不到对应的出库单，直接查label表
            return getStockoutOrderLabelEntities(logisticsNo);
        }
        List<StockoutOrderLabelEntity> resultList = new ArrayList<>();
        list.forEach(it -> {
            LambdaQueryWrapper<StockoutOrderLabelEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StockoutOrderLabelEntity::getLogisticsNo, it.getLogisticsNo())
                    .eq(StockoutOrderLabelEntity::getStockoutOrderId, it.getStockoutOrderId());
            List<StockoutOrderLabelEntity> allLabel = list(queryWrapper);
            if (CollectionUtils.isEmpty(allLabel)) {
                // 去tms重新获取面单
                printService.getTmsLabel(it.getStockoutOrderNo(), it.getLogisticsNo());
                LambdaQueryWrapper<StockoutOrderLabelEntity> wrapperAgain = new LambdaQueryWrapper<>();
                wrapperAgain.eq(StockoutOrderLabelEntity::getLogisticsNo, it.getLogisticsNo())
                        .eq(StockoutOrderLabelEntity::getStockoutOrderId, it.getStockoutOrderId());
                List<StockoutOrderLabelEntity> again = list(wrapperAgain);
                // 还是不存在，则直接报错
                if (CollectionUtils.isEmpty(again)) {
                    throw new BusinessServiceException(it.getStockoutOrderNo() + "该出库单无法正常获取面单，建议切换物流");
                }
                resultList.addAll(again);
            } else {
                resultList.addAll(allLabel);
            }
        });
        return resultList;
    }

    public List<StockoutOrderLabelEntity> getStockoutOrderLabelEntities(List<String> logisticsNo) {
        QueryWrapper<StockoutOrderLabelEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(StockoutOrderLabelEntity::getLogisticsNo, logisticsNo).orderByDesc(StockoutOrderLabelEntity::getLabelId);
        return list(queryWrapper);
    }

    // 装箱完之后打印，获取的物流单号
    public StockoutOrderLabelResponse getLogisticsDetail(String logisticsNo) {
        StockoutOrderLabelResponse response = new StockoutOrderLabelResponse();
        if (!StringUtils.hasText(logisticsNo)) {
            return response;
        }
        List<StockoutOrderLabelEntity> stockoutOrderLabelEntities = listByLogisticsNo(Collections.singletonList(logisticsNo));
        if (stockoutOrderLabelEntities.isEmpty()) {
            throw new BusinessServiceException(logisticsNo + "物流单号没有找到面单信息");
        }
        BeanUtils.copyProperties(stockoutOrderLabelEntities.get(0), response);
        return response;
    }


    /**
     * 小包获取面单
     *
     * <AUTHOR>
     * 2022-05-09
     */
    @Transactional
    public PrintListResponse printByStockOutOrderNo(List<String> stringList) {
        PrintListResponse response = new PrintListResponse();
        List<String> htmlList = stringList.stream().distinct().map(item -> {
            StockoutOrderEntity order = stockoutOrderService.getByStockoutOrderNo(item);
            if (LogisticsCompanyConstant.INTERNATIONAL_EXPRESS.contains(order.getLogisticsCompany())) {
                return "";
            }
            if (!StringUtils.hasText(order.getLogisticsNo())) {
                // 重新获取面单
                BaseGetLogisticsNoResponse response1 = printService.generateLabelByStockoutOrder(order, loginInfoService.getName(), "装箱清单 小包打印");
                // 出库单的物流单号赋值
                if (!org.springframework.util.StringUtils.hasText(response1.getLogisticsNo()))
                    throw new BusinessServiceException("TMS返回空物流单号");
                order.setLogisticsNo(response1.getLogisticsNo());
                order.setUpdateBy(loginInfoService.getName());
                order.setSecondaryNumber(response1.getSecondaryNumber());
                order.setAmazonBuyShippingInfo(1 == response1.getAmazonBuyShippingFlag() ? AmazonBuyShippingInfoEnum.ENABLE.getValue() : AmazonBuyShippingInfoEnum.DISABLE.getValue());
                stockoutOrderService.updateById(order);
                List<StockoutShipmentEntity> shipmentByStockoutOrderNo = itemService.getShipmentByStockoutOrderNo(item);
                shipmentByStockoutOrderNo.forEach(shipment -> {
                    shipment.setLogisticsNo(response1.getLogisticsNo());
                    shipment.setUpdateBy(loginInfoService.getName());
                    shipmentService.updateById(shipment);
                });
                stockoutOrderService.syncErpLogistics(order);
            }
            List<StockoutOrderLabelEntity> stockoutOrderLabelEntities = listByLogisticsNo(Collections.singletonList(order.getLogisticsNo()));
            if (stockoutOrderLabelEntities.isEmpty()) {
                throw new BusinessServiceException(order.getStockoutOrderNo() + "物流单号没有找到面单信息");
            }
            updatePrintStatus(order.getLogisticsNo());
            if (!StringUtils.hasText(stockoutOrderLabelEntities.get(0).getLabelUrl())) {
                response.setErrorPrintMsg(response.getErrorPrintMsg() + item + "菜鸟/内贸面单请到装箱清单进行打印");
                return "";
            }
            return stockoutOrderLabelEntities.get(0).getLabelUrl();
        }).filter(StringUtils::hasText).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(htmlList)) {
            throw new BusinessServiceException("暂无要打印的单据");
        }
        response.setHtmlList(htmlList);
        return response;
    }

    public List<StockoutOrderLabelResponse> printCNByStockOutOrderNo(List<String> stringList) {
        LambdaQueryWrapper<StockoutOrderLabelEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutOrderLabelEntity::getLogisticsNo, stringList);
        List<StockoutOrderLabelEntity> list = list(queryWrapper);
        return list.stream().map(one -> {
            StockoutOrderLabelResponse response = new StockoutOrderLabelResponse();
            BeanUtils.copyProperties(one, response);
            // 更新打印信息
            updatePrintStatus(one.getLogisticsNo());
            return response;
        }).collect(Collectors.toList());
    }

    public List<String> reGetLabel(List<String> stringList, Boolean forceReGetLabel) {
        List<String> result = new ArrayList<>();
        stringList.stream().distinct().forEach(item -> {
            StockoutOrderEntity order = stockoutOrderService.getByStockoutOrderNo(item);
            if (forceReGetLabel || !StringUtils.hasText(order.getLogisticsNo())) {
                try {
                    rePostTms(order);
                } catch (Exception e) {
                    result.add("出库单号:" + item + "【" + e.getMessage() + "】");
                }
            }
        });
        return result;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void rePostTms(StockoutOrderEntity order) {
        BaseGetLogisticsNoResponse response1 = printService.generateLabelByStockoutOrder(order, loginInfoService.getName(), "装箱清单-重新请求面单");
        // 出库单的物流单号赋值
        if (!org.springframework.util.StringUtils.hasText(response1.getLogisticsNo()))
            throw new BusinessServiceException("TMS返回空物流单号");
        order.setLogisticsNo(response1.getLogisticsNo());
        order.setUpdateBy(loginInfoService.getName());
        order.setSecondaryNumber(response1.getSecondaryNumber());
        stockoutOrderService.updateById(order);
        logService.addLog(order.getStockoutOrderNo(), StockoutOrderLogTypeEnum.GET_LABEL, String.format("出库单【%s】rePostTms: 重新请求面单", order.getStockoutOrderNo()));
        List<StockoutShipmentEntity> shipmentByStockoutOrderNo = itemService.getShipmentByStockoutOrderNo(order.getStockoutOrderNo());
        shipmentByStockoutOrderNo.forEach(shipment -> {
            shipment.setLogisticsNo(response1.getLogisticsNo());
            shipment.setUpdateBy(loginInfoService.getName());
            shipmentService.updateById(shipment);
        });
    }

    public PrintListResponse smallPackageRePrint(List<Integer> shipmentIds) {
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        String name = loginInfoService.getName();
        shipmentIds.forEach(shipmentId -> {
            // 校验是否是小包
            List<String> stockoutOrderNos = itemService.getStockoutOrderNoByShipmentId(shipmentId);
            if (CollectionUtils.isEmpty(stockoutOrderNos)) {
                throw new BusinessServiceException("空箱/无出库单号无法打印");
            }
            List<StockoutOrderEntity> orderEntityList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos);
            if (CollectionUtils.isEmpty(orderEntityList)) {
                throw new BusinessServiceException("无法找到对应的出库单");
            }
            StockoutShipmentEntity shipment = shipmentService.getById(shipmentId);
            shipment.setLogisticsNo(null);
            BaseGetLogisticsNoResponse resp = printService.generateLabelByStockoutShipment(Collections.singletonList(shipment), name, "装箱清单-小包重新打印");
            List<String> logisticsNos = orderEntityList.stream().map(StockoutOrderEntity::getLogisticsNo).distinct().filter(StringUtils::hasText).collect(Collectors.toList());
            int count;
            if (!CollectionUtils.isEmpty(logisticsNos)) {
                LambdaQueryWrapper<StockoutShipmentEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.in(StockoutShipmentEntity::getLogisticsNo, logisticsNos).eq(StockoutShipmentEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
                count = shipmentService.count(wrapper);
            } else {
                count = 0;
            }
            List<StockoutShipmentItemEntity> itemByStockoutOrderNo = itemService.findItemByStockoutOrderNo(stockoutOrderNos);
            if (count == 0 || itemByStockoutOrderNo.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().count() == 1) {
                orderEntityList.forEach(orderEntity -> orderEntity.setLogisticsNo(resp.getLogisticsNo()));
                stockoutOrderService.updateBatchById(orderEntityList);
            }
            if (StockoutShipmentStatusEnum.SHIPPED.name().equals(shipment.getStatus()))
                erpPickingBoxService.sendErpPickingBoxShippedSync(shipment);
            StockoutOrderLabelResponse logisticsDetail = getLogisticsDetail(resp.getLogisticsNo());
            response.getHtmlList().add(logisticsDetail.getLabelUrl());
        });
        return response;
    }

    @Transactional
    public PrintListResponse printLabelByShipment(ForwarderChannelDeliveryRequest request) {
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        Map<Integer, List<String>> shipmentOrderMap = new HashMap<>();
        List<StockoutShipmentEntity> stockoutShipmentEntities = validShipment(request, shipmentOrderMap);
        List<StockoutShipmentEntity> shipmentList = new ArrayList<>();
        stockoutShipmentEntities.forEach(shipment -> {
            shipment.setLogisticsCompany(request.getLogisticsCompany());
            shipment.setForwarderChannel(request.getForwarderChannel());
            shipment.setLogisticsNo(null);
            BaseGetLogisticsNoResponse resp = printService.generateLabelByStockoutShipment(Collections.singletonList(shipment), loginInfoService.getName(), "货代发货打印");
            StockoutOrderLabelResponse logisticsDetail = getLogisticsDetail(resp.getLogisticsNo());
            response.getHtmlList().add(logisticsDetail.getLabelUrl());
            shipmentList.add(shipment);
        });
        erpPickingBoxService.sendErpPickingBoxSyncRequestByShipmentList(shipmentList);
        request.getShipmentIds().forEach(shipmentId -> {
            List<String> stockoutOrderNos = shipmentOrderMap.get(shipmentId);
            StockoutShipmentConfirmUpdateRequest req = new StockoutShipmentConfirmUpdateRequest();
            req.setLogisticsCompany(request.getLogisticsCompany());
            req.setShipmentIds(Collections.singletonList(shipmentId));
            req.setForwarderChannel(request.getForwarderChannel());
            req.setStockoutOrderNos(stockoutOrderNos);
            req.setDeliveryDate(new Date());
            confirmService.updateShipmentConfirm(req);
        });
        return response;
    }

    private List<StockoutShipmentEntity> validShipment(ForwarderChannelDeliveryRequest request, Map<Integer, List<String>> shipmentOrderMap) {
        if (!Objects.equals(request.getLogisticsCompany(), LogisticsCompanyConstant.UPS_EC))
            throw new BusinessServiceException("目前只有UPS经济能通过货代发货，请选择UPS经济");
        List<StockoutShipmentItemEntity> items = itemService.findByShipmentIdList(request.getShipmentIds());
        if (CollectionUtils.isEmpty(items))
            throw new BusinessServiceException("空箱无法发货");
        List<String> stockoutOrderNoItem = items.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).collect(Collectors.toList());
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNoItem);
        wrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        List<StockoutShipmentItemEntity> list = itemService.list(wrapper);
        list.stream().filter(it -> !request.getShipmentIds().contains(it.getShipmentId())).findFirst().ifPresent(it -> {
            throw new BusinessServiceException(it.getStockoutOrderNo() + "此出库单有多个箱子或者混装，请核对");
        });
        List<StockoutShipmentEntity> stockoutShipmentEntities = shipmentService.listByIds(request.getShipmentIds());
        stockoutShipmentEntities.forEach(shipment -> {
            if (shipment.getWeight() == null)
                throw new BusinessServiceException(shipment.getShipmentBoxCode() + "该箱子没有重量，请填写");
            if (!StringUtils.hasText(shipment.getBoxSize()))
                throw new BusinessServiceException(shipment.getShipmentBoxCode() + "该箱子没有箱规，请填写");
            List<String> stockoutOrderNos = itemService.getStockoutOrderNoByShipmentId(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(stockoutOrderNos))
                throw new BusinessServiceException("空箱/无出库单号无法打印");
            shipmentOrderMap.put(shipment.getShipmentId(), stockoutOrderNos);
            List<StockoutOrderEntity> orderEntityList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos);
            if (CollectionUtils.isEmpty(orderEntityList))
                throw new BusinessServiceException(shipment.getShipmentBoxCode() + "无法找到对应的出库单");
            orderEntityList.stream().filter(order -> StockoutDeliveryNoticeEnum.WAIT_NOTIC_DELIVERY.name().equals(order.getNotifyShipStatus())).findFirst().ifPresent(order -> {
                throw new BusinessServiceException(order.getStockoutOrderNo() + "等通知发货!");
            });
        });
        return stockoutShipmentEntities;
    }

    @Transactional
    @JLock(keyConstant = "asyncGetChannelLabel", lockKey = "#request.shipmentIdString")
    public AsyncProcessFlowResult asyncGetChannelLabel(ForwarderChannelDeliveryRequest request) {
        LocationWrapperMessage<ForwarderChannelDeliveryRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request);
        return asyncProcessFlowService.createFlow(KafkaConstant.STOCKOUT_CHANNEL_PRINT_TOPIC, KafkaConstant.STOCKOUT_CHANNEL_PRINT_MARK, message, request.getShipmentIdString());
    }


    @Transactional
    public void uploadLabel(StockoutShipmentUploadLabelRequest request) {
        StockoutOrderEntity order = stockoutOrderService.getByStockoutOrderNo(request.getStockoutOrderNo());
        StockoutShipmentEntity shipment = shipmentService.getById(request.getShipmentId());
        if (StringUtils.hasText(request.getLogisticsNo())) {
            shipment.setLogisticsNo(request.getLogisticsNo());
            shipment.setUpdateBy(loginInfoService.getName());
            shipmentService.updateById(shipment);
        } else {
            if (!StringUtils.hasText(shipment.getLogisticsNo())) {
                throw new BusinessServiceException("勾选的装箱清单没有物流单号，请先保存单号");
            }
            request.setLogisticsNo(shipment.getLogisticsNo());
        }
        StockoutOrderLabelEntity entity = new StockoutOrderLabelEntity();
        entity.setLabelUrl(request.getLabelUrl());
        entity.setStockoutOrderId(order.getStockoutOrderId());
        entity.setCreateBy(loginInfoService.getName());
        entity.setLogisticsNo(request.getLogisticsNo());
        entity.setSource(WaybillSourceEnum.EXTERNAL_IMPORT.name());
        save(entity);
    }
}
