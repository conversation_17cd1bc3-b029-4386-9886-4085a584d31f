package com.nsy.wms.business.domain.bo.esign;

import java.math.BigDecimal;

/**
 * 查询文件上传状态
 */
public class ESignGetFilesResponse {
    String fileId;

    String fileName;

    Integer fileSize;

    Integer fileStatus;

    String fileDownloadUrl;

    Integer fileTotalPageCount;

    BigDecimal pageWidth;

    BigDecimal pageHeight;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getFileSize() {
        return fileSize;
    }

    public void setFileSize(Integer fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(Integer fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getFileDownloadUrl() {
        return fileDownloadUrl;
    }

    public void setFileDownloadUrl(String fileDownloadUrl) {
        this.fileDownloadUrl = fileDownloadUrl;
    }

    public Integer getFileTotalPageCount() {
        return fileTotalPageCount;
    }

    public void setFileTotalPageCount(Integer fileTotalPageCount) {
        this.fileTotalPageCount = fileTotalPageCount;
    }

    public BigDecimal getPageWidth() {
        return pageWidth;
    }

    public void setPageWidth(BigDecimal pageWidth) {
        this.pageWidth = pageWidth;
    }

    public BigDecimal getPageHeight() {
        return pageHeight;
    }

    public void setPageHeight(BigDecimal pageHeight) {
        this.pageHeight = pageHeight;
    }
}
