package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.request.qa.StockinQaStaffConfigPageRequest;
import com.nsy.api.wms.request.qa.StockinQaStaffConfigSaveRequest;
import com.nsy.api.wms.request.qa.StockinQaStaffConfigUpdateRequest;
import com.nsy.api.wms.request.qa.StockinQaStaffConfigWorkStatusChangeRequest;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.qa.StockinQaStaffConfigDetailResponse;
import com.nsy.api.wms.response.qa.StockinQaStaffConfigPageResponse;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserDepartmentInfoResponse;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.manage.user.upload.StockinQaStaffConfigImport;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductCategoryService;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.repository.entity.product.ProductCategoryEntity;
import com.nsy.wms.repository.entity.qa.StockinQaStaffConfigEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaStaffConfigMapper;
import com.nsy.wms.utils.JsonMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检人员品类配置业务实现
 * @date: 2024-11-13 10:09
 */
@Service
public class StockinQaStaffConfigService extends ServiceImpl<StockinQaStaffConfigMapper, StockinQaStaffConfigEntity> implements IProcessUploadDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinQaStaffConfigService.class);

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private ProductCategoryService categoryService;

    /**
     * 查询分页
     *
     * @param request
     * @return
     */
    public PageResponse<StockinQaStaffConfigPageResponse> pageList(StockinQaStaffConfigPageRequest request) {
        Page<StockinQaStaffConfigEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        LambdaQueryWrapper<StockinQaStaffConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        this.buildGroupUserInfo(request);
        queryWrapper.eq(Objects.nonNull(request.getUserId()), StockinQaStaffConfigEntity::getUserId, request.getUserId());
        queryWrapper.like(Objects.nonNull(request.getUserName()), StockinQaStaffConfigEntity::getUserName, request.getUserName());
        queryWrapper.like(Objects.nonNull(request.getUserCode()), StockinQaStaffConfigEntity::getUserCode, request.getUserCode());
        queryWrapper.eq(Objects.nonNull(request.getIsWork()), StockinQaStaffConfigEntity::getIsWork, request.getIsWork());
        queryWrapper.eq(Objects.nonNull(request.getMajorCategoryId()), StockinQaStaffConfigEntity::getMajorCategoryId, request.getMajorCategoryId());
        queryWrapper.eq(Objects.nonNull(request.getMinorCategoryId()), StockinQaStaffConfigEntity::getMinorCategoryId, request.getMinorCategoryId());
        queryWrapper.in(!CollectionUtils.isEmpty(request.getGroupUserIdList()), StockinQaStaffConfigEntity::getUserId, request.getGroupUserIdList());
        queryWrapper.orderByDesc(StockinQaStaffConfigEntity::getCreateDate);

        Page<StockinQaStaffConfigEntity> pageInfo = this.baseMapper.selectPage(page, queryWrapper);
        PageResponse<StockinQaStaffConfigPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setTotalCount(pageInfo.getTotal());
        if (CollectionUtil.isEmpty(pageInfo.getRecords())) {
            pageResponse.setContent(Collections.emptyList());
            return pageResponse;
        }
        List<SysUserDepartmentInfoResponse> userInfoList = userApiService.getSupplierSpecProductPurchaseList(pageInfo.getRecords().stream().map(StockinQaStaffConfigEntity::getUserId).collect(Collectors.toList()));
        Map<Integer, SysUserDepartmentInfoResponse> userMap = userInfoList.stream().filter(detail -> Objects.nonNull(detail.getUserId())).collect(Collectors.toMap(SysUserDepartmentInfoResponse::getUserId, Function.identity(), (v1, v2) -> v1));
        List<StockinQaStaffConfigPageResponse> responseList = new ArrayList<>();
        pageInfo.getRecords().forEach(detail -> {
            StockinQaStaffConfigPageResponse info = new StockinQaStaffConfigPageResponse();
            BeanUtils.copyProperties(detail, info);
            responseList.add(info);
            if (CollectionUtils.isEmpty(userMap) || !userMap.containsKey(detail.getUserId())) {
                return;
            }
            info.setStatus(userMap.get(detail.getUserId()).getStatus());
            info.setDepartmentName(userMap.get(detail.getUserId()).getDepartmentName());
        });
        pageResponse.setContent(responseList);
        return pageResponse;
    }

    /**
     * 保存配置
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveConfig(StockinQaStaffConfigSaveRequest request) {
        SysUserInfo userInfo = userApiService.getUserInfoByUserAccount(request.getUserCode());
        if (Objects.isNull(userInfo)) {
            throw new BusinessServiceException("用户不存在");
        }
        if (Objects.nonNull(this.getByUserId(userInfo.getUserId()))) {
            throw new BusinessServiceException("该用户已存在!");
        }
        StockinQaStaffConfigEntity entity = new StockinQaStaffConfigEntity();
        entity.setUserId(userInfo.getUserId());
        entity.setUserName(userInfo.getUserName());
        ProductCategoryEntity categoryEntity = categoryService.getById(request.getMajorCategoryId());
        entity.setMajorCategoryName(categoryEntity.getCategoryName());
        if (Objects.nonNull(request.getMinorCategoryId())) {
            ProductCategoryEntity minorCategory = categoryService.getById(request.getMinorCategoryId());
            entity.setMinorCategoryName(minorCategory.getCategoryName());
        }
        BeanUtils.copyProperties(request, entity);
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        this.save(entity);
    }

    /**
     * 更新配置
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateConfig(StockinQaStaffConfigUpdateRequest request) {
        StockinQaStaffConfigEntity entity = this.getById(request.getId());
        if (entity == null) {
            throw new BusinessServiceException("配置不存在");
        }
        ProductCategoryEntity categoryEntity = categoryService.getById(request.getMajorCategoryId());
        entity.setMajorCategoryId(request.getMajorCategoryId());
        entity.setIsWork(request.getIsWork());
        entity.setMajorCategoryName(categoryEntity.getCategoryName());
        if (Objects.nonNull(request.getMinorCategoryId())) {
            entity.setMinorCategoryId(request.getMinorCategoryId());
            ProductCategoryEntity minorCategory = categoryService.getById(request.getMinorCategoryId());
            entity.setMinorCategoryName(minorCategory.getCategoryName());
        }
        // 设置更新人
        entity.setUpdateBy(loginInfoService.getName());
        this.baseMapper.updateConfig(entity);
    }

    /**
     * 删除配置
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteConfig(Integer id) {
        StockinQaStaffConfigEntity entity = this.getById(id);
        if (entity == null) {
            throw new BusinessServiceException("配置不存在");
        }
        this.removeById(id);
    }

    /**
     * 获取详情
     *
     * @param id
     * @return
     */
    public StockinQaStaffConfigDetailResponse getDetail(Integer id) {
        StockinQaStaffConfigEntity entity = this.getById(id);
        if (entity == null) {
            throw new BusinessServiceException("配置不存在");
        }
        StockinQaStaffConfigDetailResponse response = new StockinQaStaffConfigDetailResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }

    /**
     * 根据用户ID查询配置
     *
     * @param userId
     * @return
     */
    public StockinQaStaffConfigEntity getByUserId(Integer userId) {
        LambdaQueryWrapper<StockinQaStaffConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaStaffConfigEntity::getUserId, userId);
        return this.getOne(queryWrapper);
    }

    /**
     * 获取今日在职人员
     *
     * @return
     */
    public List<StockinQaStaffConfigEntity> listWorkStaff() {
        LambdaQueryWrapper<StockinQaStaffConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaStaffConfigEntity::getIsWork, 1);
        return this.list(queryWrapper);
    }

    /**
     * 更新上班状态
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateWorkStatus(StockinQaStaffConfigWorkStatusChangeRequest request) {
        List<StockinQaStaffConfigEntity> entityList = this.listByIds(request.getIdList());
        if (CollectionUtils.isEmpty(entityList)) {
            throw new BusinessServiceException("用户配置不存在");
        }
        entityList.forEach(entity -> {
            entity.setIsWork(request.getIsWork());
            entity.setUpdateBy(loginInfoService.getName());
        });

        this.updateBatchById(entityList);
    }

    private void buildGroupUserInfo(StockinQaStaffConfigPageRequest request) {
        if (Objects.isNull(request)) {
            return;
        }
        List<Integer> groupIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(request.getGroupIdList())) {
            groupIdList.addAll(request.getGroupIdList());
        }
        if (!Objects.isNull(request.getGroupId())) {
            groupIdList.add(request.getGroupId());
        }
        //没有分组则不处理
        if (CollectionUtils.isEmpty(groupIdList)) {
            return;
        }
        List<SysUserInfo> userInfoList = userApiService.getUserByDepartmentId(groupIdList);
        //分组下没有人员不处理
        if (CollectionUtils.isEmpty(userInfoList)) {
            return;
        }
        request.setGroupUserIdList(userInfoList.stream().map(SysUserInfo::getUserId).distinct().collect(Collectors.toList()));
    }

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_STOCKIN_QA_STAFF_CONFIG_IMPORT;
    }


    /**
     * 导入当日上班情况
     *
     * @param request
     * @return
     */
    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        LOGGER.info("开始导入人员出勤情况");
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<StockinQaStaffConfigImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), StockinQaStaffConfigImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        List<String> userCodeList = importList.stream().filter(spaceImport -> StringUtils.hasText(spaceImport.getUserCode())).map(StockinQaStaffConfigImport::getUserCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userCodeList)) {
            LOGGER.info("导入的用户信息为空");
            return response;
        }
        LambdaQueryWrapper<StockinQaStaffConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockinQaStaffConfigEntity::getUserCode, userCodeList);
        List<StockinQaStaffConfigEntity> configEntityList = this.list(queryWrapper);
        Map<String, StockinQaStaffConfigEntity> userMap = configEntityList.stream().collect(Collectors.toMap(StockinQaStaffConfigEntity::getUserCode, Function.identity(), (v1, v2) -> v1));
        List<StockinQaStaffConfigEntity> updateList = new ArrayList<>();
        List<StockinQaStaffConfigImport> errorList = new ArrayList<>();
        importList.forEach(spaceImport -> {
            if (!userMap.containsKey(spaceImport.getUserCode())) {
                spaceImport.setErrorMsg("当前用户不存在或未配置负责品类信息!");
                errorList.add(spaceImport);
                return;
            }
            StockinQaStaffConfigEntity stockinQaStaffConfigEntity = userMap.get(spaceImport.getUserCode());
            stockinQaStaffConfigEntity.setIsWork(!StringUtils.hasText(spaceImport.getAttendanceFlag()) ? 0 : "是".equalsIgnoreCase(spaceImport.getAttendanceFlag()) ? 1 : 0);
            updateList.add(stockinQaStaffConfigEntity);
        });
        if (!CollectionUtils.isEmpty(errorList)) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        return response;
    }
} 