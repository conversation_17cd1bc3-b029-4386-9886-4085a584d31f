package com.nsy.wms.business.service.stockin;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.wms.enumeration.stockin.StockinShelveTaskStatusEnum;
import com.nsy.api.wms.request.stockin.UpShelveRequest;
import com.nsy.api.wms.response.stockin.UpShelvesResponse;
import com.nsy.wms.business.manage.user.upload.StockInShelveTaskItemImport;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockInShelveTaskItemImportSaveService {

    @Autowired
    ShelvePdaService shelvePdaService;
    @Autowired
    StockinShelveTaskService stockinShelveTaskService;

    public List<StockInShelveTaskItemImport> importItem(StockinShelveTaskEntity taskEntity, List<StockInShelveTaskItemImport> stockInShelveTaskItemImportList, String createBy) {
        taskEntity.setStatus(StockinShelveTaskStatusEnum.SHELVING.name());
        if (Objects.isNull(taskEntity.getOperateStartDate()))
            taskEntity.setOperateStartDate(new Date());
        taskEntity.setOperator(createBy);
        stockinShelveTaskService.updateById(taskEntity);
        List<StockInShelveTaskItemImport> errorList = new ArrayList<>();
        for (StockInShelveTaskItemImport itemImport : stockInShelveTaskItemImportList) {
            try {


                UpShelveRequest upShelveRequest = new UpShelveRequest();
                upShelveRequest.setInternalBoxCode(taskEntity.getInternalBoxCode());
                upShelveRequest.setPosition(itemImport.getPositionCode());
                upShelveRequest.setQty(itemImport.getQty());
                upShelveRequest.setSku(itemImport.getSku());
                UpShelvesResponse upShelvesResponse = shelvePdaService.upShelve(upShelveRequest);
                if (!"上架成功".equals(upShelvesResponse.getMessage())) {
                    throw new BusinessServiceException(upShelvesResponse.getMessage());
                }
            } catch (Exception e) {
                itemImport.setErrorMsg(e.getMessage());
                errorList.add(itemImport);
            }
        }

        return errorList;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void importAll(List<StockInShelveTaskItemImport> stockInShelveTaskItemImportList, String createBy) {
        List<String> statusList = new LinkedList<>();
        statusList.add(StockinShelveTaskStatusEnum.PENDING.name());
        statusList.add(StockinShelveTaskStatusEnum.SHELVING.name());
        Map<String, List<StockInShelveTaskItemImport>> collect = stockInShelveTaskItemImportList.stream().collect(Collectors.groupingBy(StockInShelveTaskItemImport::getInternalBoxCode));
        List<StockInShelveTaskItemImport> errorList = new LinkedList<>();

        collect.forEach((key, value) -> {
            StockinShelveTaskEntity taskEntity = stockinShelveTaskService.getOne(StockinShelveTaskService.buildWrapperTaskByInternalBoxCode(key, statusList));
            errorList.addAll(this.importItem(taskEntity, value, createBy));
        });

        //抛出异常回滚事务
        if (!CollectionUtils.isEmpty(errorList)) {
            throw new BusinessServiceException(JSONUtils.toJSON(errorList));
        }
    }
}
