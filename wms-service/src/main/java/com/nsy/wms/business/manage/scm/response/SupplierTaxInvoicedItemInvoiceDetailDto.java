package com.nsy.wms.business.manage.scm.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

@ApiModel(value = "SupplierTaxInvoicedItemInvoiceDetailDto", description = "供应商税务信息 - 开票明细")
public class SupplierTaxInvoicedItemInvoiceDetailDto {
    @ApiModelProperty(value = "项号", name = "gNo")
    private String gNo;

    @ApiModelProperty(value = "进项数量", name = "inputQty")
    private Integer inputQty;

    @ApiModelProperty(value = "进项金额", name = "inputAmount")
    private BigDecimal inputPrice;

    @ApiModelProperty(value = "含税单价", name = "taxInclusiveUnitPrice")
    private BigDecimal taxInclusiveUnitPrice;

    @ApiModelProperty(value = "含税金额", name = "taxInclusivePrice")
    private BigDecimal taxInclusivePrice;

    @ApiModelProperty(value = "含税金额", name = "taxInclusivePrice")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "开票明细主键ID", name = "invoicedItem")
    private Integer invoicedItem;

    public String getgNo() {
        return gNo;
    }

    public void setgNo(String gNo) {
        this.gNo = gNo;
    }

    public Integer getInputQty() {
        return inputQty;
    }

    public void setInputQty(Integer inputQty) {
        this.inputQty = inputQty;
    }

    public BigDecimal getInputPrice() {
        return inputPrice;
    }

    public void setInputPrice(BigDecimal inputPrice) {
        this.inputPrice = inputPrice;
    }

    public BigDecimal getTaxInclusiveUnitPrice() {
        return taxInclusiveUnitPrice;
    }

    public void setTaxInclusiveUnitPrice(BigDecimal taxInclusiveUnitPrice) {
        this.taxInclusiveUnitPrice = taxInclusiveUnitPrice;
    }

    public BigDecimal getTaxInclusivePrice() {
        return taxInclusivePrice;
    }

    public void setTaxInclusivePrice(BigDecimal taxInclusivePrice) {
        this.taxInclusivePrice = taxInclusivePrice;
    }

    public BigDecimal getTaxPrice() {
        return taxPrice;
    }

    public void setTaxPrice(BigDecimal taxPrice) {
        this.taxPrice = taxPrice;
    }

    public Integer getInvoicedItem() {
        return invoicedItem;
    }

    public void setInvoicedItem(Integer invoicedItem) {
        this.invoicedItem = invoicedItem;
    }
}
