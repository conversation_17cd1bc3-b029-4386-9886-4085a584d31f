package com.nsy.wms.business.service.download.stockout;

import com.nsy.api.wms.domain.stockout.StockoutCustomsDeclareFormResult;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutCustomsDeclareFormSearchRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.stockout.StockoutCustomsDeclareFormService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class StockoutCustomsDeclareFormDetailDownloadService implements IDownloadService {

    @Autowired
    StockoutCustomsDeclareFormService declareFormService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKOUT_CUSTOMS_DECLARE_FORM_DETAIL;
    }

    // 按查询条件导出
    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        StockoutCustomsDeclareFormSearchRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), StockoutCustomsDeclareFormSearchRequest.class);
        downloadRequest.setPageIndex(request.getPageIndex());
        downloadRequest.setPageSize(request.getPageSize());
        PageResponse<StockoutCustomsDeclareFormResult> stockoutCustomsDeclareFormResultPageResponse = declareFormService.pageSearchList(downloadRequest);
        DownloadResponse response = new DownloadResponse();
        response.setTotalCount(stockoutCustomsDeclareFormResultPageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(stockoutCustomsDeclareFormResultPageResponse.getContent()));
        return response;
    }
}
