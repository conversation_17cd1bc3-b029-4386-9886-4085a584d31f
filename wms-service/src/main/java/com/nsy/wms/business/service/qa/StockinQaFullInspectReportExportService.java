package com.nsy.wms.business.service.qa;

import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.qa.StockinQaInspectStatusEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.qa.StockinQaFullInspectReportPageRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.qa.StockinQaFullInspectReportPageResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-09 14:08
 */
@Service
public class StockinQaFullInspectReportExportService implements IDownloadService {

    @Autowired
    private StockinQaFullInspectService qaFullInspectService;


    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_FULL_INSPECT_REPORT_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest downloadRequest) {
        DownloadResponse response = new DownloadResponse();
        StockinQaFullInspectReportPageRequest request = JsonMapper.fromJson(downloadRequest.getRequestContent(), StockinQaFullInspectReportPageRequest.class);
        request.setPageIndex(downloadRequest.getPageIndex());
        request.setPageSize(downloadRequest.getPageSize());
        if (CollectionUtils.isEmpty(request.getInspectStatusList()))
            request.setInspectStatusList(Lists.newArrayList(StockinQaInspectStatusEnum.WAIT_INSPECT.name(), StockinQaInspectStatusEnum.INSPECT_COMPLETE.name()));
        PageResponse<StockinQaFullInspectReportPageResponse> pageList = qaFullInspectService.pageFullInspectReportList(request);
        List<StockinQaFullInspectReportPageResponse> records = pageList.getContent();
        if (CollectionUtils.isEmpty(records)) {
            response.setDataJsonStr(JsonMapper.toJson(records));
            response.setTotalCount(0L);
            return response;
        }
        response.setTotalCount(pageList.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(records));
        return response;
    }
}