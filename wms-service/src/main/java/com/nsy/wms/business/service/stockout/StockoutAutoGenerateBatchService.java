package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stock.StockTransferCrossSpaceService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.WmsDateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class StockoutAutoGenerateBatchService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutAutoGenerateBatchService.class);

    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutBatchOrderItemService stockoutBatchOrderItemService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutBatchLogService stockoutBatchLogService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutLogQueueService stockoutLogQueueService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockTransferCrossSpaceService stockTransferCrossSpaceService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    ApplicationContext context;
    @Autowired
    StockoutGenerateBatchService stockoutGenerateBatchService;
    @Autowired
    StockPrematchInfoService stockPrematchInfoService;
    @Autowired
    private BdPositionService bdPositionService;

    @JLock(keyConstant = "generateWave", lockKey = "#stockoutOrderEntity.stockoutOrderId")
    public void autoGenerateRuleLock(StockoutOrderEntity stockoutOrderEntity) {
        context.getBean(this.getClass()).autoGenerateRule(stockoutOrderEntity);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void autoGenerateRule(StockoutOrderEntity stockoutOrderEntity) {
        if (StockoutOrderStatusEnum.READY_PICK.name().equals(stockoutOrderEntity.getStatus())) {
            LOGGER.error("该出库单已经生成过波次，当前为待拣货状态");
            return;
        }

        //校验该出库单是否已经生成波次
        StockoutBatchOrderEntity stockoutBatchOrderEntity = stockoutBatchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        if (Objects.nonNull(stockoutBatchOrderEntity))
            return;
        //生成波次
        StockoutBatchEntity stockoutBatchEntity = buildSockoutBatch(stockoutOrderEntity);
        //仓间调拨或清仓出库更新调拨单状态
        if (StockoutOrderTypeEnum.TRANSFER_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())
                || StockoutOrderTypeEnum.CLEAR_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType())) {
            stockTransferCrossSpaceService.updateTransferStatus(stockoutOrderEntity.getStockoutOrderNo(), StockTransferStatusEnum.OUTBOUNDING);
        }
        StockoutBatchOrderEntity stockoutBatchOrder = buildStockoutBatchOrder(stockoutBatchEntity, stockoutOrderEntity);
        List<StockoutOrderItemEntity> orderItemEntityList = stockoutOrderItemService.list(new QueryWrapper<StockoutOrderItemEntity>().eq("stockout_order_id", stockoutOrderEntity.getStockoutOrderId()));
        List<StockoutBatchOrderItemEntity> collect = orderItemEntityList.stream().map(orderItemEntity -> buildStockoutBatchOrderItem(orderItemEntity, stockoutBatchOrder)).collect(Collectors.toList());
        stockoutBatchOrderItemService.saveBatch(collect);

        if (StockoutOrderStatusEnum.checkUpdateStatus(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.READY_PICK.name())) {
            stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.READY_PICK.name());
        } else {
            LOGGER.info("出库单:{}无法回退状态:{}到:{}", stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.READY_PICK.name());
        }

        //生成波次后生成加工计划、加工波次
        if (!Objects.isNull(stockoutBatchEntity.getIsNeedProcess()) && 1 == stockoutBatchEntity.getIsNeedProcess()) {
            stockoutGenerateBatchService.generateProcessBatch(stockoutBatchEntity.getBatchId(), stockoutBatchEntity.getPickingType(), stockoutBatchEntity.getWorkspace());
        }

        stockoutOrderService.updateById(stockoutOrderEntity);
        stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.READY_PICK, "系统自动生成波次");
        stockoutLogQueueService.addLogQueue(Stream.of(stockoutOrderEntity.getStockoutOrderId()).collect(Collectors.toList()), String.format("自动生成波次，波次号【%s】", stockoutBatchEntity.getBatchId()));

        //转换波次预配
        stockPrematchInfoService.convertToBatch(stockoutBatchEntity.getBatchId(), StockoutBatchLogTypeEnum.NEW);

        stockoutBatchLogService.addLog(stockoutBatchEntity.getBatchId(), StockoutBatchLogTypeEnum.NEW.getStockoutBatchLogType(),
                String.format("【%s】自动生成波次，拣货模式【%s】，波次号%s",
                        enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), stockoutBatchEntity.getWorkspace()),
                        enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), stockoutBatchEntity.getPickingType()),
                        stockoutBatchEntity.getBatchId()));
    }

    private StockoutBatchEntity buildSockoutBatch(StockoutOrderEntity stockoutOrderEntity) {
        StockoutBatchEntity stockoutBatchEntity = new StockoutBatchEntity();
        stockoutBatchEntity.setStatus(StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name());
        stockoutBatchEntity.setCreateBy(StockConstant.AUTO_GENERATE_BATCH);
        stockoutBatchEntity.setUpdateBy(StockConstant.AUTO_GENERATE_BATCH);
        stockoutBatchEntity.setCustomsDeclareType(stockoutOrderEntity.getCustomsDeclareType());
        stockoutBatchEntity.setIsMergeBatch(0);
        stockoutBatchEntity.setLocation(stockoutOrderEntity.getLocation());
        stockoutBatchEntity.setPickingType(stockoutOrderEntity.getPickingType());
        stockoutBatchEntity.setSpaceId(stockoutOrderEntity.getSpaceId());
        stockoutBatchEntity.setStockoutType(stockoutOrderEntity.getStockoutType());
        stockoutBatchEntity.setWorkspace(stockoutOrderEntity.getWorkspace());
        stockoutBatchEntity.setLogisticsCompany(stockoutOrderEntity.getLogisticsCompany());
        stockoutBatchEntity.setBatchSplitType(StockoutSortingTypeEnum.MANUAL_SORT.name());
        stockoutBatchEntity.setScanType(StockoutScanTypeService.getScanType(stockoutOrderEntity.getPickingType(), stockoutOrderEntity.getWorkspace(), StockoutSortingTypeEnum.MANUAL_SORT.name()));
        stockoutBatchEntity.setLogisticsLabelSize(stockoutOrderEntity.getLogisticsLabelSize());
        if (StockoutOrderTypeEnum.PROCESS_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType()) && stockoutOrderEntity.getStockoutOrderNo().startsWith(StockConstant.STOCKOUT_ORDER_NO_JGBCH)) {
            stockoutBatchEntity.setExternalBatchId(Integer.valueOf(stockoutOrderEntity.getStockoutOrderNo().replace(StockConstant.STOCKOUT_ORDER_NO_JGBCH, "")));
        }
        stockoutBatchEntity.setIsNeedProcess(stockoutOrderEntity.getNeedProcess() ? 1 : 0);
        stockoutBatchEntity.setMultipleSpace(stockoutOrderEntity.getMultipleSpace());
        Date dateStart = WmsDateUtils.getDateStart(new Date());
        StockoutBatchEntity stockoutBatchEntityLast = stockoutBatchService.getOne(new LambdaQueryWrapper<StockoutBatchEntity>()
                .ge(StockoutBatchEntity::getCreateDate, dateStart).orderByDesc(StockoutBatchEntity::getCreateDate).last("limit 1"));
        stockoutBatchEntity.setBatchIndex(Objects.isNull(stockoutBatchEntityLast) ? 1 : stockoutBatchEntityLast.getBatchIndex() + 1);
        stockoutBatchEntity.setBatchType(StockoutWavePlanTypeEnum.NORMAL_WAVE.name());
        stockoutBatchService.save(stockoutBatchEntity);
        return stockoutBatchEntity;
    }

    private StockoutBatchOrderEntity buildStockoutBatchOrder(StockoutBatchEntity stockoutBatchEntity, StockoutOrderEntity stockoutOrderEntity) {
        StockoutBatchOrderEntity stockoutBatchOrderEntity = new StockoutBatchOrderEntity();
        stockoutBatchOrderEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        stockoutBatchOrderEntity.setBatchId(stockoutBatchEntity.getBatchId());
        stockoutBatchOrderEntity.setCreateBy(StockConstant.AUTO_GENERATE_BATCH);
        stockoutBatchOrderEntity.setUpdateBy(StockConstant.AUTO_GENERATE_BATCH);
        stockoutBatchOrderEntity.setIsLack(0);
        stockoutBatchOrderEntity.setStoreId(stockoutOrderEntity.getStoreId());
        stockoutBatchOrderEntity.setStoreName(stockoutOrderEntity.getStoreName());
        stockoutBatchOrderEntity.setLocation(stockoutOrderEntity.getLocation());
        stockoutBatchOrderEntity.setStatus(StockoutOrderStatusEnum.READY_PICK.name());
        stockoutBatchOrderService.save(stockoutBatchOrderEntity);
        return stockoutBatchOrderEntity;
    }

    private StockoutBatchOrderItemEntity buildStockoutBatchOrderItem(StockoutOrderItemEntity itemEntity, StockoutBatchOrderEntity orderEntity) {
        StockoutBatchOrderItemEntity stockoutBatchOrderItemEntity = new StockoutBatchOrderItemEntity();
        if (StringUtils.hasText(itemEntity.getPositionCode())) {
            BdPositionEntity positionByCode = bdPositionService.getPositionByCode(itemEntity.getPositionCode());
            stockoutBatchOrderItemEntity.setSpaceAreaId(positionByCode.getSpaceAreaId());
            stockoutBatchOrderItemEntity.setSpaceAreaName(positionByCode.getSpaceAreaName());
        }
        stockoutBatchOrderItemEntity.setBarcode(itemEntity.getBarcode());
        stockoutBatchOrderItemEntity.setBatchOrderId(orderEntity.getBatchOrderId());
        stockoutBatchOrderItemEntity.setCreateBy(StockConstant.AUTO_GENERATE_BATCH);
        stockoutBatchOrderItemEntity.setLocation(itemEntity.getLocation());
        stockoutBatchOrderItemEntity.setProductId(itemEntity.getProductId());
        stockoutBatchOrderItemEntity.setQty(itemEntity.getQty());
        stockoutBatchOrderItemEntity.setScanQty(0);
        stockoutBatchOrderItemEntity.setSku(itemEntity.getSku());
        stockoutBatchOrderItemEntity.setSpecId(itemEntity.getSpecId());
        stockoutBatchOrderItemEntity.setStockoutOrderItemId(itemEntity.getStockoutOrderItemId());
        stockoutBatchOrderItemEntity.setUpdateBy(StockConstant.AUTO_GENERATE_BATCH);
        stockoutBatchOrderItemEntity.setIsFirstOrderByStore(itemEntity.getIsFirstOrderByStore());
        return stockoutBatchOrderItemEntity;
    }
}
