package com.nsy.wms.business.service.stockin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.HeightMeasureSortingPortStatusEnum;
import com.nsy.api.wms.enumeration.bd.HeightMeasureTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinMeasureTypeEnum;
import com.nsy.api.wms.request.bd.BdVolumeWeightRecordSkuHistoryListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingDetailRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingPageRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingRequest;
import com.nsy.api.wms.request.stockin.StockinVolumeWeightRecordMappingSkuPageRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinHeightRecordMappingPageDetailResponse;
import com.nsy.api.wms.response.stockin.StockinHeightRecordMappingResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageDetailResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingPageResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingResponse;
import com.nsy.api.wms.response.stockin.StockinVolumeWeightRecordMappingSkuPageResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.wcs.WcsHeightSortingRecordService;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.wcs.WcsHeightSortingRecordEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinVolumeWeightRecordMappingMapper;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-06-17 17:59
 */
@Service
public class StockinHeightRecordMappingService implements IDownloadService {

    @Autowired
    private WcsHeightSortingRecordService wcsHeightSortingRecordService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private StockinVolumeWeightRecordMappingMapper stockinVolumeWeightRecordMappingMapper;
    @Autowired
    private StockinVolumeWeightStandardRecordService standardRecordService;

    /**
     * 获取高度测量记录 -- 质检使用
     *
     * @param request
     * @return
     */
    public List<StockinHeightRecordMappingResponse> getHeightRecordMappingList(StockinVolumeWeightRecordMappingRequest request) {
        if (!StringUtils.hasText(request.getSku()) || !StringUtils.hasText(request.getSupplierDeliveryNo())) {
            return Collections.emptyList();
        }
        ProductSpecInfoEntity productSpecInfo = productSpecInfoService.getInfoBySkuBarcode(request.getSku());
        if (Objects.isNull(productSpecInfo)) {
            throw new BusinessServiceException("查询不到sku信息!");
        }
        request.setSku(productSpecInfo.getSku());
        request.setSupplierDeliveryNoList(Arrays.asList(request.getSupplierDeliveryNo().split(",")));
        List<StockinHeightRecordMappingResponse> recordMappingEntityList = stockinVolumeWeightRecordMappingMapper.getHeightRecordMappingList(request);
        if (CollectionUtils.isEmpty(recordMappingEntityList)) {
            return Collections.emptyList();
        }
        for (StockinHeightRecordMappingResponse detail : recordMappingEntityList) {
            detail.setHeightMeasureTypeStr(HeightMeasureTypeEnum.of(detail.getHeightMeasureType()));
            detail.setSortingPortStr(HeightMeasureSortingPortStatusEnum.of(detail.getSortingPort()));
        }
        return recordMappingEntityList;
    }

    public List<StockinHeightRecordMappingResponse> getHeightMeasureSkuHistoryList(BdVolumeWeightRecordSkuHistoryListRequest request) {
        if (!StringUtils.hasText(request.getSku())) {
            throw new BusinessServiceException("请求的规格编码信息不能为空!");
        }
        // 获取当天的开始时间
        LocalDate now = LocalDate.now();
        Date startDate = Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant());
        LambdaQueryWrapper<WcsHeightSortingRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WcsHeightSortingRecordEntity::getSku, request.getSku());
        // 添加日期条件
        queryWrapper.ge(WcsHeightSortingRecordEntity::getCreateDate, startDate);
        queryWrapper.in(WcsHeightSortingRecordEntity::getSortingPort, Lists.newArrayList(HeightMeasureSortingPortStatusEnum.STANDARD.name(), HeightMeasureSortingPortStatusEnum.NO_STANDARD.name()));
        queryWrapper.orderByDesc(WcsHeightSortingRecordEntity::getCreateDate);
        List<WcsHeightSortingRecordEntity> historyList = wcsHeightSortingRecordService.list(queryWrapper);
        if (CollectionUtil.isEmpty(historyList)) {
            return Collections.emptyList();
        }
        return historyList.stream().map(record -> {
            StockinHeightRecordMappingResponse response = BeanUtil.toBean(record, StockinHeightRecordMappingResponse.class);
            response.setHeightMeasureType(record.getMeasureType());
            response.setHeightMeasureTypeStr(HeightMeasureTypeEnum.of(record.getMeasureType()));
            response.setSortingPortStr(HeightMeasureSortingPortStatusEnum.of(response.getSortingPort()));
            if (Objects.nonNull(request.getFbaCost()) && Objects.nonNull(record.getFbaCost())) {
                response.setIsOverStandard(record.getFbaCost().compareTo(request.getFbaCost()) > 0 ? 1 : 0);
            }
            return response;
        }).collect(Collectors.toList());
    }

    public PageResponse<StockinVolumeWeightRecordMappingPageResponse> getPageList(StockinVolumeWeightRecordMappingPageRequest request) {
        Page<StockinVolumeWeightRecordMappingPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        request.setMeasureType(StockinMeasureTypeEnum.HEIGHT.name());
        IPage<StockinVolumeWeightRecordMappingPageResponse> response = stockinVolumeWeightRecordMappingMapper.pageList(page, request);
        PageResponse<StockinVolumeWeightRecordMappingPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(response.getRecords());
        pageResponse.setTotalCount(response.getTotal());
        pageResponse.setTotalCount(stockinVolumeWeightRecordMappingMapper.getPageCount(request));
        return pageResponse;
    }

    /**
     * sku条件下的总批次数统计
     *
     * @param request
     * @return
     */
    public PageResponse<StockinVolumeWeightRecordMappingSkuPageResponse> getPageSkuList(StockinVolumeWeightRecordMappingSkuPageRequest request) {
        Page<StockinVolumeWeightRecordMappingSkuPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        request.setMeasureType(StockinMeasureTypeEnum.HEIGHT.name());
        IPage<StockinVolumeWeightRecordMappingSkuPageResponse> response = stockinVolumeWeightRecordMappingMapper.heightPageSkuList(page, request);
        PageResponse<StockinVolumeWeightRecordMappingSkuPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(response.getRecords());
        pageResponse.setTotalCount(response.getTotal());
        pageResponse.setTotalCount(stockinVolumeWeightRecordMappingMapper.getPageSkuCount(request));
        return pageResponse;
    }

    /**
     * 测量总详情
     *
     * @param request
     * @return
     */
    public PageResponse<StockinHeightRecordMappingPageDetailResponse> getDetailPage(StockinVolumeWeightRecordMappingDetailRequest request) {
        if (!StringUtils.hasText(request.getSku())) throw new BusinessServiceException("传入参数不正确请检查!");
        Page<StockinVolumeWeightRecordMappingPageDetailResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        request.setMeasureType(StockinMeasureTypeEnum.HEIGHT.name());
        IPage<StockinHeightRecordMappingPageDetailResponse> response = stockinVolumeWeightRecordMappingMapper.heightPageDetailList(page, request);
        PageResponse<StockinHeightRecordMappingPageDetailResponse> pageResponse = new PageResponse<>();
        if (CollectionUtils.isEmpty(response.getRecords())) {
            pageResponse.setTotalCount(0L);
            return pageResponse;
        }
        Map<Integer, StockinVolumeWeightRecordMappingResponse> standardMap = new HashMap<>();
        //赋值标准信息
        response.getRecords().forEach(detail -> {
            detail.setHeightMeasureTypeStr(HeightMeasureTypeEnum.of(detail.getHeightMeasureType()));
            detail.setSortingPortStr(HeightMeasureSortingPortStatusEnum.of(detail.getSortingPort()));
            //不跳档则自行查询标准信息
            if (Objects.nonNull(detail.getIsOverStandard()) && detail.getIsOverStandard() == 0) {
                return;
            }
            if (Objects.isNull(detail.getStandardId()) || detail.getStandardId() <= 0) {
                return;
            }
            StockinVolumeWeightRecordMappingResponse standardInfo = standardMap.get(detail.getStandardId());
            if (standardInfo != null) {
                detail.setMeasureStandardInfo(standardInfo);
                return;
            }
            standardMap.put(detail.getStandardId(), standardRecordService.getStandInfoById(detail.getStandardId()));
            detail.setMeasureStandardInfo(standardMap.get(detail.getStandardId()));
        });
        pageResponse.setContent(response.getRecords());
        pageResponse.setTotalCount(response.getTotal());
        return pageResponse;
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_STOCKIN_HEIGHT_RECORD_MAPPING_LIST;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        StockinVolumeWeightRecordMappingPageRequest dateRequest = JsonMapper.fromJson(request.getRequestContent(), StockinVolumeWeightRecordMappingPageRequest.class);
        dateRequest.setPageIndex(request.getPageIndex());
        dateRequest.setPageSize(request.getPageSize());
        PageResponse<StockinVolumeWeightRecordMappingPageResponse> pageResult = this.getPageList(dateRequest);
        DownloadResponse response = new DownloadResponse();
        response.setDataJsonStr(JsonMapper.toJson(pageResult.getContent()));
        response.setTotalCount(pageResult.getTotalCount());
        return response;
    }
}
