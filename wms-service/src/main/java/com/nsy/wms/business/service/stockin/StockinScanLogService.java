package com.nsy.wms.business.service.stockin;

import com.nsy.api.wms.enumeration.StockinScanLogTypeEnum;
import com.nsy.api.wms.request.stockin.StockinScanLogListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinScanLogEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinScanLogMapper;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.inject.Inject;
import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockinScanLogService extends ServiceImpl<StockinScanLogMapper, StockinScanLogEntity> {

    @Inject
    LoginInfoService loginInfoService;

    @Transactional
    public void addScanLog(String content, StockinOrderTaskEntity taskEntity, StockinScanLogTypeEnum typeEnum) {
        StockinScanLogEntity stockinScanLogEntity = new StockinScanLogEntity();
        stockinScanLogEntity.setContent(content);
        stockinScanLogEntity.setCreateBy(loginInfoService.getName());
        stockinScanLogEntity.setLocation(TenantContext.getTenant());
        stockinScanLogEntity.setTaskId(taskEntity.getTaskId());
        stockinScanLogEntity.setStockinScanLogType(typeEnum.getChangeLogType());
        stockinScanLogEntity.setIpAddress(loginInfoService.getIpAddress());
        stockinScanLogEntity.setSupplierDeliveryBoxCode(taskEntity.getSupplierDeliveryBoxCode());
        this.save(stockinScanLogEntity);
    }

    @Transactional
    public void addScanLog(String content, StockinOrderTaskEntity taskEntity, StockinOrderEntity orderEntity, StockinScanLogTypeEnum typeEnum) {
        StockinScanLogEntity stockinScanLogEntity = new StockinScanLogEntity();
        stockinScanLogEntity.setContent(content);
        stockinScanLogEntity.setCreateBy(loginInfoService.getName());
        stockinScanLogEntity.setLocation(TenantContext.getTenant());
        stockinScanLogEntity.setStockinOrderNo(orderEntity.getStockinOrderNo());
        stockinScanLogEntity.setTaskId(taskEntity.getTaskId());
        stockinScanLogEntity.setStockinScanLogType(typeEnum.getChangeLogType());
        stockinScanLogEntity.setIpAddress(loginInfoService.getIpAddress());
        stockinScanLogEntity.setSupplierDeliveryBoxCode(taskEntity.getSupplierDeliveryBoxCode());
        this.save(stockinScanLogEntity);
    }

    @Transactional
    public void addScanLog(String content, StockinOrderTaskEntity taskEntity, StockinOrderEntity orderEntity, StockinOrderItemEntity itemEntity, StockinScanLogTypeEnum typeEnum) {
        StockinScanLogEntity stockinScanLogEntity = new StockinScanLogEntity();
        stockinScanLogEntity.setContent(content);
        stockinScanLogEntity.setCreateBy(loginInfoService.getName());
        stockinScanLogEntity.setLocation(TenantContext.getTenant());
        stockinScanLogEntity.setStockinOrderNo(orderEntity.getStockinOrderNo());
        stockinScanLogEntity.setTaskId(taskEntity.getTaskId());
        stockinScanLogEntity.setStockinScanLogType(typeEnum.getChangeLogType());
        stockinScanLogEntity.setIpAddress(loginInfoService.getIpAddress());
        stockinScanLogEntity.setSupplierDeliveryBoxCode(taskEntity.getSupplierDeliveryBoxCode());
        stockinScanLogEntity.setScanQty(itemEntity.getQty());
        stockinScanLogEntity.setInternalBoxCode(itemEntity.getInternalBoxCode());
        this.save(stockinScanLogEntity);
    }

    @Transactional
    public void addScanLog(String content, StockinOrderTaskEntity taskEntity, StockinOrderEntity orderEntity, Integer qty, StockinScanLogTypeEnum typeEnum) {
        StockinScanLogEntity stockinScanLogEntity = new StockinScanLogEntity();
        stockinScanLogEntity.setContent(content);
        stockinScanLogEntity.setCreateBy(loginInfoService.getName());
        stockinScanLogEntity.setLocation(TenantContext.getTenant());
        stockinScanLogEntity.setStockinOrderNo(orderEntity.getStockinOrderNo());
        stockinScanLogEntity.setTaskId(taskEntity.getTaskId());
        stockinScanLogEntity.setStockinScanLogType(typeEnum.getChangeLogType());
        stockinScanLogEntity.setIpAddress(loginInfoService.getIpAddress());
        stockinScanLogEntity.setSupplierDeliveryBoxCode(taskEntity.getSupplierDeliveryBoxCode());
        stockinScanLogEntity.setScanQty(qty);
        this.save(stockinScanLogEntity);
    }


    public List<StockinScanLogEntity> findAllByStockinOrderNoAndStockinScanLogType(String stockinOrderNo, String changeLogType) {
        return this.list(new QueryWrapper<StockinScanLogEntity>().lambda()
                .eq(StockinScanLogEntity::getStockinOrderNo, stockinOrderNo)
                .eq(StockinScanLogEntity::getStockinScanLogType, changeLogType));
    }

    public PageResponse<StockinScanLogEntity> pageList(StockinScanLogListRequest request) {
        Page<StockinScanLogEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        List<StockinScanLogEntity> pageList = this.baseMapper.pageList(page, request);
        PageResponse<StockinScanLogEntity> response = new PageResponse<>();
        response.setContent(pageList);
        response.setTotalCount(page.getTotal());
        return response;
    }
}
