package com.nsy.wms.business.service.stockout.query;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLackItemEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Component
public class StockoutOrderLackItemQueryWrapper {

    public static LambdaQueryWrapper<StockoutOrderLackItemEntity> buildQueryByOrderLackIdAndSku(Integer stockoutOrderLackId, String sku) {
        LambdaQueryWrapper<StockoutOrderLackItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(stockoutOrderLackId))
            queryWrapper.eq(StockoutOrderLackItemEntity::getStockoutOrderLackId, stockoutOrderLackId);
        if (StringUtils.hasText(sku))
            queryWrapper.eq(StockoutOrderLackItemEntity::getSku, sku);
        queryWrapper.last("limit 1");
        return queryWrapper;
    }

    public static LambdaQueryWrapper<StockoutOrderLackItemEntity> buildByOrderLackIdAndStatusList(Integer stockoutOrderLackId, List<String> statusList) {
        LambdaQueryWrapper<StockoutOrderLackItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(stockoutOrderLackId))
            queryWrapper.eq(StockoutOrderLackItemEntity::getStockoutOrderLackId, stockoutOrderLackId);
        if (!CollectionUtils.isEmpty(statusList))
            queryWrapper.in(StockoutOrderLackItemEntity::getStatus, statusList);
        return queryWrapper;
    }
}
