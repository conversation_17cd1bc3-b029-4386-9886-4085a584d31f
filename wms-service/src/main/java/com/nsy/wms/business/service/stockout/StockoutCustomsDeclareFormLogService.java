package com.nsy.wms.business.service.stockout;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.enumeration.stockout.StockoutCustomsDeclareFormLogTypeEnum;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockout.StockoutCustomsDeclareFormLogEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsDeclareFormLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 关单(StockoutCustomsDeclareForm)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-14 17:13:59
 */
@Service
public class StockoutCustomsDeclareFormLogService extends ServiceImpl<StockoutCustomsDeclareFormLogMapper, StockoutCustomsDeclareFormLogEntity> {

    @Autowired
    StockoutCustomsDeclareFormLogMapper logMapper;
    @Autowired
    LoginInfoService loginInfoService;

    public void addLog(Integer declareFormId, StockoutCustomsDeclareFormLogTypeEnum logTypeEnum, String content) {
        StockoutCustomsDeclareFormLogEntity logEntity = new StockoutCustomsDeclareFormLogEntity();
        logEntity.setCreateBy(loginInfoService.getName());
        logEntity.setIpAddress(loginInfoService.getIpAddress());
        logEntity.setType(logTypeEnum.name());
        logEntity.setContent(StrUtil.maxLength(content, 490));
        logEntity.setDeclareFormId(declareFormId);
        logMapper.insert(logEntity);
    }
}

