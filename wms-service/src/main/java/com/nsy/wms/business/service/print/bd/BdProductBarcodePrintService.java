package com.nsy.wms.business.service.print.bd;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.nsy.api.wms.enumeration.bd.PrintTypeEnum;
import com.nsy.api.wms.request.print.PrintDistributionRequest;
import com.nsy.wms.business.service.print.IPrintService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.utils.JsonMapper;
import io.jsonwebtoken.lang.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 透明计划融合标签打印服务
 * <AUTHOR>
 * @date 2024/11/8 13:43
 */
@Service
public class BdProductBarcodePrintService implements IPrintService {

    @Autowired
    ProductSpecInfoService productSpecInfoService;

    @Override
    public String templateType() {
        return PrintTypeEnum.WMS_BD_SYSTEM_PRODUCT_BARCODE.name();
    }

    @Override
    public List<Map<String, Object>> dataAssembly(PrintDistributionRequest request) {
        List<Map<String, Object>> result = new ArrayList<>();
        JSONObject requestObject = JSONUtil.parseObj(request.getRequestParams());
        List<String> skuList = requestObject.getBeanList("skuList", String.class);
        if (!Collections.isEmpty(skuList)) {
            skuList.forEach(sku -> {
                ProductSpecInfoEntity topBySku = productSpecInfoService.findTopBySku(sku);
                Map<String, Object> skuInfoMap = JsonMapper.convertToMap(topBySku);
                result.add(skuInfoMap);
            });
        }
        return result;
    }
}
