package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.InvalidRequestException;
import com.nsy.api.wms.domain.bd.StockoutBatchMergeModel;
import com.nsy.api.wms.domain.bd.StockoutBatchMergeUpdateModel;
import com.nsy.api.wms.enumeration.CustomsDeclareEnum;
import com.nsy.api.wms.enumeration.StockoutBatchLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskStatus;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingScanningStationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWaveTaskStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutBatchMergeRequest;
import com.nsy.api.wms.response.stockout.StockoutBatchMergeListResponse;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.WmsDateUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.transaction.Transactional;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockoutBatchMergeService {
    @Autowired
    private StockoutBatchService stockoutBatchService;
    @Autowired
    private EnumConversionChineseUtils dictService;
    @Autowired
    private StockoutBatchOrderItemService stockoutBatchOrderItemService;
    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private StockoutBatchLogService stockoutBatchLogService;
    @Autowired
    private BdSpaceService bdSpaceService;
    @Autowired
    private StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutBatchSplitTaskService stockoutBatchSplitTaskService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutBatchSplitTaskItemService splitItemService;
    @Autowired
    StockPrematchInfoService prematchInfoService;

    /**
     * 合并列表
     *
     * <AUTHOR>
     * 2021-07-28
     */
    public StockoutBatchMergeListResponse getStockoutBatchMergeList() {
        StockoutBatchMergeListResponse response = new StockoutBatchMergeListResponse();
        // 1波次类型为正常波次(小批次) 2波次状态为新建 3分拣类型为机器分拣 4没有指向批次 5拣货模式只能是以货找单或者二次分拣
        List<StockoutBatchEntity> resultList = stockoutBatchService.getBaseMapper().listStockoutBatchMergeList();
        if (CollectionUtils.isEmpty(resultList)) {
            return response;
        }
        // 按照库区 和 拣货模式进行分组
        Map<String, List<StockoutBatchEntity>> pickTypeMap = resultList.stream().collect(Collectors.groupingBy(s -> s.getSpaceId() + "#" + s.getPickingType() + "#" + s.getWorkspace()));
        // 分组后统计数量
        pickTypeMap.forEach((k, v) -> {
            StockoutBatchMergeModel model = new StockoutBatchMergeModel();
            String[] split = k.split("#");
            model.setSpaceId(Integer.valueOf(split[0]));
            model.setSpaceName(bdSpaceService.getById(model.getSpaceId()).getSpaceName());
            model.setPickingType(split[1]);
            model.setWorkspace(split[2]);
            model.setPickingTypeName(dictService.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_PICKING_TYPE.getName(), split[1]));
            model.setWorkspaceLabel(dictService.baseConversion(DictionaryNameEnum.WMS_STOCKOUT_WORK_LOCATION.getName(), split[2]));
            List<Integer> batchIds = v.stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList());
            List<Integer> batchOrderIds = stockoutBatchOrderService.findAllByBatchIds(batchIds).stream().map(StockoutBatchOrderEntity::getBatchOrderId).collect(Collectors.toList());
            Integer reduce = stockoutBatchOrderItemService.list(new LambdaQueryWrapper<StockoutBatchOrderItemEntity>()
                    .in(StockoutBatchOrderItemEntity::getBatchOrderId, batchOrderIds)).stream().map(StockoutBatchOrderItemEntity::getQty).reduce(0, Integer::sum);
            model.setSkuAmount(reduce);
            model.setBatchAmount(v.size());
            response.getStockoutBatchMergeModelList().add(model);
        });
        response.setStockoutBatchMergeModelList(response.getStockoutBatchMergeModelList().stream().sorted(Comparator.comparing(StockoutBatchMergeModel::getSpaceId)).collect(Collectors.toList()));
        return response;
    }


    // 进行波次合并
    @Transactional
    public void mergeStockoutBatch(StockoutBatchMergeRequest mergeRequest) {
        validRepeatKey(mergeRequest);
        // 新增波次
        StockoutBatchEntity entity = null;
        for (StockoutBatchMergeUpdateModel model : mergeRequest.getModelList()) {
            LambdaQueryWrapper<StockoutBatchEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StockoutBatchEntity::getStatus, StockoutWaveTaskStatusEnum.NEW.name())
                    .eq(StockoutBatchEntity::getIsMergeBatch, 0)
                    .eq(StockoutBatchEntity::getIsNeedProcess, 0)
                    .eq(StockoutBatchEntity::getSpaceId, model.getSpaceId())
                    .eq(StockoutBatchEntity::getPickingType, model.getPickingType())
                    .eq(StockoutBatchEntity::getWorkspace, model.getWorkspace())
                    .isNull(StockoutBatchEntity::getMergeBatchId).orderByDesc(StockoutBatchEntity::getCreateDate);
            List<StockoutBatchEntity> resultList = stockoutBatchService.list(wrapper);
            validMergeOperate(resultList, model);
            if (entity == null) {
                entity = new StockoutBatchEntity();
                buildEntity(entity, resultList.get(0).getSpaceId(), resultList);
                stockoutBatchService.save(entity);
                stockoutBatchLogService.addLog(entity.getBatchId(), StockoutBatchLogTypeEnum.NEW.getStockoutBatchLogType(), "合并生成新波次");
            }
            Integer batchId = entity.getBatchId();
            List<StockoutBatchEntity> subList = resultList.subList(0, model.getMergeBatchAmount()).stream()
                    .peek(item -> {
                        item.setMergeBatchId(batchId);
                        item.setUpdateBy(loginInfoService.getName());
                    }).collect(Collectors.toList());
            stockoutBatchService.updateBatchById(subList);
            stockoutBatchLogService.addLogBatch(subList, StockoutBatchLogTypeEnum.MERGE_BATCH.getStockoutBatchLogType(), String.format("波次合并，合并后波次【%s】：", entity.getBatchId()));

            prematchInfoService.convertToBatch(batchId, StockoutBatchLogTypeEnum.NEW);
        }
        //Fba单需要生成窄带分拣任务
        if (StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(mergeRequest.getModelList().get(0).getWorkspace())) {
            createSplitTaskNormal(entity, loginInfoService.getName());
        }
    }

    /**
     * 正常波次生成分拣任务
     */
    Integer createSplitTaskNormal(StockoutBatchEntity batchEntity, String createBy) {
        List<StockoutBatchEntity> subBatchList = stockoutBatchService.list(new QueryWrapper<StockoutBatchEntity>().eq("merge_batch_id", batchEntity.getBatchId()));
        List<StockoutBatchOrderEntity> batchOrderEntityList = stockoutBatchOrderService.list(new QueryWrapper<StockoutBatchOrderEntity>().in("batch_id", subBatchList.stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList())));
        List<Integer> batchOrderIdList = batchOrderEntityList.stream().map(StockoutBatchOrderEntity::getBatchOrderId).collect(Collectors.toList());
        List<StockoutBatchOrderItemEntity> batchOrderItemEntityList = stockoutBatchOrderItemService.list(new QueryWrapper<StockoutBatchOrderItemEntity>().in("batch_order_id", batchOrderIdList));
        // 1、分拣任务
        StockoutBatchSplitTaskEntity taskEntity = new StockoutBatchSplitTaskEntity();
        taskEntity.setTaskIndex(1);
        taskEntity.setBatchId(batchEntity.getBatchId());
        taskEntity.setBatchSplitType(batchEntity.getBatchSplitType());
        taskEntity.setStatus(StockoutBatchSplitTaskStatus.WAIT_SORT.name());
        taskEntity.setCreateBy(createBy);
        taskEntity.setOutletQty(batchOrderEntityList.size());
        stockoutBatchSplitTaskService.save(taskEntity);
        // 2、分拣任务明细
        Map<Integer, Integer> collect = batchOrderEntityList.stream().collect(Collectors.toMap(StockoutBatchOrderEntity::getBatchOrderId, StockoutBatchOrderEntity::getBatchId));
        for (StockoutBatchOrderItemEntity orderItemEntity : batchOrderItemEntityList) {
            StockoutBatchSplitTaskItemEntity taskItemEntity = stockoutBatchSplitTaskService.buildTaskItemEntity(createBy);
            taskItemEntity.setTaskId(taskEntity.getTaskId());
            taskItemEntity.setProductId(orderItemEntity.getProductId());
            taskItemEntity.setSpecId(orderItemEntity.getSpecId());
            taskItemEntity.setSku(orderItemEntity.getSku());
            taskItemEntity.setBarcode(orderItemEntity.getBarcode());
            taskItemEntity.setBatchQty(orderItemEntity.getQty());
            taskItemEntity.setSubBatchId(collect.get(orderItemEntity.getBatchOrderId()));
            if (orderItemEntity.getLackQty() != null)
                taskItemEntity.setExpectedQty(orderItemEntity.getQty() - orderItemEntity.getLackQty());
            else
                taskItemEntity.setExpectedQty(orderItemEntity.getQty());
            splitItemService.save(taskItemEntity);
        }
        // 3、日志记录
        stockoutBatchSplitTaskService.saveSplitTaskLog(batchEntity, taskEntity);
        return taskEntity.getTaskId();
    }

    public void buildEntity(StockoutBatchEntity entity, Integer spaceId, List<StockoutBatchEntity> batchEntityList) {

        entity.setIsMergeBatch(1);
        entity.setLocation(TenantContext.getTenant());
        Date dateStart = WmsDateUtils.getDateStart(new Date());
        StockoutBatchEntity stockoutBatchEntityLast = stockoutBatchService.getOne(new LambdaQueryWrapper<StockoutBatchEntity>()
                .ge(StockoutBatchEntity::getCreateDate, dateStart).orderByDesc(StockoutBatchEntity::getCreateDate).last("limit 1"));
        entity.setBatchIndex(Objects.isNull(stockoutBatchEntityLast) ? 1 : stockoutBatchEntityLast.getBatchIndex() + 1);
        entity.setBatchType(StockoutWavePlanTypeEnum.GROUP_WAVE.name());
        boolean isFba = StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(batchEntityList.get(0).getWorkspace());
        entity.setPickingType(isFba ? StockoutPickingTypeEnum.WHOLE_PICK.name() : StockoutPickingTypeEnum.SECOND_SORT.name());
        boolean isMix = batchEntityList.stream().map(StockoutBatchEntity::getWorkspace).distinct().count() > 1;
        entity.setWorkspace(isFba ? StockoutOrderWorkSpaceEnum.FBA_AREA.name() : isMix ? StockoutOrderWorkSpaceEnum.MIX_AREA.name() : StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name());
        entity.setBatchSplitType(StockoutSortingTypeEnum.AUTO_MACHINE_SORT.name());
        entity.setScanType(StockoutSortingScanningStationEnum.MACHINE_SORT_STATION.name());
        entity.setStatus(StockoutWaveTaskStatusEnum.WAIT_TO_GENERATE_PICK.name());
        entity.setStockoutType(StockoutOrderTypeEnum.SALES_DELIVERY.name());
        entity.setCustomsDeclareType(CustomsDeclareEnum.DECLARE_NORMAL.getValue());
        entity.setCreateBy(loginInfoService.getName());
        entity.setSpaceId(spaceId);
        // 合并波次，只要有一个子波次是加工，合并波次是加工波次，有一个子波次是跨库，合并波次是跨库
        boolean isNeedProcess = batchEntityList.stream().anyMatch(item -> item.getIsNeedProcess() != null && item.getIsNeedProcess().equals(1));
        boolean multipleSpace = batchEntityList.stream().anyMatch(item -> item.getMultipleSpace() != null && item.getMultipleSpace().equals(1));
        entity.setIsNeedProcess(isNeedProcess ? 1 : 0);
        entity.setMultipleSpace(multipleSpace ? 1 : 0);
    }

    private void validMergeOperate(List<StockoutBatchEntity> resultList, StockoutBatchMergeUpdateModel model) {
        if (CollectionUtils.isEmpty(resultList)) {
            throw new InvalidRequestException("找不到要合并的波次");
        }
        if (resultList.size() < model.getMergeBatchAmount()) {
            throw new InvalidRequestException("输入的波次超过现有波次数量");
        }
    }

    // mergeRequest不能有重复的分拣模式
    private void validRepeatKey(StockoutBatchMergeRequest mergeRequest) {
        List<StockoutBatchMergeUpdateModel> modelList = mergeRequest.getModelList();
        List<Integer> spaceList = modelList.stream().map(StockoutBatchMergeUpdateModel::getSpaceId).distinct().collect(Collectors.toList());
        if (spaceList.size() > 1) {
            throw new InvalidRequestException("不同仓库的波次不能被合并");
        }
        List<String> workSpaceList = modelList.stream().map(StockoutBatchMergeUpdateModel::getWorkspace).distinct().collect(Collectors.toList());
        if (workSpaceList.contains(StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name()) && workSpaceList.contains(StockoutOrderWorkSpaceEnum.B2B_AREA.name())) {
            throw new InvalidRequestException("B2B,B2C无法合并到同一个波次");
        }
    }

    /**
     * 查询被合并的波次
     *
     * <AUTHOR>
     * 2022-01-07
     */
    public List<Integer> findBatchWithMerge(StockoutBatchEntity entity) {
        if (entity.getIsMergeBatch() != 1) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<StockoutBatchEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutBatchEntity::getMergeBatchId, entity.getBatchId());
        List<StockoutBatchEntity> list = stockoutBatchService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(StockoutBatchEntity::getBatchId).collect(Collectors.toList());
    }
}
