package com.nsy.wms.business.manage.search;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.request.stock.stocklog.StockChangeLogListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockChangeLogListResponse;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class SearchApiService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchApiService.class);

    @Autowired
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.search}")
    private String apiUrl;
    @Autowired
    ObjectMapper objectMapper;

    /**
     * 获取库存交易记录列表
     *
     * @param request
     * @return
     */
    public PageResponse<StockChangeLogListResponse> getStockChangeLogList(StockChangeLogListRequest request) {
        String url = String.format("%s/wms/stock-change-log/list", apiUrl);
        HttpHeaders headers = new HttpHeaders();
        headers.set("location", TenantContext.getTenant());
        HttpEntity<StockChangeLogListRequest> requestEntity = new HttpEntity<>(request, headers);
        ResponseEntity<String> responseEntity = this.restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

        if (responseEntity.getStatusCode().value() != HttpStatus.OK.value()) {
            throw new BusinessServiceException("请求es失败");
        }
        try {
            return objectMapper.readValue(responseEntity.getBody(), new SearchApiService.StockChangeLogListRageTypeReference());
        } catch (JsonProcessingException e) {
            LOGGER.error("数据转换错误: " + e.getMessage(), e);
            throw new BusinessServiceException("数据转换错误", e);
        }
    }

    private static final class StockChangeLogListRageTypeReference extends TypeReference<PageResponse<StockChangeLogListResponse>> {
    }

}
