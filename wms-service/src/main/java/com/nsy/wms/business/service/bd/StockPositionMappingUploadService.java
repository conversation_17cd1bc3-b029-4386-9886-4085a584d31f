package com.nsy.wms.business.service.bd;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.request.bd.BdStorePositionMappingSaveRequest;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.response.SaStorePageInfoResponse;
import com.nsy.wms.business.manage.user.upload.StorePostionMappingImport;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockPositionMappingUploadService implements IProcessUploadDataService {

    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    BdStorePositionMappingService storePositionMappingService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    OmsApiService omsApiService;


    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_STORE_POSITION_MAPPING_IMPORT;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<StorePostionMappingImport> importDataList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), StorePostionMappingImport.class);
        if (CollectionUtils.isEmpty(importDataList)) {
            return response;
        }
        List<StorePostionMappingImport> errorList = new ArrayList<>();
        Map<String, Integer> storeMap = omsApiService.getAllStoreInfo()
                .stream().collect(Collectors.toMap(SaStorePageInfoResponse::getErpStoreName, SaStorePageInfoResponse::getId, (v1, v2) -> v1));

        Map<String, String> businessUnitEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_BUSINESS_UNIT.getName());

        for (StorePostionMappingImport importData : importDataList) {
            try {
                Integer storeId = storeMap.get(importData.getStoreName());
                if (Objects.isNull(storeId)) {
                    throw new BusinessServiceException(String.format("找不到店铺【%s】", importData.getStoreName()));
                }
                String businessType = businessUnitEnumMap.get(importData.getBusinessTypeLabel());
                if (!StringUtils.hasText(businessType))
                    throw new BusinessServiceException(String.format("找不到部门【%s】", importData.getBusinessTypeLabel()));

                BdStorePositionMappingSaveRequest saveRequest = new BdStorePositionMappingSaveRequest();
                saveRequest.setPositionCode(importData.getPositionCode());
                saveRequest.setStoreId(storeId);
                saveRequest.setStoreName(importData.getStoreName());
                saveRequest.setBusinessType(businessType);
                storePositionMappingService.createBdStorePositionMapping(saveRequest);
            } catch (Exception e) {
                importData.setErrorMsg(e.getMessage());
                errorList.add(importData);
            }
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return response;
    }

}
