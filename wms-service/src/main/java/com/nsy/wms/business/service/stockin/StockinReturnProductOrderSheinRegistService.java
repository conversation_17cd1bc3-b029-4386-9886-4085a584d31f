package com.nsy.wms.business.service.stockin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.enumeration.stockin.ReturnProductOrderTypeEnum;
import com.nsy.api.wms.request.stockin.StockinReturnProductRegisterRequest;
import com.nsy.api.wms.response.stockin.StockinReturnProductOrderItemListResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductRegisterResponse;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockout.StockoutReturnProductSheinItemService;
import com.nsy.wms.repository.entity.stockout.StockoutReturnProductSheinItemEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockinReturnProductOrderSheinRegistService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockinReturnProductOrderSheinRegistService.class);

    @Resource
    StockinReturnProductOrderService returnProductOrderService;
    @Resource
    ProductSpecInfoService productSpecInfoService;
    @Resource
    StockoutReturnProductSheinItemService returnProductSheinItemService;


    /**
     * 通过物流单号登记
     *
     * @param request
     * @return
     */
    public List<StockinReturnProductRegisterResponse> buildSheinByReturnBoxCode(StockinReturnProductRegisterRequest request) {
        List<StockoutReturnProductSheinItemEntity> sheinReturnItemList = returnProductSheinItemService.listByReturnBoxCode(request.getScanNumber());
        if (CollectionUtils.isEmpty(sheinReturnItemList))
            return null;

        StockoutReturnProductSheinItemEntity firstSheinReturnItem = sheinReturnItemList.get(0);
        StockinReturnProductRegisterResponse response = new StockinReturnProductRegisterResponse();
        response.setStoreId(firstSheinReturnItem.getStoreId());
        response.setType(ReturnProductOrderTypeEnum.EXCEPTION_RETURN.getCode());
        response.setTypeStr(ReturnProductOrderTypeEnum.EXCEPTION_RETURN.getValue());
        response.setLogisticsCompanyName(firstSheinReturnItem.getExpressCompanyName());
        response.setLogisticsNo(firstSheinReturnItem.getExpressNo());
        response.setInternalBoxCode(request.getInternalBoxCode());
        Map<String, List<StockinReturnProductOrderItemListResponse>> responseItemMap = sheinReturnItemList.stream().map(sheinReturnItem -> {
            if (StrUtil.isEmpty(sheinReturnItem.getErpSku())) {
                LOGGER.error(" {} sku code {} 对应sku为空", request.getScanNumber(), sheinReturnItem.getSkuCode());
                return null;
            }
            ProductSpecInfo specInfo = productSpecInfoService.getBySku(sheinReturnItem.getErpSku());
            StockinReturnProductOrderItemListResponse itemResponse = new StockinReturnProductOrderItemListResponse();
            BeanUtil.copyProperties(specInfo, itemResponse, "id");
            itemResponse.setReturnQty(sheinReturnItem.getReturnCount());
            itemResponse.setOriginQty(sheinReturnItem.getReturnCount());
            itemResponse.setSpu(returnProductOrderService.getSpu(specInfo.getSku()));
            return itemResponse;
        }).filter(Objects::nonNull).collect(Collectors.groupingBy(StockinReturnProductOrderItemListResponse::getSku));
        List<StockinReturnProductOrderItemListResponse> responseItemList = responseItemMap.values().stream().map(tempItemList -> {
            StockinReturnProductOrderItemListResponse itemListResponse = BeanUtil.toBean(tempItemList.get(0), StockinReturnProductOrderItemListResponse.class);
            itemListResponse.setReturnQty(tempItemList.stream().mapToInt(StockinReturnProductOrderItemListResponse::getReturnQty).sum());
            itemListResponse.setOriginQty(itemListResponse.getReturnQty());
            return itemListResponse;
        }).collect(Collectors.toList());
        response.setItemList(responseItemList);
        //shein退货原因
        response.setReturnTypeDesc(firstSheinReturnItem.getReturnTypeDesc());

        List<StockinReturnProductRegisterResponse> responseList = Collections.singletonList(response);
        returnProductOrderService.save(responseList, request.getSpaceId(), request.getSpaceName(), Boolean.TRUE);
        return responseList;
    }


}
