package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.request.qa.BdQaDictionaryPageRequest;
import com.nsy.api.wms.request.qa.BdQaDictionaryQueryRequest;
import com.nsy.api.wms.request.qa.BdQaDictionarySaveRequest;
import com.nsy.api.wms.response.qa.BdQaDictionaryResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.qa.BdQaDictionaryEntity;
import com.nsy.wms.repository.jpa.mapper.qa.BdQaDictionaryMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024-11-12 17:29:07
 */
@Service
public class BdQaDictionaryService extends ServiceImpl<BdQaDictionaryMapper, BdQaDictionaryEntity> {

    @Autowired
    private LoginInfoService loginInfoService;

    public PageResponse<BdQaDictionaryResponse> pageList(BdQaDictionaryPageRequest request) {
        Page<BdQaDictionaryEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        LambdaQueryWrapper<BdQaDictionaryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaDictionaryEntity::getType, "质检不合格原因归类");
        if (StringUtils.hasText(request.getDictionaryValue())) {
            queryWrapper.eq(BdQaDictionaryEntity::getDictionaryValue, request.getDictionaryValue());
        }
        queryWrapper.orderByDesc(BdQaDictionaryEntity::getUpdateDate);
        Page<BdQaDictionaryEntity> pageInfo = this.baseMapper.selectPage(page, queryWrapper);
        PageResponse<BdQaDictionaryResponse> pageResponse = new PageResponse<>();
        pageResponse.setTotalCount(pageInfo.getTotal());
        if (CollectionUtil.isEmpty(pageInfo.getRecords())) {
            pageResponse.setContent(Collections.emptyList());
            return pageResponse;
        }
        List<BdQaDictionaryResponse> responseList = new ArrayList<>();
        pageInfo.getRecords().forEach(detail -> {
            BdQaDictionaryResponse info = new BdQaDictionaryResponse();
            BeanUtils.copyProperties(detail, info);
            responseList.add(info);
        });
        pageResponse.setContent(responseList);
        return pageResponse;
    }

    public List<BdQaDictionaryEntity> listUnqualifiedReason(BdQaDictionaryQueryRequest request) {
        LambdaQueryWrapper<BdQaDictionaryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaDictionaryEntity::getCategory, request.getCategory());
        return this.list(queryWrapper);
    }

    public Map<String, String> getUnqualifiedClassify() {
        Map<String, String> map = new HashMap<>();
        LambdaQueryWrapper<BdQaDictionaryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaDictionaryEntity::getType, "质检不合格原因归类");
        List<BdQaDictionaryEntity> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        list.forEach(dict -> map.put(dict.getDictionaryKey(), dict.getDictionaryValue()));
        return map;
    }

    public void saveUnqualifiedClassify(BdQaDictionarySaveRequest request) {
        LambdaQueryWrapper<BdQaDictionaryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaDictionaryEntity::getType, "质检不合格原因归类");
        queryWrapper.eq(BdQaDictionaryEntity::getDictionaryValue, request.getDictionaryValue());
        if (this.count(queryWrapper) > 0) {
            throw new BusinessServiceException("当前质检不合格原因归类已存在!");
        }
        BdQaDictionaryEntity entity = new BdQaDictionaryEntity();
        entity.setDictionaryKey(request.getDictionaryValue());
        entity.setDictionaryValue(request.getDictionaryValue());
        entity.setType("质检不合格原因归类");
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        entity.setLocation(TenantContext.getTenant());
        this.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveUnqualifiedReason(BdQaDictionarySaveRequest request) {
        LambdaQueryWrapper<BdQaDictionaryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaDictionaryEntity::getType, "质检不合格原因");
        queryWrapper.eq(BdQaDictionaryEntity::getCategory, request.getCategory());
        queryWrapper.eq(BdQaDictionaryEntity::getDictionaryValue, request.getDictionaryValue());
        if (this.count(queryWrapper) > 0) {
            throw new BusinessServiceException("当前归类下已存在该不合格原因!");
        }
        BdQaDictionaryEntity entity = new BdQaDictionaryEntity();
        entity.setDictionaryKey(request.getDictionaryValue());
        entity.setDictionaryValue(request.getDictionaryValue());
        entity.setCategory(request.getCategory());
        entity.setType("质检不合格原因");
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());
        entity.setLocation(TenantContext.getTenant());
        this.save(entity);
        this.updateUnqualifiedClassify(entity.getCategory());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteInfo(Integer id) {
        BdQaDictionaryEntity entity = this.getById(id);
        //如果删除质检不合格原因归类 需要删除底下的不合格原因
        if ("质检不合格原因归类".equalsIgnoreCase(entity.getType())) {
            LambdaQueryWrapper<BdQaDictionaryEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BdQaDictionaryEntity::getCategory, entity.getDictionaryValue());
            queryWrapper.eq(BdQaDictionaryEntity::getType, "质检不合格原因");
            this.remove(queryWrapper);
        } else {
            this.updateUnqualifiedClassify(entity.getCategory());
        }
        this.removeById(id);
    }

    public Integer getUnqualifiedReasonCount(BdQaDictionaryQueryRequest request) {
        LambdaQueryWrapper<BdQaDictionaryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaDictionaryEntity::getType, "质检不合格原因");
        queryWrapper.eq(BdQaDictionaryEntity::getCategory, request.getCategory());
        return this.count(queryWrapper);
    }

    public void updateUnqualifiedClassify(String category) {
        LambdaQueryWrapper<BdQaDictionaryEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdQaDictionaryEntity::getType, "质检不合格原因归类");
        queryWrapper.eq(BdQaDictionaryEntity::getDictionaryValue, category);
        BdQaDictionaryEntity entity = this.getOne(queryWrapper);
        if (Objects.isNull(entity)) {
            return;
        }
        entity.setUpdateBy(loginInfoService.getName());
        entity.setUpdateDate(new Date());
        this.updateById(entity);
    }
}
