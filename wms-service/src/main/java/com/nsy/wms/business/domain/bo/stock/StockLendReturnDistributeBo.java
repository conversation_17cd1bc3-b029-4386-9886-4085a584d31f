package com.nsy.wms.business.domain.bo.stock;


import com.nsy.api.wms.enumeration.stock.StockLendLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockLendReturnLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockLendStockChangeLogTypeEnum;
import com.nsy.api.wms.request.stock.StockLendReturnDistributeItemRequest;
import com.nsy.wms.repository.entity.stock.StockLendEntity;
import com.nsy.wms.repository.entity.stock.StockLendItemEntity;
import com.nsy.wms.repository.entity.stock.StockLendReturnEntity;
import com.nsy.wms.repository.entity.stock.StockLendReturnItemEntity;

import java.util.List;


/**
 * 分配参数
 */
public class StockLendReturnDistributeBo {
    /**
     * 归还单
     */
    StockLendReturnEntity returnEntity;
    /**
     * 归还单明细
     */
    StockLendReturnItemEntity returnItem;
    /**
     * 借用单
     */
    List<StockLendEntity> lendList;
    /**
     * 借用单明细
     */
    List<StockLendItemEntity> lendItemList;

    /**
     * 借用库存交易日志类型
     */
    StockLendStockChangeLogTypeEnum stockChangeLogType;

    /**
     * 借用日志类型
     */
    StockLendLogTypeEnum lendLogType;

    /**
     * 归还单日志类型
     */
    StockLendReturnLogTypeEnum lendReturnLogType;

    /**
     * 需分配明细
     */
    List<StockLendReturnDistributeItemRequest> distributeItemList;

    public StockLendReturnEntity getReturnEntity() {
        return returnEntity;
    }

    public void setReturnEntity(StockLendReturnEntity returnEntity) {
        this.returnEntity = returnEntity;
    }

    public StockLendReturnItemEntity getReturnItem() {
        return returnItem;
    }

    public void setReturnItem(StockLendReturnItemEntity returnItem) {
        this.returnItem = returnItem;
    }

    public List<StockLendEntity> getLendList() {
        return lendList;
    }

    public void setLendList(List<StockLendEntity> lendList) {
        this.lendList = lendList;
    }

    public List<StockLendItemEntity> getLendItemList() {
        return lendItemList;
    }

    public void setLendItemList(List<StockLendItemEntity> lendItemList) {
        this.lendItemList = lendItemList;
    }

    public StockLendStockChangeLogTypeEnum getStockChangeLogType() {
        return stockChangeLogType;
    }

    public void setStockChangeLogType(StockLendStockChangeLogTypeEnum stockChangeLogType) {
        this.stockChangeLogType = stockChangeLogType;
    }

    public StockLendLogTypeEnum getLendLogType() {
        return lendLogType;
    }

    public void setLendLogType(StockLendLogTypeEnum lendLogType) {
        this.lendLogType = lendLogType;
    }

    public List<StockLendReturnDistributeItemRequest> getDistributeItemList() {
        return distributeItemList;
    }

    public void setDistributeItemList(List<StockLendReturnDistributeItemRequest> distributeItemList) {
        this.distributeItemList = distributeItemList;
    }

    public StockLendReturnLogTypeEnum getLendReturnLogType() {
        return lendReturnLogType;
    }

    public void setLendReturnLogType(StockLendReturnLogTypeEnum lendReturnLogType) {
        this.lendReturnLogType = lendReturnLogType;
    }
}
