package com.nsy.wms.business.service.stock.transfer;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.bd.BdTransferTypeEnum;
import com.nsy.api.wms.enumeration.common.SyncErpStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferInSpaceTaskTypeEnum;
import com.nsy.api.wms.response.stock.PDATransferBoxScanRequest;
import com.nsy.api.wms.response.stock.PDATransferBoxScanResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockTransferRecordService;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockTransferRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;

@Service
public class StockTransferWholeBoxService {

    @Autowired
    StockInternalBoxService internalBoxService;
    @Autowired
    StockInternalBoxItemService internalBoxItemService;
    @Autowired
    StockTransferRecordService recordService;
    @Autowired
    ProductSpecInfoService specInfoService;
    @Autowired
    LoginInfoService loginInfoService;

    public PDATransferBoxScanResponse scanBoxCode(PDATransferBoxScanRequest request) {
        // 校验
        validRequest(request);
        PDATransferBoxScanResponse response = new PDATransferBoxScanResponse();

        List<StockInternalBoxItemEntity> boxItemEntityList = internalBoxItemService.getByInternalBoxCode(request.getTransferOutCode());
        response.setUnPrematchQty(boxItemEntityList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum());
        response.setTypeStr(StockTransferInSpaceTaskTypeEnum.TEMP_ALLOCATION.getName());

        return response;
    }

    @Transactional
    public void confirmTransfer(PDATransferBoxScanRequest request) {
        // 校验
        if (!StringUtils.hasText(request.getTransferInCode())) {
            throw new BusinessServiceException("请输入调拨入箱码");
        }
        validRequest(request);
        List<StockInternalBoxItemEntity> outItemList = internalBoxItemService.getByInternalBoxCode(request.getTransferOutCode());
        StockInternalBoxEntity transferInBox = internalBoxService.getStockInternalBoxByInternalBoxCode(request.getTransferInCode());
        List<StockInternalBoxItemEntity> inItemList = internalBoxItemService.getByInternalBoxCode(request.getTransferInCode());
        for (StockInternalBoxItemEntity outItem : outItemList) {
            // 1、扣减调拨出 库存
            internalBoxItemService.minusStockInternalBoxItemQty(outItem, outItem.getQty(), StockChangeLogTypeEnum.POSITION_OUT, StockChangeLogTypeModuleEnum.STOCK_INSIDE, null);
            // 2、增加调拨入 库存
            StockInternalBoxItemEntity inItem = inItemList.stream().filter(o -> o.getSku().equals(outItem.getSku())).findFirst().orElse(null);
            if (inItem == null) {
                inItem = buildBoxItem(transferInBox, outItem.getSku());
            }
            internalBoxItemService.addStockInternalBoxItemQty(inItem, outItem.getQty(), StockChangeLogTypeEnum.POSITION_IN, StockChangeLogTypeModuleEnum.STOCK_INSIDE, null);
            // 3、调拨记录
            StockTransferRecordEntity recordEntity = new StockTransferRecordEntity();
            recordEntity.setType(BdTransferTypeEnum.BOX_TO_BOX.name());
            recordEntity.setTransferOutCode(request.getTransferOutCode());
            recordEntity.setTransferInCode(request.getTransferInCode());
            recordEntity.setSku(outItem.getSku());
            recordEntity.setTransferSkuQty(outItem.getQty());
            recordEntity.setExceptionQty(0);
            recordEntity.setUnProcessQty(0);
            recordEntity.setSyncErpStatus(SyncErpStatusEnum.SUCCESS.name());
            recordEntity.setAfterInStock(outItem.getQty());
            recordEntity.setCreateBy(loginInfoService.getName());
            recordService.save(recordEntity);
        }
        // 4、箱子状态变化
        internalBoxService.changeStockInternalBoxStatus(request.getTransferOutCode(), StockInternalBoxStatusEnum.EMPTY.name());
        internalBoxService.changeStockInternalBoxStatus(request.getTransferInCode(), StockInternalBoxStatusEnum.TO_BE_TRANSFERRED_IN.name());

    }

    private StockInternalBoxItemEntity buildBoxItem(StockInternalBoxEntity internalBoxEntity, String sku) {
        StockInternalBoxItemEntity result = new StockInternalBoxItemEntity();
        result.setLocation(internalBoxEntity.getLocation());
        result.setSpaceId(internalBoxEntity.getSpaceId());
        result.setInternalBoxId(internalBoxEntity.getInternalBoxId());
        result.setInternalBoxCode(internalBoxEntity.getInternalBoxCode());
        ProductSpecInfoEntity specInfoEntity = specInfoService.getOne(new QueryWrapper<ProductSpecInfoEntity>().lambda()
                .eq(ProductSpecInfoEntity::getSku, sku)
                .last("limit 1"));
        if (specInfoEntity == null)
            throw new BusinessServiceException(String.format("未找到sku【%s】，请确认", sku));
        result.setProductId(specInfoEntity.getProductId());
        result.setSpecId(specInfoEntity.getSpecId());
        result.setSku(specInfoEntity.getSku());
        result.setQty(0);
        result.setCreateBy(loginInfoService.getName());
        internalBoxItemService.save(result);
        return result;
    }

    public void validRequest(PDATransferBoxScanRequest request) {
        if (!StringUtils.hasText(request.getTransferOutCode())) {
            throw new BusinessServiceException("请输入调拨出箱码");
        }
        StockInternalBoxEntity transferOutBox = internalBoxService.getStockInternalBoxByInternalBoxCode(request.getTransferOutCode());
        if (!StockInternalBoxTypeEnum.BORROW_RETURN_BOX.name().equals(transferOutBox.getInternalBoxType())
                && !StockInternalBoxTypeEnum.WITHDRAWAL_BOX.name().equals(transferOutBox.getInternalBoxType())
                && !StockInternalBoxTypeEnum.RETURN_BOX.name().equals(transferOutBox.getInternalBoxType())) {
            throw new BusinessServiceException(String.format("调拨出目前仅支持【%s】【%s】【%s】", StockInternalBoxTypeEnum.BORROW_RETURN_BOX.getName(), StockInternalBoxTypeEnum.WITHDRAWAL_BOX.getName(), StockInternalBoxTypeEnum.RETURN_BOX.getName()));
        }
        if (StringUtils.hasText(request.getTransferInCode())) {
            StockInternalBoxEntity transferInBox = internalBoxService.getStockInternalBoxByInternalBoxCode(request.getTransferInCode());
            if (!StockInternalBoxTypeEnum.TRANSFER_BOX.name().equals(transferInBox.getInternalBoxType())) {
                throw new BusinessServiceException(String.format("调拨入目前仅支持【%s】", StockInternalBoxTypeEnum.TRANSFER_BOX.getName()));
            }
        }

    }
}
