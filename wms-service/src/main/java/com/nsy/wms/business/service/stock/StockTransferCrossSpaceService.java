package com.nsy.wms.business.service.stock;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.CustomsDeclareEnum;
import com.nsy.api.wms.enumeration.StockinScanLogTypeEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.request.stock.StockPlatformScheduleSupplierAddRequest;
import com.nsy.api.wms.request.stock.StockTransferClearAddRequest;
import com.nsy.api.wms.request.stock.StockTransferClearSkuRequest;
import com.nsy.api.wms.request.stock.StockTransferCrossSpaceRequest;
import com.nsy.api.wms.request.stock.StockTransferCrossSpaceSkuRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockTransferClearSkuResponse;
import com.nsy.api.wms.response.stock.StockTransferCrossSkuResponse;
import com.nsy.api.wms.response.stock.StockTransferCrossSpaceDetailResponse;
import com.nsy.api.wms.response.stock.StockTransferCrossSpaceResponse;
import com.nsy.wms.business.service.bd.BdPickingTypeRuleService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.GenerateCodeService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.query.StockTransferCrossSpaceWrapper;
import com.nsy.wms.business.service.stockin.StockinOrderService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskItemService;
import com.nsy.wms.business.service.stockin.StockinOrderTaskService;
import com.nsy.wms.business.service.stockin.StockinScanLogService;
import com.nsy.wms.business.service.stockout.StockoutBatchOrderService;
import com.nsy.wms.business.service.stockout.StockoutOrderCancelService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutReceiverInfoService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.business.service.stockout.StockoutShipperInfoService;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockPlatformScheduleEntity;
import com.nsy.wms.repository.entity.stock.StockTransferCrossSpaceEntity;
import com.nsy.wms.repository.entity.stock.StockTransferCrossSpaceItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinScanLogEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLogEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.product.ProductWmsCategorySpaceMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockTransferCrossSpaceMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.Validator;
import com.nsy.wms.utils.mp.TenantContext;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 仓间调拨业务实现
 * @date: 2021-09-08 13:45
 */
@Service
public class StockTransferCrossSpaceService extends ServiceImpl<StockTransferCrossSpaceMapper, StockTransferCrossSpaceEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockTransferCrossSpaceService.class);

    @Autowired
    StockTransferCrossSpaceItemService stockTransferCrossSpaceItemService;
    @Autowired
    BdSpaceService bdSpaceService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderCancelService stockoutOrderCancelService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockinScanLogService stockinScanLogService;
    @Autowired
    StockPlatformScheduleService stockPlatformScheduleService;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockoutShipmentService stockoutShipmentService;
    @Autowired
    StockoutShipmentItemService stockoutShipmentItemService;
    @Autowired
    StockService stockService;
    @Autowired
    GenerateCodeService codeService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockTransferCrossSpaceService stockTransferCrossSpaceService;
    @Autowired
    BdPickingTypeRuleService bdPickingTypeRuleService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    ProductWmsCategorySpaceMapper categorySpaceMapper;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutShipperInfoService stockoutShipperInfoService;
    @Autowired
    StockoutReceiverInfoService stockoutReceiverInfoService;

    /**
     * 入库完成回写调拨入库单
     *
     * @param taskEntity
     */
    @Transactional
    public void stockinComplete(StockinOrderTaskEntity taskEntity) {
        String transferNo = taskEntity.getSupplierDeliveryNo();
        List<StockinOrderTaskEntity> stockinOrderTaskEntityList = stockinOrderTaskService.list(new LambdaQueryWrapper<StockinOrderTaskEntity>().likeRight(StockinOrderTaskEntity::getSupplierDeliveryNo, transferNo));
        if (stockinOrderTaskEntityList.stream().anyMatch(entity -> !StockinOrderTaskStatusEnum.RECEIVED.name().equals(entity.getStatus()))) {
            //存在未入库完成的任务 则return
            return;
        }
        StockTransferCrossSpaceEntity stockTransferCrossSpaceEntity = this.findByTransferNo(transferNo);
        if (Objects.isNull(stockTransferCrossSpaceEntity)) {
            LOGGER.error("未找到调拨单:{}", transferNo);
        }
        List<StockTransferCrossSpaceItemEntity> list = stockTransferCrossSpaceItemService.list(new LambdaQueryWrapper<StockTransferCrossSpaceItemEntity>().eq(StockTransferCrossSpaceItemEntity::getTransferId, stockTransferCrossSpaceEntity.getTransferId()));
        Map<String, List<StockinOrderTaskItemEntity>> collect = stockinOrderTaskItemService.list(new LambdaQueryWrapper<StockinOrderTaskItemEntity>().in(StockinOrderTaskItemEntity::getTaskId,
                        stockinOrderTaskEntityList.stream().map(StockinOrderTaskEntity::getTaskId).collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(StockinOrderTaskItemEntity::getSku));
        List<StockTransferCrossSpaceItemEntity> stockTransferCrossSpaceItemEntityList = list.stream().map(entity -> {
            List<StockinOrderTaskItemEntity> stockinOrderTaskItemEntities = collect.get(entity.getSku());
            StockTransferCrossSpaceItemEntity itemEntity = new StockTransferCrossSpaceItemEntity();
            itemEntity.setTransferItemId(entity.getTransferItemId());
            itemEntity.setTransferQty(CollectionUtils.isEmpty(stockinOrderTaskItemEntities) ? 0
                    : stockinOrderTaskItemEntities.stream().mapToInt(StockinOrderTaskItemEntity::getStockinQty).sum());
            return itemEntity;
        }).collect(Collectors.toList());
        stockTransferCrossSpaceItemService.updateBatchById(stockTransferCrossSpaceItemEntityList);

        stockTransferCrossSpaceEntity.setStatus(StockTransferStatusEnum.INBOUNDED.name());
        stockTransferCrossSpaceEntity.setTransferQty(stockTransferCrossSpaceItemEntityList.stream().mapToInt(StockTransferCrossSpaceItemEntity::getTransferQty).sum());
        stockTransferCrossSpaceService.updateById(stockTransferCrossSpaceEntity);
    }

    public PageResponse<StockTransferCrossSpaceResponse> getTransferCrossSpaceList(StockTransferCrossSpaceRequest request) {
        PageResponse<StockTransferCrossSpaceResponse> pageResponse = new PageResponse<>();
        Page<StockTransferCrossSpaceEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        Page<StockTransferCrossSpaceEntity> pageResult = this.page(page, StockTransferCrossSpaceWrapper.buildTransferCrossSpaceList(request));
        List<StockTransferCrossSpaceEntity> records = pageResult.getRecords();
        pageResponse.setTotalCount(page.getTotal());
        if (CollectionUtils.isEmpty(records)) {
            pageResponse.setContent(new ArrayList<>());
            return pageResponse;
        }
        Map<String, String> whTransferStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_WH_TRANSFER_STATUS.getName());
        Map<String, String> whTransferTypeEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_WH_TRANSFER_TYPE.getName());
        List<StockTransferCrossSpaceResponse> collect = records.stream().map(entity -> {
            StockTransferCrossSpaceResponse response = new StockTransferCrossSpaceResponse();
            BeanUtilsEx.copyProperties(entity, response);
            response.setStatus(whTransferStatusEnumMap.get(entity.getStatus()));
            response.setTransferType(whTransferTypeEnumMap.get(entity.getTransferType()));
            return response;
        }).collect(Collectors.toList());
        pageResponse.setContent(collect);
        return pageResponse;
    }

    @Transactional
    public void cancelTransfer(String transferTaskNo) {
        List<StockTransferCrossSpaceEntity> byTransferTaskNo = this.findByTransferTaskNo(transferTaskNo);
        if (CollectionUtils.isEmpty(byTransferTaskNo)) {
            throw new BusinessServiceException("未找到调拨任务，请确认任务号！");
        }
        //出库调拨单
        StockTransferCrossSpaceEntity stockoutEntity = byTransferTaskNo.stream().filter(entity -> StockTransferTypeEnum.TRANSFER_OUT.name().equals(entity.getTransferType())
                || StockTransferTypeEnum.CLEAR_OUT.name().equals(entity.getTransferType())).findAny().orElseThrow(() -> new BusinessServiceException("未找到出库调拨单"));
        if (StockTransferStatusEnum.OUTBOUNDED.name().equals(stockoutEntity.getStatus())) {
            throw new BusinessServiceException("对应调拨任务已出库，无法取消，请确定。");
        }
        if (StockTransferStatusEnum.CANCELLED.name().equals(stockoutEntity.getStatus())) {
            throw new BusinessServiceException("对应调拨任务已取消，无需重复操作。");
        }
        //查询出库单
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getStockoutOrderNo, stockoutEntity.getTransferNo()));
        if (Objects.nonNull(stockoutOrderEntity)) {
            stockoutOrderCancelService.cancelOutOrder(stockoutOrderEntity.getStockoutOrderId(), 0);
        }
        Optional<StockTransferCrossSpaceEntity> stockin = byTransferTaskNo.stream().filter(entity -> StockTransferTypeEnum.TRANSFER_INBOUND.name().equals(entity.getTransferType())).findAny();
        stockin.ifPresent(entity -> {
            entity.setStatus(StockTransferStatusEnum.CANCELLED.name());
            entity.setUpdateBy(loginInfoService.getName());
            this.updateById(entity);
        });
        stockoutEntity.setStatus(StockTransferStatusEnum.CANCELLED.name());
        stockoutEntity.setUpdateBy(loginInfoService.getName());
        this.updateById(stockoutEntity);
    }

    //取消出库单-更新调拨单
    @Transactional
    public void cancelTransferOut(String stockoutOrderNo) {
        StockTransferCrossSpaceEntity byTransferNo = this.findByTransferNo(stockoutOrderNo);
        if (Objects.isNull(byTransferNo)) {
            return;
        }
        if (StockTransferStatusEnum.CANCELLED.name().equals(byTransferNo.getStatus())) {
            return;
        }
        StockTransferCrossSpaceEntity spaceEntityOut = new StockTransferCrossSpaceEntity();
        spaceEntityOut.setTransferId(byTransferNo.getTransferId());
        spaceEntityOut.setStatus(StockTransferStatusEnum.CANCELLED.name());
        this.updateById(spaceEntityOut);
        //查询调拨入库单
        StockTransferCrossSpaceEntity transferIn = findByTransferTaskNoAndType(spaceEntityOut.getTransferTaskNo(), StockTransferTypeEnum.TRANSFER_INBOUND);
        if (Objects.nonNull(transferIn)) {
            StockTransferCrossSpaceEntity spaceEntityIn = new StockTransferCrossSpaceEntity();
            spaceEntityIn.setTransferId(transferIn.getTransferId());
            spaceEntityIn.setStatus(StockTransferStatusEnum.CANCELLED.name());
            this.updateById(spaceEntityIn);
        }

    }

    //更新调拨单状态
    @Transactional
    public void updateTransferStatus(String stockoutOrderNo, StockTransferStatusEnum statusEnum) {
        StockTransferCrossSpaceEntity byTransferNo = this.findByTransferNo(stockoutOrderNo);
        if (Objects.isNull(byTransferNo)) {
            return;
        }
        StockTransferCrossSpaceEntity entity = new StockTransferCrossSpaceEntity();
        entity.setTransferId(byTransferNo.getTransferId());
        entity.setStatus(statusEnum.name());
        entity.setUpdateBy(loginInfoService.getName());
        this.updateById(entity);
    }

    //更新调拨单状态
    @Transactional
    public void updateTransferInStatus(List<String> supplierDeliveryNoList, StockTransferStatusEnum statusEnum) {
        List<StockTransferCrossSpaceEntity> list = this.list(new LambdaQueryWrapper<StockTransferCrossSpaceEntity>()
                .in(StockTransferCrossSpaceEntity::getTransferNo, supplierDeliveryNoList));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<StockTransferCrossSpaceEntity> collect = list.stream().map(entity -> {
            StockTransferCrossSpaceEntity spaceEntity = new StockTransferCrossSpaceEntity();
            spaceEntity.setTransferId(entity.getTransferId());
            spaceEntity.setStatus(statusEnum.name());
            spaceEntity.setUpdateBy(loginInfoService.getName());
            return spaceEntity;
        }).collect(Collectors.toList());
        this.updateBatchById(collect);
    }

    public Boolean isGenerateBatch(String transferTaskNo) {
        List<StockTransferCrossSpaceEntity> byTransferTaskNo = this.findByTransferTaskNo(transferTaskNo);
        if (CollectionUtils.isEmpty(byTransferTaskNo)) {
            throw new BusinessServiceException("未找到调拨任务，请确认任务号！");
        }
        //出库调拨单
        StockTransferCrossSpaceEntity stockoutEntity = byTransferTaskNo.stream().filter(entity -> StockTransferTypeEnum.TRANSFER_OUT.name().equals(entity.getTransferType())
                || StockTransferTypeEnum.CLEAR_OUT.name().equals(entity.getTransferType())).findAny().orElseThrow(() -> new BusinessServiceException("未找到出库调拨单"));
        if (StockTransferStatusEnum.OUTBOUNDED.name().equals(stockoutEntity.getStatus())) {
            throw new BusinessServiceException("对应调拨任务已出库，无法取消，请确定。");
        }
        if (StockTransferStatusEnum.CANCELLED.name().equals(stockoutEntity.getStatus())) {
            throw new BusinessServiceException("对应调拨任务已取消，无需重复操作。");
        }
        //查询出库单
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getStockoutOrderNo, stockoutEntity.getTransferNo()));
        if (Objects.isNull(stockoutOrderEntity)) {
            throw new BusinessServiceException("未找到出库单！");
        }
        StockoutBatchOrderEntity one = stockoutBatchOrderService.getOne(new LambdaQueryWrapper<StockoutBatchOrderEntity>()
                .eq(StockoutBatchOrderEntity::getStockoutOrderId, stockoutOrderEntity.getStockoutOrderId()).last("limit 1"));
        return Objects.nonNull(one);
    }

    private List<StockTransferCrossSpaceEntity> findByTransferTaskNo(String transferTaskNo) {
        return this.list(new LambdaQueryWrapper<StockTransferCrossSpaceEntity>()
                .eq(StockTransferCrossSpaceEntity::getTransferTaskNo, transferTaskNo));
    }

    public StockTransferCrossSpaceEntity findByTransferNo(String transferNo) {
        return this.getOne(new LambdaQueryWrapper<StockTransferCrossSpaceEntity>()
                .eq(StockTransferCrossSpaceEntity::getTransferNo, transferNo).last("limit 1"));
    }

    public StockTransferCrossSpaceEntity findByTransferTaskNoAndType(String transferTaskNo, StockTransferTypeEnum stockTransferType) {
        return this.getOne(new LambdaQueryWrapper<StockTransferCrossSpaceEntity>()
                .eq(StockTransferCrossSpaceEntity::getTransferTaskNo, transferTaskNo)
                .eq(StockTransferCrossSpaceEntity::getTransferType, stockTransferType.name()).last("limit 1"));
    }

    public PageResponse<StockTransferCrossSkuResponse> getTransferSkuList(StockTransferCrossSpaceSkuRequest request) {
        Validator.isValid(request, Objects::nonNull, "调拨单主键Id不允许为空");
        Validator.isValid(request.getTransferId(), Objects::nonNull, "调拨单主键Id不允许为空");
        StockTransferCrossSpaceEntity entity = this.getById(request.getTransferId());
        if (Objects.isNull(entity)) {
            throw new BusinessServiceException("未找到调拨单");
        }
        PageResponse<StockTransferCrossSkuResponse> pageResponse = new PageResponse<>();
        Page<StockTransferCrossSpaceItemEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        LambdaQueryWrapper<StockTransferCrossSpaceItemEntity> queryWrapper = new LambdaQueryWrapper<StockTransferCrossSpaceItemEntity>()
                .eq(StockTransferCrossSpaceItemEntity::getTransferId, entity.getTransferId());
        if (StringUtils.hasText(request.getBarcode())) {
            ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopByBarcode(request.getBarcode());
            if (productSpecInfoEntity == null)
                throw new BusinessServiceException("不存在该条码的商品信息");
            request.setBarcode(productSpecInfoEntity.getBarcode());
        }
        if (StringUtils.hasText(request.getBarcode())) {
            queryWrapper.eq(StockTransferCrossSpaceItemEntity::getBarcode, request.getBarcode());
        }
        if (StringUtils.hasText(request.getSku())) {
            queryWrapper.eq(StockTransferCrossSpaceItemEntity::getSku, request.getSku());
        }
        Page<StockTransferCrossSpaceItemEntity> pageResult = stockTransferCrossSpaceItemService.page(page, queryWrapper);
        List<StockTransferCrossSpaceItemEntity> records = pageResult.getRecords();
        pageResponse.setTotalCount(page.getTotal());
        if (CollectionUtils.isEmpty(records)) {
            pageResponse.setContent(new ArrayList<>());
            return pageResponse;
        }
        List<Integer> specIds = records.stream().map(StockTransferCrossSpaceItemEntity::getSpecId).collect(Collectors.toList());
        Map<Integer, List<ProductSpecInfoEntity>> collect = productSpecInfoService.findAllBySpecIdIn(specIds).stream().collect(Collectors.groupingBy(ProductSpecInfoEntity::getSpecId));
        List<StockTransferCrossSkuResponse> list = records.stream().map(itemEntity -> {
            StockTransferCrossSkuResponse response = new StockTransferCrossSkuResponse();
            BeanUtilsEx.copyProperties(itemEntity, response);
            List<ProductSpecInfoEntity> specInfoEntities = collect.get(itemEntity.getSpecId());
            if (!CollectionUtils.isEmpty(specInfoEntities)) {
                ProductSpecInfoEntity specInfoEntity = specInfoEntities.get(0);
                response.setPreviewImageUrl(specInfoEntity.getPreviewImageUrl());
                response.setThumbnailImageUrl(specInfoEntity.getThumbnailImageUrl());
                response.setColor(specInfoEntity.getColor());
                response.setSize(specInfoEntity.getSize());
            }
            return response;

        }).collect(Collectors.toList());
        pageResponse.setContent(list);
        return pageResponse;
    }

    public StockTransferCrossSpaceDetailResponse getTransferCrossSpaceDetail(Integer transferId) {
        StockTransferCrossSpaceEntity spaceEntity = this.getById(transferId);
        if (Objects.isNull(spaceEntity)) {
            throw new BusinessServiceException("未找到调拨单");
        }
        StockTransferCrossSpaceDetailResponse response = new StockTransferCrossSpaceDetailResponse();
        BeanUtilsEx.copyProperties(spaceEntity, response);
        response.setStatus(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_WH_TRANSFER_STATUS.getName(), spaceEntity.getStatus()));
        response.setTransferType(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_WH_TRANSFER_TYPE.getName(), spaceEntity.getTransferType()));
        //查询单号状态
        if (StockTransferTypeEnum.TRANSFER_INBOUND.name().equals(spaceEntity.getTransferType())) {
            //入库
            //查询月台预约
            List<StockPlatformScheduleEntity> list = stockPlatformScheduleService.list(new LambdaQueryWrapper<StockPlatformScheduleEntity>()
                    .likeRight(StockPlatformScheduleEntity::getSupplierDeliveryNo, spaceEntity.getTransferNo()));
            if (!CollectionUtils.isEmpty(list)) {
                response = buildStockinOperateDate(response, list);
            }
        } else {
            //出库
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new QueryWrapper<StockoutOrderEntity>().lambda()
                    .eq(StockoutOrderEntity::getStockoutOrderNo, spaceEntity.getTransferNo()).last("limit 1"));
            if (Objects.isNull(stockoutOrderEntity)) {
                throw new BusinessServiceException("未找到出库单");
            }
            response = buildOperateDate(response, stockoutOrderEntity);
        }
        return response;
    }

    /**
     * 构造出库操作节点日期
     */
    public StockTransferCrossSpaceDetailResponse buildOperateDate(StockTransferCrossSpaceDetailResponse response, StockoutOrderEntity stockoutOrderEntity) {
        StockTransferCrossSpaceDetailResponse result = new StockTransferCrossSpaceDetailResponse();
        BeanUtils.copyProperties(response, result);
        result.setWaitOutboundDate(stockoutOrderEntity.getCreateDate());

        List<StockoutOrderLogEntity> logEntityList = stockoutOrderLogService.list(new QueryWrapper<StockoutOrderLogEntity>().lambda().eq(StockoutOrderLogEntity::getStockoutOrderNo, stockoutOrderEntity.getStockoutOrderNo()));
        if (!CollectionUtils.isEmpty(logEntityList)) {
            logEntityList.stream().filter(o -> o.getOrderLogType().equals(StockoutOrderLogTypeEnum.READY_PICK.getType())).findFirst().ifPresent(tempLog -> result.setOutboundingDate(tempLog.getCreateDate()));
            logEntityList.stream().filter(o -> o.getOrderLogType().equals(StockoutOrderLogTypeEnum.DELIVERED.getType())).findFirst().ifPresent(tempLog -> result.setOutboundedDate(tempLog.getCreateDate()));
            logEntityList.stream().filter(o -> o.getOrderLogType().equals(StockoutOrderLogTypeEnum.CANCEL.getType())).findFirst().ifPresent(tempLog -> result.setCancelledDate(tempLog.getCreateDate()));
        }
        return result;
    }

    /**
     * 构造入库操作节点日期
     */
    public StockTransferCrossSpaceDetailResponse buildStockinOperateDate(StockTransferCrossSpaceDetailResponse response, List<StockPlatformScheduleEntity> stockPlatformScheduleEntityList) {
        StockTransferCrossSpaceDetailResponse result = new StockTransferCrossSpaceDetailResponse();
        BeanUtils.copyProperties(response, result);
        result.setWartInboundDate(stockPlatformScheduleEntityList.get(0).getCreateDate());
        List<StockinOrderTaskEntity> list = stockinOrderTaskService.list(new LambdaQueryWrapper<StockinOrderTaskEntity>()
                .in(StockinOrderTaskEntity::getSupplierDeliveryNo, stockPlatformScheduleEntityList.stream().map(StockPlatformScheduleEntity::getSupplierDeliveryNo).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<Integer> taskIds = list.stream().map(StockinOrderTaskEntity::getTaskId).collect(Collectors.toList());
        List<StockinOrderEntity> orderEntityList = stockinOrderService.list(new LambdaQueryWrapper<StockinOrderEntity>().in(StockinOrderEntity::getTaskId, taskIds)
                .orderByAsc(StockinOrderEntity::getCreateDate));
        if (CollectionUtils.isEmpty(orderEntityList)) {
            return result;
        }
        result.setInboundingDate(orderEntityList.get(0).getCreateDate());
        if (list.stream().allMatch(entity -> StockinOrderTaskStatusEnum.RECEIVED.name().equals(entity.getStatus()))) {
            StockinScanLogEntity one = stockinScanLogService.getOne(new LambdaQueryWrapper<StockinScanLogEntity>()
                    .in(StockinScanLogEntity::getTaskId, taskIds)
                    .eq(StockinScanLogEntity::getStockinScanLogType, StockinScanLogTypeEnum.STOCKIN_SCAN_LOG_TYPE_RECEIVING_COMPLETE.getChangeLogType())
                    .orderByDesc(StockinScanLogEntity::getCreateDate)
                    .last("limit 1"));
            Optional.ofNullable(one).ifPresent(logEntity -> result.setInboundedDate(logEntity.getCreateDate()));
        }
        return result;
    }

    private void setBackStockTransfer(StockTransferCrossSpaceEntity outSpaceEntity, StockTransferCrossSpaceEntity inSpaceEntity, List<StockoutShipmentItemEntity> stockoutShipmentItemEntityList) {
        Map<String, List<StockoutShipmentItemEntity>> collect = stockoutShipmentItemEntityList.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getSku));
        int sum = stockoutShipmentItemEntityList.stream().mapToInt(StockoutShipmentItemEntity::getQty).sum();
        StockTransferCrossSpaceEntity spaceEntity = new StockTransferCrossSpaceEntity();
        spaceEntity.setExpectedTransferQty(sum);
        spaceEntity.setUpdateBy(loginInfoService.getName());
        spaceEntity.setTransferId(inSpaceEntity.getTransferId());
        stockTransferCrossSpaceService.updateById(spaceEntity);

        spaceEntity = new StockTransferCrossSpaceEntity();
        spaceEntity.setTransferId(outSpaceEntity.getTransferId());
        spaceEntity.setUpdateBy(loginInfoService.getName());
        spaceEntity.setTransferQty(sum);
        spaceEntity.setStatus(StockTransferStatusEnum.OUTBOUNDED.name());
        stockTransferCrossSpaceService.updateById(spaceEntity);

        List<StockTransferCrossSpaceItemEntity> list = stockTransferCrossSpaceItemService.list(new LambdaQueryWrapper<StockTransferCrossSpaceItemEntity>().eq(StockTransferCrossSpaceItemEntity::getTransferId, inSpaceEntity.getTransferId()));
        List<StockTransferCrossSpaceItemEntity> stockTransferCrossSpaceItemEntityList = list.stream().map(entity -> {
            List<StockoutShipmentItemEntity> stockoutShipmentItemEntities = collect.get(entity.getSku());
            StockTransferCrossSpaceItemEntity itemEntity = new StockTransferCrossSpaceItemEntity();
            itemEntity.setTransferItemId(entity.getTransferItemId());
            itemEntity.setExpectedTransferQty(CollectionUtils.isEmpty(stockoutShipmentItemEntities) ? 0
                    : stockoutShipmentItemEntities.stream().mapToInt(StockoutShipmentItemEntity::getQty).sum());
            return itemEntity;
        }).collect(Collectors.toList());
        stockTransferCrossSpaceItemService.updateBatchById(stockTransferCrossSpaceItemEntityList);

        List<StockTransferCrossSpaceItemEntity> listOut = stockTransferCrossSpaceItemService.list(new LambdaQueryWrapper<StockTransferCrossSpaceItemEntity>().eq(StockTransferCrossSpaceItemEntity::getTransferId, outSpaceEntity.getTransferId()));
        List<StockTransferCrossSpaceItemEntity> outItemEntityList = listOut.stream().map(entity -> {
            List<StockoutShipmentItemEntity> stockoutShipmentItemEntities = collect.get(entity.getSku());
            StockTransferCrossSpaceItemEntity itemEntity = new StockTransferCrossSpaceItemEntity();
            itemEntity.setTransferItemId(entity.getTransferItemId());
            itemEntity.setTransferQty(CollectionUtils.isEmpty(stockoutShipmentItemEntities) ? 0
                    : stockoutShipmentItemEntities.stream().mapToInt(StockoutShipmentItemEntity::getQty).sum());
            return itemEntity;
        }).collect(Collectors.toList());
        stockTransferCrossSpaceItemService.updateBatchById(outItemEntityList);
    }

    @Transactional
    public void generateStockinOrder(String stockoutOrderNo) {
        //查询出库单
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        if (Objects.isNull(stockoutOrderEntity)) {
            throw new BusinessServiceException("未找到出库单");
        }
        StockTransferCrossSpaceEntity outSpaceEntity = this.getOne(new QueryWrapper<StockTransferCrossSpaceEntity>().lambda().eq(StockTransferCrossSpaceEntity::getTransferNo, stockoutOrderNo)
                .eq(StockTransferCrossSpaceEntity::getTransferType, StockTransferTypeEnum.TRANSFER_OUT.name()).last("limit 1"));
        if (Objects.isNull(outSpaceEntity)) {
            throw new BusinessServiceException("未找到出库调拨单");
        }
        StockTransferCrossSpaceEntity inSpaceEntity = this.getOne(new QueryWrapper<StockTransferCrossSpaceEntity>().lambda().eq(StockTransferCrossSpaceEntity::getTransferTaskNo, outSpaceEntity.getTransferTaskNo())
                .eq(StockTransferCrossSpaceEntity::getTransferType, StockTransferTypeEnum.TRANSFER_INBOUND.name()).last("limit 1"));
        if (Objects.isNull(inSpaceEntity)) {
            throw new BusinessServiceException("未找到入库调拨单");
        }
        //查询装箱清单
        List<StockoutShipmentItemEntity> stockoutShipmentItemEntityList = stockoutShipmentItemService.list(new LambdaQueryWrapper<StockoutShipmentItemEntity>()
                .eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo)
                .eq(StockoutShipmentItemEntity::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(stockoutShipmentItemEntityList)) {
            throw new BusinessServiceException("未找到装箱清单");
        }
        //回写入库调拨单
        setBackStockTransfer(outSpaceEntity, inSpaceEntity, stockoutShipmentItemEntityList);
        generateStockinOrder(inSpaceEntity, stockoutShipmentItemEntityList);

    }

    private void generateStockinOrder(StockTransferCrossSpaceEntity inSpaceEntity, List<StockoutShipmentItemEntity> stockoutShipmentItemEntityList) {
        List<StockoutShipmentEntity> stockoutShipmentEntityList = stockoutShipmentService.list(new LambdaQueryWrapper<StockoutShipmentEntity>()
                .in(StockoutShipmentEntity::getShipmentId, stockoutShipmentItemEntityList.stream().map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(stockoutShipmentEntityList)) {
            throw new BusinessServiceException("未找到装箱清单");
        }
        List<StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier> suppliers = new ArrayList<>();
        StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier stockPlatformScheduleSupplier = getStockPlatformScheduleSupplier(inSpaceEntity, stockoutShipmentItemEntityList, stockoutShipmentEntityList);
        suppliers.add(stockPlatformScheduleSupplier);

        StockPlatformScheduleSupplierAddRequest stockPlatformScheduleSupplierAddRequest = new StockPlatformScheduleSupplierAddRequest();
        stockPlatformScheduleSupplierAddRequest.setPlatformScheduleSupplierList(suppliers);
        stockPlatformScheduleService.supplierAddStockPlatformSchedule(stockPlatformScheduleSupplierAddRequest);
    }

    @NotNull
    private StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier getStockPlatformScheduleSupplier(StockTransferCrossSpaceEntity inSpaceEntity, List<StockoutShipmentItemEntity> stockoutShipmentItemEntityList, List<StockoutShipmentEntity> value) {
        StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier request = new StockPlatformScheduleSupplierAddRequest.StockPlatformScheduleSupplier();
        request.setSupplierDeliveryNo(inSpaceEntity.getTransferNo());
        request.setStockinType(StockinTypeEnum.ALLOT);
        request.setSupplierName(inSpaceEntity.getStockoutSpaceName());
        Map<String, List<StockoutShipmentEntity>> collect = value.stream().collect(Collectors.groupingBy(StockoutShipmentEntity::getShipmentBoxCode));
        request.setPlanBoxNum(collect.size());
        request.setDeliveryDate(new Date());
        List<StockPlatformScheduleSupplierAddRequest.Item> itemList = getItemList(inSpaceEntity, stockoutShipmentItemEntityList, value);
        request.setItem(itemList);
        return request;
    }

    private List<StockPlatformScheduleSupplierAddRequest.Item> getItemList(StockTransferCrossSpaceEntity inSpaceEntity, List<StockoutShipmentItemEntity> stockoutShipmentItemEntityList, List<StockoutShipmentEntity> value) {
        List<Integer> shipmentIds = value.stream().map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList());
        Map<Integer, StockoutShipmentEntity> stockoutShipmentEntityMap = value.stream().collect(Collectors.groupingBy(StockoutShipmentEntity::getShipmentId, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))));
        List<StockoutShipmentItemEntity> stockoutShipmentItemEntities = stockoutShipmentItemEntityList.stream().filter(entity -> shipmentIds.contains(entity.getShipmentId())).collect(Collectors.toList());
        return stockoutShipmentItemEntities.stream().map(itemEntity -> {
            StockPlatformScheduleSupplierAddRequest.Item item = new StockPlatformScheduleSupplierAddRequest.Item();
            StockoutShipmentEntity stockoutShipmentEntity = stockoutShipmentEntityMap.get(itemEntity.getShipmentId());
            item.setBoxIndex(stockoutShipmentEntity.getBoxIndex());
            item.setOrderNo("");
            item.setSku(itemEntity.getSku());
            item.setQty(itemEntity.getQty());
            item.setSpaceId(inSpaceEntity.getStockinSpaceId());
            item.setSupplierDeliveryBoxCode(stockoutShipmentEntity.getShipmentBoxCode());
            return item;
        }).collect(Collectors.toList());
    }

    public PageResponse<StockTransferClearSkuResponse> getClearSkuList(StockTransferClearSkuRequest request) {
        Validator.isValid(request.getSpaceId(), Objects::nonNull, "仓库id不能为空");
        PageResponse<StockTransferClearSkuResponse> pageResponse = new PageResponse<>();
        Page<StockTransferClearSkuResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockTransferClearSkuResponse> stockListResponseIPage = stockService.getBaseMapper().pageClearSkuList(page, request);
        pageResponse.setContent(stockListResponseIPage.getRecords());
        pageResponse.setTotalCount(stockListResponseIPage.getTotal());
        return pageResponse;
    }

    @Transactional
    public void addClearSku(StockTransferClearAddRequest request) {
        Validator.isValid(request.getSpaceId(), Objects::nonNull, "仓库id不能为空");
        Validator.isValid(request.getSpecIdList(), list -> !CollectionUtils.isEmpty(list), "规格id不能为空");
        List<StockEntity> list = stockService.getBaseMapper().listClearSku(request);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("未找到sku库存");
        }
        Map<Integer, ProductSpecInfoEntity> collect = productSpecInfoService.findAllBySpecIdIn(list.stream().map(StockEntity::getSpecId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(ProductSpecInfoEntity::getSpecId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        buildStockTransferclearOut(request.getSpaceId(), list, collect);
    }

    public void buildStockTransferclearOut(Integer spaceId, List<StockEntity> stockEntityList, Map<Integer, ProductSpecInfoEntity> collect) {
        StockTransferCrossSpaceEntity spaceEntity = new StockTransferCrossSpaceEntity();
        spaceEntity.setTransferType(StockTransferTypeEnum.CLEAR_OUT.name());
        String code = codeService.randomTimeCode("");
        spaceEntity.setTransferTaskNo(code);
        spaceEntity.setTransferNo("QCCK" + code);
        spaceEntity.setStockoutSpaceId(spaceId);
        spaceEntity.setStatus(StockTransferStatusEnum.WAIT_OUTBOUND.name());
        spaceEntity.setSpaceId(spaceId);
        spaceEntity.setLocation(TenantContext.getTenant());
        spaceEntity.setExpectedTransferQty(stockEntityList.stream().mapToInt(StockEntity::getStock).sum());
        spaceEntity.setTransferQty(0);
        String spaceName = bdSpaceService.getById(spaceId).getSpaceName();
        spaceEntity.setSpaceName(spaceName);
        spaceEntity.setStockoutSpaceName(spaceName);
        spaceEntity.setCreateBy(loginInfoService.getName());
        stockTransferCrossSpaceService.save(spaceEntity);
        this.buildStockTransferCrossSpaceItem(stockEntityList, spaceEntity, collect);
        //生成出库单
        this.buildStockoutOrder(spaceEntity, stockEntityList, collect);
    }

    public void buildStockTransferCrossSpaceItem(List<StockEntity> stockEntityList, StockTransferCrossSpaceEntity spaceEntity,
                                                 Map<Integer, ProductSpecInfoEntity> collect) {
        List<StockTransferCrossSpaceItemEntity> list = stockEntityList.stream().map(stock -> {
            StockTransferCrossSpaceItemEntity spaceItemEntity = new StockTransferCrossSpaceItemEntity();
            spaceItemEntity.setTransferQty(0);
            ProductSpecInfoEntity specInfoEntity = collect.get(stock.getSpecId());
            spaceItemEntity.setTransferId(spaceEntity.getTransferId());
            spaceItemEntity.setSpecId(specInfoEntity.getSpecId());
            spaceItemEntity.setSku(specInfoEntity.getSku());
            spaceItemEntity.setProductId(specInfoEntity.getProductId());
            spaceItemEntity.setLocation(stock.getLocation());
            spaceItemEntity.setExpectedTransferQty(stock.getStock());
            spaceItemEntity.setBarcode(specInfoEntity.getBarcode());
            spaceItemEntity.setCreateBy(loginInfoService.getName());
            return spaceItemEntity;
        }).collect(Collectors.toList());
        stockTransferCrossSpaceItemService.saveBatch(list);
    }


    //新增出库单-清仓出库
    public void buildStockoutOrder(StockTransferCrossSpaceEntity spaceEntity, List<StockEntity> stockEntityList,
                                   Map<Integer, ProductSpecInfoEntity> collect) {
        StockoutOrderEntity stockoutOrderEntity = new StockoutOrderEntity();
        stockoutOrderEntity.setCustomsDeclareType(CustomsDeclareEnum.DECLARE_NORMAL.getValue());
        stockoutOrderEntity.setPickingType(StockoutPickingTypeEnum.WHOLE_PICK.name());
        stockoutOrderEntity.setStatus(StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
        stockoutOrderEntity.setLack(Boolean.FALSE);
        stockoutOrderEntity.setUrgent(Boolean.FALSE);
        stockoutOrderEntity.setStockoutType(StockoutOrderTypeEnum.CLEAR_DELIVERY.name());
        stockoutOrderEntity.setStockoutOrderNo(spaceEntity.getTransferNo());
        stockoutOrderEntity.setSpaceId(spaceEntity.getStockoutSpaceId());
        stockoutOrderEntity.setNeedProcess(Boolean.FALSE);
        stockoutOrderEntity.setLocation(TenantContext.getTenant());
        stockoutOrderEntity.setWorkspace(StockoutOrderWorkSpaceEnum.OTHER_AREA.name());
        stockoutOrderEntity.setCreateBy(loginInfoService.getName());
        stockoutOrderService.save(stockoutOrderEntity);
        this.buildStockoutOrderItem(stockoutOrderEntity, stockEntityList, collect);
        String pickingTypeEnumStr = bdPickingTypeRuleService.getPickingTypeEnumStr(stockoutOrderEntity.getStockoutOrderId(), stockoutOrderEntity.getSpaceId(), stockoutOrderEntity.getStockoutType(), stockoutOrderEntity.getWorkspace());
        StockoutOrderEntity stockoutOrderEntity1 = new StockoutOrderEntity();
        stockoutOrderEntity1.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        stockoutOrderEntity1.setPickingType(pickingTypeEnumStr);
        stockoutOrderService.updateById(stockoutOrderEntity1);
        //生成发货人和收件人
        stockoutShipperInfoService.addShipperInfoFromStockoutOrder(stockoutOrderEntity);
        stockoutReceiverInfoService.addReceiverInfoFromSotckoutOrder(stockoutOrderEntity);
    }

    private void buildStockoutOrderItem(StockoutOrderEntity stockoutOrderEntity, List<StockEntity> stockEntityList, Map<Integer, ProductSpecInfoEntity> collect) {
        List<StockoutOrderItemEntity> list = stockEntityList.stream().map(stock -> {
            StockoutOrderItemEntity itemEntity = new StockoutOrderItemEntity();
            ProductSpecInfoEntity specInfoEntity = collect.get(stock.getSpecId());
//            ProductInfoEntity productInfoEntity = productInfoService.getOne(new QueryWrapper<ProductInfoEntity>().lambda()
//                    .eq(ProductInfoEntity::getProductId, specInfoEntity.getProductId()));
//            ProductWmsCategorySpaceEntity categorySpaceEntity = categorySpaceMapper.selectOne(new QueryWrapper<ProductWmsCategorySpaceEntity>().lambda()
//                    .eq(ProductWmsCategorySpaceEntity::getWmsCategoryId, productInfoEntity.getWmsCategoryId()));
//            if (categorySpaceEntity != null)
//                itemEntity.setSpaceAreaName(categorySpaceEntity.getSpaceAreaName());
            itemEntity.setSku(specInfoEntity.getSku());
            itemEntity.setBarcode(specInfoEntity.getBarcode());
            itemEntity.setLack(Boolean.FALSE);
            itemEntity.setLocation(stockoutOrderEntity.getLocation());
            itemEntity.setOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            itemEntity.setProductId(specInfoEntity.getProductId());
            itemEntity.setQty(stock.getStock());
            itemEntity.setScanQty(0);
            itemEntity.setShipmentQty(0);
            itemEntity.setSpecId(specInfoEntity.getSpecId());
            itemEntity.setStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            itemEntity.setLackQty(0);
            itemEntity.setOrderItemId("");
            return itemEntity;
        }).collect(Collectors.toList());
        stockoutOrderItemService.saveBatch(list);
    }

}
