package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.request.temu.TemuAuth;
import com.nsy.api.wms.request.temu.TemuGetBoxmarkinfoRequest;
import com.nsy.api.wms.request.temu.TemuGetLabelRequest;
import com.nsy.api.wms.request.temu.TemuSendShiporderRequest;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.temu.SauPddConfigResponse;
import com.nsy.api.wms.response.temu.TemuSendShiporderResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutOrderTemuGetLabelBo;
import com.nsy.wms.business.manage.notify.NotifyApiService;
import com.nsy.wms.business.manage.notify.request.DingTalkRobotWebhookSendRequest;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.wms.business.service.bd.TemuWarehouseService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.stockout.ship.StockoutShipService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.TemuWarehouseEntity;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderStoreManufacturerMappingEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuExtendEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderTemuExtendItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderTemuExtendItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderTemuExtendMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.PrintTransferUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockoutOrderTemuExtendService extends ServiceImpl<StockoutOrderTemuExtendMapper, StockoutOrderTemuExtendEntity> {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderTemuExtendService.class);

    @Resource
    StockoutOrderService stockoutOrderService;
    @Resource
    StockoutOrderItemService stockoutOrderItemService;
    @Resource
    ThirdPartyApiService thirdPartyApiService;
    @Resource
    OmsApiService omsApiService;
    @Resource
    StockoutOrderTemuExtendItemService extendItemService;
    @Resource
    StockoutOrderLogService stockoutOrderLogService;
    @Resource
    NotifyApiService notifyApiService;
    @Resource
    TemuWarehouseService warehouseService;
    @Resource
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutOrderTemuExtendItemMapper temuExtendItemMapper;
    @Autowired
    StockoutOrderTemuExtendShiporderService shiporderService;
    @Autowired
    StockoutOrderShipService stockoutOrderShipService;
    @Autowired
    StockoutShipmentErpPickingBoxService erpPickingBoxService;
    @Autowired
    StockoutShipService stockoutShipService;
    @Resource
    PrintTemplateService printTemplateService;
    @Resource
    StockoutShipmentService shipmentService;
    @Resource
    StockoutOrderStoreManufacturerMappingService stockoutOrderStoreManufacturerMappingService;


    /**
     * TEMU打印地址
     */
    private static final String PRINT_URL = "https://openapi.kuajingmaihuo.com/tool/print?dataKey=%s";

    /**
     * 同步条码
     * <p>
     * 1. 查询【准备中】【拼多多】平台的出库单
     * 2. 获取拼多多条码信息，补充扩展表
     * 3. 修改出库单状态【待生成波次】
     * 3. 同步失败发送钉钉
     */
    @Transactional
    public void syncTemuLabel() {
        List<String> stockoutOrderNoList = this.baseMapper.getReadyList(Boolean.FALSE);
        if (stockoutOrderNoList.isEmpty()) {
            LOGGER.info("没有数据");
            return;
        }
        StringBuilder sendDingMsgBuilder = new StringBuilder();
        LOGGER.info(CollectionUtil.join(stockoutOrderNoList, ";"));
        stockoutOrderNoList.forEach(stockoutOrderNo -> {
            try {
                StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
                SpringUtil.getBean(StockoutOrderTemuExtendService.class).syncPerTemuLabel(stockoutOrder);
            } catch (BusinessServiceException e) {
                sendDingMsgBuilder.append(e.getMessage()).append(';');
                LOGGER.error(e.getMessage(), e);
            } catch (Exception e) {
                LOGGER.error(e.getMessage(), e);
            }
        });

        //发送钉钉
        if (sendDingMsgBuilder.length() > 0) {
            DingTalkRobotWebhookSendRequest request = new DingTalkRobotWebhookSendRequest();
            request.setMsgType("text");
            request.setText(new DingTalkRobotWebhookSendRequest.Text(String.format("wms消息：拼多多获取条码失败：%s", sendDingMsgBuilder)));
            notifyApiService.sendTemuWebhookMessage(request);
        }

    }

    /**
     * 逐个单子同步
     *
     * @param stockoutOrder
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void syncPerTemuLabel(StockoutOrderEntity stockoutOrder) {
        List<StockoutOrderItemEntity> stockoutOrderItemList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
        if (stockoutOrderItemList.isEmpty()) {
            throw new BusinessServiceException(String.format("【%s】出库单明细为空", stockoutOrder.getStockoutOrderNo()));
        }
        //补充字段
        fillEmptyField(stockoutOrder.getStockoutOrderNo());
        //获取pdd店铺权限
        TemuAuth temuAuth = fetchTemuAuth(stockoutOrder.getStoreId());
        List<StockoutOrderTemuExtendItemEntity> extendItemList = extendItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
        List<Long> productSkuIdList = extendItemList.stream().map(StockoutOrderTemuExtendItemEntity::getProductSkuId).collect(Collectors.toList());
        //获取pdd条形码
        Map<Long, StockoutOrderTemuGetLabelBo> labelBoMap = fetchLabelBoMap(temuAuth, productSkuIdList, stockoutOrder.getStockoutOrderNo());
        extendItemList.forEach(item -> {
            StockoutOrderTemuGetLabelBo labelBo = labelBoMap.get(item.getProductSkuId());
            if (ObjectUtil.isNull(labelBo))
                throw new BusinessServiceException(String.format("出库单【%s】，ProductSkuId【%s】，查询不到商品条码", stockoutOrder.getStockoutOrderNo(), item.getProductSkuId()));
            if (!StringUtils.hasText(labelBo.getLabelInfo()))
                throw new BusinessServiceException(String.format("出库单【%s】，ProductSkuId【%s】，尺寸信息为空", stockoutOrder.getStockoutOrderNo(), item.getProductSkuId()));
            if (!StringUtils.hasText(labelBo.getColor()))
                throw new BusinessServiceException(String.format("出库单【%s】，ProductSkuId【%s】，颜色信息为空", stockoutOrder.getStockoutOrderNo(), item.getProductSkuId()));
            if (ObjectUtil.isNull(labelBo.getLabelCode()))
                throw new BusinessServiceException(String.format("出库单【%s】，ProductSkuId【%s】，条码为空", stockoutOrder.getStockoutOrderNo(), item.getProductSkuId()));
            if (ObjectUtil.isNull(labelBo.getProductSkuId()))
                throw new BusinessServiceException(String.format("出库单【%s】，ProductSkuId【%s】，ProductSkuId为空", stockoutOrder.getStockoutOrderNo(), item.getProductSkuId()));

            extendItemService.update(new LambdaUpdateWrapper<StockoutOrderTemuExtendItemEntity>()
                    .eq(StockoutOrderTemuExtendItemEntity::getId, item.getId())
                    .set(StockoutOrderTemuExtendItemEntity::getColor, labelBo.getColor())
                    .set(StockoutOrderTemuExtendItemEntity::getLabelInfo, labelBo.getLabelInfo())
                    .set(StockoutOrderTemuExtendItemEntity::getLabelCode, labelBo.getLabelCode()));
        });
        //更新已获取条码
        this.update(new LambdaUpdateWrapper<StockoutOrderTemuExtendEntity>()
                .set(StockoutOrderTemuExtendEntity::getIsGetLabel, Boolean.TRUE)
                .eq(StockoutOrderTemuExtendEntity::getStockoutOrderId, stockoutOrder.getStockoutOrderId()));

        stockoutOrderLogService.addLog(stockoutOrder.getStockoutOrderNo(), StockoutOrderLogTypeEnum.PDD_GET_LABEL, "获取条码完成");
    }

    /**
     * 补充字段
     *
     * @param stockoutOrderNo
     */
    private void fillEmptyField(String stockoutOrderNo) {
        StockoutOrderTemuExtendEntity extendEntity = this.findByStockoutOrderNo(stockoutOrderNo);
        if (!StringUtils.hasText(extendEntity.getSubWarehouseShortname())) {
            TemuWarehouseEntity temuWarehouse = warehouseService.findBySubWarehouseId(extendEntity.getSubWarehouseId());
            if (Objects.isNull(temuWarehouse))
                throw new BusinessServiceException(String.format("子仓id【%s】未维护", extendEntity.getSubWarehouseId()));
            this.update(new LambdaUpdateWrapper<StockoutOrderTemuExtendEntity>()
                    .eq(StockoutOrderTemuExtendEntity::getStockoutOrderNo, stockoutOrderNo)
                    .set(StockoutOrderTemuExtendEntity::getSubWarehouseShortname, temuWarehouse.getWarehouseShortName()));
        }

    }

    /**
     * 获取temu的条形码信息
     *
     * @return
     */
    private Map<Long, StockoutOrderTemuGetLabelBo> fetchLabelBoMap(TemuAuth temuAuth, List<Long> productSkuIdList, String stockoutOrderNo) {
        TemuGetLabelRequest getGoodsLabelRequest = new TemuGetLabelRequest();
        getGoodsLabelRequest.setPage(1L);
        getGoodsLabelRequest.setPageSize(100L);
        getGoodsLabelRequest.setTemuAuth(temuAuth);
        Map<Long, StockoutOrderTemuGetLabelBo> getLabelBoMap = new HashMap<>(16);

        for (Long productSkuId : productSkuIdList) {
            getGoodsLabelRequest.setProductSkuIdList(Collections.singletonList(productSkuId));
            LOGGER.info(JsonMapper.toJson(getGoodsLabelRequest));
            String response = thirdPartyApiService.getGoodsLabel(getGoodsLabelRequest);
            JSONObject jobj = JSONUtil.parseObj(response);
            JSONArray labelJarr = jobj.getJSONObject("labelCodePageResult").getJSONArray("data");
            if (labelJarr.isEmpty()) {
                throw new BusinessServiceException(String.format("出库单【%s】, productSkuId【%s】没有获取到条码", stockoutOrderNo, productSkuId));
            }
            JSONObject labelJobj = labelJarr.getJSONObject(0);
            getLabelBoMap.put(productSkuId, buildGetLabelBo(stockoutOrderNo, productSkuId, labelJobj));
        }
        return getLabelBoMap;
    }

    private StockoutOrderTemuGetLabelBo buildGetLabelBo(String stockoutOrderNo, Long productSkuId, JSONObject labelJobj) {
        StockoutOrderTemuGetLabelBo bo = new StockoutOrderTemuGetLabelBo();
        JSONObject productSkuDTO = labelJobj.getJSONObject("productSkuDTO");
        if (ObjectUtil.isNull(productSkuDTO))
            throw new BusinessServiceException(String.format("出库单【%s】, productSkuId【%s】 获取sku 信息失败", stockoutOrderNo, productSkuId));
        bo.setSku(productSkuDTO.getStr("extCode"));
        bo.setProductSkuId(productSkuDTO.getLong("productSkuId"));

        JSONObject productSkuLabelCodeDTO = labelJobj.getJSONObject("productSkuLabelCodeDTO");
        if (ObjectUtil.isNotNull(productSkuLabelCodeDTO))
            bo.setLabelCode(productSkuLabelCodeDTO.getLong("labelCode"));

        JSONObject productSkuSpecI18nMap = labelJobj.getJSONObject("productSkuSpecI18nMap");
        if (ObjectUtil.isNotNull(productSkuSpecI18nMap)) {
            JSONArray productSkuSpecArry = productSkuSpecI18nMap.getJSONArray("en");
            if (ObjectUtil.isNull(productSkuSpecArry))
                throw new BusinessServiceException(String.format("出库单【%s】, productSkuId【%s】获取sku 详细信息失败", stockoutOrderNo, productSkuId));

            productSkuSpecArry.jsonIter().forEach(productSkuSpec -> {
                if ("Color".equals(productSkuSpec.getStr("parentSpecName"))) {
                    bo.setColor(productSkuSpec.getStr("specName"));
                }
                if ("Size".equals(productSkuSpec.getStr("parentSpecName"))) {
                    bo.setLabelInfo(productSkuSpec.getStr("specName"));
                }
            });
        }

        return bo;
    }

    /**
     * 获取店铺接口权限
     *
     * @param storeId
     * @return
     */
    public TemuAuth fetchTemuAuth(Integer storeId) {
        SauPddConfigResponse pddAuth = omsApiService.getPddAuthByStoreId(storeId);
        if (Objects.isNull(pddAuth)) throw new BusinessServiceException(String.format("店铺【%s】没有temu鉴权", storeId));
        TemuAuth temuAuth = new TemuAuth();
        temuAuth.setAccessToken(pddAuth.getAccessToken());
        temuAuth.setApiUrl(pddAuth.getUrl());
        temuAuth.setAppKey(pddAuth.getAppkey());
        temuAuth.setAppSecret(pddAuth.getAppsecret());
        return temuAuth;
    }

//    /**
//     * 检查是否所有item获取条码
//     *
//     * @return
//     */
//    private Boolean checkAllGetLabel(Integer stockoutOrderId) {
//        List<StockoutOrderTemuExtendItemEntity> extendItemList = extendItemService.listByStockoutOrderId(stockoutOrderId);
//        return extendItemList.stream().allMatch(extendItem -> ObjectUtil.isNotNull(extendItem.getLabelCode()));
//    }


    /**
     * 通过出库单号查找
     *
     * @param stockoutOrderNo
     * @return
     */
    public StockoutOrderTemuExtendEntity findByStockoutOrderNo(String stockoutOrderNo) {
        return getOne(new LambdaQueryWrapper<StockoutOrderTemuExtendEntity>().eq(StockoutOrderTemuExtendEntity::getStockoutOrderNo, stockoutOrderNo));
    }

    /**
     * 通过出库单号查找
     *
     * @param stockoutOrderNo
     * @return
     */
    public StockoutOrderTemuExtendEntity getByStockoutOrderNo(String stockoutOrderNo) {
        StockoutOrderTemuExtendEntity stockoutOrderTemuExtendEntity = findByStockoutOrderNo(stockoutOrderNo);
        if (Objects.isNull(stockoutOrderTemuExtendEntity))
            throw new BusinessServiceException(String.format("扩展表未找到 %s", stockoutOrderNo));
        return stockoutOrderTemuExtendEntity;
    }

    /**
     * 通过出库单号查找
     *
     * @param stockoutOrderNoList
     * @return
     */
    public List<StockoutOrderTemuExtendEntity> listByStockoutOrderNoList(List<String> stockoutOrderNoList) {
        if (CollectionUtils.isEmpty(stockoutOrderNoList)) return new ArrayList<>();
        return list(new LambdaQueryWrapper<StockoutOrderTemuExtendEntity>().in(StockoutOrderTemuExtendEntity::getStockoutOrderNo, stockoutOrderNoList));
    }

    /**
     * 通过出库单id查找
     *
     * @param stockoutOrderIdList
     * @return
     */
    public List<StockoutOrderTemuExtendEntity> listByStockoutOrderIdList(List<Integer> stockoutOrderIdList) {
        if (CollectionUtils.isEmpty(stockoutOrderIdList)) return new ArrayList<>();
        return list(new LambdaQueryWrapper<StockoutOrderTemuExtendEntity>().in(StockoutOrderTemuExtendEntity::getStockoutOrderId, stockoutOrderIdList));
    }


    /**
     * 打印箱唛
     *
     * @param stockoutOrderNos
     * @return
     */
    @Transactional
    public String printBoxmarkinfo(StringListRequest stockoutOrderNos) {
        List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos.getStringList());
        if (stockoutOrderList.isEmpty()) throw new BusinessServiceException("出库单为空");
        //a. 判断出库单 【拼多多】
        boolean allPdd = stockoutOrderList.stream().allMatch(stockoutOrder -> StockoutOrderPlatformEnum.PDD.getName().equals(stockoutOrder.getPlatformName()));
        if (!allPdd) throw new BusinessServiceException("部分出库单非拼多多");
        //b. 判断店铺是否一样
        long distinctStore = stockoutOrderList.stream().map(StockoutOrderEntity::getStoreId).distinct().count();
        if (distinctStore != 1)
            throw new BusinessServiceException("店铺不相同");
        List<Integer> stockoutOrderIdList = stockoutOrderList.stream().map(StockoutOrderEntity::getStockoutOrderId).distinct().collect(Collectors.toList());
        List<StockoutOrderTemuExtendEntity> extendList = this.listByStockoutOrderIdList(stockoutOrderIdList);
        List<StockoutOrderTemuExtendEntity> unGetShiperorderList = extendList.stream().filter(extendEntity -> !StringUtils.hasText(extendEntity.getDeliveryOrderSn())).collect(Collectors.toList());
        //打印箱唛前，调用获取发货单
        if (!unGetShiperorderList.isEmpty())
            shiporderService.getShiporderAndBackfill(unGetShiperorderList.stream().map(StockoutOrderTemuExtendEntity::getStockoutOrderId).collect(Collectors.toList()));

        extendList = this.listByStockoutOrderIdList(stockoutOrderIdList);
        extendList.forEach(extendEntity -> {
            if (!StringUtils.hasText(extendEntity.getDeliveryOrderSn())) {
                throw new BusinessServiceException(String.format("出库单【%s】发货单还在创建中，请稍等", extendEntity.getStockoutOrderNo()));
            }
            stockoutOrderLogService.addLog(extendEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.PDD_PRINT_BOXMARKINFO, "打印箱唛");
        });

        List<String> deliveryOrderSnList = extendList.stream().map(StockoutOrderTemuExtendEntity::getDeliveryOrderSn).collect(Collectors.toList());
        TemuGetBoxmarkinfoRequest request = new TemuGetBoxmarkinfoRequest();
        request.setTemuAuth(fetchTemuAuth(stockoutOrderList.get(0).getStoreId()));
        request.setDeliveryOrderSnList(deliveryOrderSnList);
        String data = thirdPartyApiService.getBoxmarkinfo(request);
        return String.format(PRINT_URL, data);
    }

    /**
     * 拼多多操作发货
     *
     * @param stockoutOrderNoList
     */
    public void temuSendShiporder(List<String> stockoutOrderNoList) {
        stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList)
                .stream()
                .collect(Collectors.groupingBy(StockoutOrderEntity::getStoreId))
                .forEach((storeId, stockoutOrderList) -> {
                    temuSendShiporder(stockoutOrderNoList, storeId);
                });
    }

    /**
     * 拼多多操作发货
     *
     * @param stockoutOrderNoList
     */
    private void temuSendShiporder(List<String> stockoutOrderNoList, Integer storeId) {
        if (stockoutOrderNoList.isEmpty()) throw new BusinessServiceException("出库单列表为空");
        List<StockoutOrderTemuExtendEntity> temuExtendList = this.listByStockoutOrderNoList(stockoutOrderNoList);
        List<String> deliveryOrderSnList = temuExtendList.stream().map(StockoutOrderTemuExtendEntity::getDeliveryOrderSn).collect(Collectors.toList());
        LOGGER.info(JsonMapper.toJson(deliveryOrderSnList));
        //操作发货
        TemuSendShiporderRequest request = new TemuSendShiporderRequest();
        request.setTemuAuth(fetchTemuAuth(storeId));
        request.setDeliveryOrderSnList(deliveryOrderSnList);
        TemuSendShiporderResponse response = thirdPartyApiService.sendShiporder(request);
        if (!response.getIsSuccess()) {
            throw new BusinessServiceException(response.getErrorInfoVOList().stream()
                    .map(temp -> String.format("【%s】:%s %s", temp.getId(), temp.getErrorCode(), temp.getErrorMsg()))
                    .collect(Collectors.joining(",")));
        }

    }


    /**
     * 通过订单号查找
     *
     * @param orderNo
     * @return
     */
    public List<StockoutOrderTemuExtendEntity> listByOrderNo(String orderNo) {
        return list(new LambdaQueryWrapper<StockoutOrderTemuExtendEntity>()
                .eq(StockoutOrderTemuExtendEntity::getOrderNo, orderNo));
    }

    /**
     * 通过订单号查找
     *
     * @param orderNo
     * @return
     */
    public StockoutOrderTemuExtendEntity findByOrderNo(String orderNo) {
        return baseMapper.findByOrderNo(orderNo);
    }


    /**
     * 按订单号删除
     *
     * @param orderNos
     */
    public void del(StringListRequest orderNos) {
        if (orderNos.getStringList().isEmpty()) throw new BusinessServiceException("订单号不能为空");
        remove(new LambdaQueryWrapper<StockoutOrderTemuExtendEntity>().in(StockoutOrderTemuExtendEntity::getOrderNo, orderNos.getStringList()));
    }

    /**
     * 发货
     *
     * @param stockoutOrderNoList
     */
    @Transactional
    @JLock(keyConstant = "temuDeliver", lockKey = "#stockoutOrderNoList[0]")
    public void deliver(List<String> stockoutOrderNoList) {
        List<StockoutOrderTemuExtendEntity> extendList = listByStockoutOrderNoList(stockoutOrderNoList);
        Map<String, StockoutOrderTemuExtendEntity> extendMap = extendList.stream().collect(Collectors.toMap(StockoutOrderTemuExtendEntity::getStockoutOrderNo, Function.identity(), (v1, v2) -> v1));
        stockoutOrderNoList.forEach(stockoutOrderNo -> {
            StockoutOrderTemuExtendEntity extend = extendMap.get(stockoutOrderNo);
            if (Objects.isNull(extend))
                throw new BusinessServiceException(String.format("%s 非拼多多单", stockoutOrderNo));
            if (StrUtil.isEmpty(extend.getDeliveryOrderSn()))
                throw new BusinessServiceException(String.format("%s 未创建发货单", stockoutOrderNo));
        });

        StockoutShipmentConfirmUpdateRequest request = new StockoutShipmentConfirmUpdateRequest();
        request.setStockoutOrderNos(stockoutOrderNoList);
        List<Integer> shipmentIdList = shipmentItemService.findByStockoutOrderNo(stockoutOrderNoList)
                .stream()
                .map(StockoutShipmentEntity::getShipmentId)
                .collect(Collectors.toList());
        request.setShipmentIds(shipmentIdList);

        List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList);
        stockoutOrderList.forEach(stockoutOrder -> {
            if (StockoutOrderStatusEnum.DELIVERED.name().equals(stockoutOrder.getStatus()))
                throw new BusinessServiceException(String.format("出库单 %s 已发货", stockoutOrder.getStockoutOrderNo()));
        });
        //操作发货
        stockoutShipService.shipShipmentConfirm(request, true);
    }

    /**
     * 超过三天未抢仓预警
     */
    public void notifyNoWarehouseOverThreeDay() {
        List<StockoutOrderTemuExtendEntity> noWarehouseOverThreeDayList = baseMapper.findNoWarehouseOverThreeDay();
        if (noWarehouseOverThreeDayList.isEmpty()) {
            LOGGER.info("不存在超过三天未抢仓订单");
            return;
        }

        DingTalkRobotWebhookSendRequest request = new DingTalkRobotWebhookSendRequest();
        request.setMsgType("text");
        request.setText(new DingTalkRobotWebhookSendRequest.Text(String.format("wms消息：%s 抢占发货单失败，即将过期， 请及时关注！", noWarehouseOverThreeDayList.stream().map(StockoutOrderTemuExtendEntity::getOrderNo).collect(Collectors.joining(",")))));
        notifyApiService.sendTemuGrabWebhookMessage(request);


//        // 1. 获取所有【抓单】的，【待发货】的出库单
//        List<StockoutOrderTemuExtendEntity> noCreateShiporderList = this.baseMapper.findNoCreateShiporderList(StockoutOrderStatusEnum.READY_DELIVERY.name());
//        List<String> stockoutOrderNoList = noCreateShiporderList.stream().map(StockoutOrderTemuExtendEntity::getStockoutOrderNo).collect(Collectors.toList());
//        if (stockoutOrderNoList.isEmpty()) return;
//        List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNoList);
//        if (stockoutOrderList.isEmpty()) return;
//        Map<String, String> orderNoStockoutOrderNoMap = noCreateShiporderList.stream().collect(Collectors.toMap(StockoutOrderTemuExtendEntity::getOrderNo, StockoutOrderTemuExtendEntity::getStockoutOrderNo, (v1, v2) -> v1));
//        Map<String, String> stockoutOrderStoreNameMap = stockoutOrderList.stream().collect(Collectors.toMap(StockoutOrderEntity::getStockoutOrderNo, StockoutOrderEntity::getStoreName, (v1, v2) -> v1));
//        TemuGetPurchaseOrderInfoListResponse infoListResponse = stockoutOrderTemuExtendInfoService.fetchPurchaseOrderInfoWithAddr(stockoutOrderList, noCreateShiporderList);
//
//        //作废的
//        Map<String, List<StockoutTemuStoreNameBo>> storeNameMap = infoListResponse.getItemList().stream()
//                .filter(item -> ObjectUtil.isNotNull(item.getPurchaseOrder()))
//                .map(item -> {
//                    if (item.getPurchaseOrder().getStatus() != 8) {
//                        return null;
//                    }
//                    StockoutTemuStoreNameBo bo = new StockoutTemuStoreNameBo();
//                    bo.setOrderNo(item.getPurchaseOrderNo());
//                    bo.setStoreName("");
//                    String stockoutOrderNo = orderNoStockoutOrderNoMap.get(item.getPurchaseOrderNo());
//                    if (StrUtil.isEmpty(stockoutOrderNo)) {
//                        return bo;
//                    }
//                    bo.setStoreName(stockoutOrderStoreNameMap.get(stockoutOrderNo));
//                    return bo;
//                })
//                .filter(Objects::nonNull)
//                .collect(Collectors.groupingBy(StockoutTemuStoreNameBo::getStoreName));
//
//        String orderNoStr = storeNameMap.entrySet().stream()
//                .map(entry -> String.format("【 %s 】 %s", entry.getKey(), entry.getValue().stream().map(StockoutTemuStoreNameBo::getOrderNo).collect(Collectors.joining(","))))
//                .collect(Collectors.joining(";"));
//        //作废订单发送钉钉
//        if (StrUtil.isNotEmpty(orderNoStr)) {
//            request = new DingTalkRobotWebhookSendRequest();
//            request.setMsgType("text");
//            request.setText(new DingTalkRobotWebhookSendRequest.Text(String.format("wms消息：拼多多订单作废：%s", orderNoStr)));
//            notifyApiService.sendTemuGrabWebhookMessage(request);
//        }
    }

    /**
     * 统计未抢占仓库数
     *
     * @return
     */
    public Integer countNoSeizureWarehouse() {
        return this.baseMapper.countNoSeizureWarehouse();
    }

    /**
     * 通过时间范围查找发货单
     *
     * @param begin
     * @param end
     * @return
     */
    public int countByShiporderCreateDate(Date begin, Date end) {
        return count(new LambdaQueryWrapper<StockoutOrderTemuExtendEntity>()
                .ge(StockoutOrderTemuExtendEntity::getShiporderCreateDate, begin)
                .le(StockoutOrderTemuExtendEntity::getShiporderCreateDate, end));
    }

    /**
     * 打印快递单号
     *
     * @param request
     * @return
     */
    public PrintListResponse printExpressDeliverySn(StringListRequest request) {
        PrintListResponse response = new PrintListResponse();

        List<StockoutOrderTemuExtendEntity> extendList = listByStockoutOrderNoList(request.getStringList());
        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.TEMU_EXPRESS_DELIVERY_SN.getTemplateName());
        List<String> printList = extendList.stream().filter(entity -> StrUtil.isNotEmpty(entity.getExpressDeliverySn()))
                .collect(Collectors.groupingBy(StockoutOrderTemuExtendEntity::getExpressDeliverySn))
                .values().stream()
                .map(entityList -> {
                    StockoutOrderTemuExtendEntity entity = entityList.get(0);
                    Map<String, String> data = new HashMap<>();
                    data.put("expressCompanyName", entity.getExpressCompanyName());
                    data.put("expressDeliverySn", entity.getExpressDeliverySn());
                    return PrintTransferUtils.transfer(templateEntity.getContent(), data);
                }).collect(Collectors.toList());

        response.setHtmlList(printList);
        response.setSpec(templateEntity.getSpec());
        response.setTemplateName(templateEntity.getName());
        return response;
    }

    public PrintListResponse printMergeLabel(String shipmentBoxCode) {
        StockoutShipmentEntity shipment = shipmentService.findTopByShipmentBoxCode(shipmentBoxCode);
        List<StockoutShipmentItemEntity> shipmentItemList = shipmentItemService.findByShipmentId(shipment.getShipmentId());
        if (shipmentItemList.isEmpty())
            throw new BusinessServiceException("装箱清单明细为空");
        StockoutShipmentItemEntity firstShipmentItem = shipmentItemList.get(0);
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(firstShipmentItem.getStockoutOrderNo());
        StockoutOrderStoreManufacturerMappingEntity storeManufacturerMapping = stockoutOrderStoreManufacturerMappingService.findByStoreId(stockoutOrder.getStoreId(), StockoutOrderPlatformEnum.PDD.getName());
        if (Objects.isNull(storeManufacturerMapping)) {
            throw new BusinessServiceException(String.format("temu店铺信息未配置 %s", stockoutOrder.getStoreName()));
        }

        PrintTemplateEntity templateEntity = printTemplateService.getByName(PrintTemplateNameEnum.TEMU_MERGE_LABEL.getTemplateName());

        List<String> htmlList = shipmentItemList.stream().map(shipmentItem -> {
            StockoutOrderTemuExtendItemEntity extendItem = extendItemService.getOneByStockoutOrderItemId(shipmentItem.getStockoutOrderItemId());
            if (Objects.isNull(extendItem))
                throw new BusinessServiceException(String.format("扩展表明细不存在 %s", shipmentItem.getStockoutOrderItemId()));
            Map<String, String> map = new HashMap<>();
            map.put("manufacturerName", storeManufacturerMapping.getManufacturerName());
            map.put("manufacturerAddress", storeManufacturerMapping.getManufacturerAddress());
            map.put("manufacturerEmail", storeManufacturerMapping.getManufacturerEmail());
            map.put("sku", extendItem.getSku());
            map.put("color", extendItem.getColor());
            map.put("labelInfo", extendItem.getLabelInfo());
            map.put("productSkuId", extendItem.getProductSkuId().toString());
            map.put("labelCode", extendItem.getLabelCode().toString());

            String html = PrintTransferUtils.transfer(templateEntity.getContent(), map);
            List<String> tempHtmlList = new ArrayList<>(shipmentItem.getQty());
            for (int i = 0; i < shipmentItem.getQty(); i++) {
                tempHtmlList.add(html);
            }
            return tempHtmlList;
        }).flatMap(Collection::stream).collect(Collectors.toList());

        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(htmlList);
        response.setTemplateName(templateEntity.getName());
        response.setSpec(templateEntity.getSpec());
        return response;
    }
}