package com.nsy.wms.business.service.stockout;

import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.wms.request.upload.UploadRequest;
import com.nsy.api.wms.response.upload.UploadResponse;
import com.nsy.wms.business.manage.erp.ErpStockoutApiService;
import com.nsy.wms.business.manage.thirdparty.OmsApiService;
import com.nsy.wms.business.manage.thirdparty.response.SaStorePageInfoResponse;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.manage.user.upload.StockoutLogisticsBatchShipmentImport;
import com.nsy.wms.business.service.upload.IProcessUploadDataService;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class StockoutLogisticsShipmentUploadService implements IProcessUploadDataService {
    @Resource
    private StockoutLogisticsBatchService logisticsBatchService;
    @Autowired
    ErpStockoutApiService erpStockoutApiService;
    @Autowired
    TmsCacheService tmsCacheService;
    @Autowired
    OmsApiService omsApiService;

    @Override
    public QuartzUploadQueueTypeEnum type() {
        return QuartzUploadQueueTypeEnum.WMS_LOGISTICS_BATCH_OTHER_ORDER;
    }

    @Override
    public UploadResponse processUploadData(UploadRequest request) {
        UploadResponse response = new UploadResponse();
        if (!StringUtils.hasText(request.getDataJsonStr())) {
            return response;
        }
        List<StockoutLogisticsBatchShipmentImport> importList = JsonMapper.jsonStringToObjectArray(request.getDataJsonStr(), StockoutLogisticsBatchShipmentImport.class);
        if (CollectionUtils.isEmpty(importList)) {
            return response;
        }
        List<StockoutLogisticsBatchShipmentImport> errorList = new ArrayList<>();
        Map<String, SaStorePageInfoResponse> storeMap = omsApiService.getAllStoreInfo()
                .stream().collect(Collectors.toMap(SaStorePageInfoResponse::getStoreName, Function.identity(), (v1, v2) -> v1));
        List<TmsLogisticsCompany> logisticsCompanyList = tmsCacheService.getAllLogisticsCompanyList();
        Map<String, String> companyMap = logisticsCompanyList.stream().collect(Collectors.toMap(TmsLogisticsCompany::getLogisticsCompany, TmsLogisticsCompany::getLogisticsMethod, (v1, v2) -> v1));
        importList.forEach(row -> {
            try {
                logisticsBatchService.uploadShipment(row, storeMap, companyMap);
            } catch (RuntimeException e) {
                row.setErrorMsg(e.getMessage());
                errorList.add(row);
            }
        });
        if (!CollectionUtils.isEmpty(errorList)) {
            response.setDataJsonStr(JsonMapper.toJson(errorList));
        }
        return response;
    }

}
