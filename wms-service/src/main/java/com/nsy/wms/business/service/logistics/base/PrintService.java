package com.nsy.wms.business.service.logistics.base;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.LogisticsCompanyConstant;
import com.nsy.api.wms.constants.MybatisQueryConstant;
import com.nsy.api.wms.constants.TmsCommonConstant;
import com.nsy.api.wms.enumeration.CustomsDeclareEnum;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.stockout.AmazonBuyShippingInfoEnum;
import com.nsy.api.wms.enumeration.stockout.LogisticsTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderHandleFailTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderScanTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.WaybillSourceEnum;
import com.nsy.api.wms.request.stockout.ErpRetryPrintLabelRequest;
import com.nsy.api.wms.request.stockout.LabelRequest;
import com.nsy.api.wms.request.stockout.StringListRequest;
import com.nsy.api.wms.response.stockout.PrintLabelResponse;
import com.nsy.api.wms.response.stockout.StockoutOrderLabelResponse;
import com.nsy.business.base.enums.LocationEnum;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpSyncPrintExceptionRequest;
import com.nsy.wms.business.manage.erp.response.ErpBasePrintLabelResponse;
import com.nsy.wms.business.manage.erp.response.TmsWaybillCloudPrintResponse;
import com.nsy.wms.business.manage.erp.response.WaybillCloudPrintCnResponse;
import com.nsy.wms.business.manage.erp.response.WaybillCloudPrintResponse;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.BaseGetLogisticsNoResponse;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderHandleFailRecordService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderLabelService;
import com.nsy.wms.business.service.stockout.StockoutOrderLogService;
import com.nsy.wms.business.service.stockout.StockoutOrderScanTaskService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutShipmentErpPickingBoxService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.business.service.stockout.StockoutTransparencyCodeService;
import com.nsy.wms.business.service.stockout.handle.StockoutTypeFactory;
import com.nsy.wms.business.service.stockout.ready.StockoutOrderOttoHandleService;
import com.nsy.wms.business.service.stockout.ready.StockoutOrderReadyStatusHandlerService;
import com.nsy.wms.business.service.stockout.ready.StockoutOrderTikTokLocalShippingHandleService;
import com.nsy.wms.business.service.stockout.ready.StockoutOrderTikTokShopHandleService;
import com.nsy.wms.business.service.stockout.ready.StockoutOrderTransparencyHandleService;
import com.nsy.wms.business.service.stockout.ship.StockoutShipService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderLabelEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.utils.mp.TenantContext;

import cn.hutool.core.util.StrUtil;


@Service
public class PrintService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PrintService.class);
    @Autowired
    LogisticsServiceHelper logisticsServiceHelper;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderLabelService labelService;
    @Autowired
    StockoutShipmentErpPickingBoxService erpPickingBoxService;
    @Autowired
    StockoutOrderScanTaskService scanTaskService;
    @Autowired
    TmsApiService tmsApiService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockoutTypeFactory stockoutTypeFactory;
    @Autowired
    StockoutTransparencyCodeService transparencyCodeService;
    @Autowired
    StockoutShipService stockoutShipService;
    @Autowired
    StockoutOrderTransparencyHandleService stockoutOrderTransparencyHandleService;
    @Autowired
    StockoutOrderHandleFailRecordService stockoutOrderHandleFailRecordService;
    @Autowired
    BaseLogisticsService baseLogisticsService;
    @Autowired
    StockoutOrderReadyStatusHandlerService readyStatusHandlerService;
    @Autowired
    TmsCacheService tmsCacheService;
    @Resource
    StockoutOrderOttoHandleService stockoutOrderOttoHandleService;
    @Resource
    StockoutOrderTikTokShopHandleService stockoutOrderTikTokShopHandleService;
    @Resource
    StockoutOrderTikTokLocalShippingHandleService stockoutOrderTikTokLocalShippingHandleService;
    @Resource
    BdSystemParameterService bdSystemParameterService;

    /**
     * 出库单生成面单
     *
     * @param stockoutOrderEntity
     * @param userName
     * @param operation
     */
    @Transactional
    @JLock(keyConstant = "generateLabelByStockoutOrder", lockKey = "#stockoutOrderEntity.stockoutOrderNo")
    public BaseGetLogisticsNoResponse generateLabelByStockoutOrder(StockoutOrderEntity stockoutOrderEntity, String userName, String operation) {
        String logisticsCompany = stockoutOrderEntity.getLogisticsCompany();
        if (!StringUtils.hasText(logisticsCompany)) {
            throw new BusinessServiceException(String.format("出库单【%s】,没有设置物流公司", stockoutOrderEntity.getStockoutOrderNo()));
        }
        String value = bdSystemParameterService.getCacheByKey(BdSystemParameterEnum.WMS_STOCKOUT_B2B_SELF_LOGISTICS.getKey());
        if (StringUtils.hasText(value) && StrUtil.equalsAnyIgnoreCase(stockoutOrderEntity.getLogisticsCompany(), value.split(","))) {
            BaseGetLogisticsNoResponse noResponse = new BaseGetLogisticsNoResponse();
            if (StrUtil.equalsIgnoreCase(stockoutOrderEntity.getLocation(), LocationEnum.WEIYUE.name())) {
                noResponse.setLogisticsNo("");
            } else {
                noResponse.setLogisticsNo(StrUtil.isNotBlank(stockoutOrderEntity.getLogisticsNo()) ? stockoutOrderEntity.getLogisticsNo() : "");
            }
            return noResponse;
        }
        BaseLogisticsService logisticsService = logisticsServiceHelper.getLogisticsService(logisticsCompany);
        List<StockoutOrderItemEntity> stockoutOrderItems = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        BaseGetLogisticsNoResponse response = logisticsService.getLogisticsNo(stockoutOrderEntity, stockoutOrderItems, userName);
        // 记录日志
        String message = String.format("出库单【%s】,%s打印【%s】-获取物流面单【%s】", stockoutOrderEntity.getStockoutOrderNo(), operation, stockoutOrderEntity.getLogisticsCompany(),
                StringUtils.hasText(response.getLogisticsNo()) ? response.getLogisticsNo() : "");
        stockoutOrderLogService.addStockoutOrderLog(StockoutOrderLogTypeEnum.GET_LABEL, stockoutOrderEntity.getStockoutOrderNo(), message, userName, stockoutOrderEntity.getLocation());
//        pushZuoHai(stockoutOrderEntity, response);
        return response;
    }

    /**
     * erp打印异常重新打印，
     * 1、小包重新获取面单 成功则状态变为透明计划预占中；2、透明计划预占T code
     * 12都成功则变为待生成波次
     */
    @JLock(keyConstant = "generateLabelByStockoutOrder", lockKey = "#request.erpPickId")
    public String erpRetryPrintLabel(ErpRetryPrintLabelRequest request) {
        LoginInfoService.setName(request.getOperator());
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getErpPickId, request.getErpPickId()));
        if (stockoutOrderEntity == null || Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.CANCELLED.name()) || Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.CANCELLING.name()))
            throw new BusinessServiceException(String.format("商通拣货单id【%s】在wms找不到未取消的出库单", request.getErpPickId()));
        List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        String logisticsNo = this.otherPlatformRetry(stockoutOrderEntity, stockoutOrderItemEntities);
        if (StringUtils.hasText(logisticsNo)) {
            return logisticsNo;
        }
        try {
            readyStatusHandlerService.validBefore(stockoutOrderEntity);
            if (!StockoutOrderStatusEnum.READY.name().equals(stockoutOrderEntity.getStatus()) && !StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equals(stockoutOrderEntity.getStatus()))
                return StringUtils.hasText(stockoutOrderEntity.getLogisticsNo()) ? stockoutOrderEntity.getLogisticsNo() : "";
            //获取面单失败，需要将出库单的购买配送状态优先设置成 取消设置
            if (!AmazonBuyShippingInfoEnum.DISABLE.getValue().equals(stockoutOrderEntity.getAmazonBuyShippingInfo())) {
                stockoutOrderEntity.setAmazonBuyShippingInfo(AmazonBuyShippingInfoEnum.DISABLE.getValue());
                stockoutOrderService.updateById(stockoutOrderEntity);
            }
            BaseGetLogisticsNoResponse response = new BaseGetLogisticsNoResponse();
            Map<String, TmsLogisticsCompany> allLogisticsCompanyMap = tmsCacheService.getAllLogisticsCompanyWithoutStatus(TenantContext.getTenant());
            TmsLogisticsCompany company = allLogisticsCompanyMap.get(stockoutOrderEntity.getLogisticsCompany());
            if (company != null && !TmsCommonConstant.NO_GET_LABEL_LOGISTICS.contains(company.getLogisticsMethod())
                    && !StrUtil.equalsAnyIgnoreCase(company.getLogisticsType(), LogisticsTypeEnum.FREIGHT_FORWARDER.name(), LogisticsTypeEnum.DOMESTIC_EXPRESS.name())) {
                if (StockoutOrderStatusEnum.valueOf(stockoutOrderEntity.getStatus()).getIndex() > 4 && StrUtil.isNotBlank(stockoutOrderEntity.getLogisticsNo())) {
                    return response.getLogisticsNo();
                }
                response = generateLabelByStockoutOrder(stockoutOrderEntity, request.getOperator(), "打印异常重新");
            }
            // 出库单的物流单号赋值
            stockoutOrderEntity = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getErpPickId, request.getErpPickId()));
            stockoutOrderEntity.setLogisticsNo(response.getLogisticsNo());
            stockoutOrderEntity.setUpdateBy(request.getOperator());
            stockoutOrderEntity.setSecondaryNumber(response.getSecondaryNumber());
            stockoutOrderEntity.setAmazonBuyShippingInfo(1 == response.getAmazonBuyShippingFlag() ? AmazonBuyShippingInfoEnum.ENABLE.getValue() : AmazonBuyShippingInfoEnum.DISABLE.getValue());
            // 更新状态，待出库/待生成波次
            stockoutTypeFactory.differType(stockoutOrderEntity).addLogisticsNoLog(stockoutOrderEntity, response);
            stockoutOrderHandleFailRecordService.handleCompleted(stockoutOrderEntity.getStockoutOrderId(), StockoutOrderHandleFailTypeEnum.LOGISTICS_GET_FAIL.name());
            stockoutOrderService.updateById(stockoutOrderEntity);
            stockoutOrderService.syncErpLogistics(stockoutOrderEntity);
            LoginInfoService.removeName();
            return response.getLogisticsNo();
        } catch (Exception e) {
            // 打印面单失败，重新插入失败队列
            StockoutOrderEntity orderEntityLast = stockoutOrderService.getByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            if (!StockoutOrderStatusEnum.CANCELLED.name().equals(orderEntityLast.getStatus())) {
                if (StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equals(stockoutOrderEntity.getStatus())) {
                    List<String> orderNos = stockoutOrderItemEntities.stream()
                            .map(StockoutOrderItemEntity::getOrderNo).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
                    String tid = CollectionUtils.isEmpty(orderNos) ? "" : orderNos.get(0);
                    // 推送erp面单失败列表
                    erpApiService.syncPrintException(new ErpSyncPrintExceptionRequest(tid, stockoutOrderEntity.getErpPickId(), String.format("【wms】%s", e.getMessage()), loginInfoService.getName()));
                }
                stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.GET_LOGISTICS_FAIL_RECORD, String.format("erp重新获取物流失败：%s", e.getMessage()));
                //保存获取面单失败记录表
                stockoutOrderHandleFailRecordService.saveOrUpdateRecords(stockoutOrderEntity.getStockoutOrderId(), stockoutOrderEntity.getStockoutOrderNo(),
                        StockoutOrderHandleFailTypeEnum.LOGISTICS_GET_FAIL, e.getMessage());
                LOGGER.error(stockoutOrderEntity.getStockoutOrderNo() + "出库单获取面单异常！", e);
            }
            LoginInfoService.removeName();
            throw e;
        }
    }

    public String otherPlatformRetry(StockoutOrderEntity stockoutOrderEntity, List<StockoutOrderItemEntity> stockoutOrderItemEntities) {
        if (StockoutOrderPlatformEnum.OTTO.name().equals(stockoutOrderEntity.getPlatformName())) {
            stockoutOrderOttoHandleService.handleOrder(stockoutOrderEntity);
            StockoutOrderEntity byId = stockoutOrderService.getById(stockoutOrderEntity.getStockoutOrderId());
            if (!StockoutOrderStatusEnum.READY.name().equals(byId.getStatus()) && !StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equals(byId.getStatus()))
                return StringUtils.hasText(byId.getLogisticsNo()) ? byId.getLogisticsNo() : "";
        }
        if (StockoutOrderPlatformEnum.TIKTOK_SHOP.getName().equals(stockoutOrderEntity.getPlatformName())) {
            stockoutOrderTikTokShopHandleService.handleOrder(stockoutOrderEntity);
            StockoutOrderEntity byId = stockoutOrderService.getById(stockoutOrderEntity.getStockoutOrderId());
            if (!StockoutOrderStatusEnum.READY.name().equals(byId.getStatus()) && !StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equals(byId.getStatus()))
                return StringUtils.hasText(byId.getLogisticsNo()) ? byId.getLogisticsNo() : "";
        }
        if (StockoutOrderPlatformEnum.TIKTOK_LOCAL_SHIPPING.getName().equals(stockoutOrderEntity.getPlatformName())) {
            stockoutOrderTikTokLocalShippingHandleService.handleOrder(stockoutOrderEntity);
            StockoutOrderEntity byId = stockoutOrderService.getById(stockoutOrderEntity.getStockoutOrderId());
            if (!StockoutOrderStatusEnum.READY.name().equals(byId.getStatus()) && !StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equals(byId.getStatus()))
                return StringUtils.hasText(byId.getLogisticsNo()) ? byId.getLogisticsNo() : "";
        }
        if (stockoutOrderItemService.validTransparency(stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderItemEntities)) {
            stockoutOrderTransparencyHandleService.preMatchTCode(stockoutOrderEntity.getStockoutOrderId(), false);
            StockoutOrderEntity byId = stockoutOrderService.getById(stockoutOrderEntity.getStockoutOrderId());
            if (!StockoutOrderStatusEnum.READY.name().equals(byId.getStatus()) && !StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name().equals(byId.getStatus()))
                return StringUtils.hasText(byId.getLogisticsNo()) ? byId.getLogisticsNo() : "";
        }
        return null;
    }

    /**
     * 复核装箱之后
     * n个装箱清单生成面单，随机选一个出库单-获得地址 等信息
     * n个装箱必须是同一个物流公司
     */
    @Transactional
    public BaseGetLogisticsNoResponse generateLabelByStockoutShipment(List<StockoutShipmentEntity> shipmentList, String userName, String operation) {
        valid(shipmentList);
        String location = shipmentList.get(0).getLocation();
        List<StockoutShipmentItemEntity> itemList = shipmentItemService.findByShipmentIdList(shipmentList.stream()
                .map(StockoutShipmentEntity::getShipmentId).collect(Collectors.toList()));
        List<String> stockoutOrderNos = itemList.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).filter(StringUtils::hasText).distinct().collect(Collectors.toList());
        List<Integer> stockoutOrderItemIds = itemList.stream().map(StockoutShipmentItemEntity::getStockoutOrderItemId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stockoutOrderNos))
            throw new BusinessServiceException("装箱清单明细必须要有出库单号，请核对");
        StockoutOrderEntity orderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNos.get(0));
        // 构建主要的出库单, 先取第一个
        orderEntity.setLogisticsCompany(shipmentList.get(0).getLogisticsCompany());
        orderEntity.setLogisticsNo(shipmentList.get(0).getLogisticsNo());
        orderEntity.setCustomsDeclareType(shipmentList.get(0).getCustomsDeclareType());
        List<StockoutOrderItemEntity> stockoutOrderItems = stockoutOrderItemService.listByIds(stockoutOrderItemIds);
        // 过滤装箱sku
        Map<Integer, StockoutShipmentItemEntity> itemMap = new HashMap<>();
        itemList.forEach(itemEntity -> itemMap.merge(itemEntity.getStockoutOrderItemId(), itemEntity, (pre, now) -> {
            StockoutShipmentItemEntity entity = new StockoutShipmentItemEntity();
            BeanUtils.copyProperties(pre, entity);
            entity.setQty(pre.getQty() + now.getQty());
            return entity;
        }));
        // 如果出库单没有对应的sku，就不放进箱子请求
        stockoutOrderItems = stockoutOrderItems.stream().filter(item -> itemMap.containsKey(item.getStockoutOrderItemId()))
                .peek(item -> item.setQty(itemMap.get(item.getStockoutOrderItemId()).getQty())).collect(Collectors.toList());
        // 开始获取面单
        String logisticsCompany = shipmentList.get(0).getLogisticsCompany();
        BaseLogisticsService logisticsService = logisticsServiceHelper.getLogisticsService(logisticsCompany);
        BaseGetLogisticsNoResponse response = logisticsService.getLogisticsNo(orderEntity, stockoutOrderItems, userName);
        // 获取面单后，更新物流单号，记录日志
        stockoutOrderNos.forEach(stockoutOrderNo -> {
            String message = String.format("出库单【%s】,%s打印【%s】-物流面单【%s】", stockoutOrderNo, operation, logisticsCompany,
                    StringUtils.hasText(response.getLogisticsNo()) ? response.getLogisticsNo() : "");
            stockoutOrderLogService.addStockoutOrderLog(StockoutOrderLogTypeEnum.GET_LABEL, stockoutOrderNo, message, userName, location);
            StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
            if (!StockoutOrderWorkSpaceEnum.B2C_BAG_AREA.name().equalsIgnoreCase(stockoutOrderEntity.getWorkspace()))
                updateLogisticsNo(stockoutOrderEntity, userName, response.getLogisticsNo(), shipmentList.get(0).getLogisticsCompany());
        });
        // 更新装箱清单物流单号，记录日志
        shipmentList.forEach(shipment -> {
            shipment.setUpdateBy(userName);
            shipment.setLogisticsNo(response.getLogisticsNo());
        });
        shipmentService.updateBatchById(shipmentList);
        if (!Objects.equals("货代发货打印", operation))
            erpPickingBoxService.sendErpPickingBoxSyncRequestByShipmentList(shipmentList);
        return response;
    }

    private void valid(List<StockoutShipmentEntity> shipmentList) {
        if (CollectionUtils.isEmpty(shipmentList)) {
            throw new BusinessServiceException("必须有装箱清单才能申请面单");
        }
        if (shipmentList.stream().noneMatch(shipment -> StringUtils.hasText(shipment.getLogisticsCompany()))) {
            throw new BusinessServiceException("所有的装箱清单必须有物流公司，请核对");
        }
        Set<String> companySet = shipmentList.stream().map(StockoutShipmentEntity::getLogisticsCompany).collect(Collectors.toSet());
        if (companySet.size() != 1) {
            throw new BusinessServiceException("所有的装箱清单必须是同一个物流公司，请核对");
        }
    }

    private void updateLogisticsNo(StockoutOrderEntity stockoutOrderEntity, String name, String logisticsNo, String logisticsCompany) {
        stockoutOrderEntity.setUpdateBy(name);
        stockoutOrderEntity.setLogisticsNo(logisticsNo);
        stockoutOrderEntity.setLogisticsCompany(logisticsCompany);
        stockoutOrderService.updateById(stockoutOrderEntity);
    }

    // 菜鸟打印并发货，按单/按箱
    @Transactional
    public StockoutOrderLabelResponse getCNLabel(String stockoutOrderNo, String boxCode) {
        BaseGetLogisticsNoResponse labelResponse;
        List<String> boxCodeList = new ArrayList<>();
        StockoutOrderEntity order = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        String operation = "复核页面";
        if (StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name().equals(order.getPickingType())) {
            operation = "波次扫描页面";
        }
        if (TmsCommonConstant.FILTER_LOGISTICS.contains(order.getLogisticsCompany())) {
            throw new BusinessServiceException("请到发货页面进行面单打印");
        }
        if (StringUtils.hasText(boxCode)) {
            // 按箱打印
            StockoutShipmentEntity shipment = shipmentService.findTopByShipmentBoxCode(boxCode);
            if (Objects.equals(1, shipment.getBoxIndex()) && StringUtils.hasText(order.getLogisticsNo())) {
                shipment.setLogisticsNo(order.getLogisticsNo());
            } else {
                shipment.setLogisticsNo(null);
            }
            labelResponse = generateLabelByStockoutShipment(Collections.singletonList(shipment), loginInfoService.getName(), operation);
            boxCodeList.add(boxCode);
        } else {
            // 按单打印
            LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo);
            wrapper.eq(StockoutShipmentItemEntity::getIsDeleted, 0);
            List<StockoutShipmentItemEntity> list = shipmentItemService.list(wrapper);
            if (CollectionUtils.isEmpty(list)) {
                throw new BusinessServiceException(stockoutOrderNo + "该出库单未找到相关装箱数据，请核对");
            }
            List<StockoutShipmentEntity> shipmentEntityList = shipmentService.listByIds(list.stream()
                    .map(StockoutShipmentItemEntity::getShipmentId).collect(Collectors.toList()));
            labelResponse = generateLabelByStockoutShipment(shipmentEntityList, loginInfoService.getName(), operation);
            boxCodeList.addAll(shipmentEntityList.stream().map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.toList()));
        }
        labelService.updatePrintStatus(labelResponse.getLogisticsNo());
        StockoutOrderLabelResponse response = labelService.getLogisticsDetail(labelResponse.getLogisticsNo());
        response.setShipmentBoxCode(boxCodeList);
        StockoutOrderScanTaskEntity taskEntity = scanTaskService.getOne(new QueryWrapper<StockoutOrderScanTaskEntity>().lambda().eq(StockoutOrderScanTaskEntity::getStockoutOrderNo, stockoutOrderNo).last(MybatisQueryConstant.QUERY_FIRST));
        if (taskEntity != null && StockoutOrderScanTaskStatusEnum.REVIEWED.name().equals(taskEntity.getStatus())
                || Objects.equals(order.getPickingType(), StockoutPickingTypeEnum.FIND_DOC_BY_GOODS.name())) {
            // 菜鸟自动发货
            stockoutShipService.getStockoutShipService(stockoutOrderNo).autoShipmentShip(stockoutOrderNo);
        }
        return response;
    }

    @Transactional
    public void getTmsLabelList(StringListRequest request) {
        List<StockoutOrderEntity> stockoutOrderNoList = stockoutOrderService.getByStockoutOrderNoList(request.getStringList());
        stockoutOrderNoList.forEach(order -> getTmsLabel(order.getStockoutOrderNo(), order.getLogisticsNo()));
    }


    // 如果在wms的label表中不存在，则请求tms
    public void getTmsLabel(String stockoutOrderNo, String logisticsNo) {
        StockoutOrderEntity order = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        List<StockoutOrderItemEntity> itemEntityList = stockoutOrderItemService.listByStockoutOrderId(order.getStockoutOrderId());
        LabelRequest request = new LabelRequest();
        if (!CollectionUtils.isEmpty(itemEntityList)) {
            request.setTid(itemEntityList.get(0).getOrderNo());
        }
        if (!StringUtils.hasText(logisticsNo)) {
            throw new BusinessServiceException(stockoutOrderNo + "该出库单暂未获取到物流单号，无法请求面单");
        }
        request.setLogisticsNo(logisticsNo);
        PrintLabelResponse response = tmsApiService.getNewLabel(request);
        if (response == null) {
            return;
        }
        if (StringUtils.hasText(response.getLabelUrl())) {
            Arrays.stream(response.getLabelUrl().split(",")).forEach(labelUrl -> {
                List<StockoutOrderLabelEntity> orderLabelEntityList = labelService.listByStockoutOrderId(order.getStockoutOrderId());
                Optional<StockoutOrderLabelEntity> optional = orderLabelEntityList.stream().filter(t -> t.getLabelUrl().equals(labelUrl)).findAny();
                if (optional.isPresent()) return;
                StockoutOrderLabelEntity stockoutOrderLabelEntity = new StockoutOrderLabelEntity();
                stockoutOrderLabelEntity.setLabelUrl(labelUrl);
                stockoutOrderLabelEntity.setStockoutOrderId(order.getStockoutOrderId());
                stockoutOrderLabelEntity.setCreateBy(loginInfoService.getName());
                stockoutOrderLabelEntity.setLogisticsNo(logisticsNo);
                stockoutOrderLabelEntity.setSource(WaybillSourceEnum.SYSTEM_PRINTING.name());
                labelService.save(stockoutOrderLabelEntity);
            });
        }
        if (StringUtils.hasText(response.getPrintData())) {
            StockoutOrderLabelEntity stockoutOrderLabelEntity = new StockoutOrderLabelEntity();
            stockoutOrderLabelEntity.setPrintContent(response.getPrintData());
            stockoutOrderLabelEntity.setStockoutOrderId(order.getStockoutOrderId());
            stockoutOrderLabelEntity.setCreateBy(loginInfoService.getName());
            stockoutOrderLabelEntity.setLogisticsNo(logisticsNo);
            stockoutOrderLabelEntity.setSource(WaybillSourceEnum.SYSTEM_PRINTING.name());
            labelService.save(stockoutOrderLabelEntity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @JLock(keyConstant = "erpRePrintLabel", lockKey = "#request.erpPickId")
    public ErpBasePrintLabelResponse erpRePrintLabel(ErpRetryPrintLabelRequest request) {
        LoginInfoService.setName(request.getOperator());
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getErpPickId, request.getErpPickId()));
        if (stockoutOrderEntity == null || Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.CANCELLED.name()) || Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.CANCELLING.name()))
            throw new BusinessServiceException(String.format("商通拣货单id【%s】在wms找不到未取消的出库单", request.getErpPickId()));
        if (!Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.READY.name()) && !Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name())
                && !StringUtils.hasText(stockoutOrderEntity.getLogisticsNo())) {
            throw new BusinessServiceException(String.format("拣货单id【%s】已推送WMS系统，且状态不是[准备中/获取单号失败]，无法再次获取面单", request.getErpPickId()));
        }
        if (StockoutOrderWorkSpaceEnum.FBA_AREA.name().equals(stockoutOrderEntity.getWorkspace())) {
            List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            if (stockoutOrderItemService.validTransparency(stockoutOrderEntity.getStockoutOrderNo(), stockoutOrderItemEntities)) {
                throw new BusinessServiceException("透明计划无法获取面单！");
            }
        }
        BaseGetLogisticsNoResponse response = generateLabelByStockoutOrder(stockoutOrderEntity, request.getOperator(), "erp重新打印面单");
        // 出库单的物流单号赋值
        stockoutOrderEntity.setLogisticsNo(response.getLogisticsNo());
        stockoutOrderEntity.setUpdateBy(request.getOperator());
        stockoutOrderEntity.setSecondaryNumber(response.getSecondaryNumber());
        if (Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.READY.name()) || Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name())) {
            if (StockoutOrderTypeEnum.OVERSEA_DELIVERY.name().equalsIgnoreCase(stockoutOrderEntity.getStockoutType())) {
                stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.READY_OUTBOUND.name());
            } else {
                stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
            }
        }
        stockoutOrderService.updateById(stockoutOrderEntity);
        stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.GET_LABEL, String.format("出库单【%s】通过 订单详情 获取面单", stockoutOrderEntity.getStockoutOrderNo()));
        List<StockoutOrderLabelEntity> labelEntities = labelService.listByLogisticsNo(Collections.singletonList(response.getLogisticsNo()));
        ErpBasePrintLabelResponse erpBasePrintLabelResponse = new ErpBasePrintLabelResponse();
        erpBasePrintLabelResponse.setLogisticsNos(response.getLogisticsNo());
        erpBasePrintLabelResponse.setTrackingNumber(response.getLogisticsNo());
        erpBasePrintLabelResponse.setBaoguanOf9610(Objects.equals(stockoutOrderEntity.getCustomsDeclareType(), CustomsDeclareEnum.DECLARE_9610.getValue()));
        erpBasePrintLabelResponse.setLpNumber(response.getLogisticsNo());
        erpBasePrintLabelResponse.setHtmls(response.getLabelUrlList());
        if (!CollectionUtils.isEmpty(labelEntities) && StringUtils.hasText(labelEntities.get(0).getPrintContent())) {
            ErpBasePrintLabelResponse.CainiaoPrintContent cainiaoPrintContent = new ErpBasePrintLabelResponse.CainiaoPrintContent();
            WaybillCloudPrintResponse waybillCloudPrintResponse = new WaybillCloudPrintResponse();
            waybillCloudPrintResponse.setObjectId(response.getLogisticsNo());
            waybillCloudPrintResponse.setWaybillCode(response.getLogisticsNo());
            waybillCloudPrintResponse.setPrintData(labelEntities.get(0).getPrintContent());
            cainiaoPrintContent.setWaybillCloudPrintResponse(waybillCloudPrintResponse);
            erpBasePrintLabelResponse.setCainiaoPrintContent(cainiaoPrintContent);
        }
        LoginInfoService.removeName();
        return erpBasePrintLabelResponse;
    }

    public TmsWaybillCloudPrintResponse erpRePrintLabelCN(ErpRetryPrintLabelRequest request) {
        StockoutOrderEntity stockoutOrderEntity = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getErpPickId, request.getErpPickId()));
        if (stockoutOrderEntity == null || Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.CANCELLED.name()) || Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.CANCELLING.name()))
            throw new BusinessServiceException(String.format("商通拣货单id【%s】在wms找不到未取消的出库单", request.getErpPickId()));
        BaseGetLogisticsNoResponse response = generateLabelByStockoutOrder(stockoutOrderEntity, request.getOperator(), "erp重新打印面单");
        stockoutOrderEntity.setLogisticsNo(response.getLogisticsNo());
        stockoutOrderEntity.setUpdateBy(request.getOperator());
        stockoutOrderEntity.setSecondaryNumber(response.getSecondaryNumber());
        if (Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.READY.name()) || Objects.equals(stockoutOrderEntity.getStatus(), StockoutOrderStatusEnum.LOGISTICS_GET_FAIL.name())) {
            stockoutOrderEntity.setStatus(LogisticsCompanyConstant.GOODCANG_USSC_UPS.equalsIgnoreCase(stockoutOrderEntity.getLogisticsCompany()) ? StockoutOrderStatusEnum.READY_OUTBOUND.name() : StockoutOrderStatusEnum.READY_WAVE_GENERATED.name());
        }
        stockoutOrderService.updateById(stockoutOrderEntity);
        // 用于BI统计
        stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.READY_WAVE_GENERATED, String.format("出库单【%s】待生成波次", stockoutOrderEntity.getStockoutOrderNo()));
        stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.GET_LABEL, String.format("出库单【%s】通过拣货单获取面单", stockoutOrderEntity.getStockoutOrderNo()));
        List<StockoutOrderLabelEntity> labelEntities = labelService.listByLogisticsNo(Collections.singletonList(response.getLogisticsNo()));
        if (CollectionUtils.isEmpty(labelEntities) || !StringUtils.hasText(labelEntities.get(0).getPrintContent())) {
            LoginInfoService.removeName();
            throw new BusinessServiceException("非内贸菜鸟物流的请勿调用菜鸟打印接口");
        }
        TmsWaybillCloudPrintResponse cainiaoPrintContent = new TmsWaybillCloudPrintResponse();
        WaybillCloudPrintCnResponse waybillCloudPrintCnResponse = new WaybillCloudPrintCnResponse();
        waybillCloudPrintCnResponse.setObjectId(UUID.randomUUID().toString().replaceAll("-", ""));
        waybillCloudPrintCnResponse.setWaybillCode(response.getLogisticsNo());
        waybillCloudPrintCnResponse.setPrintData(labelEntities.get(0).getPrintContent());
        cainiaoPrintContent.setWaybillCloudPrintResponseList(Collections.singletonList(waybillCloudPrintCnResponse));
        List<StockoutShipmentEntity> shipmentByStockoutOrderNo = shipmentItemService.getShipmentByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        shipmentByStockoutOrderNo.forEach(shipment -> {
            shipment.setLogisticsNo(stockoutOrderEntity.getLogisticsNo());
            shipment.setLogisticsCompany(stockoutOrderEntity.getLogisticsCompany());
            shipmentService.updateById(shipment);
            erpPickingBoxService.sendErpPickingBoxSyncRequest(shipment);
        });
        LoginInfoService.removeName();
        return cainiaoPrintContent;
    }
}
