package com.nsy.wms.business.manage.scm;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.core.apicore.util.EncodingUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.CacheKeyConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.shared.SelectModel;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.qa.DevelopImageType;
import com.nsy.api.wms.request.stock.CheckSupplierSkuPriceStatusRequest;
import com.nsy.api.wms.request.stockin.StockinReturnReviewTaskGenerateRequest;
import com.nsy.api.wms.response.stockin.CheckSupplierSkuPriceStatusDto;
import com.nsy.wms.business.manage.erp.request.ErpGenerateReturnOrderRequest;
import com.nsy.wms.business.manage.scm.request.BatchGetVersionWorkmanshipAttachmentRequest;
import com.nsy.wms.business.manage.scm.request.DownloadConcessionApplyRequest;
import com.nsy.wms.business.manage.scm.request.GenerateBatchRequest;
import com.nsy.wms.business.manage.scm.request.GetSupplierNameDropDownRequest;
import com.nsy.wms.business.manage.scm.request.GetSupplierSpecProductPurchaseRequest;
import com.nsy.wms.business.manage.scm.request.GetVersionWorkmanshipAttachmentDto;
import com.nsy.wms.business.manage.scm.request.ProcessCancelRequest;
import com.nsy.wms.business.manage.scm.request.ProcessPickingTaskCompleteRequest;
import com.nsy.wms.business.manage.scm.request.ProcessStockoutCompleteRequest;
import com.nsy.wms.business.manage.scm.request.ProductDevelopAttachRequest;
import com.nsy.wms.business.manage.scm.request.PurchaseOrderLogAddRequest;
import com.nsy.wms.business.manage.scm.request.PurchaseOrderRequest;
import com.nsy.wms.business.manage.scm.request.PurchaseOrderWorkmanshipInfoRequest;
import com.nsy.wms.business.manage.scm.request.PurchaseRefundTaskAddRequest;
import com.nsy.wms.business.manage.scm.request.SchedulingOrderPlanBomDetailRequest;
import com.nsy.wms.business.manage.scm.request.ShelveCompleteRequest;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.business.manage.scm.request.SupplierInvoiceCompanyDropDownRequest;
import com.nsy.wms.business.manage.scm.request.SupplierPurchasePriceRequest;
import com.nsy.wms.business.manage.scm.request.SupplierTaxDetailSampleRequest;
import com.nsy.wms.business.manage.scm.request.SupplierTaxInvoicedItemInvoiceRequest;
import com.nsy.wms.business.manage.scm.response.AttachDto;
import com.nsy.wms.business.manage.scm.response.BatchProductWorkmanshipAttachmentDto;
import com.nsy.wms.business.manage.scm.response.BdPurchaseOrderLabelDto;
import com.nsy.wms.business.manage.scm.response.OldProductPackageInfo;
import com.nsy.wms.business.manage.scm.response.ProductMainMarkTagResponse;
import com.nsy.wms.business.manage.scm.response.ProductWorkmanshipAttachmentDto;
import com.nsy.wms.business.manage.scm.response.PurchaseOrderConcessionApplyResponse;
import com.nsy.wms.business.manage.scm.response.PurchaseOrderDeliveryItemInfoDto;
import com.nsy.wms.business.manage.scm.response.PurchaseOrderWorkmanshipInfoResponse;
import com.nsy.wms.business.manage.scm.response.PurchasePackageInfoDto;
import com.nsy.wms.business.manage.scm.response.SchedulingOrderPlanBomDetailResponse;
import com.nsy.wms.business.manage.scm.response.StronglyRecommendStyleLabelsDto;
import com.nsy.wms.business.manage.scm.response.SupplierPurchasePriceResponse;
import com.nsy.wms.business.manage.scm.response.SupplierSpecProductPurchaseDTO;
import com.nsy.wms.business.manage.scm.response.SupplierTaxGetSampleDatailDto;
import com.nsy.wms.business.manage.scm.response.SupplierTaxInvoicedItemInvoiceDto;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractSyncDataOneRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractSyncDataRequest;
import com.nsy.wms.business.manage.supplier.request.StockoutCustomsDeclareContractWmsAuditRequest;
import com.nsy.wms.business.manage.supplier.response.LogisticsMappingInfo;
import com.nsy.wms.business.manage.supplier.response.SpotInfoResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalApiAsyncQueueService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.SupplierTaxInvoiceInfoFeedbackRequest;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ScmApiService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ScmApiService.class);

    @Inject
    private RestTemplate restTemplate;
    @Value("${nsy.service.url.scm}")
    private String scmServiceUrl;

    @Inject
    private StockoutShipmentItemService shipmentItemService;
    @Inject
    private LoginInfoService loginInfoService;
    @Autowired
    private ExternalApiLogService externalApiLogService;
    @Autowired
    private ExternalApiAsyncQueueService externalApiAsyncQueueService;
    @Autowired
    private ObjectMapper objectMapper;

    public void processStockoutComplete(String stockoutOrderNo, Integer batchId) {
        List<ProcessStockoutCompleteRequest.Item> itemList = new LinkedList<>();
        shipmentItemService.list(new QueryWrapper<StockoutShipmentItemEntity>().lambda()
                .eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo)
                .eq(StockoutShipmentItemEntity::getIsDeleted, 0)).stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getSku))
            .forEach((sku, shipmentItemEntities) -> {
                itemList.add(new ProcessStockoutCompleteRequest.Item(sku, shipmentItemEntities.stream().mapToInt(StockoutShipmentItemEntity::getQty).sum()));
            });
        String uri = String.format("%s/process-stockout-complete", scmServiceUrl);
        ProcessStockoutCompleteRequest request = new ProcessStockoutCompleteRequest();
        request.setBatchId(batchId);
        request.setUpdateBy(loginInfoService.getUserName());
        request.setItemList(itemList);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.PROCESS_STOCKOUT_COMPLETE, uri,
            JsonMapper.toJson(request), stockoutOrderNo, String.format("wms波次【%s】出库完成，同步SCM", request.getBatchId()));
        try {
            this.restTemplate.put(uri, request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }


    public void shelveComplete(List<String> purchasePlanNoList, Map<String, Integer> lackMap) {
        if (CollectionUtils.isEmpty(purchasePlanNoList)) {
            return;
        }
        String uri = String.format("%s/shelve-complete", scmServiceUrl);
        ShelveCompleteRequest request = new ShelveCompleteRequest();
        request.setPurchasePlanNoList(purchasePlanNoList);
        request.setLackMap(lackMap);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SHELVE_COMPLETE, uri,
            JsonMapper.toJson(request), String.join(",", purchasePlanNoList), String.format("wms采购计划单【%s】上架完成，同步SCM", String.join(",", request.getPurchasePlanNoList())));
        try {
            this.restTemplate.put(uri, request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成scm待分拣波次
     *
     * @param request
     */
    public void generateSplitBatch(GenerateBatchRequest request) {
        String uri = String.format("%s/generate-split-batch", scmServiceUrl);
        request.setLocation(TenantContext.getTenant());
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GENERATE_SPLIT_BATCH, uri,
            JsonMapper.toJson(request), request.getWmsBatchId().toString(), String.format("wms生成波次【%s】，SCM生成加工波次", request.getWmsBatchId()));
        try {
            this.restTemplate.put(uri, request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成scm待分拣波次
     *
     * @param request
     */
    public void processLackPickingComplete(ProcessPickingTaskCompleteRequest request) {
        String uri = String.format("%s/lack-picking-task-complete", scmServiceUrl);
        request.setLocation(TenantContext.getTenant());
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.PROCESS_LACK_PICKING_COMPLETE, uri,
            JsonMapper.toJson(request), request.getLackPickingTaskId().toString(), String.format("wms加工缺货拣货完成, 拣货任务ID【%s】，同步SCM", request.getLackPickingTaskId()));
        try {
            this.restTemplate.put(uri, request);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 取消加工
     *
     * @param request
     */
    public void cancelProcess(ProcessCancelRequest request) {
        String uri = String.format("%s/cancel-process", scmServiceUrl);
        request.setLocation(TenantContext.getTenant());
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.PROCESS_CANCEL, uri,
            JsonMapper.toJson(request), request.getWmsBatchId().toString(), String.format("wms取消加工，波次号【%s】", request.getWmsBatchId()));
        try {
            request.setLocation(TenantContext.getTenant());
            HttpHeaders headers = new HttpHeaders();
            headers.put("user-name", Collections.singletonList(EncodingUtils.url(loginInfoService.getName())));
            headers.put("real-name", Collections.singletonList(EncodingUtils.url(loginInfoService.getName())));
            ResponseEntity<String> response = this.restTemplate.exchange(uri, HttpMethod.PUT, new HttpEntity<>(request, headers), String.class);
            if (HttpStatus.OK.value() != response.getStatusCode().value()) {
                throw new BusinessServiceException("取消加工 失败");
            }
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取现货收货列表数据源
     *
     * @param logisticsNo
     * @param purchasePlanNo
     * @return
     */
    public List<SpotInfoResponse> getSpotInfoList(String logisticsNo, String purchasePlanNo) {
        String url = String.format("%s/purchase-order/spot/info?%s&%s", scmServiceUrl, StringUtils.hasText(logisticsNo) ? "logisticsNo=" + logisticsNo : "", StringUtils.hasText(purchasePlanNo) ? "purchasePlanNo=" + purchasePlanNo : "");
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GET_SPOT_INFO, url,
            logisticsNo, logisticsNo, "获取现货计划单数据");
        try {
            SpotInfoResponse[] responses = this.restTemplate.getForObject(url, SpotInfoResponse[].class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(responses), ExternalApiLogStatusEnum.SUCCESS);
            return Objects.isNull(responses) ? Lists.newArrayList() : Arrays.asList(responses);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 根据供应商Ids获取供应商信息
     *
     * @param supplierIds 供应商Ids
     * @return
     */
    public List<SupplierDto> getSupplierInfoList(List<Integer> supplierIds) {
        if (CollectionUtils.isEmpty(supplierIds)) {
            return new ArrayList<>();
        }
        String url = String.format("%s/bd-purchase/supplier-by-ids?supplierIds=%s", scmServiceUrl, supplierIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        try {
            SupplierDto[] supplierDtoArr = this.restTemplate.getForObject(url, SupplierDto[].class);
            return Objects.isNull(supplierDtoArr) ? Lists.newArrayList() : Arrays.asList(supplierDtoArr);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 根据物流单号or现货计划单查出两者映射关系关系
     *
     * @param documentNo
     * @return
     */
    public List<LogisticsMappingInfo> getLogisticsMappingList(String documentNo, boolean isGenerateLogisticsNo) {
        String url = String.format("%s/purchase-order/spot/logistics-mapping?documentNo=%s&generateLogisticsNo=%s", scmServiceUrl, documentNo, isGenerateLogisticsNo);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GET_LOGISTICS_PLAN_MAPPING, url,
            documentNo, documentNo, "获取现货计划单数据");
        try {
            LogisticsMappingInfo[] responses = this.restTemplate.getForObject(url, LogisticsMappingInfo[].class);
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(responses), ExternalApiLogStatusEnum.SUCCESS);
            return Objects.isNull(responses) ? Lists.newArrayList() : Arrays.asList(responses);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 现货采购-收货异常跟进-任务生成
     *
     * @param request
     * @return
     */
    public void generateOrUpdateSpotReturnTask(PurchaseRefundTaskAddRequest request) {
        String url = String.format("%s/purchase-refund-task/create-task", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.GET_LOGISTICS_PLAN_MAPPING, url,
            JsonMapper.toJson(request), request.getItemList().stream().map(PurchaseRefundTaskAddRequest.Item::getPurchaseOrderNo).distinct().collect(Collectors.joining(StringConstant.COMMA)), "生成现货退货任务");
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("location", TenantContext.getTenant());
            HttpEntity<PurchaseRefundTaskAddRequest> requestEntity = new HttpEntity<>(request, headers);
            this.restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 判断工厂是否生产sku
     * true 生产
     * false 不生产
     *
     * @param supplierId
     * @param sku
     * @return
     */
    public Boolean checkSupplierExistsSku(Integer supplierId, String sku) {
        String url = String.format("%s/supplier-product/check-supplier-exists-sku?supplierId=%s&sku=%s", scmServiceUrl, supplierId, sku);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.CHECK_SUPPLIER_EXISTS_SKU, url,
            String.format("%s-%s", supplierId, sku), supplierId.toString(), "判断工厂是否生产sku");
        try {
            Boolean response = this.restTemplate.getForObject(url, Boolean.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
            return response;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 新增采购单日志
     *
     * @param request
     * @return
     */
    public void addPurchaseOrderLog(PurchaseOrderLogAddRequest request) {
        String url = String.format("%s/purchase-order-log/add", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.ADD_PURCHASE_ORDER_LOG, url,
            JsonMapper.toJson(request), request.getItemList().stream().map(PurchaseOrderLogAddRequest.PurchaseOrderLogAddItem::getOrderNo).distinct().collect(Collectors.joining(StringConstant.COMMA)), "生成采购单日志");
        // 保存到异步调用队列
        externalApiAsyncQueueService.add(apiLogEntity, String.format("returnTaskId-%d", request.getReturnProductTaskId()));
    }

    /**
     * 用于仓库调用，判断是否有采购价
     *
     * @return
     */
    public List<CheckSupplierSkuPriceStatusDto> checkSupplierSkuPriceStatus(CheckSupplierSkuPriceStatusRequest request) {
        String url = String.format("%s/supplier-product/check-supplier-sku-price-status", scmServiceUrl);
        String json = this.restTemplate.postForObject(url, request, String.class);
        return JSON.parseArray(json, CheckSupplierSkuPriceStatusDto.class);
    }

    /**
     * 批量匹配供应商
     *
     * @param requestList
     * @return
     */
    public List<SupplierTaxInvoicedItemInvoiceDto> invoiceBatch(List<SupplierTaxInvoicedItemInvoiceRequest> requestList) {
        String url = String.format("%s/supplier-tax-invoiced-item/invoice-batch", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SCM_INVOICE_BATCH, url,
            JsonMapper.toJson(requestList), requestList.get(0).getBusinessId().toString(), "批量匹配供应商");
        try {
            String responseStr = this.restTemplate.postForObject(url, requestList, String.class);
            List<SupplierTaxInvoicedItemInvoiceDto> response = objectMapper.readValue(responseStr, new SupplierTaxInvoicedItemListTypeReference());
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(response), ExternalApiLogStatusEnum.SUCCESS);
            return response;
        } catch (JsonProcessingException | RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 匹配单个供应商
     *
     * @param requestList
     * @return
     */
    public List<SupplierTaxInvoicedItemInvoiceDto> invoiceOne(List<SupplierTaxInvoicedItemInvoiceRequest> requestList) {
        String url = String.format("%s/supplier-tax-invoiced-item/invoice-by-batch-request", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SCM_INVOICE_ONE, url,
            JsonMapper.toJson(requestList), requestList.get(0).getBusinessId().toString(), "匹配单个供应商");
        try {
            String responseStr = this.restTemplate.postForObject(url, requestList, String.class);
            List<SupplierTaxInvoicedItemInvoiceDto> response = objectMapper.readValue(responseStr, new SupplierTaxInvoicedItemListTypeReference());
            externalApiLogService.updateLog(apiLogEntity, JsonMapper.toJson(response), ExternalApiLogStatusEnum.SUCCESS);
            return response;
        } catch (JsonProcessingException | RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 通过供应商id获取信息
     *
     * @param request
     * @return
     */
    public List<SupplierTaxGetSampleDatailDto> getSampleDetailBySupplierId(SupplierTaxDetailSampleRequest request) {
        String url = String.format("%s/supplier-tax/get-sample-detail-by-supplier-ids", scmServiceUrl);
        String responseStr = this.restTemplate.postForObject(url, request, String.class);
        try {
            return objectMapper.readValue(responseStr, new SupplierTaxGetSampleDatailDtoListTypeReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 通过供应商id获取信息
     *
     * @param ids
     * @return
     */
    public void rejectSupplier(List<Integer> ids) {
        String url = String.format("%s/supplier-tax-invoiced-item/reject", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SCM_REJECT_SUPPLIER, url,
            JsonMapper.toJson(ids), ids.stream().map(Object::toString).collect(Collectors.joining(",")), "供应商驳回");
        try {
            this.restTemplate.postForObject(url, ids, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new RuntimeException(e);
        }
    }

    /**
     * 同步合同数据
     *
     * @return
     */
    public void syncCustomsContract(List<StockoutCustomsDeclareContractSyncDataOneRequest> syncSupplierRequestList) {
        StockoutCustomsDeclareContractSyncDataRequest result = new StockoutCustomsDeclareContractSyncDataRequest();
        result.setList(syncSupplierRequestList);

        String url = String.format("%s/supplier-tax-invoiced-item/generate-contract-feedback", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SCM_GENERATE_CONTRACT_FEEDBACK, url,
            JsonMapper.toJson(result), syncSupplierRequestList.get(0).getDeclareContractNo(), "SCM生成合同反馈");
        try {
            this.restTemplate.postForEntity(url, result, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
        }
    }

    /**
     * 驳回合同反馈
     *
     * @param request
     */
    public void rejectCustomsContract(StockoutCustomsDeclareContractWmsAuditRequest request) {
        String url = String.format("%s/supplier-tax-invoiced-item/reject-contract-feedback", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.WMS_CONTRACT_REJECT, url,
            JsonMapper.toJson(request), request.getDeclareContract().get(0).getDeclareContractId().toString(), "驳回合同反馈SCM");
        try {
            this.restTemplate.postForEntity(url, request, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 开票信息同步
     *
     * @return
     */
    public void invoiceInfoFeedback(SupplierTaxInvoiceInfoFeedbackRequest request) {
        String url = String.format("%s/supplier-tax-invoiced-item/invoice-info-feedback", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.SCM_GENERATE_CONTRACT_FEEDBACK, url,
            JsonMapper.toJson(request), request.getFormId().toString(), "开票信息同步");
        try {
            this.restTemplate.postForEntity(url, request, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
        }
    }


    /**
     * 供应商列表
     *
     * @param cooperateStatus
     * @param supplierType
     */
    public List<SupplierDto> supplierList(String cooperateStatus, String supplierType) {
        String url = String.format("%s/supplier/list?cooperateStatus=%s&supplierType=%s", scmServiceUrl, cooperateStatus, supplierType);
        HttpHeaders headers = new HttpHeaders();
        headers.add("location", TenantContext.getTenant());
        HttpEntity<PurchaseRefundTaskAddRequest> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<String> response = this.restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            try {
                PageResponse<SupplierDto> pageResponse = objectMapper.readValue(response.getBody(), new SupplierDtoTypeReference());
                return pageResponse.getContent();
            } catch (JsonProcessingException e) {
                throw new BusinessServiceException("请求失败", e);
            }
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    /**
     * 实时查询包装方式
     */
/*    public List<PurchasePackageInfoDto> getPurchasePackageInfo(GetPurchasePackageInfoRequest request) {
        String url = String.format("%s/purchase-order-item/purchase-package-info", scmServiceUrl);
        String responseStr = this.restTemplate.postForObject(url, request, String.class);
        try {
            return objectMapper.readValue(responseStr, new PurchasePackageInfoReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }*/
    public SupplierDto querySupplierByCode(String supplierCode) {
        String url = String.format("%s/supplier/querySupplierByCode/%s", scmServiceUrl, supplierCode);
        ResponseEntity<SupplierDto> forEntity = this.restTemplate.getForEntity(url, SupplierDto.class);
        return forEntity.getBody();

    }

    /**
     * 获取供应商下拉
     *
     * @param request
     * @return
     */
    public List<SelectModel> getSupplierNameDropDown(GetSupplierNameDropDownRequest request) {
        String url = String.format("%s/supplier/supplier-name-drop-down", scmServiceUrl);
        String responseStr = this.restTemplate.postForObject(url, request, String.class);
        try {
            return JSONUtil.toList(responseStr, SelectModel.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取所有强推款标签
     */
    @Cacheable(CacheKeyConstant.WMS_STRONGLY_RECOMMEND_STYLE_LABELS)
    public List<StronglyRecommendStyleLabelsDto> getAllStronglyRecommendStyleLabelsDtoList() {
        try {
            String url = String.format("%s/strongly-recommend-style-labels/list", scmServiceUrl);
            ResponseEntity<String> resStr = this.restTemplate.getForEntity(url, String.class);
            return JsonMapper.jsonStringToObjectArray(resStr.getBody(), StronglyRecommendStyleLabelsDto.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 获取供应商商品sku采购价
     *
     * @param request
     * @return
     */
    public List<SupplierSpecProductPurchaseDTO> getSupplierSpecProductPurchaseList(GetSupplierSpecProductPurchaseRequest request) {
        try {
            String url = String.format("%s/supplier-spec-product-purchase/list", scmServiceUrl);
            String responseStr = this.restTemplate.postForObject(url, request, String.class);
            return objectMapper.readValue(responseStr, new SupplierSpecProductPurchaseReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    public List<PurchaseOrderDeliveryItemInfoDto> getSupplierSpecProductPurchaseList(List<String> purchaseNoList) {
        if (CollectionUtils.isEmpty(purchaseNoList)) {
            return Collections.emptyList();
        }
        try {
            String url = String.format("%s/purchase-order-item/query-delivery-item-by-purchase", scmServiceUrl);
            String responseStr = this.restTemplate.postForObject(url, purchaseNoList, String.class);
            return objectMapper.readValue(responseStr, new PurchaseOrderDeliveryItemInfoReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 开票公司下拉
     *
     * @return
     */
    public List<SelectModel> getInvoiceCompanyDropDown(SupplierInvoiceCompanyDropDownRequest request) {
        try {
            String url = String.format("%s/supplier-tax/invoice-company-drop-down", scmServiceUrl);
            String responseStr = this.restTemplate.postForObject(url, request, String.class);
            return objectMapper.readValue(responseStr, new SelectModelReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }


    /**
     * 通过id获取供应商
     *
     * @param supplierId
     * @return
     */
    public SupplierDto getSupplierDtoBySupplierId(Integer supplierId) {
        String url = String.format("%s/supplier/get-dto-by-supplier-id/" + supplierId, scmServiceUrl);
        try {
            return this.restTemplate.getForObject(url, SupplierDto.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    public List<BdPurchaseOrderLabelDto> getAllBdPurchaseOrderLabel() {
        try {
            String url = String.format("%s/all-bd-purchase-order-label/from-scm", scmServiceUrl);
            String responseStr = this.restTemplate.getForObject(url, String.class);
            return objectMapper.readValue(responseStr, new BdPurchaseOrderLabelDtoReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 获取工艺单
     *
     * @param request
     * @return
     */
    public List<AttachDto> getDevelopAttachList(ProductDevelopAttachRequest request) {
        String workmanshipVersion = request.getWorkmanshipVersion();
        ProductWorkmanshipAttachmentDto workmanshipAttachmentDto;
        if (org.apache.commons.lang3.StringUtils.isBlank(workmanshipVersion)) {
            workmanshipAttachmentDto = this.getProductWorkmanshipAttachmentByType(request.getType().getWorkmanshipType(), request.getProductId());
        } else {
            workmanshipAttachmentDto = this.getVersionProductWorkmanshipAttachment(workmanshipVersion, request.getProductId(), request.getType(), 0);
        }
        return workmanshipAttachmentDto.getAttachments()
            .stream()
            .map(AttachDto::build)
            .collect(Collectors.toList());
    }

    /**
     * 获取商品工艺附件信息(最新版本)
     */
    public ProductWorkmanshipAttachmentDto getProductWorkmanshipAttachmentByType(String attachmentType, Integer productId) {
        BatchProductWorkmanshipAttachmentDto workmanshipAttachmentDto = getBatchProductWorkmanshipAttachmentByType(attachmentType, Collections.singletonList(productId));
        List<ProductWorkmanshipAttachmentDto> workmanshipAttachments = workmanshipAttachmentDto.getProductWorkmanshipAttachments();
        if (workmanshipAttachments.isEmpty()) {
            return ProductWorkmanshipAttachmentDto.empty(productId, attachmentType);
        }
        return workmanshipAttachments.get(0);
    }

    /**
     * 批量获取商品工艺附件信息(最新版本)
     *
     * @param productIds 商品系统productIds
     */
    public BatchProductWorkmanshipAttachmentDto getBatchProductWorkmanshipAttachmentByType(String attachmentType, List<Integer> productIds) {
        try {
            String url = String.format("%s/product-workmanship/attachment/batch-by-type?attachmentType=%s", scmServiceUrl, attachmentType);
            String responseStr = this.restTemplate.postForObject(url, productIds, String.class);
            return objectMapper.readValue(responseStr, BatchProductWorkmanshipAttachmentDto.class);
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            LOGGER.error("getBatchProductWorkmanshipAttachmentByType error,attachmentType={},productIds={},cause={}", attachmentType, productIds, e.getMessage(), e);
        }
        return BatchProductWorkmanshipAttachmentDto.empty();
    }

    /**
     * 获取指定版本商品工艺附件
     */
    public ProductWorkmanshipAttachmentDto getVersionProductWorkmanshipAttachment(String versionNo, Integer productId, DevelopImageType type, Integer latestOnly) {
        GetVersionWorkmanshipAttachmentDto getVersionWorkmanshipAttachmentDto = new GetVersionWorkmanshipAttachmentDto(productId, versionNo);
        BatchProductWorkmanshipAttachmentDto workmanshipAttachmentDto = this.getBatchProductWorkmanshipAttachment(Collections.singletonList(getVersionWorkmanshipAttachmentDto), type, latestOnly);
        List<ProductWorkmanshipAttachmentDto> workmanshipAttachments = workmanshipAttachmentDto.getProductWorkmanshipAttachments();
        if (workmanshipAttachments.isEmpty()) {
            return ProductWorkmanshipAttachmentDto.empty(productId, type.getWorkmanshipType());
        }
        return workmanshipAttachments.get(0);
    }

    public BatchProductWorkmanshipAttachmentDto getBatchProductWorkmanshipAttachment(List<GetVersionWorkmanshipAttachmentDto> items, DevelopImageType type, Integer latestOnly) {
        BatchGetVersionWorkmanshipAttachmentRequest request = new BatchGetVersionWorkmanshipAttachmentRequest();
        request.setItems(items);
        request.setAttachmentType(type.getWorkmanshipType());
        request.setLatestOnly(latestOnly);
        try {
            String url = String.format("%s/batch/product-workmanship/version/attachment", scmServiceUrl);
            String responseStr = this.restTemplate.postForObject(url, request, String.class);
            return objectMapper.readValue(responseStr, BatchProductWorkmanshipAttachmentDto.class);
        } catch (JsonProcessingException e) {
            LOGGER.error("getBatchProductWorkmanshipSheetAttachment error,req={},cause={}", JsonMapper.toJson(request), e.getMessage(), e);
        }
        return BatchProductWorkmanshipAttachmentDto.empty();
    }

    /**
     * 查询排单计划skc对应的bom详情信息
     */
    public SchedulingOrderPlanBomDetailResponse getSchedulingOrderPlanBomDetail(Integer productId, String skc) {
        try {
            SchedulingOrderPlanBomDetailRequest request = new SchedulingOrderPlanBomDetailRequest();
            request.setProductId(productId);
            request.setSkc(skc);
            String url = String.format("%s/product-bom/scheduling-order-plan-bom-detail/get-by-info", scmServiceUrl);
            String responseStr = this.restTemplate.postForObject(url, request, String.class);
            return objectMapper.readValue(responseStr, SchedulingOrderPlanBomDetailResponse.class);
        } catch (RuntimeException | JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    public List<SupplierPurchasePriceResponse> getSupplierProductSpecPurchasePrice(SupplierPurchasePriceRequest request) {
        try {
            String url = String.format("%s/purchase-order-item/purchase-price-info", scmServiceUrl);
            String responseStr = this.restTemplate.postForObject(url, request, String.class);
            return objectMapper.readValue(responseStr, new SupplierPurchasePriceResponseTypeReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    /**
     * 查看商品主唛吊牌信息
     */
    public List<ProductMainMarkTagResponse> queryProductMainMarkTagByProductId(Integer productId) {
        try {
            String url = String.format("%s/product-main-mark-tag/%s", scmServiceUrl, productId);
            String responseStr = this.restTemplate.getForObject(url, String.class);
            return objectMapper.readValue(responseStr, new ProductMainMarkTagResponseTypeReference());
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    public PurchaseOrderConcessionApplyResponse queryConcessionApplyOrder(String orderNo, String skc) {
        if (!StringUtils.hasText(orderNo) || !StringUtils.hasText(skc)) {
            return new PurchaseOrderConcessionApplyResponse();
        }
        try {
            DownloadConcessionApplyRequest request = new DownloadConcessionApplyRequest(orderNo, skc);
            String url = String.format("%s/purchase-order-documentary-record/query-concession-apply-order", scmServiceUrl);
            return this.restTemplate.postForObject(url, request, PurchaseOrderConcessionApplyResponse.class);
        } catch (RuntimeException e) {
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    public PurchaseOrderWorkmanshipInfoResponse purchaseOrderWorkmanshipInfoForQa(String purchasePlanNo, String sku) {
        try {
            String url = String.format("%s/purchase-order-workmanship-info-for-qa", scmServiceUrl);
            PurchaseOrderWorkmanshipInfoRequest request = new PurchaseOrderWorkmanshipInfoRequest();
            request.setOrderNo(purchasePlanNo);
            request.setSku(sku);
            return this.restTemplate.postForObject(url, request, PurchaseOrderWorkmanshipInfoResponse.class);
        } catch (RuntimeException e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    public OldProductPackageInfo getOldProductPackageInfo(Integer productId) {
        try {
            String url = String.format("%s/product-workmanship-package/%s", scmServiceUrl, productId);
            String responseStr = this.restTemplate.getForObject(url, String.class);
            return objectMapper.readValue(responseStr, OldProductPackageInfo.class);
        } catch (JsonProcessingException e) {
            LOGGER.error(e.getMessage(), e);
            return null;
        }
    }

    public List<Integer> generateReviewTask(StockinReturnReviewTaskGenerateRequest reviewTaskGenerateRequest) {
        try {
            String url = String.format("%s/stockin-return-review-task/generate-task", scmServiceUrl);
            ResponseEntity<String> responseEntity = this.restTemplate.postForEntity(url, reviewTaskGenerateRequest, String.class);
            return JsonMapper.jsonStringToObjectArray(responseEntity.getBody(), Integer.class);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw new BusinessServiceException(e.getMessage(), e);
        }
    }

    public void generateReturnOrder(ErpGenerateReturnOrderRequest request) {
        String url = String.format("%s/purchase-order/sync-purchase-return", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.RETURN_PRODUCT_SYNC, url,
            JsonMapper.toJson(request), request.getItemList().stream().map(ErpGenerateReturnOrderRequest.ErpReturnOrderItem::getSku).distinct().collect(Collectors.joining(StringConstant.COMMA)), "采购退货，scm生成退货单");
        try {
            this.restTemplate.postForEntity(url, request, Void.class);
            externalApiLogService.updateLog(apiLogEntity, ExternalApiLogStatusEnum.SUCCESS);
        } catch (Exception e) {
            externalApiLogService.updateLog(apiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
            throw e;
        }
    }

    /**
     * 同步其他入库单收货信息到SCM
     *
     * @param request 请求参数
     */
    public void syncReceivingByOtherStockIn(PurchaseOrderRequest request) {
        String url = String.format("%s/purchase-order/wms-sync-received/for-over-receipt", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.OTHER_STOCKIN_ORDER_SYNC_SCM, url,
            JsonMapper.toJson(request), request.getReceivingNo(), "同步其他入库单收货信息到SCM");
        //保存到异步调用队列
        externalApiAsyncQueueService.addIgnore(apiLogEntity, request.getReceivingNo());
    }

    /**
     * 采购接收单完成同步SCM
     *
     * @param request 请求参数
     */
    public void syncPurchaseOrder(PurchaseOrderRequest request) {
        String url = String.format("%s/purchase-order/wms-sync-received", scmServiceUrl);
        ExternalApiLogEntity apiLogEntity = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.PURCHASE_ORDER_SYNC_SCM, url,
            JsonMapper.toJson(request), request.getReceivingNo(), "采购接收单完成同步SCM");
        //保存到异步调用队列
        externalApiAsyncQueueService.addIgnore(apiLogEntity, request.getReceivingNo());
    }

    private static final class SupplierPurchasePriceResponseTypeReference extends TypeReference<ArrayList<SupplierPurchasePriceResponse>> {
    }

    private static final class ProductMainMarkTagResponseTypeReference extends TypeReference<ArrayList<ProductMainMarkTagResponse>> {
    }


    private static final class SupplierTaxInvoicedItemListTypeReference extends TypeReference<ArrayList<SupplierTaxInvoicedItemInvoiceDto>> {
    }

    private static final class SupplierTaxGetSampleDatailDtoListTypeReference extends TypeReference<ArrayList<SupplierTaxGetSampleDatailDto>> {
    }

    private static final class SupplierDtoTypeReference extends TypeReference<PageResponse<SupplierDto>> {
    }

    private static final class PurchasePackageInfoReference extends TypeReference<ArrayList<PurchasePackageInfoDto>> {
    }

    private static final class SupplierSpecProductPurchaseReference extends TypeReference<ArrayList<SupplierSpecProductPurchaseDTO>> {
    }

    private static final class SelectModelReference extends TypeReference<ArrayList<SelectModel>> {
    }

    private static final class BdPurchaseOrderLabelDtoReference extends TypeReference<ArrayList<BdPurchaseOrderLabelDto>> {
    }

    private static final class PurchaseOrderDeliveryItemInfoReference extends TypeReference<ArrayList<PurchaseOrderDeliveryItemInfoDto>> {
    }

}
