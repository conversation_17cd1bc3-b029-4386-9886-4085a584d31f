package com.nsy.wms.business.manage.supplier.request;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * create by ran
 */
public class SyncInboundsRequiredCheckRequest {
    @NotNull
    @NotEmpty
    private List<InboundsRequiredCheck> inboundsRequiredChecks;

    public List<InboundsRequiredCheck> getInboundsRequiredChecks() {
        return inboundsRequiredChecks;
    }

    public void setInboundsRequiredChecks(List<InboundsRequiredCheck> inboundsRequiredChecks) {
        this.inboundsRequiredChecks = inboundsRequiredChecks;
    }
}
