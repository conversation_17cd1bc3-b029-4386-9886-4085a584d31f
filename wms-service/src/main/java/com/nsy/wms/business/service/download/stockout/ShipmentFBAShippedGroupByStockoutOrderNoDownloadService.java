package com.nsy.wms.business.service.download.stockout;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.beust.jcommander.internal.Sets;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.ShipmentDownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.business.service.product.ProductCustomsDeclareElementService;
import com.nsy.wms.business.service.stockout.StockoutOrderPackMappingInfoService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import com.nsy.wms.business.service.stockout.StockoutShipmentAmazonRelationService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderPackMappingInfoEntity;
import com.nsy.wms.utils.JsonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ShipmentFBAShippedGroupByStockoutOrderNoDownloadService implements IDownloadService {
    @Autowired
    private ShipmentAllShippedDownloadService shipmentAllShippedDownloadService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Inject
    StockoutOrderPackMappingInfoService packMappingInfoService;
    @Autowired
    private StockoutShipmentService shipmentService;
    @Autowired
    StockoutShipmentAmazonRelationService stockoutShipmentAmazonRelationService;
    @Resource
    ProductCustomsDeclareElementService productCustomsDeclareElementService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_SHIPMENT_FBA_SHIPPED_BY_STOCKOUT_ORDER_NO;
    }

    // 按查询条件导出已发货
    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        ShipmentDownloadRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), ShipmentDownloadRequest.class);
        if (downloadRequest.getSearchRequest() != null && CollectionUtils.isEmpty(downloadRequest.getShipmentIds()) && !CollectionUtils.isEmpty(downloadRequest.getSearchRequest().getFbaShipmentIdList())) {
            buildShipmentIdByFba(downloadRequest, response);
        } else {
            shipmentAllShippedDownloadService.buildShipmentId(downloadRequest, request, response);
        }
        if (request.getPageIndex() > 1 || CollectionUtils.isEmpty(downloadRequest.getShipmentIds()) && CollectionUtils.isEmpty(downloadRequest.getDeclareDocumentNo()))
            return response;
        List<ShipmentBoxSkuExport> shipmentBoxSkuExports = shipmentService.getBaseMapper().shipmentBoxSkuListOrderByStockoutOrderNo(downloadRequest.getShipmentIds(), downloadRequest.getDeclareDocumentNo())
                .stream().filter(it -> StrUtil.isNotBlank(it.getStockoutOrderNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shipmentBoxSkuExports)) {
            return response;
        }
        List<String> sonList = shipmentBoxSkuExports.stream().map(ShipmentBoxSkuExport::getStockoutOrderNo).distinct().collect(Collectors.toList());
        Map<String, StockoutOrderEntity> collect = stockoutOrderService.list(new LambdaQueryWrapper<StockoutOrderEntity>()
                .select(StockoutOrderEntity::getStockoutOrderNo, StockoutOrderEntity::getWorkspace, StockoutOrderEntity::getStockoutType, StockoutOrderEntity::getPlatformName)
                .in(StockoutOrderEntity::getStockoutOrderNo, sonList))
                .stream().collect(Collectors.toMap(StockoutOrderEntity::getStockoutOrderNo, Function.identity()));
        Map<String, List<ShipmentBoxSkuExport>> shipmentBoxSkuExportsMap = shipmentBoxSkuExports.stream().collect(Collectors.groupingBy(ShipmentBoxSkuExport::getStockoutOrderNo));
        ArrayList<ShipmentBoxSkuExport> skuExports = new ArrayList<>();
        buildResult(collect, shipmentBoxSkuExportsMap, skuExports);
        response.setDataJsonStr(JsonMapper.toJson(skuExports));
        return response;
    }

    private void buildResult(Map<String, StockoutOrderEntity> collect, Map<String, List<ShipmentBoxSkuExport>> operateMap, List<ShipmentBoxSkuExport> skuExports) {
        for (Map.Entry<String, List<ShipmentBoxSkuExport>> entry : operateMap.entrySet()) {
            Set<Integer> set = Sets.newHashSet();
            List<ShipmentBoxSkuExport> sortByBoxIndex = entry.getValue().stream().sorted(Comparator.comparing(ShipmentBoxSkuExport::getBoxIndex)).collect(Collectors.toList());
            for (ShipmentBoxSkuExport export : sortByBoxIndex) {
                export.setElementValue(productCustomsDeclareElementService.replaceStoreSku(export.getSpu(), export.getStoreSku()));
                if (export.getBoxIndex() == null || export.getBoxIndex().contains("NO"))
                    continue;
                Integer index = export.getShipmentId();
                String[] split = null;
                if (StrUtil.isNotBlank(export.getBoxSize()) && !StrUtil.equals("袋子", export.getBoxSize())) {
                    split = this.processString(export.getBoxSize());
                    export.setLength(split[0]);
                    export.setWidth(split[1]);
                    export.setHeight(split[2]);
                }
                export.setBoxIndex(String.format("NO.%s", export.getBoxIndex() == null ? "" : export.getBoxIndex()));
                if (set.contains(index)) {
                    export.setVolumeWeight(null);
                    export.setBoxSize(null);
                } else {
                    set.add(index);
                    if (split != null) {
                        BigDecimal multiply = Arrays.stream(split).map(BigDecimal::new).reduce(BigDecimal::multiply).orElse(BigDecimal.ZERO);
                        export.setVolumeWeight(multiply.divide(StockConstant.SHIPMENT_VOLUME_RATIO).toPlainString());
                    }
                }
                StockoutOrderEntity stockoutOrderEntity = collect.getOrDefault(export.getStockoutOrderNo(), new StockoutOrderEntity());
                if (StockoutOrderWorkSpaceEnum.B2B_AREA.name().equals(stockoutOrderEntity.getWorkspace())) {
                    export.setStoreSku(export.getSku());
                    export.setStoreBarcode(JSONUtils.toJSON(CollUtil.newArrayList(export.getColor(), export.getSize())));
                }
                if (StockoutOrderTypeEnum.SPACE_TRANSFER_DELIVERY.name().equals(stockoutOrderEntity.getStockoutType()) && StringUtils.hasText(export.getOrderItemId())) {
                    StockoutOrderPackMappingInfoEntity mapping = packMappingInfoService.getByOrderItemNo(export.getOrderItemId());
                    export.setStoreSku(mapping == null ? export.getStoreSku() : mapping.getOriginSku());
                    export.setStoreBarcode(export.getSku());
                }
//                Integer totalQty = orderQtyMap.get(entry.getKey());
//                if (totalQty != null) {
//                    buildPrice(export, totalQty, stockoutOrderCountryMap, entry.getKey(), stockoutOrderEntity);
//                }
                skuExports.add(export);
            }
        }
    }

    public String[] processString(String input) {
        String[] parts;
        //包含：则去掉：前所有数据
        if (input.contains(":")) {
            String[] temp = input.split(":");
            // 去掉：前数据然后根据*切割
            parts = temp[temp.length - 1].split("\\*");
        } else {
            // 根据*切割
            parts = input.split("\\*");
        }
        return parts;
    }

    //    private Map<String, List<ShipmentBoxSkuExport>> sortByShipmentCount(List<String> declareDocumentNo, Map<String, List<ShipmentBoxSkuExport>> shipmentBoxSkuExportsMap) {
    //        Map<String, List<ShipmentBoxSkuExport>> operateMap;
    //        String orderNo = declareDocumentNo.get(0);
    //        // 单据同名订单放第一
    //        operateMap = new LinkedHashMap<>(shipmentBoxSkuExportsMap.size());
    //        if (Objects.nonNull(shipmentBoxSkuExportsMap.getOrDefault(orderNo, null))) {
    //            operateMap.put(orderNo, shipmentBoxSkuExportsMap.get(orderNo));
    //        }
    //        // 根据箱数由多到少排序
    //        List<Map.Entry<String, List<ShipmentBoxSkuExport>>> entries = new ArrayList<>(shipmentBoxSkuExportsMap.entrySet());
    //        LinkedHashMap<String, List<ShipmentBoxSkuExport>> sort = entries.stream().filter(item -> !orderNo.equals(item.getKey())).sorted(Comparator.comparingInt(v -> Math.negateExact(v.getValue().size())))
    //                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1, LinkedHashMap::new));
    //        operateMap.putAll(sort);
    //        return operateMap;
    //    }

    private void buildShipmentIdByFba(ShipmentDownloadRequest downloadRequest, DownloadResponse response) {
        List<Integer> shipmentIdByFbaShipmentId = stockoutShipmentAmazonRelationService.getBaseMapper().findShipmentIdByFbaShipmentId(downloadRequest.getSearchRequest().getFbaShipmentIdList());
        downloadRequest.setShipmentIds(shipmentIdByFbaShipmentId);
        // 要先设置totalCount
        response.setTotalCount(Long.valueOf(shipmentIdByFbaShipmentId.size()));
    }
}
