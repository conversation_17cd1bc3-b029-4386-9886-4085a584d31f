package com.nsy.wms.business.manage.scm.response;

import com.nsy.api.wms.domain.qa.AttachmentDto;
import com.nsy.api.wms.domain.qa.BdTagDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-11-07
 */
@ApiModel(value = "PurchaseOrderWorkmanshipInfoResponse", description = "QA采购单级别工艺信息-响应体")
public class PurchaseOrderWorkmanshipInfoResponse {
    @ApiModelProperty(value = "采购单明细Id", name = "orderItemId")
    private Integer orderItemId;
    @ApiModelProperty(value = "包装方式名称", name = "packageName")
    private String packageName;
    @ApiModelProperty(value = "包装袋规格", name = "packageSize")
    private String packageSize;
    @ApiModelProperty(value = "成分", name = "fabricType")
    private String fabricType;
    @ApiModelProperty(value = "英文成分", name = "fabricTypeEn")
    private String fabricTypeEn;
    @ApiModelProperty(value = "工艺单PDF附件", name = "workmanshipSheet")
    private AttachmentDto workmanshipSheet;
    @ApiModelProperty(value = "版本号", name = "versionNo")
    private String versionNo;
    @ApiModelProperty(value = "洗水唛模板", name = "washLabel")
    private String washLabel;
    @ApiModelProperty(value = "吊牌", name = "bdTagDto")
    private BdTagDto bdTagDto;

    @ApiModelProperty("包装后长")
    private BigDecimal packageLong;
    @ApiModelProperty("包装后宽")
    private BigDecimal packageWidth;
    @ApiModelProperty("包装后高")
    private BigDecimal packageHeight;
    @ApiModelProperty("周长")
    private BigDecimal perimeter;
    @ApiModelProperty("重量(kg)")
    private BigDecimal weight;
    @ApiModelProperty("长(inch)")
    private BigDecimal lengthInch;
    @ApiModelProperty("宽(inch)")
    private BigDecimal widthInch;
    @ApiModelProperty("高(inch)")
    private BigDecimal heightInch;
    @ApiModelProperty("重量(磅)")
    private BigDecimal weightPound;
    @ApiModelProperty("体积重(磅)")
    private BigDecimal volumeWeightPound;

    @ApiModelProperty("体积重(kg)")
    private BigDecimal volumeWeightKg;

    @ApiModelProperty("计费重量(kg)")
    private BigDecimal chargedWeightKg;
    @ApiModelProperty("标准类型")
    private String standardType;
    @ApiModelProperty("计费重量(磅)")
    private BigDecimal chargedWeight;
    @ApiModelProperty("FBA配送费($)")
    private BigDecimal fbaCost;

    public BigDecimal getPackageLong() {
        return packageLong;
    }

    public void setPackageLong(BigDecimal packageLong) {
        this.packageLong = packageLong;
    }

    public BigDecimal getPackageWidth() {
        return packageWidth;
    }

    public void setPackageWidth(BigDecimal packageWidth) {
        this.packageWidth = packageWidth;
    }

    public BigDecimal getPackageHeight() {
        return packageHeight;
    }

    public void setPackageHeight(BigDecimal packageHeight) {
        this.packageHeight = packageHeight;
    }

    public BigDecimal getPerimeter() {
        return perimeter;
    }

    public void setPerimeter(BigDecimal perimeter) {
        this.perimeter = perimeter;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getLengthInch() {
        return lengthInch;
    }

    public void setLengthInch(BigDecimal lengthInch) {
        this.lengthInch = lengthInch;
    }

    public BigDecimal getWidthInch() {
        return widthInch;
    }

    public void setWidthInch(BigDecimal widthInch) {
        this.widthInch = widthInch;
    }

    public BigDecimal getHeightInch() {
        return heightInch;
    }

    public void setHeightInch(BigDecimal heightInch) {
        this.heightInch = heightInch;
    }

    public BigDecimal getWeightPound() {
        return weightPound;
    }

    public void setWeightPound(BigDecimal weightPound) {
        this.weightPound = weightPound;
    }

    public BigDecimal getVolumeWeightPound() {
        return volumeWeightPound;
    }

    public void setVolumeWeightPound(BigDecimal volumeWeightPound) {
        this.volumeWeightPound = volumeWeightPound;
    }

    public String getStandardType() {
        return standardType;
    }

    public void setStandardType(String standardType) {
        this.standardType = standardType;
    }

    public BigDecimal getChargedWeight() {
        return chargedWeight;
    }

    public void setChargedWeight(BigDecimal chargedWeight) {
        this.chargedWeight = chargedWeight;
    }

    public BigDecimal getFbaCost() {
        return fbaCost;
    }

    public void setFbaCost(BigDecimal fbaCost) {
        this.fbaCost = fbaCost;
    }

    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getFabricType() {
        return fabricType;
    }

    public void setFabricType(String fabricType) {
        this.fabricType = fabricType;
    }

    public String getFabricTypeEn() {
        return fabricTypeEn;
    }

    public void setFabricTypeEn(String fabricTypeEn) {
        this.fabricTypeEn = fabricTypeEn;
    }

    public AttachmentDto getWorkmanshipSheet() {
        return workmanshipSheet;
    }

    public void setWorkmanshipSheet(AttachmentDto workmanshipSheet) {
        this.workmanshipSheet = workmanshipSheet;
    }

    public String getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(String versionNo) {
        this.versionNo = versionNo;
    }

    public String getWashLabel() {
        return washLabel;
    }

    public void setWashLabel(String washLabel) {
        this.washLabel = washLabel;
    }

    public BdTagDto getBdTagDto() {
        return bdTagDto;
    }

    public void setBdTagDto(BdTagDto bdTagDto) {
        this.bdTagDto = bdTagDto;
    }

    public void setWorkmanshipSheetByParam(String originName, String name, String url) {
        AttachmentDto dto = new AttachmentDto();
        dto.setOriginName(originName);
        dto.setName(name);
        dto.setUrl(url);
        setWorkmanshipSheet(dto);
    }

    public String getPackageSize() {
        return packageSize;
    }

    public void setPackageSize(String packageSize) {
        this.packageSize = packageSize;
    }

    public BigDecimal getVolumeWeightKg() {
        return volumeWeightKg;
    }

    public void setVolumeWeightKg(BigDecimal volumeWeightKg) {
        this.volumeWeightKg = volumeWeightKg;
    }

    public BigDecimal getChargedWeightKg() {
        return chargedWeightKg;
    }

    public void setChargedWeightKg(BigDecimal chargedWeightKg) {
        this.chargedWeightKg = chargedWeightKg;
    }
}
