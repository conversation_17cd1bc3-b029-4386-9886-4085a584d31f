package com.nsy.wms.business.service.wcs;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.wms.request.wcs.WcsHeightSortingRecordAddRequest;
import com.nsy.api.wms.request.wcs.WcsHeightSortingRecordPageRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.wcs.WcsHeightSortingRecordPageResponse;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.wcs.WcsHeightSortingRecordEntity;
import com.nsy.wms.repository.jpa.mapper.wcs.WcsHeightSortingRecordMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 测量高度分拣记录Service
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class WcsHeightSortingRecordService extends ServiceImpl<WcsHeightSortingRecordMapper, WcsHeightSortingRecordEntity> {

    @Inject
    private LoginInfoService loginInfoService;

    /**
     * 新增测量高度分拣记录
     *
     * @param request 新增请求参数
     * @return 记录ID
     */
    public Integer addRecord(WcsHeightSortingRecordAddRequest request) {
        WcsHeightSortingRecordEntity entity = new WcsHeightSortingRecordEntity();

        // 复制属性
        BeanUtils.copyProperties(request, entity);

        // 设置创建信息
        entity.setLocation("QUANZHOU");
        entity.setCreateBy(loginInfoService.getName());
        entity.setUpdateBy(loginInfoService.getName());

        // 保存到数据库
        this.save(entity);

        return entity.getId();
    }

    /**
     * 分页查询测量高度分拣记录
     *
     * @param request 分页查询请求参数
     * @return 分页查询结果
     */
    public PageResponse<WcsHeightSortingRecordPageResponse> page(WcsHeightSortingRecordPageRequest request) {
        // 创建分页对象
        Page<WcsHeightSortingRecordEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());

        // 执行分页查询
        Page<WcsHeightSortingRecordEntity> result = baseMapper.selectPageWithConditions(page, request);

        // 转换结果
        List<WcsHeightSortingRecordPageResponse> contentList = result.getRecords().stream().map(record -> BeanUtil.toBean(record, WcsHeightSortingRecordPageResponse.class)).collect(Collectors.toList());

        // 构建分页响应
        PageResponse<WcsHeightSortingRecordPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(contentList);
        pageResponse.setTotalCount(result.getTotal());

        return pageResponse;
    }
} 