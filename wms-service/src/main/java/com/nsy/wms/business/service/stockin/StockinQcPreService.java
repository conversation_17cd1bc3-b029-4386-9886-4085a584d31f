package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.domain.stock.StockInternalBox;
import com.nsy.api.wms.domain.stock.StockinOrderItemBean;
import com.nsy.api.wms.domain.stockin.QcInboundsMessage;
import com.nsy.api.wms.enumeration.QcInboundsResultStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderItemStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderLogTypeEnum;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.domain.bo.stockin.StockinQaTaskUpdateBo;
import com.nsy.wms.business.manage.supplier.SupplierApiService;
import com.nsy.wms.business.manage.supplier.enumeration.InboundsRequiredCheckCheckStatusEnum;
import com.nsy.wms.business.manage.supplier.enumeration.InboundsRequiredCheckPushTypeEnum;
import com.nsy.wms.business.manage.supplier.request.InboundsRequiredCheck;
import com.nsy.wms.business.manage.supplier.request.SyncInboundsRequiredCheckRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxAdjustService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.query.StockInternalBoxQueryWrapper;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderTaskItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinQcInboundsEntity;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class StockinQcPreService {

    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockinOrderItemService stockinOrderItemService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    SupplierApiService supplierApiService;
    @Autowired
    StockinQcInboundsService stockinQcInboundsService;
    @Autowired
    StockinOrderTaskItemService stockinOrderTaskItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;
    @Autowired
    StockinOrderLogService stockinOrderLogService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockInternalBoxAdjustService stockInternalBoxAdjustService;
    @Autowired
    StockinQcBuildService stockinQcBuildService;

    public void updateTaskItem(List<StockInternalBoxItemEntity> list) {
        StockInternalBoxItemEntity item = list.get(0);
        List<String> updateList = new ArrayList<>(list.size());
        List<String> noNeedList = new ArrayList<>(list.size());
        list.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).distinct().forEach(stockinOrderNo -> {
            StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(stockinOrderNo);
            StockinOrderTaskEntity stockinOrderTaskEntity = stockinOrderTaskService.getById(stockinOrderEntity.getTaskId());
            //获取入库单中不属于这个箱子的，但是同个sku明细
            List<StockinOrderItemBean> itemEntityList = stockinOrderItemService.getBaseMapper().findDiffBoxItem(stockinOrderEntity.getStockinOrderId(), item.getSku(), item.getInternalBoxCode());
            if (CollectionUtils.isEmpty(itemEntityList)) {
                updateList.add(stockinOrderTaskEntity.getSupplierDeliveryNo());
                return;
            }
            //判断是否还存在其他的质检箱,校验质检结果
            List<StockinOrderItemBean> otherQaBox = itemEntityList.stream().filter(orderItem -> StockInternalBoxTypeEnum.QA_BOX.name().equals(orderItem.getInternalBoxType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(otherQaBox)) {
                List<String> otherQaBoxCode = otherQaBox.stream().map(StockinOrderItemBean::getInternalBoxCode).distinct().collect(Collectors.toList());
                for (String boxCode : otherQaBoxCode) {
                    List<StockinQcInboundsEntity> result = stockinQcInboundsService.getBaseMapper().findResult(Collections.singletonList(boxCode), stockinOrderEntity.getSupplierDeliveryBoxCode(), item.getSku());
                    if (CollectionUtils.isEmpty(result) || !(QcInboundsResultStatusEnum.PUT_ON.getDesc().equals(result.get(0).getResult()) || QcInboundsResultStatusEnum.CONCESSION_RECEIVE.getDesc().equals(result.get(0).getResult()) || QcInboundsResultStatusEnum.RECEIVE_BUT_SOME_RETURN.getDesc().equals(result.get(0).getResult()))) {
                        noNeedList.add(stockinOrderTaskEntity.getSupplierDeliveryNo());
                        return;
                    }
                }
            }
            updateList.add(stockinOrderTaskEntity.getSupplierDeliveryNo());
        });
        List<String> needUpdateList = updateList.stream().filter(str1 -> !noNeedList.stream().anyMatch(str2 -> str2.equals(str1))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needUpdateList)) {
            List<StockinOrderTaskEntity> stockinOrderTaskEntityList = stockinOrderTaskService.findAllBySupplierDeliveryNoList(needUpdateList);
            stockinOrderTaskEntityList.forEach(taskEntity -> {
                //更新任务明细
                List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(taskEntity.getTaskId(), item.getSku());
                if (CollectionUtils.isEmpty(taskItemEntityList))
                    return;
                List<StockinOrderTaskItemEntity> collect = taskItemEntityList.stream().map(entity -> {
                    StockinOrderTaskItemEntity taskItemEntity = new StockinOrderTaskItemEntity();
                    taskItemEntity.setTaskItemId(entity.getTaskItemId());
                    taskItemEntity.setIsPreQaQualified(1);
                    taskItemEntity.setUpdateBy(loginInfoService.getName());
                    return taskItemEntity;
                }).collect(Collectors.toList());

                stockinOrderTaskItemService.updateBatchById(collect);
                //查询入库任务明细的入库单明细(仅查询收货箱)
                List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.getBaseMapper().findAllByTaskItemId(taskItemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskItemId).collect(Collectors.toList()), StockInternalBoxTypeEnum.RECEIVE_BOX.name());
                if (CollectionUtils.isEmpty(stockinOrderItemEntityList)) return;
                //更新入库单明细为待上架
                StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(taskEntity.getTaskId());
                this.updateOrderItemStatus(stockinOrderEntity, stockinOrderItemEntityList, StockinOrderItemStatusEnum.WAIT_SHELVE.name());
                //去更新质检任务
                this.updateQcTask(stockinOrderEntity, stockinOrderItemEntityList, Boolean.FALSE);
                //更新收货箱的状态
                stockinOrderItemEntityList.stream().filter(orderItemEntity -> orderItemEntity.getQty() > 0).map(StockinOrderItemEntity::getInternalBoxCode).distinct().forEach(internalBoxCode -> stockInternalBoxService.updateInternalBoxStatusByItem(internalBoxCode));
            });
        }
    }

    private void updateOrderItemStatus(StockinOrderEntity stockinOrderEntity, List<StockinOrderItemEntity> stockinOrderItemEntityList, String status) {
        //查询内部箱明细
        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>().eq(StockInternalBoxItemEntity::getStockInOrderNo, stockinOrderEntity.getStockinOrderNo())
                .eq(StockInternalBoxItemEntity::getSku, stockinOrderItemEntityList.get(0).getSku()));
        List<StockInternalBoxItemEntity> boxItemEntityList = new ArrayList<>(stockinOrderItemEntityList.size());
        stockinOrderItemEntityList.forEach(item -> {
            Optional<StockInternalBoxItemEntity> any = list.stream().filter(boxItemEntity ->
                    boxItemEntity.getInternalBoxCode().equals(item.getInternalBoxCode())
                            && boxItemEntity.getPurchasePlanNo().equals(item.getPurchasePlanNo())).findAny();
            any.ifPresent(boxItem -> {
                StockInternalBoxItemEntity itemEntity = new StockInternalBoxItemEntity();
                itemEntity.setInternalBoxItemId(boxItem.getInternalBoxItemId());
                itemEntity.setStatus(status);
                itemEntity.setUpdateBy(loginInfoService.getName());
                boxItemEntityList.add(itemEntity);
            });
        });
        List<StockinOrderItemEntity> collect = stockinOrderItemEntityList.stream().map(item -> {
            StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
            orderItemEntity.setStockinOrderItemId(item.getStockinOrderItemId());
            orderItemEntity.setStatus(status);
            orderItemEntity.setUpdateBy(loginInfoService.getName());
            return orderItemEntity;
        }).collect(Collectors.toList());
        stockInternalBoxItemService.updateBatchById(boxItemEntityList);
        stockinOrderItemService.updateBatchById(collect);
    }

  /*  private void updateOrderItemStatusSingle(List<StockinOrderItemEntity> stockinOrderItemEntityList, String status) {
        //查询内部箱明细   收货箱 只更新箱内的
        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>().eq(StockInternalBoxItemEntity::getInternalBoxCode, stockinOrderItemEntityList.get(0).getInternalBoxCode())
                .eq(StockInternalBoxItemEntity::getSku, stockinOrderItemEntityList.get(0).getSku()));
        List<StockInternalBoxItemEntity> boxItemEntityList = new ArrayList<>();
        list.stream().forEach(boxItem -> {
            StockInternalBoxItemEntity itemEntity = new StockInternalBoxItemEntity();
            itemEntity.setInternalBoxItemId(boxItem.getInternalBoxItemId());
            itemEntity.setStatus(status);
            itemEntity.setUpdateBy(loginInfoService.getName());
            boxItemEntityList.add(itemEntity);
        });
//        List<StockinOrderItemEntity> collect = stockinOrderItemEntityList.stream().map(item -> {
//            StockinOrderItemEntity orderItemEntity = new StockinOrderItemEntity();
//            orderItemEntity.setStockinOrderItemId(item.getStockinOrderItemId());
//            orderItemEntity.setStatus(status);
//            orderItemEntity.setCreateBy(loginInfoService.getName());
//            return orderItemEntity;
//        }).collect(Collectors.toList());
        stockInternalBoxItemService.updateBatchById(boxItemEntityList);
//        stockinOrderItemService.updateBatchById(collect);
    }*/

    public void updateQcTask(StockinOrderEntity stockinOrderEntity, List<StockinOrderItemEntity> stockinOrderItemEntityList, Boolean isAllCheck) {
        if (CollectionUtils.isEmpty(stockinOrderItemEntityList)) {
            return;
        }
        StockinOrderItemEntity stockinOrderItemEntity = stockinOrderItemEntityList.get(0);
        List<StockInternalBoxItemEntity> boxItemEntityList = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getSku, stockinOrderItemEntity.getSku())
                .in(StockInternalBoxItemEntity::getInternalBoxCode, stockinOrderItemEntityList.stream().map(StockinOrderItemEntity::getInternalBoxCode).distinct().collect(Collectors.toList()))
                .eq(StockInternalBoxItemEntity::getStockInOrderNo, stockinOrderEntity.getStockinOrderNo()));
        if (CollectionUtils.isEmpty(boxItemEntityList)) {
            return;
        }
        pushSupplier(stockinOrderEntity, boxItemEntityList, isAllCheck);

        Map<String, List<StockInternalBoxItemEntity>> collect = boxItemEntityList.stream().collect(Collectors.groupingBy(item -> item.getInternalBoxCode() + "_" + item.getSku()));
        collect.forEach((key, value) -> {
            StockInternalBoxItemEntity stockInternalBoxItemInfo = value.get(0);
            //生成或更新质检任务
            messageProducer.sendMessage(KafkaConstant.STOCK_IN_QA_TASK_TOPIC_NAME, KafkaConstant.STOCK_IN_QA_TASK_TOPIC,
                    Key.of(stockInternalBoxItemInfo.getInternalBoxCode() + "_" + stockInternalBoxItemInfo.getSku()),
                    new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(),
                            new StockinQaTaskUpdateBo(stockInternalBoxItemInfo.getInternalBoxCode(), stockInternalBoxItemInfo.getSku())));
        });
    }

    private void pushSupplier(StockinOrderEntity stockinOrderNo, List<StockInternalBoxItemEntity> dataList, Boolean isAllCheck) {
        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(dataList.stream().map(StockInternalBoxItemEntity::getSku).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(ProductSpecInfoEntity::getSku, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0))));
        List<InboundsRequiredCheck> list = new ArrayList<>(8);
        for (StockInternalBoxItemEntity itemEntity : dataList) {
            StockInternalBox internalBox = stockInternalBoxService.getInternalBox(itemEntity.getInternalBoxCode());
            InboundsRequiredCheck inboundsRequiredCheck = new InboundsRequiredCheck();
            inboundsRequiredCheck.setBoxBarcode(itemEntity.getInternalBoxCode());
            inboundsRequiredCheck.setCheckStatus(InboundsRequiredCheckCheckStatusEnum.TO_BE_INSPECTED.getDesc());
            inboundsRequiredCheck.setDemandQcCount(isAllCheck ? StockInternalBoxTypeEnum.QA_BOX.name().equals(internalBox.getInternalBoxType()) ? itemEntity.getQty() : stockinQcBuildService.getQaQty(
                    itemEntity.getSpaceId(), stockinOrderNo.getSupplierId(), itemEntity.getQty()) : 0);
            inboundsRequiredCheck.setInboundsDate(new Date());
            ProductSpecInfoEntity specInfoEntity = specMap.get(itemEntity.getSku());
            inboundsRequiredCheck.setNeedHeight(Objects.isNull(specInfoEntity.getPackageHeight()) ? 1 : 0);
            boolean isNeedWeight = Objects.isNull(specInfoEntity.getActualWeight()) || specInfoEntity.getActualWeight().compareTo(BigDecimal.ZERO) == 0;
            inboundsRequiredCheck.setNeedWeight(isNeedWeight ? 1 : 0);
            inboundsRequiredCheck.setProductId(specInfoEntity.getProductId());
            inboundsRequiredCheck.setProductInboundsCount(isAllCheck ? itemEntity.getQty() : 0);
            inboundsRequiredCheck.setProductSku(specInfoEntity.getSku());
            inboundsRequiredCheck.setPurchaseNumber(itemEntity.getPurchasePlanNo());
            inboundsRequiredCheck.setPushType(InboundsRequiredCheckPushTypeEnum.INBOUNDS.getDesc());
            inboundsRequiredCheck.setReceiveOrder(stockinOrderNo.getSupplierDeliveryBoxCode());
            inboundsRequiredCheck.setSku(itemEntity.getSku());
            stockInternalBoxAdjustService.buildSupplierInfo(inboundsRequiredCheck, inboundsRequiredCheck.getSku(), stockinOrderNo.getTaskId());
            //月台不存在则取出库单
            if (!StringUtils.hasText(inboundsRequiredCheck.getSupplierName())) {
                inboundsRequiredCheck.setSupplierId(stockinOrderNo.getSupplierId());
                inboundsRequiredCheck.setSupplierName(stockinOrderNo.getSupplierName());
            }
            inboundsRequiredCheck.setIsAllCheck(isAllCheck ? 1 : 0);
            list.add(inboundsRequiredCheck);
        }
        if (CollectionUtils.isEmpty(list))
            return;
        SyncInboundsRequiredCheckRequest syncInboundsRequiredCheckRequest = new SyncInboundsRequiredCheckRequest();
        syncInboundsRequiredCheckRequest.setInboundsRequiredChecks(list);
        messageProducer.sendMessage(KafkaConstant.SYNC_INBOUNDS_REQUIRED_CHECK_TOPIC_NAME, KafkaConstant.SYNC_INBOUNDS_REQUIRED_CHECK_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), syncInboundsRequiredCheckRequest));
    }

    public void returnTaskItem(List<StockInternalBoxItemEntity> list) {
        StockInternalBoxItemEntity item = list.get(0);
        List<String> updateList = new ArrayList<>(list.size());
        List<String> noNeedList = new ArrayList<>(list.size());
        list.stream().map(StockInternalBoxItemEntity::getStockInOrderNo).distinct().forEach(stockinOrderNo -> {
            StockinOrderEntity stockinOrderEntity = stockinOrderService.getByStockinOrderNo(stockinOrderNo);
            StockinOrderTaskEntity stockinOrderTaskEntity = stockinOrderTaskService.getById(stockinOrderEntity.getTaskId());
            //获取入库单中不属于这个箱子的，但是同个sku明细
            List<StockinOrderItemBean> itemEntityList = stockinOrderItemService.getBaseMapper().findDiffBoxItem(stockinOrderEntity.getStockinOrderId(), item.getSku(), item.getInternalBoxCode());
            if (CollectionUtils.isEmpty(itemEntityList)) {
                updateList.add(stockinOrderTaskEntity.getSupplierDeliveryNo());
                return;
            }
            //判断是否还存在其他的质检箱,校验质检结果
            List<StockinOrderItemBean> otherQaBox = itemEntityList.stream().filter(orderItem -> StockInternalBoxTypeEnum.QA_BOX.name().equals(orderItem.getInternalBoxType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(otherQaBox)) {
                List<String> otherQaBoxCode = otherQaBox.stream().map(StockinOrderItemBean::getInternalBoxCode).distinct().collect(Collectors.toList());
                for (String boxCode : otherQaBoxCode) {
                    List<StockinQcInboundsEntity> result = stockinQcInboundsService.getBaseMapper().findResult(Collections.singletonList(boxCode), stockinOrderEntity.getSupplierDeliveryBoxCode(), item.getSku());
                    if (CollectionUtils.isEmpty(result) || !QcInboundsResultStatusEnum.FACTORY_REWORK.getDesc().equals(result.get(0).getResult())) {
                        noNeedList.add(stockinOrderTaskEntity.getSupplierDeliveryNo());
                        return;
                    }
                }
            }
            updateList.add(stockinOrderTaskEntity.getSupplierDeliveryNo());
        });
        List<String> needUpdateList = updateList.stream().filter(str1 -> !noNeedList.stream().anyMatch(str2 -> str2.equals(str1))).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needUpdateList)) {
            //更新任务明细
            List<StockinOrderTaskEntity> stockinOrderTaskEntityList = stockinOrderTaskService.findAllBySupplierDeliveryNoList(needUpdateList);
            stockinOrderTaskEntityList.forEach(taskEntity -> {
                List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(taskEntity.getTaskId(), item.getSku());
                if (CollectionUtils.isEmpty(taskItemEntityList)) {
                    return;
                }
                List<StockinOrderTaskItemEntity> collect = taskItemEntityList.stream().map(taskItem -> {
                    StockinOrderTaskItemEntity taskItemEntity = new StockinOrderTaskItemEntity();
                    taskItemEntity.setTaskItemId(taskItem.getTaskItemId());
                    taskItemEntity.setIsPreQaReturn(1);
                    taskItemEntity.setUpdateBy(loginInfoService.getName());
                    return taskItemEntity;
                }).collect(Collectors.toList());
                stockinOrderTaskItemService.updateBatchById(collect);
                //去更新质检任务
                //查询入库任务明细的入库单明细(仅查询收货箱)
                List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.getBaseMapper().findAllByTaskItemId(taskItemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskItemId).collect(Collectors.toList()), StockInternalBoxTypeEnum.RECEIVE_BOX.name());
                if (CollectionUtils.isEmpty(stockinOrderItemEntityList)) return;
                //更新入库单明细为待退货
                StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(taskEntity.getTaskId());
                this.updateOrderItemStatus(stockinOrderEntity, stockinOrderItemEntityList, StockinOrderItemStatusEnum.WAIT_RETURN.name());
                this.updateQcTask(stockinOrderEntity, stockinOrderItemEntityList, Boolean.FALSE);
                stockinOrderItemEntityList.stream().filter(orderItemEntity -> orderItemEntity.getQty() > 0).map(StockinOrderItemEntity::getInternalBoxCode).distinct().forEach(internalBoxCode -> stockInternalBoxService.updateInternalBoxStatusByItem(internalBoxCode));
            });
        }
    }

    public void notifyWaitDeal(List<StockinOrderEntity> stockinOrderEntityList, String sku) {
        List<String> needUpdateList = stockinOrderTaskService.listByIds(stockinOrderEntityList.stream().map(StockinOrderEntity::getTaskId).collect(Collectors.toList()))
                .stream().map(StockinOrderTaskEntity::getSupplierDeliveryNo).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needUpdateList)) {
            //更新任务明细
            List<StockinOrderTaskEntity> stockinOrderTaskEntityList = stockinOrderTaskService.findAllBySupplierDeliveryNoList(needUpdateList);
            stockinOrderTaskEntityList.forEach(taskEntity -> {
                List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(taskEntity.getTaskId(), sku);
                if (CollectionUtils.isEmpty(taskItemEntityList)) {
                    return;
                }
                List<StockinOrderTaskItemEntity> collect = taskItemEntityList.stream().map(taskItem -> {
                    StockinOrderTaskItemEntity taskItemEntity = new StockinOrderTaskItemEntity();
                    taskItemEntity.setTaskItemId(taskItem.getTaskItemId());
                    taskItemEntity.setIsPreQaQualified(2);
                    taskItemEntity.setUpdateBy(loginInfoService.getName());
                    return taskItemEntity;
                }).collect(Collectors.toList());
                stockinOrderTaskItemService.updateBatchById(collect);
                //去更新质检任务
                //查询入库任务明细的入库单明细(仅查询收货箱)
                List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.getBaseMapper().findAllByTaskItemId(taskItemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskItemId).collect(Collectors.toList()), StockInternalBoxTypeEnum.RECEIVE_BOX.name())
                        .stream().filter(item -> StockinOrderItemStatusEnum.WAIT_QC.name().equals(item.getStatus())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(stockinOrderItemEntityList)) return;
                //更新入库单明细为待退货
                StockinOrderEntity stockinOrderEntity = stockinOrderService.findTopByTaskId(taskEntity.getTaskId());
                this.updateOrderItemStatus(stockinOrderEntity, stockinOrderItemEntityList, StockinOrderItemStatusEnum.WAIT_DEAL.name());
                this.updateQcTask(stockinOrderEntity, stockinOrderItemEntityList, Boolean.FALSE);
                stockinOrderItemEntityList.stream().filter(orderItemEntity -> orderItemEntity.getQty() > 0).map(StockinOrderItemEntity::getInternalBoxCode).distinct().forEach(internalBoxCode -> stockInternalBoxService.updateInternalBoxStatusByItem(internalBoxCode));
            });
        }
    }

    @Transactional
    public void allCheck(QcInboundsMessage request) {
        String internalBoxCode = request.getBoxBarcode();
        LambdaQueryWrapper<StockInternalBoxEntity> queryWrapper = StockInternalBoxQueryWrapper.buildWrapperByInternalBoxCode(internalBoxCode);
        StockInternalBoxEntity boxEntity = stockInternalBoxService.getOne(queryWrapper);
        if (!StockInternalBoxTypeEnum.QA_BOX.name().equals(boxEntity.getInternalBoxType()) && !StockInternalBoxTypeEnum.RECEIVE_BOX.name().equals(boxEntity.getInternalBoxType()))
            throw new BusinessServiceException("该箱子不是质检箱或者收货箱！");

        LambdaQueryWrapper<StockInternalBoxItemEntity> taskItemQueryWrapper = new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .eq(StockInternalBoxItemEntity::getInternalBoxCode, internalBoxCode)
                .eq(StockInternalBoxItemEntity::getSku, request.getSku())
                .isNotNull(StockInternalBoxItemEntity::getStockInOrderNo);
        List<StockInternalBoxItemEntity> stockInternalBoxItemEntityList = stockInternalBoxItemService.list(taskItemQueryWrapper);
        if (CollectionUtils.isEmpty(stockInternalBoxItemEntityList))
            return;
        List<StockinOrderEntity> stockinOrderEntityList = stockinOrderService.getByStockinOrderNoList(stockInternalBoxItemEntityList.stream()
                .map(StockInternalBoxItemEntity::getStockInOrderNo).collect(Collectors.toList()));
        List<String> needUpdateList = stockinOrderTaskService.listByIds(stockinOrderEntityList.stream().map(StockinOrderEntity::getTaskId).collect(Collectors.toList()))
                .stream().map(StockinOrderTaskEntity::getSupplierDeliveryNo).collect(Collectors.toList());

        //更新任务明细
        List<StockinOrderTaskEntity> stockinOrderTaskEntityList = stockinOrderTaskService.findAllBySupplierDeliveryNoList(needUpdateList);
        // 质检箱
        if (StockInternalBoxTypeEnum.QA_BOX.name().equals(boxEntity.getInternalBoxType())) {
            // 质检箱
            this.updateQaBox(stockinOrderTaskEntityList, internalBoxCode, request);
        } else if (StockInternalBoxTypeEnum.RECEIVE_BOX.name().equals(boxEntity.getInternalBoxType())) {
            // 收货箱
            this.updateReceiveBox(stockinOrderTaskEntityList, internalBoxCode, request);
        }


    }

    private void updateQaBox(List<StockinOrderTaskEntity> stockinOrderTaskEntityList, String internalBoxCode, QcInboundsMessage request) {
        stockinOrderTaskEntityList.forEach(taskEntity -> {
            List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(taskEntity.getTaskId(), request.getSku());
            if (CollectionUtils.isEmpty(taskItemEntityList)) {
                return;
            }
            List<StockinOrderTaskItemEntity> collect = taskItemEntityList.stream().map(taskItem -> {
                StockinOrderTaskItemEntity taskItemEntity = new StockinOrderTaskItemEntity();
                taskItemEntity.setTaskItemId(taskItem.getTaskItemId());
                taskItemEntity.setIsPreQaQualified(3);
                taskItemEntity.setUpdateBy(loginInfoService.getName());
                return taskItemEntity;
            }).collect(Collectors.toList());
            stockinOrderTaskItemService.updateBatchById(collect);
            StockinOrderEntity stockinOrderEntity = stockinOrderService.getByTaskId(taskEntity.getTaskId());
            if (Objects.nonNull(stockinOrderEntity)) {
                String content = String.format("质检箱号【%s】，SKU【%s】，要求全检", internalBoxCode, request.getSku());
                stockinOrderLogService.addLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.QC_PROCESSING.getName(), content);
                List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.getBaseMapper().findAllByTaskItemId(taskItemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskItemId).collect(Collectors.toList()), StockInternalBoxTypeEnum.RECEIVE_BOX.name())
                        .stream().filter(item -> StockinOrderItemStatusEnum.WAIT_DEAL.name().equals(item.getStatus())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(stockinOrderItemEntityList)) return;
                //更新入库单明细为待质检
                this.updateOrderItemStatus(stockinOrderEntity, stockinOrderItemEntityList, StockinOrderItemStatusEnum.WAIT_QC.name());
                //去更新质检任务
                this.updateQcTask(stockinOrderEntity, stockinOrderItemEntityList, Boolean.TRUE);
            }
        });
    }

    private void updateReceiveBox(List<StockinOrderTaskEntity> stockinOrderTaskEntityList, String internalBoxCode, QcInboundsMessage request) {
        stockinOrderTaskEntityList.forEach(taskEntity -> {
            List<StockinOrderTaskItemEntity> taskItemEntityList = stockinOrderTaskItemService.findAllByTaskIdAndSku(taskEntity.getTaskId(), request.getSku());
            if (CollectionUtils.isEmpty(taskItemEntityList)) {
                return;
            }
            StockinOrderEntity stockinOrderEntity = stockinOrderService.getByTaskId(taskEntity.getTaskId());
            if (Objects.nonNull(stockinOrderEntity)) {
                String content = String.format("收货箱号【%s】，SKU【%s】，要求全检", internalBoxCode, request.getSku());
                stockinOrderLogService.addLog(stockinOrderEntity.getStockinOrderId(), StockinOrderLogTypeEnum.QC_PROCESSING.getName(), content);

                List<StockinOrderItemEntity> stockinOrderItemEntityList = stockinOrderItemService.getBaseMapper().findAllByTaskItemIdAndBox(taskItemEntityList.stream().map(StockinOrderTaskItemEntity::getTaskItemId).collect(Collectors.toList()), internalBoxCode, request.getSku());
                if (CollectionUtils.isEmpty(stockinOrderItemEntityList)) return;
                //更新入库单明细为待处理
                this.updateOrderItemStatus(stockinOrderEntity, stockinOrderItemEntityList, StockinOrderItemStatusEnum.WAIT_DEAL.name());
                //去更新质检任务
                //this.updateQcTask(stockinOrderEntity, stockinOrderItemEntityList, Boolean.TRUE);
            }
        });
    }
}
