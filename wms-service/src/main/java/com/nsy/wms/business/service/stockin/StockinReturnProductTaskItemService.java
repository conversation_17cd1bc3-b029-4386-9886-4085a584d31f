package com.nsy.wms.business.service.stockin;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.stockin.StockinReturnProductItemModel;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiInfoEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnNatureEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnTypeEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockin.LxAdjustmentOrderRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductTaskListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.lx.LxAdjustmentOrderDetailInfo;
import com.nsy.api.wms.response.lx.LxAdjustmentOrderResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskListItemResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskListResponse;
import com.nsy.business.base.dto.request.lingxing.LxMappingQueryRequest;
import com.nsy.business.base.dto.request.lingxing.LxMappingRequest;
import com.nsy.business.base.dto.request.lingxing.LxMappingSaveRequest;
import com.nsy.business.base.dto.response.lingxing.LxMappingResponse;
import com.nsy.business.base.enums.etl.BusinessTypeEnum;
import com.nsy.wms.business.manage.etl.EtlApiService;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.scm.request.SupplierDto;
import com.nsy.wms.business.manage.thirdparty.ThirdPartyApiService;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.bd.BdAreaService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSupplierPositionMappingService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSupplierPositionMappingEntity;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductTaskMapper;
import com.nsy.wms.utils.BarCodeUtils;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.FreeMarkerTemplateUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import freemarker.template.Template;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class StockinReturnProductTaskItemService extends ServiceImpl<StockinReturnProductTaskItemMapper, StockinReturnProductTaskItemEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockinReturnProductTaskItemService.class);

    @Autowired
    private StockinReturnProductTaskService taskService;
    @Autowired
    private StockinReturnProductTaskMapper taskMapper;
    @Autowired
    private EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    private StockinReturnProductTaskPdaService stockinReturnProductTaskPdaService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private ScmApiService scmApiService;
    @Autowired
    private BdSupplierPositionMappingService supplierPositionMappingService;
    @Autowired
    private StockinReturnProductTaskScanService stockinReturnProductTaskScanService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private EtlApiService etlApiService;
    @Autowired
    private BdPositionService positionService;
    @Autowired
    private BdAreaService areaService;
    @Autowired
    private ThirdPartyApiService thirdPartyApiService;
    @Autowired
    private ExternalApiLogService externalApiLogService;

    public PrintListResponse printDetailList(IdListRequest request) {
        List<StockinReturnProductTaskEntity> taskEntities = taskService.listByIds(request.getIdList());
        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());
        response.setTemplateName("退货任务明细");
        response.setSpec("210*297");
        Template template = FreeMarkerTemplateUtils.getTemplate("StockInReturnProductTaskDetail.ftl");
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> taskList = taskEntities.stream().map(task -> {
            Map<String, Object> taskMap = new HashMap<>();
            taskMap.put("taskId", BarCodeUtils.getBarCodeBase64(task.getReturnProductTaskId().toString()));
            taskMap.put("returnProductTaskIdStr", task.getReturnProductTaskId().toString());
            taskMap.put("supplierName", task.getSupplierName());
            taskMap.put("returnNature", Arrays.stream(StockinReturnNatureEnum.values()).filter(s -> s.name().equals(task.getReturnNature()))
                    .findAny().map(StockinReturnNatureEnum::getName).orElse(""));
            taskMap.put("nowDate", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            taskMap.put("logisticsNo", task.getLogisticsNo());
            taskMap.put("logisticsCompany", task.getLogisticsCompany());
            if (task.getCreateDate() != null) {
                taskMap.put("createDate", DateFormatUtils.format(task.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            taskMap.put("printBy", loginInfoService.getName());
            if (task.getDeliveryDate() != null) {
                taskMap.put("deliveryDateStr", DateFormatUtils.format(task.getDeliveryDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (task.getShippingAddress() != null) {
                taskMap.put("shippingAddress", task.getShippingAddress());
            }
            List<StockinReturnProductTaskItemEntity> itemList = listByTaskId(task.getReturnProductTaskId());
            if (CollectionUtils.isEmpty(itemList)) {
                throw new BusinessServiceException(task.getReturnProductTaskId() + "此任务没有明细，请选择有退货明细的进行打印");
            }
            AtomicInteger total = new AtomicInteger();
            List<Map<String, Object>> itemMapList = itemList.stream().map(item -> {
                Map<String, Object> itemMap = new HashMap<>();
                itemMap.put("sku", item.getSku());
                itemMap.put("actualReturnQty", item.getActualReturnQty());
                itemMap.put("sourceAreaName", areaService.findAreaName(item.getSourceAreaId()));
                itemMap.put("packingMethod", item.getPackingMethod());
                itemMap.put("versionNo", item.getVersionNo());
                itemMap.put("purchasePlanNo", item.getPurchasePlanNo());
                itemMap.put("unqualifiedReason", item.getUnqualifiedReason());
                total.addAndGet(item.getActualReturnQty());
                return itemMap;
            }).collect(Collectors.toList());
            taskMap.put("itemList", itemMapList);
            taskMap.put("totalReturnQty", total.get());
            return taskMap;
        }).collect(Collectors.toList());
        map.put("taskList", taskList);

        response.getHtmlList().add(FreeMarkerTemplateUtils.renderTemplate(template, map));
        return response;
    }

    public PageResponse<StockinReturnProductTaskListItemResponse> getExportTaskListItem(StockinReturnProductTaskListRequest request) {
        Page<StockinReturnProductTaskListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinReturnProductTaskListItemResponse> pageList = taskMapper.getExportTaskListItem(page, request);
        PageResponse<StockinReturnProductTaskListItemResponse> pageResponse = new PageResponse<>();
        Map<String, String> stockinSupplyReturnStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_SUPPLY_RETURN_STATUS.getName());
        Map<String, String> stockinReturnNatureEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_RETURN_NATURE.getName());
        Map<String, String> freightCarrierEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_FREIGHT_CARRIER.getName());
        Map<String, String> handelMethodEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_RETURN_HANDEL_METHOD.getName());
        pageList.getRecords().forEach(t -> {
            t.setStatus(stockinSupplyReturnStatusEnumMap.get(t.getStatus()));
            t.setReturnNature(stockinReturnNatureEnumMap.get(t.getReturnNature()));
            t.setFreightCarrierStr(ObjectUtils.isEmpty(t.getFreightCarrier()) ? "" : freightCarrierEnumMap.get(String.valueOf(t.getFreightCarrier())));
            t.setHandleMethodStr(ObjectUtils.isEmpty(t.getHandleMethod()) ? "" : handelMethodEnumMap.get(String.valueOf(t.getHandleMethod())));
        });
        pageResponse.setContent(pageList.getRecords());
        pageResponse.setTotalCount(pageList.getTotal());
        return pageResponse;
    }

    public void syncExternalReturnOrder(List<String> orderList) {
        if (CollUtil.isEmpty(orderList)) {
            throw new BusinessServiceException("请填写需要同步的外部订单号");
        }
        LxAdjustmentOrderRequest request = new LxAdjustmentOrderRequest();
        request.setLocation(TenantContext.getTenant());
        request.setAdjustStatus(20);
        request.setType(0);
        request.setOrderSn(orderList.stream().collect(Collectors.joining(",")));
        PageResponse<LxAdjustmentOrderResponse> adjustmentOrderList = thirdPartyApiService.getAdjustmentOrderList(request);
        if (ObjectUtils.isEmpty(adjustmentOrderList) || CollUtil.isEmpty(adjustmentOrderList.getContent())) {
            throw new BusinessServiceException("查询不到相关的外部订单数据");
        }
        for (LxAdjustmentOrderResponse detail : adjustmentOrderList.getContent()) {
            this.dealAdjustmentOrderInfo(detail);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void dealAdjustmentOrderInfo(LxAdjustmentOrderResponse adjustmentOrder) {
        if (!this.validOrderSync(adjustmentOrder.getOrderSn())) {
            //已处理过则不在重复处理
            return;
        }
        if (CollUtil.isEmpty(adjustmentOrder.getItemList()) || !adjustmentOrder.getItemList().stream().map(LxAdjustmentOrderDetailInfo::getAdjustmentValidNum).filter(qty -> qty < 0).findAny().isPresent()) {
            //明细信息为空或调出数量为增加的不做处理
            return;
        }
        ExternalApiLogEntity externalApiLogEntity = this.findRecords(adjustmentOrder);
        try {
            SysUserInfo sysUserInfo = this.transferLxUserInfo(adjustmentOrder.getAdjustmentUid(), adjustmentOrder.getAdjustmentRealname(), adjustmentOrder.getOrderSn());
            //判断是否已经同步过数据，已同步过则不做处理
            if (!StringUtils.hasText(adjustmentOrder.getRemark())) {
                throw new BusinessServiceException(String.format("%s请填写对应的时颖供应商", adjustmentOrder.getOrderSn()));
            }
            SupplierDto supplierDto = scmApiService.querySupplierByCode(adjustmentOrder.getRemark());
            if (ObjectUtils.isEmpty(supplierDto))
                throw new BusinessServiceException(String.format("%s时颖erp不存在该供应商:%s请确认", adjustmentOrder.getOrderSn(), adjustmentOrder.getRemark()));
            List<String> skuList = adjustmentOrder.getItemList().stream().map(LxAdjustmentOrderDetailInfo::getSku).distinct().collect(Collectors.toList());
            Map<String, ProductSpecInfoEntity> productMap = productSpecInfoService.findAllBySkuIn(skuList).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));
            List<String> noExistSku = skuList.stream().filter(detail -> !productMap.containsKey(detail)).collect(Collectors.toList());
            if (!CollUtil.isEmpty(noExistSku))
                throw new BusinessServiceException(String.format("单号%s,sku:%s,时颖erp不存在该供应商请确认", adjustmentOrder.getOrderSn(), noExistSku.stream().collect(Collectors.joining(","))));
            BdSupplierPositionMappingEntity supplierPositionMappingEntity = supplierPositionMappingService.getSupplierPositionMappingEntity(supplierDto.getSupplierId(), StockinReturnNatureEnum.REFUND_RETURN.name(), null);
            //1. 创建退货任务，记录日志
            LoginInfoService.setName(adjustmentOrder.getAdjustmentRealname());
            StockinReturnProductTaskEntity task = stockinReturnProductTaskPdaService.saveReturnTaskEntity(this.buildReturnProductTaskInfo(adjustmentOrder, supplierDto, sysUserInfo, supplierPositionMappingEntity));
            Map<String, List<LxAdjustmentOrderDetailInfo>> itemMap = adjustmentOrder.getItemList().stream().filter(detail -> !ObjectUtils.isEmpty(adjustmentOrder.getAdjustmentRealname()) && detail.getAdjustmentValidNum() < 0)
                    .collect(Collectors.groupingBy(LxAdjustmentOrderDetailInfo::getSku));
            for (Map.Entry<String, List<LxAdjustmentOrderDetailInfo>> entry : itemMap.entrySet()) {
                int returnCount = Math.abs(entry.getValue().stream().mapToInt(LxAdjustmentOrderDetailInfo::getAdjustmentValidNum).filter(qty -> qty < 0).sum());
                StockinReturnProductItemModel itemModel = new StockinReturnProductItemModel();
                itemModel.setProductId(productMap.get(entry.getKey()).getProductId());
                itemModel.setBarcode(productMap.get(entry.getKey()).getBarcode());
                itemModel.setSpecId(productMap.get(entry.getKey()).getSpecId());
                itemModel.setSku(entry.getKey());
                itemModel.setPositionId(supplierPositionMappingEntity.getPositionId());
                itemModel.setPositionCode(supplierPositionMappingEntity.getPositionCode());
                stockinReturnProductTaskScanService.saveReturnTaskItemEntity(itemModel, returnCount, task);
            }
            stockinReturnProductTaskScanService.checkAndChangeTaskStatus(task);
            this.saveAdjustmentOrderMapping(adjustmentOrder.getOrderSn(), task.getReturnProductTaskId());
            externalApiLogService.updateLog(externalApiLogEntity, null, ExternalApiLogStatusEnum.SUCCESS);
        } catch (BusinessServiceException e) {
            LOGGER.error(e.getMessage(), e);
            LoginInfoService.removeName();
            externalApiLogService.updateLog(externalApiLogEntity, e.getMessage(), ExternalApiLogStatusEnum.FAIL);
        }
    }

    private Integer transferLxSpaceId(Integer wid, String warehouseName, String orderNo) {
        if (ObjectUtils.isEmpty(wid)) {
            throw new BusinessServiceException(String.format("订单%s仓库不存在", orderNo));
        }
        LxMappingQueryRequest lxMappingQueryRequest = new LxMappingQueryRequest();
        lxMappingQueryRequest.setExtendNoList(Collections.singletonList(String.valueOf(wid)));
        lxMappingQueryRequest.setBusinessType(BusinessTypeEnum.WMS_LX_SYNC_SPACE.getCode());
        String result = etlApiService.getBusinessNoList(lxMappingQueryRequest);
        List<LxMappingResponse> spaceList = JSONUtils.fromJSONArray(result, LxMappingResponse.class);
        if (CollUtil.isEmpty(spaceList)) {
            throw new BusinessServiceException(String.format("订单%s仓库%s不存在", orderNo, warehouseName));
        }
        return Integer.valueOf(spaceList.get(0).getBusinessNo());
    }

    private StockinReturnProductItemModel buildReturnProductTaskInfo(LxAdjustmentOrderResponse adjustmentOrder, SupplierDto supplierDto, SysUserInfo sysUserInfo, BdSupplierPositionMappingEntity supplierPositionMappingEntity) {
        BdPositionEntity positionInfo = positionService.getPositionByCode(supplierPositionMappingEntity.getPositionCode());
        StockinReturnProductItemModel stockinReturnProductItemModel = new StockinReturnProductItemModel();
        stockinReturnProductItemModel.setSpaceId(this.transferLxSpaceId(adjustmentOrder.getWid(), adjustmentOrder.getWarehouseName(), adjustmentOrder.getOrderSn()));
        stockinReturnProductItemModel.setSpaceName(adjustmentOrder.getWarehouseName());
        stockinReturnProductItemModel.setSupplierId(supplierDto.getSupplierId());
        stockinReturnProductItemModel.setSupplierName(supplierDto.getSupplierName());
        stockinReturnProductItemModel.setPositionId(supplierPositionMappingEntity.getPositionId());
        stockinReturnProductItemModel.setPositionCode(supplierPositionMappingEntity.getPositionCode());
        stockinReturnProductItemModel.setSpaceAreaId(positionInfo.getAreaId());
        stockinReturnProductItemModel.setSpaceAreaName(positionInfo.getAreaName());
        stockinReturnProductItemModel.setReturnType(StockinReturnTypeEnum.FACTORY_RETURN.name());
        stockinReturnProductItemModel.setReturnNature(StockinReturnNatureEnum.REFUND_RETURN.name());
        stockinReturnProductItemModel.setCreateBy(sysUserInfo.getUserName());
        return stockinReturnProductItemModel;
    }

    /**
     * 校验订单是否同步
     *
     * @return
     */
    private Boolean validOrderSync(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessServiceException("订单不存在!");
        }
        LxMappingQueryRequest lxMappingQueryRequest = new LxMappingQueryRequest();
        lxMappingQueryRequest.setExtendNoList(Collections.singletonList(orderNo));
        lxMappingQueryRequest.setBusinessType(BusinessTypeEnum.WMS_LX_ADJUSTMENT_ORDER_SYNC.getCode());
        String result = etlApiService.getBusinessNoList(lxMappingQueryRequest);
        List<LxMappingResponse> spaceList = JSONUtils.fromJSONArray(result, LxMappingResponse.class);
        if (CollUtil.isEmpty(spaceList)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private void saveAdjustmentOrderMapping(String orderNo, Integer returnProductTaskId) {
        LxMappingSaveRequest lxMappingSaveRequest = new LxMappingSaveRequest();
        LxMappingRequest lxMappingRequest = new LxMappingRequest();
        lxMappingRequest.setBusinessNo(returnProductTaskId.toString());
        lxMappingRequest.setExternalNo(orderNo);
        lxMappingRequest.setLocation(TenantContext.getTenant());
        lxMappingRequest.setBusinessType(BusinessTypeEnum.WMS_LX_ADJUSTMENT_ORDER_SYNC.getCode());
        lxMappingSaveRequest.setMappingList(Collections.singletonList(lxMappingRequest));
        etlApiService.saveMapping(lxMappingSaveRequest);
    }

    private SysUserInfo transferLxUserInfo(Integer adjustmentId, String adjustmentName, String orderNo) {
        LxMappingQueryRequest lxMappingQueryRequest = new LxMappingQueryRequest();
        lxMappingQueryRequest.setExtendNoList(Collections.singletonList(String.valueOf(adjustmentId)));
        lxMappingQueryRequest.setBusinessType(BusinessTypeEnum.USER_LX_INFO_MAPPING.getCode());
        String result = etlApiService.getBusinessNoList(lxMappingQueryRequest);
        List<LxMappingResponse> userInfoList = JSONUtils.fromJSONArray(result, LxMappingResponse.class);
        if (CollUtil.isEmpty(userInfoList)) {
            throw new BusinessServiceException(String.format("订单%s调整人:%s，在时颖erp不存在!", orderNo, adjustmentName));
        }
        SysUserInfo userInfo = userApiService.getUserInfoByUserId(Integer.valueOf(userInfoList.get(0).getBusinessNo()));
        if (ObjectUtils.isEmpty(userInfo)) {
            throw new BusinessServiceException(String.format("订单%s调整人:%s，在时颖erp不存在!", orderNo, adjustmentName));
        }
        return userInfo;
    }

    /**
     * 5分钟调用一次接口如果，调用失败则获取之前的保存数据更新信息，避免处理不及时生成太多失败记录
     *
     * @param adjustmentOrder
     * @return
     */
    public ExternalApiLogEntity findRecords(LxAdjustmentOrderResponse adjustmentOrder) {
        ExternalApiLogEntity apiLogInfo = externalApiLogService.getExternalApiLogInfo(ExternalApiInfoEnum.LX_SYNC_RETURN_ORDER.getApiType().name(), adjustmentOrder.getOrderSn());
        if (ObjectUtils.isEmpty(apiLogInfo)) {
            apiLogInfo = externalApiLogService.recordBaseLog(ExternalApiInfoEnum.LX_SYNC_RETURN_ORDER, "/return-product-task/sync/external/order",
                    JsonMapper.toJson(adjustmentOrder), adjustmentOrder.getOrderSn(), "同步领星调整单");
        }
        return apiLogInfo;
    }

    public List<StockinReturnProductTaskItemEntity> listByTaskId(Integer taskId) {
        LambdaQueryWrapper<StockinReturnProductTaskItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockinReturnProductTaskItemEntity::getReturnProductTaskId, taskId);
        return list(wrapper);
    }
}




