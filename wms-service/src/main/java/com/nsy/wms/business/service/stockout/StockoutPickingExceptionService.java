package com.nsy.wms.business.service.stockout;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemOrder;
import com.nsy.api.wms.domain.stock.StockInternalBoxItemSourcePositionBo;
import com.nsy.api.wms.domain.stockout.StockoutClearPickingBoxItem;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockInternalBoxTypeEnum;
import com.nsy.api.wms.enumeration.stock.TakeStockPlanTypeEnum;
import com.nsy.api.wms.enumeration.stock.TakeStockTaskModeEnum;
import com.nsy.api.wms.enumeration.stockout.StockTakeStockTaskStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutPickingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWavePlanTypeEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockin.InternalBoxExceptionRequest;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockInternalBoxItemService;
import com.nsy.wms.business.service.stock.StockInternalBoxService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stock.StockPrematchRemoveService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stock.StockTakeStockLogService;
import com.nsy.wms.business.service.stock.StockTakeStockTaskItemService;
import com.nsy.wms.business.service.stock.StockTakeStockTaskService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxEntity;
import com.nsy.wms.repository.entity.stock.StockInternalBoxItemEntity;
import com.nsy.wms.repository.entity.stock.StockPrematchInfoEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderScanTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutPickingTaskEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.WmsDateUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 拣货异常调平
 * 1.发货数>拣货数时，需要扣除原库位，并触发盘点任务
 * 2.发货数<拣货数，需要将拣货箱中的数量还给原库位，并触发盘点
 * @author: caishaohui
 * @time: 2023/5/18 16:06
 */
@Service
public class StockoutPickingExceptionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutPickingExceptionService.class);

    @Autowired
    StockPrematchInfoService stockPrematchInfoService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockService stockService;
    @Autowired
    StockInternalBoxService stockInternalBoxService;
    @Autowired
    StockInternalBoxItemService stockInternalBoxItemService;
    @Autowired
    StockoutPickingTaskService pickingTaskService;
    @Autowired
    StockTakeStockTaskService stockTakeStockTaskService;
    @Autowired
    StockTakeStockTaskItemService stockTakeStockTaskItemService;
    @Autowired
    BdPositionService bdPositionService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    StockTakeStockLogService logService;
    @Autowired
    MessageProducer messageProducer;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutOrderLackService stockoutOrderLackService;
    @Autowired
    StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Autowired
    StockoutOrderScanTaskService scanTaskService;
    @Autowired
    StockoutOrderScanTaskItemService taskItemService;
    @Autowired
    StockPrematchRemoveService prematchRemoveService;
    @Resource
    StockoutOrderService stockoutOrderService;
    @Resource
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutQcInboundsDefectivesService stockoutQcInboundsDefectivesService;
    @Autowired
    StockoutQcInboundsService stockoutQcInboundsService;
    @Autowired
    BdSystemParameterService bdSystemParameterService;

    /**
     * 发货数>拣货数时，需要扣除原库位，并触发盘点任务
     * 1.存在预配  按照预配数量去扣除原库位
     * 2.取拣货任务缺货数，以及未完成数去扣除原库位
     * 3.取出库质检次品数去扣除原库位
     *
     * @param batchId 波次ID
     * @param specId  规格编码ID
     * @param qty     扣减数量
     */
    @Transactional
    public List<StockInternalBoxItemSourcePositionBo> minusSourcePositionStock(Integer batchId, Integer stockoutOrderId, Integer specId, int qty, Integer stockoutOrderItemId) {
        StockoutBatchEntity batchEntity = stockoutBatchService.getById(batchId);
        String stockoutOrderNo = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>()
                .select(StockoutOrderEntity::getStockoutOrderNo)
                .eq(StockoutOrderEntity::getStockoutOrderId, stockoutOrderId)).getStockoutOrderNo();
        ProductSpecInfoEntity productSpecInfoEntity = productSpecInfoService.findTopBySpecId(specId);
        LOGGER.info("发货数>拣货数时，需要扣除原库位 : batchId: {} ，stockoutOrderNo: {} ，sku: {} ，qty: {} ，stockoutOrderItemId: {} ", batchId, stockoutOrderNo, productSpecInfoEntity.getSku(), qty, stockoutOrderItemId);
        StockChangeLogTypeEnum changeLogTypeEnum = batchEntity.getPickingType().equals(StockoutPickingTypeEnum.WHOLE_PICK.name()) ? StockChangeLogTypeEnum.SCAN_LEVELLING : StockChangeLogTypeEnum.SPLIT_EXCEPTION;

        // 3.去出库单明细的原库位扣除
        return this.minusSourceStockoutOrderItem(batchId, productSpecInfoEntity.getSku(), qty, changeLogTypeEnum, stockoutOrderItemId, stockoutOrderNo);

    }

    private List<StockInternalBoxItemSourcePositionBo> minusSourceStockoutOrderItem(Integer batchId, String sku, int qty, StockChangeLogTypeEnum changeLogTypeEnum, Integer stockoutOrderItemId, String stockoutOrderNo) {
        List<StockInternalBoxItemSourcePositionBo> result = new LinkedList<>();
        LOGGER.info("发货数>拣货数时，需要扣除原库位 : stockoutOrderItemId: {} ", stockoutOrderItemId);

        if (Objects.isNull(stockoutOrderItemId))
            return result;

        StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.getById(stockoutOrderItemId);
        if (Objects.isNull(stockoutOrderItemEntity))
            return result;

        //如果存在预配 需要扣除
        List<StockPrematchInfoEntity> list = stockPrematchInfoService.findByStockoutOrderIdAndSpecIdAndPositionCode(stockoutOrderItemEntity.getStockoutOrderId(), stockoutOrderItemEntity.getSpecId(), stockoutOrderItemEntity.getPositionCode());
        if (!CollectionUtils.isEmpty(list))
            prematchRemoveService.minusPrematch(list, qty);

        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setBatchId(batchId);
        stockUpdateRequest.setPositionCode(stockoutOrderItemEntity.getPositionCode());
        stockUpdateRequest.setChangeLogType(changeLogTypeEnum);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_OUT);
        stockUpdateRequest.setQty(-qty);
        stockUpdateRequest.setSku(sku);
        stockUpdateRequest.setContent(String.format("拣货箱商品不足，需要扣除原始库位【%s】%s件", stockoutOrderItemEntity.getPositionCode(), qty));
        stockUpdateRequest.setStockoutOrderNo(stockoutOrderNo);
        List<StockUpdateRequest> stockUpdateRequestList = Lists.newArrayList(stockUpdateRequest);
        result = buildSourcePositionBo(stockUpdateRequestList);
        stockService.updateStock(stockUpdateRequest);
        //触发盘点任务
        messageProducer.sendMessage(KafkaConstant.STOCKOUT_PICKING_EXCEPTION_TAKE_TASK_TOPIC_NAME, KafkaConstant.STOCKOUT_PICKING_EXCEPTION_TAKE_TASK_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), stockUpdateRequestList));
        return result;
    }


    //构建来源
    private List<StockInternalBoxItemSourcePositionBo> buildSourcePositionBo(List<StockUpdateRequest> stockUpdateRequestList) {
        if (CollectionUtils.isEmpty(stockUpdateRequestList))
            return Collections.emptyList();
        return stockUpdateRequestList.stream().map(item -> {
            BdPositionEntity positionByCode = bdPositionService.getPositionByCode(item.getPositionCode());
            return new StockInternalBoxItemSourcePositionBo(positionByCode.getAreaId(), positionByCode.getPositionCode(), item.getSku(), Math.abs(item.getQty()), null);
        }).collect(Collectors.toList());
    }

    /**
     * 拣货数>分拣数，分拣缺货时，需要扣减拣货箱，新增原库位，并触发盘点任务
     * 根据库位分组，不同SKU要分别调用
     *
     * @param sourcePositionBoList 拣货箱变动记录
     * @param batchId
     * @param stockoutOrderNo
     */
    @Transactional
    public void addSourcePositionStock(List<StockInternalBoxItemSourcePositionBo> sourcePositionBoList, Integer batchId, StockChangeLogTypeEnum changeLogTypeEnum, String stockoutOrderNo) {
        if (CollectionUtils.isEmpty(sourcePositionBoList))
            return;
        LOGGER.info("分拣、复核异常，需要新增原库位数量 : {} ", JsonMapper.toJson(sourcePositionBoList));
        Map<String, List<StockInternalBoxItemSourcePositionBo>> minusSourcePositionQtyMap = sourcePositionBoList.stream().collect(Collectors.groupingBy(StockInternalBoxItemSourcePositionBo::getSourcePositionCode));
        List<StockUpdateRequest> stockUpdateRequestList = new ArrayList<>();
        minusSourcePositionQtyMap.forEach((sourcePositionCode, boList) -> {
            stockUpdateRequestList.add(buildByStockInternalBoxItemSourcePositionBo(sourcePositionCode, batchId, boList, changeLogTypeEnum, stockoutOrderNo));
        });
        if (!CollectionUtils.isEmpty(stockUpdateRequestList)) {
            stockUpdateRequestList.stream().forEach(stockUpdateRequest -> stockService.updateStock(stockUpdateRequest));
            //触发盘点任务
            messageProducer.sendMessage(KafkaConstant.STOCKOUT_PICKING_EXCEPTION_TAKE_TASK_TOPIC_NAME, KafkaConstant.STOCKOUT_PICKING_EXCEPTION_TAKE_TASK_TOPIC, new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), stockUpdateRequestList));
        }
    }

    @Transactional
    @JLock(keyConstant = "createTakeStockTask", lockKey = "createTakeStockTask")
    public void createTakeStockTask(List<StockUpdateRequest> stockUpdateRequestList, String createBy) {
        //1.过滤掉已存在的未盘点的任务
        List<StockUpdateRequest> collect = stockUpdateRequestList.stream().filter(request -> {
            Integer count = stockTakeStockTaskService.getBaseMapper().countByPositionCodeAndSku(request.getSku(), request.getPositionCode());
            return count == 0;
        }).collect(Collectors.toList());
        //2.生成盘点任务
        if (!CollectionUtils.isEmpty(collect)) {
            this.createTempTakeStockTask(collect, createBy);
        }
    }


    private void createTempTakeStockTask(List<StockUpdateRequest> stockUpdateRequestList, String createBy) {
        List<String> positionCodeList = stockUpdateRequestList.stream().map(StockUpdateRequest::getPositionCode).collect(Collectors.toList());
        List<BdPositionEntity> positionEntityList = bdPositionService.getBaseMapper().selectList(new LambdaQueryWrapper<BdPositionEntity>()
                .in(BdPositionEntity::getPositionCode, positionCodeList)
                .eq(BdPositionEntity::getIsDeleted, 0));
        Map<String, BdPositionEntity> positionMap = positionEntityList.stream().collect(Collectors.toMap(BdPositionEntity::getPositionCode, positionEntity -> positionEntity));
        StockTakeStockTaskEntity taskEntity = getStockTakeStockTaskEntity(createBy, positionEntityList);
        List<StockTakeStockTaskItemEntity> itemEntityList = new ArrayList<>();

        List<String> skuList = stockUpdateRequestList.stream().map(StockUpdateRequest::getSku).collect(Collectors.toList());
        List<ProductSpecInfoEntity> specInfoEntityList = productSpecInfoService.list(new LambdaQueryWrapper<ProductSpecInfoEntity>()
                .in(ProductSpecInfoEntity::getSku, skuList));
        Map<String, ProductSpecInfoEntity> map = specInfoEntityList.stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, specInfoEntity -> specInfoEntity));
        stockUpdateRequestList.forEach(item -> {
            StockTakeStockTaskItemEntity taskItemEntity = new StockTakeStockTaskItemEntity();
            BdPositionEntity positionEntity = positionMap.get(item.getPositionCode());
            ProductSpecInfoEntity productSpecInfoEntity = map.get(item.getSku());
            BeanUtils.copyProperties(item, taskItemEntity, "id", "createBy", "updateBy", "createDate", "updateDate", "version");
            BeanUtils.copyProperties(positionEntity, taskItemEntity, "id", "createBy", "updateBy", "createDate", "updateDate", "version");
            taskItemEntity.setProductId(productSpecInfoEntity.getProductId());
            taskItemEntity.setSpecId(productSpecInfoEntity.getSpecId());
            taskItemEntity.setLocation(TenantContext.getTenant());
            taskItemEntity.setCreateBy(createBy);
            taskItemEntity.setSku(productSpecInfoEntity.getSku());
            taskItemEntity.setTaskId(taskEntity.getTaskId());
            taskItemEntity.setStatus(StockTakeStockTaskStatusEnum.TO_BE_INVENTORY.name());
            itemEntityList.add(taskItemEntity);
        });
        stockTakeStockTaskItemService.saveBatch(itemEntityList);
        //更新主表的库位数和库区数
        updateStockTakeStockTask(taskEntity);
        logService.addLog(taskEntity.getTaskId(), "盘点任务生成", "盘点任务生成");
    }

    private void updateStockTakeStockTask(StockTakeStockTaskEntity taskEntity) {
        StockTakeStockTaskEntity stockTakeStockTaskEntity = new StockTakeStockTaskEntity();
        stockTakeStockTaskEntity.setTaskId(taskEntity.getTaskId());
        List<StockTakeStockTaskItemEntity> itemEntityList = stockTakeStockTaskItemService.list(new LambdaQueryWrapper<StockTakeStockTaskItemEntity>()
                .select(StockTakeStockTaskItemEntity::getPositionCode, StockTakeStockTaskItemEntity::getSpaceAreaId)
                .eq(StockTakeStockTaskItemEntity::getTaskId, taskEntity.getTaskId()));
        stockTakeStockTaskEntity.setPositionQty((int) itemEntityList.stream().map(StockTakeStockTaskItemEntity::getPositionCode).distinct().count());
        stockTakeStockTaskEntity.setSpaceAreaQty((int) itemEntityList.stream().map(StockTakeStockTaskItemEntity::getSpaceAreaId).distinct().count());
        stockTakeStockTaskService.updateById(stockTakeStockTaskEntity);
    }

    @NotNull
    private StockTakeStockTaskEntity getStockTakeStockTaskEntity(String createBy, List<BdPositionEntity> positionEntityList) {
        Date date = new Date();
        StockTakeStockTaskEntity taskEntity = stockTakeStockTaskService.getOne(new LambdaQueryWrapper<StockTakeStockTaskEntity>().between(StockTakeStockTaskEntity::getCreateDate, WmsDateUtils.getDateStart(date), WmsDateUtils.getDateEnd(date))
                .in(StockTakeStockTaskEntity::getStatus, CollectionUtil.newArrayList(StockTakeStockTaskStatusEnum.TO_BE_INVENTORY.name(), StockTakeStockTaskStatusEnum.INVENTORYING.name()))
                .eq(StockTakeStockTaskEntity::getPlanType, TakeStockPlanTypeEnum.VARIANCE_INVENTORY.name())
                .last("limit 1"));
        if (Objects.isNull(taskEntity)) {
            taskEntity = new StockTakeStockTaskEntity();
            taskEntity.setLocation(TenantContext.getTenant());
            taskEntity.setPlanType(TakeStockPlanTypeEnum.VARIANCE_INVENTORY.name());
            taskEntity.setTaskGenerateMode(TakeStockTaskModeEnum.BY_POSITION.name());
            taskEntity.setStatus(StockTakeStockTaskStatusEnum.TO_BE_INVENTORY.name());
            taskEntity.setCreateBy(createBy);
            taskEntity.setPositionQty(positionEntityList.size());
            taskEntity.setSpaceAreaQty((int) positionEntityList.stream().map(BdPositionEntity::getSpaceAreaId).distinct().count());
        }
        stockTakeStockTaskService.saveOrUpdate(taskEntity);
        return taskEntity;
    }


    /**
     * 清除拣货箱，将拣货箱库存归还回给原库位
     *
     * @param batchId
     */
    @Transactional
    public void clearPickingBox(Integer batchId) {
        List<String> pickingBoxCodeList = pickingTaskService.list(new QueryWrapper<StockoutPickingTaskEntity>().lambda()
                .select(StockoutPickingTaskEntity::getPickingBoxCode)
                .eq(StockoutPickingTaskEntity::getBatchId, batchId)
                .isNotNull(StockoutPickingTaskEntity::getPickingBoxCode))
                .stream().map(StockoutPickingTaskEntity::getPickingBoxCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pickingBoxCodeList))
            return;
        clearPickingBox(pickingBoxCodeList, "波次完结");
    }

    /**
     * 清除拣货箱，将拣货箱库存归还回给原库位
     */
    @Transactional
    public void clearPickingBox(List<String> pickingBoxCodeList, String operateType) {
        List<StockInternalBoxEntity> internalBoxEntityList = stockInternalBoxService.getByInternalBoxCodeListAndStatus(pickingBoxCodeList, null);
        if (internalBoxEntityList.stream().anyMatch(item -> !StockInternalBoxTypeEnum.PICKING_BOX.name().equals(item.getInternalBoxType())))
            throw new BusinessServiceException("请选择拣货箱！");

        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.getBaseMapper().listStockByBoxCode(pickingBoxCodeList);
        if (CollectionUtils.isEmpty(list))
            return;

        List<StockInternalBoxItemEntity> collect = list.stream().filter(boxItem -> StringUtils.hasText(boxItem.getSourcePositionCode()) && boxItem.getQty() > 0).collect(Collectors.toList());
        //原库位增加
        collect.stream().forEach(boxItem -> {
            stockService.updateStock(buildByStockInternalBoxItemEntity(boxItem, operateType));
        });

        //移除拣货箱
        stockInternalBoxItemService.minusStockInternalBoxItemQty(collect, collect.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum(), StockChangeLogTypeEnum.STOCKOUT_ORDER_EXCEPTION_LEVELLING, StockChangeLogTypeModuleEnum.STOCK_OUT, null);

        //删除箱子
        stockInternalBoxService.update(new UpdateWrapper<StockInternalBoxEntity>()
                .in("internal_box_code", pickingBoxCodeList)
                .set("is_deleted", IsDeletedConstant.DELETED));
    }

    /**
     * 清除拣货箱，将拣货箱库存归还回给原库位
     */
    @Transactional
    public void clearBox(List<String> boxCodeList) {
        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.list(new LambdaQueryWrapper<StockInternalBoxItemEntity>()
                .in(StockInternalBoxItemEntity::getInternalBoxCode, boxCodeList));
        if (CollectionUtils.isEmpty(list))
            return;

        list.stream().forEach(item -> {

            stockInternalBoxItemService.minusStockInternalBoxItemQty(item, item.getQty(), StockChangeLogTypeEnum.STOCKOUT_ORDER_EXCEPTION_LEVELLING, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
        });

        //删除箱子
        stockInternalBoxService.update(new UpdateWrapper<StockInternalBoxEntity>()
                .in("internal_box_code", boxCodeList)
                .set("is_deleted", IsDeletedConstant.DELETED));
    }


    /**
     * 处理拣货箱异常数据
     */
    @Transactional
    public void dealException(InternalBoxExceptionRequest request) {
        StockInternalBoxItemEntity internalBoxItemEntity = stockInternalBoxItemService.getById(request.getInternalBoxItemId());
        if (Objects.isNull(internalBoxItemEntity))
            return;

        //减少内部箱库存
        if (request.getQty() < 0)
            stockInternalBoxItemService.minusStockInternalBoxItemQty(internalBoxItemEntity, Math.abs(request.getQty()), StockChangeLogTypeEnum.EXCEPTION_DEAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
        else
            stockInternalBoxItemService.addStockInternalBoxItemQty(internalBoxItemEntity, request.getQty(), StockChangeLogTypeEnum.EXCEPTION_DEAL, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
    }

    @Transactional
    public void clearPickingBoxByJob() {
        //获取更新时间超过两天未动的拣货箱数据
        Date dateBefore = new DateTime(new Date()).minusDays(1).toDate();
        List<StockoutClearPickingBoxItem> waitClearPickingBoxItem = stockInternalBoxItemService.getBaseMapper().getWaitClearPickingBoxItem(dateBefore);
        //根据波次ID去处理
        Map<Integer, List<StockoutClearPickingBoxItem>> collect =
                waitClearPickingBoxItem.stream().collect(Collectors.groupingBy(StockoutClearPickingBoxItem::getBatchId));
        List<Integer> boxItemIds = new ArrayList<>(waitClearPickingBoxItem.size());
        for (Map.Entry<Integer, List<StockoutClearPickingBoxItem>> entry : collect.entrySet()) {
            Integer key = entry.getKey();
            List<StockoutClearPickingBoxItem> value = entry.getValue();
            //判断波次底下的出库单是否都待发货/已发货/已取消
            Integer count = StockoutWavePlanTypeEnum.GROUP_WAVE.name().equals(value.get(0).getBatchType())
                    ? stockoutBatchService.getBaseMapper().countMergeBatchByStockoutOrderStatus(key)
                    : stockoutBatchService.getBaseMapper().countByStockoutOrderStatus(key);
            if (count > 0) {
                LOGGER.info("波次Id 【 {} 】 ,存在未完结出库单，不处理", key);
                continue;
            }
            //所有的出库单都待发货/已发货/已取消，将拣货箱内的库存还回原库位
            LOGGER.info("波次Id 【 {} 】 ,出库单明细待发货/已发货/已取消，清除内部箱明细", key);
            boxItemIds.addAll(value.stream().map(StockoutClearPickingBoxItem::getInternalBoxItemId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(boxItemIds))
            return;
        List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.listByIds(boxItemIds);
        returnSourcePosition(list, StockChangeLogTypeEnum.STOCKOUT_ORDER_EXCEPTION_LEVELLING, "job扫描清理");

    }

    /**
     * 归还原库位
     *
     * @param list
     */
    public void returnSourcePosition(List<StockInternalBoxItemEntity> list, StockChangeLogTypeEnum typeEnum, String operateType) {
        List<StockInternalBoxItemEntity> boxItemList = list.stream().filter(boxItem -> StringUtils.hasText(boxItem.getSourcePositionCode()) && boxItem.getQty() > 0).collect(Collectors.toList());
        //原库位增加
        boxItemList.stream().forEach(boxItem -> {
            stockService.updateStock(buildByStockInternalBoxItemEntity(boxItem, operateType));
        });

        //移除拣货箱
        stockInternalBoxItemService.minusStockInternalBoxItemQty(boxItemList, boxItemList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum(), typeEnum, StockChangeLogTypeModuleEnum.STOCK_OUT, null);
    }

    //按单拣货清除内部箱
    public void clearPickingBoxByWholePickJob() {
        // 出库单更新时间需要大于十分钟，防止还有复核消息没有消费
        Date dateBefore = new DateTime(new Date()).minusMinutes(10).toDate();
        List<StockInternalBoxItemOrder> itemOrders = stockInternalBoxItemService.getBaseMapper().getWaitClearPickingBoxItemWholePick(dateBefore);
        if (CollectionUtils.isEmpty(itemOrders)) return;

        List<StockInternalBoxItemEntity> boxItemList = stockInternalBoxItemService.listByIds(itemOrders.stream().map(StockInternalBoxItemOrder::getInternalBoxItemId).collect(Collectors.toList()));
        Map<Integer, String> collect = itemOrders.stream().collect(Collectors.toMap(StockInternalBoxItemOrder::getInternalBoxItemId, StockInternalBoxItemOrder::getStockoutOrderNo));
        LOGGER.info("波次Id 【 {} 】 ,出库单明细待发货/已发货/已取消，清除内部箱明细", boxItemList.stream().map(item -> item.getBatchId().toString()).collect(Collectors.joining(",")));
        //原库位增加
        boxItemList.stream().forEach(boxItem -> {

            //赋值出库单号，以记录库存交易日志
            boxItem.setStockoutOrderNo(collect.get(boxItem.getInternalBoxItemId()));
            stockService.updateStock(buildByStockInternalBoxItemEntity(boxItem, "job扫描清理(按单拣货)"));
        });

        //移除拣货箱
        stockInternalBoxItemService.minusStockInternalBoxItemQty(boxItemList, boxItemList.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum(), StockChangeLogTypeEnum.STOCKOUT_ORDER_EXCEPTION_LEVELLING, StockChangeLogTypeModuleEnum.STOCK_OUT, null);

    }

    private StockUpdateRequest buildByStockInternalBoxItemEntity(StockInternalBoxItemEntity stockInternalBoxItemEntity, String operateType) {
        Integer batchId = stockInternalBoxItemEntity.getBatchId();
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setBatchId(batchId);
        stockUpdateRequest.setPositionCode(stockInternalBoxItemEntity.getSourcePositionCode());
        stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.STOCKOUT_ORDER_EXCEPTION_LEVELLING);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_OUT);
        stockUpdateRequest.setQty(stockInternalBoxItemEntity.getQty());
        stockUpdateRequest.setSku(stockInternalBoxItemEntity.getSku());
        stockUpdateRequest.setStockoutOrderNo(stockInternalBoxItemEntity.getStockoutOrderNo());
        stockUpdateRequest.setContent(String.format("%s%s，清除拣货箱【%s】商品【%s】，原始库位【%s】增加%s件",
                operateType, Objects.nonNull(batchId) ? "，波次号" + batchId : "", stockInternalBoxItemEntity.getInternalBoxCode(), stockInternalBoxItemEntity.getSku(), stockInternalBoxItemEntity.getSourcePositionCode(), stockInternalBoxItemEntity.getQty()));
        return stockUpdateRequest;
    }

    private StockUpdateRequest buildByStockInternalBoxItemSourcePositionBo(String sourcePositionCode, Integer batchId, List<StockInternalBoxItemSourcePositionBo> sourcePositionBoList, StockChangeLogTypeEnum changeLogTypeEnum, String stockoutOrderNo) {
        StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
        stockUpdateRequest.setBatchId(batchId);
        stockUpdateRequest.setPositionCode(sourcePositionCode);
        stockUpdateRequest.setChangeLogType(changeLogTypeEnum);
        stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_OUT);
        int sum = sourcePositionBoList.stream().mapToInt(StockInternalBoxItemSourcePositionBo::getQty).sum();
        stockUpdateRequest.setQty(sum);
        stockUpdateRequest.setStockoutOrderNo(stockoutOrderNo);
        stockUpdateRequest.setSku(sourcePositionBoList.get(0).getSku());
        List<String> sourceInternalBoxCode = sourcePositionBoList.stream().map(StockInternalBoxItemSourcePositionBo::getSourceInternalBoxCode).filter(StringUtils::hasText).collect(Collectors.toList());
        String logContent = CollectionUtils.isEmpty(sourceInternalBoxCode) ? "清除发货库位" : String.format("清除拣货箱【%s】", sourceInternalBoxCode.stream().distinct().collect(Collectors.joining(",")));
        stockUpdateRequest.setContent(String.format("%s，商品【%s】，原始库位【%s】增加%s件", logContent, sourcePositionBoList.get(0).getSku(), sourcePositionCode, sum));
        return stockUpdateRequest;
    }

    /**
     * 复核缺货清理拣货箱
     *
     * @param stockoutOrderEntity
     */
    @Transactional
    public void noticeLackClearBox(StockoutOrderEntity stockoutOrderEntity) {

        // 需配置参数 通知缺货是否自动归还拣货箱库存  true or false  未配置默认为false  modify by caish 2024-04-03
        BdSystemParameterEntity systemParameter = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_STOCKOUT_NOTICE_LACK_CLEAR_BOX.getKey());
        if (Objects.isNull(systemParameter) || !StockConstant.TRUE.equalsIgnoreCase(systemParameter.getConfigValue()))
            return;

        StockoutOrderScanTaskEntity scanTaskEntity = scanTaskService.getOne(new QueryWrapper<StockoutOrderScanTaskEntity>().lambda()
                .eq(StockoutOrderScanTaskEntity::getStockoutOrderNo, stockoutOrderEntity.getStockoutOrderNo()));
        if (scanTaskEntity == null)
            throw new BusinessServiceException("复核任务不存在");

        List<StockoutOrderScanTaskItemEntity> scanTaskItemList = taskItemService.list(new QueryWrapper<StockoutOrderScanTaskItemEntity>().lambda()
                .eq(StockoutOrderScanTaskItemEntity::getTaskId, scanTaskEntity.getTaskId()));
        List<StockoutOrderScanTaskItemEntity> lackScanTaskItemList = scanTaskItemList.stream().filter(scanTaskItem -> 1 == scanTaskItem.getIsLack()).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(lackScanTaskItemList)) {
            StockoutBatchOrderEntity stockoutBatchOrderEntity = stockoutBatchOrderService.getBatchOrderByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
            StockoutBatchEntity batchEntity = stockoutBatchService.getById(stockoutBatchOrderEntity.getBatchId());
            this.dealBoxItemByBatch(batchEntity, lackScanTaskItemList, stockoutOrderEntity);
        }

    }

    /**
     * 1.遍历复核任务明细
     * （1） 查询波次下ID，拣货箱对应sku的数量
     * （2）计算出波次下sku对应的待复核数、未复合数、缺货数，拣货箱内的数量 ≤ 其他单待复核的数量
     * （3）归还多余的内部箱
     *
     * @param batchEntity
     * @param scanTaskItemList
     * @param stockoutOrderEntity
     */
    private void dealBoxItemByBatch(StockoutBatchEntity batchEntity, List<StockoutOrderScanTaskItemEntity> scanTaskItemList, StockoutOrderEntity stockoutOrderEntity) {
        List<StockUpdateRequest> stockUpdateRequestList = new ArrayList<>();
        scanTaskItemList.stream().forEach(item -> {

            //（1） 查询波次下ID，拣货箱对应sku的数量
            //判断商品绑定在合并波次还是子波次 加工款和缝制货绑在合并波次
            boolean hasMergeBatchId = Objects.nonNull(batchEntity.getMergeBatchId()) && batchEntity.getMergeBatchId() > 0;
            Integer batchId = hasMergeBatchId ? item.getIsNeedProcess().equals(1) || StringUtils.hasText(item.getChangeType()) ? batchEntity.getBatchId() : batchEntity.getMergeBatchId() : batchEntity.getBatchId();

            List<StockInternalBoxItemEntity> list = stockInternalBoxItemService.getBaseMapper().findListByTypeBatchIdAndSku(StockInternalBoxTypeEnum.PICKING_BOX.name(), batchId, item.getSku());
            //过滤掉加工的部分，分拣明细的sku是定制款，拣货箱里还是胚款，可能导致归还错误
            list = list.stream().filter(boxItem -> StockConstant.DISABLED.equals(boxItem.getIsProcess()) && boxItem.getQty() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list))
                return;

            //（2）计算出波次下sku对应的待复核数、未复合数、缺货数，拣货箱内的数量 ≤ 其他单待复核的数量
            //判断是否合并波次,调用不同的查询
            Integer qty = hasMergeBatchId
                    ? taskItemService.getBaseMapper().sumAllScanQtyByMergeBatchId(batchEntity.getMergeBatchId(), item.getTaskId(), item.getSku())
                    : taskItemService.getBaseMapper().sumAllScanQtyByBatchId(batchEntity.getBatchId(), item.getTaskId(), item.getSku());

            int boxQty = list.stream().mapToInt(StockInternalBoxItemEntity::getQty).sum();
            //（3）归还多余的内部箱
            if (boxQty > qty) {
                //赋值出库单号以记录库存交易日志
                list.forEach(boxItemEntity -> boxItemEntity.setStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo()));
                //移除拣货箱多余数量
                List<StockInternalBoxItemSourcePositionBo> sourcePositionBoList = stockInternalBoxItemService.minusStockInternalBoxItemQty(list, boxQty - qty, StockChangeLogTypeEnum.SCAN_LEVELLING, StockChangeLogTypeModuleEnum.STOCK_OUT, null);

                //原库位增加
                Map<String, List<StockInternalBoxItemSourcePositionBo>> minusSourcePositionQtyMap = sourcePositionBoList.stream().collect(Collectors.groupingBy(StockInternalBoxItemSourcePositionBo::getSourcePositionCode));
                minusSourcePositionQtyMap.forEach((sourcePositionCode, boList) -> {
                    stockUpdateRequestList.add(buildByStockInternalBoxItemSourcePositionBo(sourcePositionCode, batchId, boList, StockChangeLogTypeEnum.SCAN_LEVELLING, stockoutOrderEntity.getStockoutOrderNo()));
                });
            }

        });

        if (!CollectionUtils.isEmpty(stockUpdateRequestList)) {
            stockUpdateRequestList.stream().forEach(stockUpdateRequest -> stockService.updateStock(stockUpdateRequest));
        }
    }
}
