package com.nsy.wms.business.service.stockin;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockin.StockinOrderTaskStatusEnum;
import com.nsy.api.wms.request.stockin.StockinSpotInfoListRequest;
import com.nsy.api.wms.request.stockin.StockinSpotTaskListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinSpotInfoListResponse;
import com.nsy.wms.business.manage.scm.ScmApiService;
import com.nsy.wms.business.manage.supplier.response.LogisticsMappingInfo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.repository.entity.stockin.StockinOrderEntity;
import com.nsy.wms.repository.entity.stockin.StockinOrderItemEntity;
import com.nsy.wms.repository.entity.stockin.StockinSpotInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinSpotInfoMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StockinSpotInfoService extends ServiceImpl<StockinSpotInfoMapper, StockinSpotInfoEntity> {

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    ScmApiService scmApiService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockinOrderService stockinOrderService;

    public PageResponse<StockinSpotInfoListResponse> getListByRequest(StockinSpotInfoListRequest request) {
        //3、调用采购系统校验物流单号和现货计划单号的关系表
        PageResponse<StockinSpotInfoListResponse> pageResponse = new PageResponse<>();
        Page<StockinSpotInfoEntity> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockinSpotInfoEntity> pageList = this.page(page, buildQuery(request));
        List<StockinSpotInfoListResponse> responses = new LinkedList<>();
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pageResponse;
        }
        Map<String, String> collect = stockinOrderService.list(new LambdaQueryWrapper<StockinOrderEntity>().in(StockinOrderEntity::getLogisticsNo,
                pageList.getRecords().stream().map(StockinSpotInfoEntity::getLogisticsNo).collect(Collectors.toList())))
                .stream().collect(Collectors.toMap(StockinOrderEntity::getLogisticsNo, StockinOrderEntity::getStockinOrderNo));
        for (StockinSpotInfoEntity record : pageList.getRecords()) {
            StockinSpotInfoListResponse response = new StockinSpotInfoListResponse();
            response.setLogisticsNo(record.getLogisticsNo());
            response.setLogisticsCompany(record.getLogisticsCompany());
            response.setOperator(record.getCreateBy());
            response.setOperateDate(record.getCreateDate());
            response.setStatus(StringUtils.hasText(record.getStatus()) ? enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_STOCKIN_TASK_TYPE.getName(), record.getStatus()) : StockinOrderTaskStatusEnum.PENDING.getStatus());
            response.setPurchasePlanNo(record.getPurchasePlanNo());
            response.setStockinOrderNo(collect.get(record.getLogisticsNo()));
            responses.add(response);
        }
        pageResponse.setTotalCount(pageList.getTotal());
        pageResponse.setContent(responses);
        return pageResponse;
    }

    private QueryWrapper<StockinSpotInfoEntity> buildQuery(StockinSpotInfoListRequest request) {
        QueryWrapper<StockinSpotInfoEntity> queryWrapper = new QueryWrapper<>();
        if (StringUtils.hasText(request.getDocumentNo())) {
            queryWrapper.lambda().and(wrapper -> wrapper.eq(StockinSpotInfoEntity::getLogisticsNo, request.getDocumentNo())
                    .or().in(StockinSpotInfoEntity::getPurchasePlanNo, request.getDocumentNo()));
        }
        if (StringUtils.hasText(request.getLogisticsNo())) {
            queryWrapper.lambda().eq(StockinSpotInfoEntity::getLogisticsNo, request.getLogisticsNo());
        }
        if (StringUtils.hasText(request.getPurchasePlanNo())) {
            queryWrapper.lambda().eq(StockinSpotInfoEntity::getPurchasePlanNo, request.getPurchasePlanNo());
        }
        if (StringUtils.hasText(request.getStatus())) {
            queryWrapper.lambda().eq(StockinSpotInfoEntity::getStatus, request.getStatus());
        }
        if (request.getStartDate() != null) {
            queryWrapper.lambda().gt(StockinSpotInfoEntity::getCreateDate, request.getStartDate());
        }
        if (request.getEndDate() != null) {
            queryWrapper.lambda().lt(StockinSpotInfoEntity::getCreateDate, request.getEndDate());
        }
        queryWrapper.lambda().orderByDesc(StockinSpotInfoEntity::getCreateDate);
        return queryWrapper;
    }


    public List<StockinSpotInfoEntity> listBySupplierDeliveryNo(String supplierDeliveryNo) {
        return this.list(new LambdaQueryWrapper<StockinSpotInfoEntity>().eq(StockinSpotInfoEntity::getSupplierDeliveryNo, supplierDeliveryNo));
    }

    public List<StockinSpotInfoEntity> listByPurchaseNoList(List<String> purchaseNoList) {
        if (CollectionUtils.isEmpty(purchaseNoList)) return new ArrayList<>();
        return this.list(new LambdaQueryWrapper<StockinSpotInfoEntity>().in(StockinSpotInfoEntity::getPurchasePlanNo, purchaseNoList));
    }

    public List<StockinSpotInfoEntity> listByPurchaseNo(String purchaseNo) {
        return this.list(new LambdaQueryWrapper<StockinSpotInfoEntity>().eq(StockinSpotInfoEntity::getPurchasePlanNo, purchaseNo));
    }

    public List<StockinSpotInfoEntity> listBySupplierDeliveryNoList(List<String> supplierDeliveryNoList) {
        if (CollectionUtils.isEmpty(supplierDeliveryNoList)) {
            return Lists.newArrayList();
        }
        return this.list(new LambdaQueryWrapper<StockinSpotInfoEntity>().in(StockinSpotInfoEntity::getSupplierDeliveryNo, supplierDeliveryNoList));
    }

    @Transactional
    public void addSpotInfo(List<LogisticsMappingInfo> logisticsMappingList) {
        logisticsMappingList.forEach(logisticsMapping -> {
            StockinSpotInfoEntity stockinSpotInfoEntity = this.getBaseMapper().getNoIgnoreTenant(logisticsMapping.getLogisticsNo(), logisticsMapping.getPurchasePlanNo());
            if (Objects.nonNull(stockinSpotInfoEntity)) {
                if (!stockinSpotInfoEntity.getLocation().equals(TenantContext.getTenant()))
                    throw new BusinessServiceException(String.format("其他地区已存在相同物流单号【%s】和计划单号【%s】", logisticsMapping.getLogisticsNo(), logisticsMapping.getPurchasePlanNo()));
                return;
            }
            StockinSpotInfoEntity spotInfoEntity = new StockinSpotInfoEntity();
            spotInfoEntity.setLogisticsNo(logisticsMapping.getLogisticsNo());
            spotInfoEntity.setLogisticsCompany(logisticsMapping.getLogisticsCompany());
            spotInfoEntity.setPurchasePlanNo(logisticsMapping.getPurchasePlanNo());
            spotInfoEntity.setShipmentQty(logisticsMapping.getShipmentQty());
            spotInfoEntity.setCreateBy(loginInfoService.getName());
            spotInfoEntity.setUpdateBy(loginInfoService.getName());
            spotInfoEntity.setLocation(TenantContext.getTenant());
            //pending-待收货,receiving-收货中,received-收货完成
            spotInfoEntity.setStatus(StockinOrderTaskStatusEnum.PENDING.name());
            this.save(spotInfoEntity);
        });
    }

    public void updateReceivingStatus(String logisticsNo, String supplierDeliveryNo) {
        List<StockinSpotInfoEntity> list = this.list(new LambdaQueryWrapper<StockinSpotInfoEntity>()
                .eq(StockinSpotInfoEntity::getLogisticsNo, logisticsNo));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("物流未签收！");
        }
        List<StockinSpotInfoEntity> collect1 = list.stream().filter(item -> StockinOrderTaskStatusEnum.PENDING.name().equals(item.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect1))
            return;
        List<StockinSpotInfoEntity> collect = collect1.stream().peek(spotInfoEntity -> {
            spotInfoEntity.setStatus(StockinOrderTaskStatusEnum.RECEIVING.name());
            spotInfoEntity.setSupplierDeliveryNo(supplierDeliveryNo);
            spotInfoEntity.setUpdateBy(loginInfoService.getName());
        }).collect(Collectors.toList());
        this.updateBatchById(collect);
    }

    public boolean updateReceivedStatus(String logisticsNo, List<StockinOrderItemEntity> orderItemEntityList, StockinSpotTaskListRequest stockinSpotTaskListRequest) {
        List<StockinSpotInfoEntity> list = this.list(new LambdaQueryWrapper<StockinSpotInfoEntity>()
                .eq(StockinSpotInfoEntity::getLogisticsNo, logisticsNo));
        if (CollectionUtils.isEmpty(list))
            return true;
        List<String> purchasePlanNos = orderItemEntityList.stream().map(StockinOrderItemEntity::getPurchasePlanNo).collect(Collectors.toList());
        String purchasePlanNo = stockinSpotTaskListRequest.getPurchasePlanNo();
        List<StockinSpotInfoEntity> spotInfoEntityList = list.stream()
                .filter(item -> !StockinOrderTaskStatusEnum.RECEIVED.name().equals(item.getStatus()) && (purchasePlanNos.contains(item.getPurchasePlanNo()) || item.getPurchasePlanNo().equals(purchasePlanNo))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spotInfoEntityList))
            return true;
        List<StockinSpotInfoEntity> collect = spotInfoEntityList.stream().map(spotInfoEntity -> {
            StockinSpotInfoEntity stockinSpotInfoEntity = new StockinSpotInfoEntity();
            stockinSpotInfoEntity.setSpotInfoId(spotInfoEntity.getSpotInfoId());
            stockinSpotInfoEntity.setStatus(StockinOrderTaskStatusEnum.RECEIVED.name());
            stockinSpotInfoEntity.setUpdateBy(loginInfoService.getName());
            return stockinSpotInfoEntity;
        }).collect(Collectors.toList());
        this.updateBatchById(collect);

        int count = this.count(new LambdaQueryWrapper<StockinSpotInfoEntity>().select(StockinSpotInfoEntity::getSpotInfoId)
                .eq(StockinSpotInfoEntity::getLogisticsNo, logisticsNo).ne(StockinSpotInfoEntity::getStatus, StockinOrderTaskStatusEnum.RECEIVED.name()));
        if (count > 0) {
            return false;
        }
        return true;
    }

    public StockinSpotInfoEntity getByLogisticsNoAndPurchasePlanNo(String logisticsNo, String purchasePlanNo) {
        return this.getOne(new LambdaQueryWrapper<StockinSpotInfoEntity>().eq(StockinSpotInfoEntity::getLogisticsNo, logisticsNo)
                .eq(StockinSpotInfoEntity::getPurchasePlanNo, purchasePlanNo)
                .last("limit 1"));
    }
}
