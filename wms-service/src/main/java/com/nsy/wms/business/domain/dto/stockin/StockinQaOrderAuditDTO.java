package com.nsy.wms.business.domain.dto.stockin;

import com.nsy.api.wms.enumeration.qa.FlowTaskQaOperationEnum;

/**
 * <AUTHOR>
 * @date 2024/12/2 17:46
 */
public class StockinQaOrderAuditDTO {

    private FlowTaskQaOperationEnum operationEnum;

    private Integer qaOrderId;

    private String userAccount;

    /**
     * 补充日志内容
     */
    private String content;

    //默认为否
    private boolean isAdmin = false;

    public StockinQaOrderAuditDTO() {
    }

    public StockinQaOrderAuditDTO(FlowTaskQaOperationEnum operationEnum, Integer qaOrderId, boolean isAdmin, String userAccount) {
        this.operationEnum = operationEnum;
        this.qaOrderId = qaOrderId;
        this.isAdmin = isAdmin;
        this.userAccount = userAccount;
    }

    public StockinQaOrderAuditDTO(FlowTaskQaOperationEnum operationEnum, Integer qaOrderId, boolean isAdmin, String userAccount, String content) {
        this.operationEnum = operationEnum;
        this.qaOrderId = qaOrderId;
        this.isAdmin = isAdmin;
        this.userAccount = userAccount;
        this.content = content;
    }


    public FlowTaskQaOperationEnum getOperationEnum() {
        return operationEnum;
    }

    public void setOperationEnum(FlowTaskQaOperationEnum operationEnum) {
        this.operationEnum = operationEnum;
    }

    public Integer getQaOrderId() {
        return qaOrderId;
    }

    public void setQaOrderId(Integer qaOrderId) {
        this.qaOrderId = qaOrderId;
    }

    public boolean isAdmin() {
        return isAdmin;
    }

    public void setAdmin(boolean admin) {
        isAdmin = admin;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
