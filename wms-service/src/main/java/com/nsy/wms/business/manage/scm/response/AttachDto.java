package com.nsy.wms.business.manage.scm.response;

import com.nsy.api.core.apicore.util.DateUtils;

/**
 * <AUTHOR>
 * @date 2024/11/18 9:38
 */
public class AttachDto {
    private Integer id;
    private String originName;
    private String name;
    private String url;
    private Integer sort;
    private String createDate;

    public static AttachDto build(WorkmanshipAttachment workmanshipAttachment) {
        AttachDto attach = new AttachDto();
        attach.setId(workmanshipAttachment.getId());
        attach.setName(workmanshipAttachment.getAttachmentName());
        attach.setOriginName(workmanshipAttachment.getOriginName());
        attach.setUrl(workmanshipAttachment.getAttachmentUrl());
        attach.setCreateDate(DateUtils.format(workmanshipAttachment.getCreateDate(), DateUtils.DATE_FORMAT_DATE4));
        return attach;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOriginName() {
        return originName;
    }

    public void setOriginName(String originName) {
        this.originName = originName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }
}

