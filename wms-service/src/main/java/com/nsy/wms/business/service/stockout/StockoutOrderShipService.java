package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.constants.SpaceAreaMapConstant;
import com.nsy.api.wms.constants.StockConstant;
import com.nsy.api.wms.domain.stockout.StockoutOrderItemErpSpace;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.overseas.OverseasWarehouseOrderStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderPlatformEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutSortingTypeEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.response.stockout.GetSecondaryNumResponse;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpFinishPartialPickItemRequest;
import com.nsy.wms.business.manage.erp.request.ErpFinishPartialPickRequest;
import com.nsy.wms.business.manage.oms.request.FbtOrderShipShipmentInfoDto;
import com.nsy.wms.business.manage.oms.request.OrderShippedMessage;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.service.bd.BdErpSpaceMappingService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.external.ExternalOrderCheckQueueService;
import com.nsy.wms.business.service.stock.StockLendService;
import com.nsy.wms.business.service.stock.StockPrematchInfoService;
import com.nsy.wms.business.service.stock.StockPrematchRemoveService;
import com.nsy.wms.business.service.stock.StockPrematchService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stock.StockTransferTrackingService;
import com.nsy.wms.business.service.stockout.handle.OverseaStockoutService;
import com.nsy.wms.business.service.stockout.handle.StockoutTypeFactory;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.elasticjob.stock.StockTransferCrossSpaceJob;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOverseasWarehouseOrderEntity;
import com.nsy.wms.repository.entity.stock.StockPrematchInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderMapper;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.Key;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StockoutOrderShipService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutOrderShipService.class);

    @Autowired
    StockoutShipmentMapper stockoutShipmentMapper;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    OverseaStockoutService overseaStockoutService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutShipmentConfirmService shipmentConfirmService;
    @Autowired
    ExternalOrderCheckQueueService checkQueueService;
    @Autowired
    StockoutOrderPrematchInfoService stockoutOrderPrematchInfoService;
    @Autowired
    StockoutOrderScanTaskService scanTaskService;
    @Autowired
    StockoutOrderScanTaskItemService scanTaskItemService;
    @Autowired
    StockoutOrderScanCheckService scanCheckService;
    @Autowired
    StockoutOrderScanLogService scanLogService;
    @Autowired
    StockoutLogQueueService stockoutLogQueueService;
    @Autowired
    StockoutOrderLackService lackService;
    @Autowired
    StockoutShipmentAmazonRelationService syncAmazonService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockoutOrderOperateService operateService;
    @Autowired
    StockLendService stockLendService;
    @Autowired
    StockTransferCrossSpaceJob stockTransferCrossSpaceJob;
    @Autowired
    StockTransferTrackingService transferTrackingService;
    @Autowired
    StockoutOrderLogService stockoutOrderLogService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutBatchSplitTaskService splitTaskService;
    @Autowired
    StockoutBatchSplitTaskItemService splitTaskItemService;
    @Autowired
    StockoutOrderMapper stockoutOrderMapper;
    @Autowired
    private StockPrematchInfoService stockPrematchInfoService;
    @Autowired
    private StockService stockService;
    @Autowired
    BdPositionService positionService;
    @Autowired
    StockoutTypeFactory stockoutTypeFactory;
    @Autowired
    private StockoutShipmentService shipmentService;
    @Autowired
    MessageProducer producer;
    @Autowired
    StockoutOrderTrackingService stockoutOrderTrackingService;
    @Autowired
    StockoutTransparencyCodeService transparencyCodeService;
    @Autowired
    StockoutPickingTaskItemService stockoutPickingTaskItemService;
    @Autowired
    StockoutBatchOrderService batchOrderService;
    @Autowired
    StockPrematchService stockPrematchService;
    @Autowired
    BdErpSpaceMappingService bdErpSpaceMappingService;
    @Autowired
    StockoutErpPickService stockoutErpPickService;
    @Autowired
    StockPrematchRemoveService stockPrematchRemoveService;
    @Autowired
    TmsApiService tmsApiService;
    @Autowired
    StockoutOverseasWarehouseOrderService overseasWarehouseOrderService;

    /**
     * 出库单->已发货
     * 触发发货差异库存核对
     *
     * @param stockoutOrderEntityList 出库单
     */
    public void changeStockoutOrderShippedBy(List<StockoutOrderEntity> stockoutOrderEntityList) {
        if (stockoutOrderEntityList.isEmpty()) {
            throw new BusinessServiceException("找不到待发货的出库单，请核对出库单是否为待发货状态");
        }
        for (StockoutOrderEntity stockoutOrderEntity : stockoutOrderEntityList) {
            //出库单已取消不能更新为已发货
            if (StockoutOrderStatusEnum.CANCELLED.name().equals(stockoutOrderEntity.getStatus()) || StockoutOrderStatusEnum.CANCELLING.name().equals(stockoutOrderEntity.getStatus()))
                continue;
            //确认出库单下所有装箱清单都已发货才修改状态
            int waitToShippedShipment = stockoutShipmentMapper.countWaitToShippedShipment(stockoutOrderEntity.getStockoutOrderNo());
            if (waitToShippedShipment > 0) {
                LOGGER.info("出库单号: {} 出库单装箱清单未全部发货", stockoutOrderEntity.getStockoutOrderNo());
                continue;
            }
            scanCheckService.finishScanTask(stockoutOrderEntity);
            stockoutOrderEntity.setDeliveryDate(new Date());
            stockoutOrderEntity.setUpdateBy(loginInfoService.getName());
            stockoutOrderService.updateById(stockoutOrderEntity);
            stockoutOrderService.updateStockoutOrderStatusByEntity(stockoutOrderEntity, StockoutOrderStatusEnum.DELIVERED.name());
            //发货清预配
            stockPrematchRemoveService.removeByStockoutOrder(Collections.singletonList(stockoutOrderEntity.getStockoutOrderId()), StockoutOrderLogTypeEnum.DELIVERED);
            //发货通知物流系统
            if (StringUtils.hasText(stockoutOrderEntity.getLogisticsNo())) {
                tmsApiService.syncDeliveryInfo(stockoutOrderEntity.getLogisticsNo());
            }
            //stockoutOrderPrematchInfoService.syncShipDiffStockBy(stockoutOrderEntity);

        }

        //this.addStockoutCheckQueueBy(stockoutOrderEntityList, loginInfoService.getUserName());
    }




    /**
     * 出库单装箱清单已发货，出库单已发货
     */
    public void stockoutOrderShipmentShipped(List<String> stockoutOrderNos, String forwarderChannel) {
        List<StockoutShipmentSearchResult> allShipments = stockoutShipmentMapper.findShipmentConfirmList(stockoutOrderNos, null, null);
        Map<String, List<StockoutShipmentSearchResult>> map = allShipments.stream().collect(Collectors.groupingBy(StockoutShipmentSearchResult::getStockoutOrderNo));
        // 装箱清单全部 -> 已发货, 才更新出库单: 收集所有装箱清单已发货的出库单
        List<String> outOrderNoList = new ArrayList<>();
        stockoutOrderNos.forEach(o -> {
            List<StockoutShipmentSearchResult> shipmentSearchResults = map.get(o);
            if (!CollectionUtils.isEmpty(shipmentSearchResults) && shipmentSearchResults.stream().allMatch(s -> StockoutShipmentStatusEnum.SHIPPED.name().equals(s.getStatus()))) {
                outOrderNoList.add(o);
            } else {
                LOGGER.info("出库单号: {} 出库单装箱清单未全部发货", o);
            }
        });
        if (!CollectionUtils.isEmpty(outOrderNoList)) {
            // 出库单 -> 发货
            this.stockoutOrderShip(outOrderNoList, forwarderChannel, map);
        }
    }

    /**
     * 出库单发货
     */
    public void stockoutOrderShip(List<String> outOrderNoList, String forwarderChannel, Map<String, List<StockoutShipmentSearchResult>> map) {
        // step 1: 更新待发货出库单 -> 已发货
        List<StockoutOrderEntity> stockoutOrderEntityList = stockoutOrderService.list(new LambdaQueryWrapper<StockoutOrderEntity>().in(StockoutOrderEntity::getStockoutOrderNo, outOrderNoList)
                .eq(StockoutOrderEntity::getStatus, StockoutOrderStatusEnum.READY_DELIVERY.name()));
        if (CollectionUtils.isEmpty(stockoutOrderEntityList)) {
            LOGGER.info("出库单号: {} 不是待发货状态，不更新为已发货", JsonMapper.toJson(outOrderNoList));
            return;
        }
        this.changeStockoutOrderShippedBy(stockoutOrderEntityList);
        // step 2:出库类型为内部借用出库 -> 回填借用单借出数、状态，同步ERP
        stockLendService.processStockLend(stockoutOrderEntityList);
        LOGGER.info("更新出库单 -> 已发货, 仓间调拨需要生成入库单, 推送kafka消息, 出库单号: {}", JsonMapper.toJson(outOrderNoList));
        // step 3: 仓间调拨需要生成入库单
        stockTransferCrossSpaceJob.generateStockinOrder(stockoutOrderEntityList);
        // step 4: 仓间调拨出库需要生成在途跟踪数据
        stockoutOrderEntityList.forEach(it -> stockoutOrderTrackingService.doShip(it));
        // fba是自提的，日志类型不同
        if (!StringUtils.hasText(forwarderChannel)) {
            stockoutOrderEntityList.forEach(order -> stockoutTypeFactory.differType(order).shipLog(order));
        } else {
            outOrderNoList.forEach(item -> {
                String logContent = String.format("箱子编号【%s】自提确定，货代【%s】",
                        map.get(item).stream().map(StockoutShipmentSearchResult::getShipmentBoxCode).distinct().collect(Collectors.joining(",")),
                        forwarderChannel);
                stockoutOrderLogService.addLog(item, StockoutOrderLogTypeEnum.CUSTOMER_PICK_CONFIRM, logContent);
            });
        }
        stockoutLogQueueService.addLogQueue(stockoutOrderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()), String.format("%s 发货出库", loginInfoService.getName()));
        stockoutOrderEntityList.forEach(order -> {
            //透明计划变更为已使用
            transparencyCodeService.syncOmsTCode(order, -1);
            //发货通知物流系统
            if (StringUtils.hasText(order.getLogisticsNo())) {
                tmsApiService.syncDeliveryInfo(order.getLogisticsNo());
            }
            if (StrUtil.equals(order.getStockoutType(), StockoutOrderTypeEnum.FIRST_LEG_DELIVERY.name()) && StrUtil.equals(order.getPlatformName(), StockoutOrderPlatformEnum.TIKTOK.getName())) {
                OrderShippedMessage message = new OrderShippedMessage();
                message.setLocation(TenantContext.getTenant());
                List<String> orderNoList = stockoutOrderItemService.getBaseMapper().getOrderNoByStockoutOrderId(order.getStockoutOrderId());
                List<StockoutShipmentSearchResult> shipmentSearchResultList = map.get(order.getStockoutOrderNo());
                if (CollectionUtils.isEmpty(shipmentSearchResultList)) {
                    LOGGER.info("推送oms FBT发货消息，出库单号: {} 装箱清单为空", order.getStockoutOrderNo());
                } else {
                    buildShipmentInfo(message, shipmentSearchResultList);
                }
                message.setOrderNoList(orderNoList);
                producer.sendMessage(KafkaConstant.SYNC_OMS_ORDER_SHIP_TOPIC_NAME, KafkaConstant.SYNC_OMS_ORDER_SHIP_TOPIC, Key.of(orderNoList.get(0)), message);
            }
        });
    }

    private void buildShipmentInfo(OrderShippedMessage message, List<StockoutShipmentSearchResult> shipmentSearchResultList) {
        List<Integer> shipmentIdList = shipmentSearchResultList.stream().map(StockoutShipmentSearchResult::getShipmentId).distinct().collect(Collectors.toList());
        List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = syncAmazonService.listByShipmentId(shipmentIdList);
        List<FbtOrderShipShipmentInfoDto> fbtOrderShipShipmentInfoDtoList = new ArrayList<>();
        amazonRelationEntities.stream().collect(Collectors.groupingBy(StockoutShipmentAmazonRelationEntity::getFbaShipmentId)).forEach((fbaShipmentId, relationEntities) -> {
            FbtOrderShipShipmentInfoDto dto = new FbtOrderShipShipmentInfoDto();
            dto.setFbtShipmentId(fbaShipmentId);
            List<Integer> shipmentIdTotal = relationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getShipmentId).distinct().collect(Collectors.toList());
            List<StockoutShipmentSearchResult> shipmentListResult = shipmentSearchResultList.stream().filter(it -> shipmentIdTotal.contains(it.getShipmentId())).collect(Collectors.toList());
            dto.setForwarderChannelList(shipmentListResult.stream().map(StockoutShipmentSearchResult::getForwarderChannel).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList()));
            dto.setLogisticsNoList(shipmentListResult.stream().map(StockoutShipmentSearchResult::getLogisticsNo).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList()));
            dto.setDeliveryTime(new Date());
            dto.setLogisticsCompanyList(shipmentListResult.stream().map(StockoutShipmentSearchResult::getLogisticsCompany).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList()));
            fbtOrderShipShipmentInfoDtoList.add(dto);
        });
        message.setFbtOrderShipShipmentInfoDtoList(fbtOrderShipShipmentInfoDtoList);
    }

    /**
     * 拣货单完成同步(finish_partial_pick_topic)
     */
    public void finishErpPartialPick(List<String> stockoutOrderNos) {
        List<StockoutOrderEntity> stockoutOrderEntityList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos);
        for (StockoutOrderEntity stockoutOrderEntity : stockoutOrderEntityList) {
            if (!StockoutOrderStatusEnum.READY_DELIVERY.name().equals(stockoutOrderEntity.getStatus())) {
                continue;
            }
            // 出库类型不同，request的取值不一样
            ErpFinishPartialPickRequest request = stockoutTypeFactory.differType(stockoutOrderEntity).buildFinishPartialPickRequest(stockoutOrderEntity);
            LocationWrapperMessage<ErpFinishPartialPickRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), request);
            producer.sendLongMessage(KafkaConstant.FINISH_PARTIAL_PICK_BUSINESS_MARK, KafkaConstant.FINISH_PARTIAL_PICK_TOPIC, message);
        }
    }

    public void finishErpPartialPickWithoutBatch(List<String> stockoutOrderNos) {
        List<StockoutOrderEntity> stockoutOrderEntityList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos);
        for (StockoutOrderEntity stockoutOrderEntity : stockoutOrderEntityList) {
            if (!StockoutOrderStatusEnum.READY_DELIVERY.name().equals(stockoutOrderEntity.getStatus())) {
                continue;
            }
            // 出库类型不同，request的取值不一样
            ErpFinishPartialPickRequest request = overseaStockoutService.buildFinishPartialPickRequest(stockoutOrderEntity);
            LocationWrapperMessage<ErpFinishPartialPickRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), request);
            producer.sendLongMessage(KafkaConstant.FINISH_PARTIAL_PICK_BUSINESS_MARK, KafkaConstant.FINISH_PARTIAL_PICK_TOPIC, message);
        }
    }

    /**
     * 构造同步erp拣货单 request
     *
     * @param stockoutOrderEntity     出库单
     * @param taskEntity              分拣任务
     * @param splitTaskItemEntityList 分拣任务明细
     * @return request
     */
    public ErpFinishPartialPickRequest buildErpFinishPartialPickRequest(StockoutOrderEntity stockoutOrderEntity, StockoutBatchSplitTaskEntity taskEntity, List<StockoutBatchSplitTaskItemEntity> splitTaskItemEntityList) {
        List<ErpFinishPartialPickItemRequest> itemRequestList = new LinkedList<>();
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        //获取对应库位的erpSpaceId
        List<StockoutOrderItemErpSpace> stockoutOrderItemErpSpaces = bdErpSpaceMappingService.getBaseMapper().getErpSpaceIdByStockoutOrderItem(stockoutOrderItemEntityList.stream().map(StockoutOrderItemEntity::getStockoutOrderItemId).collect(Collectors.toList()));
        Map<Integer, List<StockoutOrderItemErpSpace>> collect = stockoutOrderItemErpSpaces.stream().collect(Collectors.groupingBy(StockoutOrderItemErpSpace::getStockoutOrderItemId));

        for (StockoutOrderItemEntity stockoutOrderItemEntity : stockoutOrderItemEntityList) {
            Integer scanQty;
            if (taskEntity.getBatchSplitType().equals(StockoutSortingTypeEnum.AUTO_MACHINE_SORT.name())) {
                scanQty = stockoutOrderItemEntity.getScanQty(); // 装箱数=实际扫描数
            } else {
                StockoutBatchSplitTaskItemEntity taskItemEntity = splitTaskItemEntityList.stream().filter(o -> o.getStockoutOrderItemId().equals(stockoutOrderItemEntity.getStockoutOrderItemId())
                        && o.getScanQty() > 0).findFirst().orElse(null);
                scanQty = taskItemEntity != null ? taskItemEntity.getScanQty() : 0;
            }
            List<StockoutOrderItemErpSpace> itemErpSpaces = collect.get(stockoutOrderItemEntity.getStockoutOrderItemId());
            //加工的默认泉州台商仓
            Integer erpSpaceId = CollectionUtils.isEmpty(itemErpSpaces)
                    ? null : StockConstant.ENABLE.equals(itemErpSpaces.get(0).getIsNeedProcess())
                    ? SpaceAreaMapConstant.getProcessSpaceId(LocationEnum.valueOf(stockoutOrderEntity.getLocation())) : itemErpSpaces.get(0).getErpSpaceId();
            itemRequestList.add(new ErpFinishPartialPickItemRequest(stockoutOrderItemEntity.getOrderItemId(), stockoutOrderItemEntity.getSku(), scanQty, erpSpaceId, stockoutOrderItemEntity.getErpPickItemId()));
        }
        ErpFinishPartialPickRequest request = new ErpFinishPartialPickRequest(stockoutOrderEntity.getErpPickId(), loginInfoService.getUserName(), loginInfoService.getUserName(), loginInfoService.getIpAddress());
        request.setItems(itemRequestList);
        return request;
    }

    /**
     * 构造同步erp拣货单 request
     *
     * @param stockoutOrderEntity 出库单
     * @return request
     */
    public ErpFinishPartialPickRequest buildErpFinishPartialPickRequest(StockoutOrderEntity stockoutOrderEntity) {
        List<ErpFinishPartialPickItemRequest> itemRequestList = stockoutErpPickService.getErpFinishPartialPickItemRequests(stockoutOrderEntity);
        ErpFinishPartialPickRequest request = new ErpFinishPartialPickRequest(stockoutOrderEntity.getErpPickId(), loginInfoService.getUserName(), loginInfoService.getUserName(), loginInfoService.getIpAddress());
        request.setItems(itemRequestList);
        return request;
    }

    /**
     * 海外发货准备, 出库单 --- 》 待发货
     * 该方法负责更新库存、生成装箱清单，并同步信息到商通
     * 1.发货库位+ 存储库位-
     * 2.移除预占信息
     * 3.生成装箱清单，同步erp装箱清单
     * 4.更新出库单状态为待发货
     */
    @Transactional
    public StockoutShipmentEntity readyShipOversea(StockoutOrderEntity stockoutOrder, GetSecondaryNumResponse secondaryNumberResponse) {
        // 更新到发货库位，删除预占
        List<StockPrematchInfoEntity> prematchInfo = stockPrematchInfoService.list(new QueryWrapper<StockPrematchInfoEntity>().lambda()
                .eq(StockPrematchInfoEntity::getStockoutOrderId, stockoutOrder.getStockoutOrderId()));
        if (CollectionUtils.isEmpty(prematchInfo))
            throw new BusinessServiceException("无预占信息，请核对");
        List<StockUpdateRequest> updateRequests = new LinkedList<>();
        BdPositionEntity position = positionService.getDeliverPosition(stockoutOrder.getSpaceId());
        prematchInfo.forEach(prematchInfoEntity -> {
            // 1.发货库位+ 存储库位-
            StockUpdateRequest addRequest = new StockUpdateRequest();
            addRequest.setSku(prematchInfoEntity.getSku());
            addRequest.setPositionCode(position.getPositionCode());
            addRequest.setChangeLogType(StockChangeLogTypeEnum.STOCKOUT_PICKING);
            addRequest.setTypeModule(StockChangeLogTypeModuleEnum.PINGKING);
            addRequest.setQty(prematchInfoEntity.getPrematchQty());
            addRequest.setStockoutOrderNo(stockoutOrder.getStockoutOrderNo());
            StockUpdateRequest reduceRequest = new StockUpdateRequest();
            BeanUtilsEx.copyProperties(addRequest, reduceRequest);
            reduceRequest.setPositionCode(prematchInfoEntity.getPositionCode());
            reduceRequest.setQty(-prematchInfoEntity.getPrematchQty());
            updateRequests.add(addRequest);
            updateRequests.add(reduceRequest);
            stockService.updateStockBatch(updateRequests);
            updateRequests.clear();
        });
        // 2.移除预占信息
        stockPrematchRemoveService.removeByStockoutOrder(Collections.singletonList(stockoutOrder.getStockoutOrderId()), StockoutOrderLogTypeEnum.DELIVERY_CONFIRM);
        // 3.生成装箱清单，同步erp装箱清单
        String oldLogisticsNo = stockoutOrder.getLogisticsNo();
        stockoutOrder.setLogisticsNo(StringUtils.hasText(secondaryNumberResponse.getSecondaryNumber()) ? secondaryNumberResponse.getSecondaryNumber() : stockoutOrder.getLogisticsNo());
        stockoutOrderService.updateById(stockoutOrder);
        StockoutShipmentEntity stockoutShipmentEntity = overseaStockoutService.createShipmentAndSyncErp(null, stockoutOrder, null, secondaryNumberResponse.getPackageWeight(), secondaryNumberResponse.getLogisticsPlatformCarrier());
        stockoutShipmentEntity.setTransferLogisticsNo(StringUtils.hasText(secondaryNumberResponse.getSecondaryNumber()) ? oldLogisticsNo : null);
        if (stockoutShipmentEntity.getWeight() == null && secondaryNumberResponse.getPackageWeight() != null)
            stockoutShipmentEntity.setWeight(secondaryNumberResponse.getPackageWeight());
        shipmentService.updateById(stockoutShipmentEntity);

        // 4.更新出库单状态为待发货
        stockoutOrder.setStatus(StockoutOrderStatusEnum.READY_DELIVERY.name());
        stockoutOrderService.updateById(stockoutOrder);

        return stockoutShipmentEntity;
    }


    /**
     * 海外仓发货，出库单 --- 》 已发货
     * 1. 海外发货准备, 出库单 --- 》 待发货
     * 2. 拣货单完成同步(finish_partial_pick_topic)
     * 3. 装箱清单 / 出库单 发货
     */
    @Transactional
    public void shipOversea(StockoutOrderEntity stockoutOrder, GetSecondaryNumResponse secondaryNumberResponse) {
        StockoutShipmentEntity stockoutShipmentEntity = readyShipOversea(stockoutOrder, secondaryNumberResponse);
        // 1. 拣货单完成同步(finish_partial_pick_topic)
        finishErpPartialPick(Collections.singletonList(stockoutOrder.getStockoutOrderNo()));
        StockoutShipmentConfirmUpdateRequest shipRequest = new StockoutShipmentConfirmUpdateRequest();
        shipRequest.setShipmentIds(Collections.singletonList(stockoutShipmentEntity.getShipmentId()));
        shipRequest.setStockoutOrderNos(Collections.singletonList(stockoutOrder.getStockoutOrderNo()));
        shipRequest.setLogisticsCompany(stockoutShipmentEntity.getLogisticsCompany());
        // 2. 装箱清单 / 出库单 发货
        shipmentConfirmService.updateShipmentConfirm(shipRequest);
        // 海外仓订单发货结束后，更新海外仓订单状态为已发货，并同步物流单号
        updateOverseasWarehouseOrderAfterShipped(stockoutOrder, stockoutShipmentEntity);
    }


    // 出库单快速发货，不生成波次，直接生成装箱清单，然后直接出库
    @Transactional
    @JLock(keyConstant = "quickShip", lockKey = "#entity.stockoutOrderNo")
    public void quickShip(StockoutOrderEntity entity) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderId(entity.getStockoutOrderId());
        if (StrUtil.equalsAny(stockoutOrder.getStatus(), StockoutOrderStatusEnum.CANCELLING.name(), StockoutOrderStatusEnum.CANCELLED.name())) {
            throw new BusinessServiceException("取消的出库单无法发货");
        }
        if (StrUtil.equals(stockoutOrder.getStatus(), StockoutOrderStatusEnum.DELIVERED.name())) {
            return;
        }
        StockoutShipmentEntity stockoutShipmentEntity = readyShipOversea(stockoutOrder, new GetSecondaryNumResponse());
        finishErpPartialPickWithoutBatch(Collections.singletonList(stockoutOrder.getStockoutOrderNo()));
        if (StrUtil.equals(TenantContext.getTenant(), LocationEnum.MISI.name())) {
            return;
        }
        StockoutShipmentConfirmUpdateRequest shipRequest = new StockoutShipmentConfirmUpdateRequest();
        shipRequest.setShipmentIds(Collections.singletonList(stockoutShipmentEntity.getShipmentId()));
        shipRequest.setStockoutOrderNos(Collections.singletonList(stockoutOrder.getStockoutOrderNo()));
        shipRequest.setLogisticsCompany(stockoutOrder.getLogisticsCompany());
        // 2. 装箱清单 / 出库单 发货
        shipmentConfirmService.updateShipmentConfirm(shipRequest);
    }

    /**
     * 海外仓订单发货结束后，更新海外仓订单状态为已发货，并同步装箱清单的物流单号
     *
     * @param stockoutOrderEntity    出库单实体
     * @param stockoutShipmentEntity
     */
    private void updateOverseasWarehouseOrderAfterShipped(StockoutOrderEntity stockoutOrderEntity, StockoutShipmentEntity stockoutShipmentEntity) {
        try {
            // 查找对应的海外仓订单
            StockoutOverseasWarehouseOrderEntity overseasOrder = overseasWarehouseOrderService.findByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
            if (overseasOrder != null) {
                // 更新海外仓订单状态为已发货
                overseasWarehouseOrderService.updateStatusAndOverseasOrderNo(overseasOrder, OverseasWarehouseOrderStatusEnum.SHIPPED.name(), null, stockoutShipmentEntity.getLogisticsNo());
                stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.OVERSEA_SHIP_RESULT,
                        String.format("海外仓订单发货完成，状态已更新为已发货，物流单号：%s", stockoutShipmentEntity.getLogisticsNo()));
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            LOGGER.error("更新海外仓订单状态失败，出库单号：{}，错误信息：{}", stockoutOrderEntity.getStockoutOrderNo(), e.getMessage(), e);
            stockoutOrderLogService.addLog(stockoutOrderEntity.getStockoutOrderNo(), StockoutOrderLogTypeEnum.OVERSEA_SHIP_RESULT,
                "海外仓订单发货完成，但更新海外仓订单状态失败：" + e.getMessage());
        }
    }
}
