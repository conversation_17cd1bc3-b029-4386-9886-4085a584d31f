package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.KafkaConstant;
import com.nsy.api.wms.enumeration.StockoutOrderLogTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentBindCustomerShipmentRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentConfirmUpdateRequest;
import com.nsy.api.wms.response.stockout.AsyncProcessFlowResult;
import com.nsy.api.wms.response.stockout.StockoutShipmentLogisticsRequest;
import com.nsy.wms.business.domain.bo.mq.LocationWrapperMessage;
import com.nsy.wms.business.service.async.AsyncProcessFlowService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentErpPickingBoxEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 装箱清单相关操作类
 * HXD
 * 2023/5/8
 **/
@Service
public class StockoutShipmentOperateService {
    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutShipmentOperateService.class);


    @Resource
    LoginInfoService loginInfoService;
    @Resource
    AsyncProcessFlowService asyncProcessFlowService;
    @Autowired
    private StockoutShipmentService shipmentService;
    @Autowired
    private StockoutShipmentConfirmService shipmentConfirmService;
    @Autowired
    private StockoutShipmentItemService itemService;
    @Resource
    private StockoutOrderService stockoutOrderService;
    @Resource
    private StockoutOrderLogService orderLogService;
    @Resource
    private StockoutShipmentAmazonRelationService amazonRelationService;
    @Resource
    private StockoutShipmentErpPickingBoxService erpPickingBoxService;


    // 异步-自提发货
    @Transactional
    @JLock(keyConstant = "asyncSelfGetLogistics", lockKey = "#request.shipmentBoxCodeList[0]")
    public AsyncProcessFlowResult asyncSelfGetLogistics(StockoutShipmentLogisticsRequest request) {
        LocationWrapperMessage<StockoutShipmentLogisticsRequest> message = new LocationWrapperMessage<>(TenantContext.getTenant(), loginInfoService.getName(), request);
        return asyncProcessFlowService.createFlow(KafkaConstant.STOCKOUT_SHIPMENT_SELF_SHIPPED, KafkaConstant.STOCKOUT_SHIPMENT_SELF_SHIPPED_MARK, message, request.getShipmentBoxCodeList().get(0));
    }

    /**
     * 勾选装箱清单，直接发货
     * <AUTHOR>
     * 2023-10-26
     */
    @Transactional
    public void quickShip(IdListRequest request) {
        LOGGER.info("装箱清单-快速发货：{}", JsonMapper.toJson(request.getIdList()));
        List<StockoutShipmentEntity> stockoutShipmentEntities = shipmentService.listByIds(request.getIdList());
        if (stockoutShipmentEntities.stream().anyMatch(item -> !StringUtils.hasText(item.getLogisticsCompany()) || !StringUtils.hasText(item.getLogisticsNo()))) {
            throw new BusinessServiceException("物流公司和物流单号均要填写！");
        }
        stockoutShipmentEntities.forEach(item -> {
            StockoutShipmentConfirmUpdateRequest confirmUpdateRequest = new StockoutShipmentConfirmUpdateRequest();
            confirmUpdateRequest.setShipmentIds(Collections.singletonList(item.getShipmentId()));
            confirmUpdateRequest.setLogisticsNo(item.getLogisticsNo());
            confirmUpdateRequest.setLogisticsCompany(item.getLogisticsCompany());
            List<StockoutShipmentItemEntity> byShipmentId = itemService.findByShipmentId(item.getShipmentId());
            confirmUpdateRequest.setStockoutOrderNos(byShipmentId.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList()));
            shipmentConfirmService.updateShipmentConfirm(confirmUpdateRequest);
        });
    }

    public BigDecimal getShipmentSkuWeight(String shipmentBoxCode) {
        BigDecimal weight = shipmentService.getBaseMapper().countSkuWeightByShipmentBoxCode(shipmentBoxCode);
        if (weight == null) {
            return BigDecimal.ZERO;
        }
        return weight;
    }

    /**
     * 用于申请箱贴时：校验每个箱子
     * 其装箱内容（每个sku及其对应的qty）必须以5的倍数存在相同的箱子
     *
     * <AUTHOR>
     * 2025-05-29
     */
    public void checkShipmentMultipleOfFive(String orderNo) {
        List<StockoutShipmentItemEntity> list = itemService.list(new LambdaQueryWrapper<StockoutShipmentItemEntity>()
                .eq(StockoutShipmentItemEntity::getOrderNo, orderNo).eq(StockoutShipmentItemEntity::getIsDeleted, 0));
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessServiceException("订单号" + orderNo + "找不到对应的装箱清单记录");
        }
        List<Integer> shipmentIds = list.stream().map(StockoutShipmentItemEntity::getShipmentId).distinct().collect(Collectors.toList());
        List<StockoutShipmentEntity> shipmentList = shipmentService.listByIds(shipmentIds);
        if (CollectionUtils.isEmpty(shipmentList)) {
            throw new BusinessServiceException("订单号" + orderNo + "找不到对应的装箱清单");
        }
        // 按装箱ID分组装箱明细
        Map<Integer, List<StockoutShipmentItemEntity>> shipmentItemMap = list.stream()
                .collect(Collectors.groupingBy(StockoutShipmentItemEntity::getShipmentId));
        // 创建装箱内容签名到箱子信息的映射
        Map<String, List<String>> contentSignatureBoxesMap = new HashMap<>();
        // 为每个箱子生成内容签名
        for (StockoutShipmentEntity shipment : shipmentList) {
            List<StockoutShipmentItemEntity> items = shipmentItemMap.get(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }
            // 按SKU聚合数量，然后生成装箱内容签名
            Map<String, Integer> skuQtyMap = items.stream()
                    .collect(Collectors.groupingBy(
                            StockoutShipmentItemEntity::getSku,
                            Collectors.summingInt(StockoutShipmentItemEntity::getQty)
                    ));
            // 按sku排序后拼接sku:总数量生成签名
            String contentSignature = skuQtyMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .map(entry -> entry.getKey() + ":" + entry.getValue())
                    .collect(Collectors.joining(","));
            // 收集相同内容签名的箱子编号
            contentSignatureBoxesMap.computeIfAbsent(contentSignature, k -> new ArrayList<>())
                    .add("第" + shipment.getBoxIndex() + "箱-" + shipment.getShipmentBoxCode());
        }
        // 校验每种内容签名的箱子数量必须是5的倍数
        for (Map.Entry<String, List<String>> entry : contentSignatureBoxesMap.entrySet()) {
            List<String> boxCodes = entry.getValue();
            int count = boxCodes.size();
            if (count % 5 != 0) {
                String boxCodesStr = String.join(",", boxCodes);
                throw new BusinessServiceException(String.format("这些箱子不是5的倍数，请检查：【%s】", boxCodesStr));
            }
        }
    }

    /**
     * 绑定客户发货信息
     */
    @Transactional
    public void bindCustomerShipment(StockoutShipmentBindCustomerShipmentRequest request) {
        // 1. 验证装箱清单是否存在
        List<StockoutShipmentEntity> shipmentList = shipmentService.listByIds(request.getShipmentIds());
        if (CollectionUtils.isEmpty(shipmentList)) {
            throw new BusinessServiceException("选择的装箱清单不存在");
        }
        // 验证选择的箱子数量是否匹配
        if (shipmentList.size() != request.getShipmentIds().size()) {
            throw new BusinessServiceException("部分选择的装箱清单不存在");
        }
        // 2. 验证工作区域 - 只允许内贸工作区域的箱子
        validateDomesticWorkspace(shipmentList);
        // 3. 验证装箱状态
        validateShipmentStatus(shipmentList);
        // 4. 删除已存在的绑定关系
        removeExistingRelations(request.getShipmentIds());
        // 5. 创建新的绑定关系
        createAmazonRelations(request, shipmentList);
        // 6. 记录操作日志
        recordOperationLog(request, shipmentList);
    }

    /**
     * 验证是否为内贸工作区域、销售出库类型
     */
    private void validateDomesticWorkspace(List<StockoutShipmentEntity> shipmentList) {
        // 通过装箱清单获取对应的出库单信息来验证工作区域
        List<String> nonDomesticBoxCodes = new ArrayList<>();
        for (StockoutShipmentEntity shipment : shipmentList) {
            // 通过装箱清单明细获取出库单信息
            List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentId(shipment.getShipmentId());
            if (CollectionUtils.isEmpty(itemList)) {
                continue;
            }
            String stockoutOrderNo = itemList.get(0).getStockoutOrderNo();
            StockoutOrderEntity orderEntity = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
            if (orderEntity == null || !StockoutOrderWorkSpaceEnum.DOMESTIC_AREA.name().equals(orderEntity.getWorkspace())) {
                nonDomesticBoxCodes.add(shipment.getShipmentBoxCode());
            }
        }
        if (!nonDomesticBoxCodes.isEmpty()) {
            throw new BusinessServiceException("以下箱子不属于内贸工作区域，无法绑定：" + String.join("、", nonDomesticBoxCodes));
        }
    }

    /**
     * 验证装箱状态
     */
    private void validateShipmentStatus(List<StockoutShipmentEntity> shipmentList) {
        List<String> invalidStatusBoxCodes = new ArrayList<>();
        for (StockoutShipmentEntity shipment : shipmentList) {
            // 只有装箱完成状态的箱子才能绑定
            if (!StockoutShipmentStatusEnum.PACKING_END.name().equals(shipment.getStatus())) {
                invalidStatusBoxCodes.add(shipment.getShipmentBoxCode());
            }
        }
        if (!invalidStatusBoxCodes.isEmpty()) {
            throw new BusinessServiceException("以下箱子状态不允许绑定：" + String.join("、", invalidStatusBoxCodes));
        }
    }

    /**
     * 删除已存在的绑定关系
     */
    private void removeExistingRelations(List<Integer> shipmentIds) {
        LambdaQueryWrapper<StockoutShipmentAmazonRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StockoutShipmentAmazonRelationEntity::getShipmentId, shipmentIds);
        amazonRelationService.remove(wrapper);
    }

    /**
     * 创建亚马逊绑定关系
     */
    private void createAmazonRelations(StockoutShipmentBindCustomerShipmentRequest request, List<StockoutShipmentEntity> shipmentList) {
        List<StockoutShipmentAmazonRelationEntity> relationList = new ArrayList<>(shipmentList.size());
        for (StockoutShipmentEntity shipment : shipmentList) {
            // 获取ERP拣货箱ID
            StockoutShipmentErpPickingBoxEntity pickingBox = erpPickingBoxService.getTopByShipmentId(shipment.getShipmentId());
            StockoutShipmentAmazonRelationEntity relation = new StockoutShipmentAmazonRelationEntity();
            relation.setShipmentId(shipment.getShipmentId());
            relation.setLocation(shipment.getLocation());
            relation.setFbaShipmentId(request.getFbaShipmentId());
            relation.setDestinationFulfillmentCenterId(request.getDestinationFulfillmentCenterId());
            relation.setAmazonReferenceId(request.getAmazonReferenceId());
            relation.setCreateBy(loginInfoService.getName());
            relation.setUpdateBy(loginInfoService.getName());
            if (pickingBox != null) {
                relation.setErpPickingBoxId(pickingBox.getErpPickingBoxId());
                relation.setErpTid(pickingBox.getErpTid());
            }
            relationList.add(relation);
        }
        amazonRelationService.saveBatch(relationList);
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(StockoutShipmentBindCustomerShipmentRequest request, List<StockoutShipmentEntity> shipmentList) {
        for (StockoutShipmentEntity shipment : shipmentList) {
            LOGGER.info("{}绑定客户发货信息: {}", loginInfoService.getName(), String.format("箱号: %s, FBA Shipment ID: %s", shipment.getShipmentBoxCode(), request.getFbaShipmentId()));
            List<StockoutShipmentItemEntity> itemList = itemService.findByShipmentId(shipment.getShipmentId());
            List<String> collect = itemList.stream().map(StockoutShipmentItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
            collect.forEach(orderNo -> orderLogService.addLog(orderNo, StockoutOrderLogTypeEnum.BIND_CUSTOMER_SHIPMENT, String.format("箱号: %s, FBA Shipment ID: %s", shipment.getShipmentBoxCode(), request.getFbaShipmentId())));
        }
    }
}
