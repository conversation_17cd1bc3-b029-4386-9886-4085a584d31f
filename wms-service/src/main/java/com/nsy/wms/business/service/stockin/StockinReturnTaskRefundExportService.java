package com.nsy.wms.business.service.stockin;

import com.alibaba.fastjson.JSONObject;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.stockin.StockinReturnNatureEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.StockinReturnProductTaskListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductTaskListItemResponse;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class StockinReturnTaskRefundExportService implements IDownloadService {
    @Resource
    StockinReturnProductTaskItemService taskItemService;

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_FACTORY_REFUND_RETURN_TASK_DETAIL;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();

        StockinReturnProductTaskListRequest pageRequest = JSONObject.parseObject(request.getRequestContent(), StockinReturnProductTaskListRequest.class);
        pageRequest.setPageIndex(request.getPageIndex());
        pageRequest.setPageSize(request.getPageSize());
        pageRequest.setReturnNature(StockinReturnNatureEnum.REFUND_RETURN.name());
        pageRequest.setLocation(TenantContext.getTenant());
        PageResponse<StockinReturnProductTaskListItemResponse> pageResponse = taskItemService.getExportTaskListItem(pageRequest);
        response.setTotalCount(pageResponse.getTotalCount());
        response.setDataJsonStr(JsonMapper.toJson(pageResponse.getContent()));

        return response;
    }
}
