package com.nsy.wms.business.service.bd;

import com.nsy.api.wms.constants.IsDeletedConstant;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.wms.repository.entity.bd.BdLackDeliveryRuleEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdLackDeliveryRuleMapper;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 月台预约规则
 */
@Service
public class BdLackDeliveryRuleService extends ServiceImpl<BdLackDeliveryRuleMapper, BdLackDeliveryRuleEntity> {

    @Autowired
    BdChangeLogService changeLogService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;


    /**
     * 获取店铺+工作区域对应的发货规则阀值
     *
     * @param storeId   店铺id
     * @param workspace 工作区域
     * @return
     */
    public BdLackDeliveryRuleEntity getRuleValue(Integer storeId, String workspace) {
        LambdaQueryWrapper<BdLackDeliveryRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(storeId)) {
            queryWrapper.eq(BdLackDeliveryRuleEntity::getStoreId, storeId);
        }
        if (StringUtils.hasText(workspace)) {
            queryWrapper.eq(BdLackDeliveryRuleEntity::getWorkspace, workspace);
        }
        queryWrapper.eq(BdLackDeliveryRuleEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        return this.baseMapper.selectOne(queryWrapper);
    }

    /**
     * 获取部门的发货规则阈值
     *
     */
    public List<BdLackDeliveryRuleEntity> getByBusinessType(List<String> businessTypeList) {
        LambdaQueryWrapper<BdLackDeliveryRuleEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (!businessTypeList.isEmpty()) {
            queryWrapper.in(BdLackDeliveryRuleEntity::getBusinessType, businessTypeList);
        }
        queryWrapper.eq(BdLackDeliveryRuleEntity::getIsDeleted, IsDeletedConstant.NOT_DELETED);
        return this.baseMapper.selectList(queryWrapper);
    }

}
