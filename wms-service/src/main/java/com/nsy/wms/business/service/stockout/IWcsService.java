package com.nsy.wms.business.service.stockout;

import com.nsy.api.wms.request.stockout.NBSAddWaveRequest;
import com.nsy.api.wms.response.stockout.WcsResult;

public interface IWcsService {

    /**
     * 判断自己是否支持处理该波次
     */
    boolean isSupport(String wmsBatchId);

    /**
     * 获取分拣信息
     */
    WcsResult handleByNbsGetWave(NBSAddWaveRequest nbsAddWaveRequest);

    /**
     * 分拣完成
     */
    void handleByNbsWaveComplete(NBSAddWaveRequest nbsAddWaveRequest);
}
