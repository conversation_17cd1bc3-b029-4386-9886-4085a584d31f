package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.constants.ProcessConstant;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskInfo;
import com.nsy.api.wms.domain.stockout.StockoutPickingTaskItemInfo;
import com.nsy.api.wms.enumeration.stockout.PrematchProcessTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.request.stockout.StockoutOrderItemAddRequest;
import com.nsy.wms.business.service.bd.BdPickingTaskRuleService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.repository.entity.bd.BdPickingTaskRuleItemEntity;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockPrematchInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemProcessInfoEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderItemProcessInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockoutOrderItemProcessInfoService extends ServiceImpl<StockoutOrderItemProcessInfoMapper, StockoutOrderItemProcessInfoEntity> {

    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutOrderProcessImageInfoService processImageInfoService;
    @Autowired
    ProductSpecInfoService productSpecInfoService;
    @Autowired
    BdPickingTaskRuleService pickingTaskRuleService;
    @Autowired
    BdPositionService positionService;

    /**
     * 出库单明细加工信息
     */
    public void addByStockoutOrderItemInfo(List<StockoutOrderItemAddRequest> outOrderItemList, StockoutOrderEntity stockoutOrderEntity) {
        List<StockoutOrderItemAddRequest> needProcessList = outOrderItemList.stream().filter(o -> o.getIsProcess().equals(1)).collect(Collectors.toList());
        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByStockoutOrderId(stockoutOrderEntity.getStockoutOrderId());
        Set<String> orderItemIdSet = new HashSet<>(16);
        for (StockoutOrderItemAddRequest processItem : needProcessList) {
            //订单id + 库位编号维度
            String key = processItem.getOrderItemId() + processItem.getPositionCode();
            if (orderItemIdSet.contains(key)) {
                continue;
            }
            orderItemIdSet.add(key);
            StockoutOrderItemEntity stockoutOrderItem = stockoutOrderItemEntityList.stream()
                    .filter(o -> o.getOrderItemId().equals(processItem.getOrderItemId())
                            && o.getIsNeedProcess().equals(1)
                            && o.getPositionCode().equals(processItem.getPositionCode()))
                    .findFirst().orElse(null);
            if (stockoutOrderItem == null)
                throw new BusinessServiceException(String.format("未找到出库单加工明细：【%s】", processItem.getSku()));
            // 元素图片不能为空
            if (CollectionUtils.isEmpty(processItem.getProcessInfo().getElementInfos())) {
                throw new BusinessServiceException(String.format("元素图片不能为空：【%s】", processItem.getSku()));
            }
            StockoutOrderItemProcessInfoEntity itemProcessInfoEntity = new StockoutOrderItemProcessInfoEntity();
            itemProcessInfoEntity.setStockoutOrderItemId(stockoutOrderItem.getStockoutOrderItemId());
            itemProcessInfoEntity.setOrderNo(stockoutOrderItem.getOrderNo());
            itemProcessInfoEntity.setOrderItemId(stockoutOrderItem.getOrderItemId());
            itemProcessInfoEntity.setCustomProcess(processItem.getProcessInfo().getCustomProcess());
            itemProcessInfoEntity.setCustomType(processItem.getProcessInfo().getCustomType());
            itemProcessInfoEntity.setBaseSku(processItem.getProcessInfo().getBaseSku());
            itemProcessInfoEntity.setCustomerSku(processItem.getProcessInfo().getCustomerSku());
            itemProcessInfoEntity.setPatternSpecificationNum(buildPatternSpecificationNum(processItem.getProcessInfo().getPatternSpecificationNum()));
            itemProcessInfoEntity.setSupplierMaterials(processItem.getProcessInfo().getSupplierMaterials());
            itemProcessInfoEntity.setHoleNum(processItem.getProcessInfo().getHoleNum());
            this.save(itemProcessInfoEntity);
            processImageInfoService.addByProcessItem(processItem.getProcessInfo(), itemProcessInfoEntity.getStockoutItemProcessInfoId());
        }
    }

    private String buildPatternSpecificationNum(String patternSpecificationNum) {
        if (com.nsy.api.core.apicore.util.StringUtils.hasText(patternSpecificationNum)) {
            // 图案规格最大长度备注
            int maxLength = 100;
            if (patternSpecificationNum.length() > maxLength) {
                return patternSpecificationNum.substring(0, maxLength);
            } else {
                return patternSpecificationNum;
            }
        } else {
            return "";
        }
    }


    /**
     * 生成加工拣货任务
     */
    public List<StockoutPickingTaskInfo> createProcessPickingTask(String stockoutType, BdPickingTaskRuleItemEntity ruleItem, List<StockPrematchInfoEntity> processStockPrematchInfoList) {
        List<StockoutPickingTaskInfo> processTaskInfos = pickingTaskRuleService.buildByOneRuleItem(ruleItem, processStockPrematchInfoList);
        processTaskInfos.forEach(stockoutPickingTaskInfo -> stockoutPickingTaskInfo.setPrematchProcessType(PrematchProcessTypeEnum.PROCESS));
        // 非轻定制的加工类型要生成第二次拣货（备货加工）
        if (!StockoutOrderTypeEnum.LIGHT_CUSTOMIZATION_DELIVERY.name().equalsIgnoreCase(stockoutType)) {
            addProcessSecondPickingTaskBySubBatch(processTaskInfos);
        }
        return processTaskInfos;
    }

    private void addProcessSecondPickingTaskBySubBatch(List<StockoutPickingTaskInfo> processTaskInfos) {
        List<StockoutPickingTaskItemInfo> lightProcessTaskItem = new ArrayList<>();
        List<StockoutPickingTaskInfo> lightProcessTaskResult = new ArrayList<>(processTaskInfos.size());
        processTaskInfos.forEach(o -> lightProcessTaskItem.addAll(o.getItemInfoList().stream().filter(it -> StringUtils.hasText(it.getCustomerSku()) && !it.getCustomerSku().equals(it.getSku())).collect(Collectors.toList())));
        if (CollectionUtils.isEmpty(lightProcessTaskItem))
            return;
        Map<String, ProductSpecInfoEntity> specMap = productSpecInfoService.findAllBySkuIn(lightProcessTaskItem.stream().map(StockoutPickingTaskItemInfo::getCustomerSku).collect(Collectors.toList())).stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, Function.identity()));

        BdPositionEntity position = positionService.getPositionByCode(ProcessConstant.AFTER_PROCESS_POSITION_CODE);
        if (position == null) {
            throw new BusinessServiceException("未找到加工发货库位，请核对此库位是否存在：" + ProcessConstant.AFTER_PROCESS_POSITION_CODE);
        }
        for (StockoutPickingTaskInfo processTaskInfo : processTaskInfos) {
            List<StockoutPickingTaskItemInfo> collect = processTaskInfo.getItemInfoList().stream().filter(it -> StringUtils.hasText(it.getCustomerSku()) && !it.getCustomerSku().equals(it.getSku())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect))
                continue;
            StockoutPickingTaskInfo taskInfo = new StockoutPickingTaskInfo();
            List<StockoutPickingTaskItemInfo> taskItemInfoList = collect.stream().map(item -> {
                StockoutPickingTaskItemInfo taskItemInfo = new StockoutPickingTaskItemInfo();
                taskItemInfo.setSku(item.getCustomerSku());
                ProductSpecInfoEntity sku = specMap.get(item.getCustomerSku());
                taskItemInfo.setBarcode(sku.getBarcode());
                taskItemInfo.setProductId(sku.getProductId());
                taskItemInfo.setCustomerSku(null);
                taskItemInfo.setSpecId(sku.getSpecId());
                taskItemInfo.setPositionCode(position.getPositionCode());
                taskItemInfo.setPositionId(position.getPositionId());
                taskItemInfo.setSpaceAreaId(position.getSpaceAreaId());
                taskItemInfo.setSpaceAreaName(position.getSpaceAreaName());
                taskItemInfo.setExpectedQty(item.getExpectedQty());
                return taskItemInfo;
            }).collect(Collectors.toList());
            taskInfo.setItemInfoList(taskItemInfoList);
            taskInfo.setPrematchProcessType(PrematchProcessTypeEnum.NORMAL);
            taskInfo.setExpectedQty(taskItemInfoList.stream().mapToInt(StockoutPickingTaskItemInfo::getExpectedQty).sum());
            taskInfo.setPositionQty((int) taskItemInfoList.stream().map(StockoutPickingTaskItemInfo::getPositionId).distinct().count());
            taskInfo.setBatchId(processTaskInfo.getBatchId());
            lightProcessTaskResult.add(taskInfo);
        }
        processTaskInfos.addAll(lightProcessTaskResult);

    }

    public StockoutOrderItemProcessInfoEntity findFirstByStockoutOrderItemId(Integer stockoutOrderItemId) {
        return this.getOne(new LambdaQueryWrapper<StockoutOrderItemProcessInfoEntity>()
                .eq(StockoutOrderItemProcessInfoEntity::getStockoutOrderItemId, stockoutOrderItemId)
                .last("limit 1"));
    }
}
