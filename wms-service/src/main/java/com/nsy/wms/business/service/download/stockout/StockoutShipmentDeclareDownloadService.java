package com.nsy.wms.business.service.download.stockout;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.wms.domain.stockout.DeclareItemInfo;
import com.nsy.api.wms.domain.stockout.ShipmentBoxInfo;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareDepponExport;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareDepponExportItem;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareHaShengExport;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareHaShengExportItem;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareHeBoSiExport;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareHeBoSiExportItem;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareJiuFangExport;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareKaiQiAirExport;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareKaiQiAirExportItem;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareKaiQiExport;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareKaiQiExportItem;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareLinChuangExport;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareLinChuangExportItem;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareYiYouExport;
import com.nsy.api.wms.domain.stockout.ShipmentDeclareYiYouExportItem;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockout.ShipmentDownloadRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.wms.business.domain.bo.stockout.StockoutShipmentDeclareDownloadItemBo;
import com.nsy.wms.business.domain.bo.stockout.StockoutShipmentSpuQtyBo;
import com.nsy.wms.business.manage.tms.TmsCacheService;
import com.nsy.wms.business.manage.tms.response.TmsLogisticsCompany;
import com.nsy.wms.business.service.BrandCommonService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.product.ProductCustomsDeclareCompanyService;
import com.nsy.wms.business.service.product.ProductCustomsDeclareElementService;
import com.nsy.wms.business.service.stockout.StockoutShipmentAmazonRelationService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.repository.entity.product.ProductCustomsDeclareCompanyEntity;
import com.nsy.wms.repository.entity.product.ProductStoreSkuMappingEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentAmazonRelationEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentItemMapper;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StockoutShipmentDeclareDownloadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutShipmentDeclareDownloadService.class);

    @Resource
    private ShipmentAllShippedDownloadService shipmentAllShippedDownloadService;
    @Resource
    private ProductStoreSkuMappingService storeSkuMappingService;
    @Resource
    private StockoutShipmentItemMapper shipmentItemMapper;
    @Resource
    private StockoutShipmentAmazonRelationService shipmentAmazonRelationService;
    @Resource
    private ProductCustomsDeclareCompanyService productCustomsDeclareCompanyService;
    @Resource
    private StockoutShipmentService shipmentService;
    @Resource
    private BrandCommonService brandCommonService;
    @Resource
    private StockoutShipmentAmazonRelationService amazonRelationService;
    @Autowired
    private ProductCustomsDeclareElementService productCustomsDeclareElementService;
    @Resource
    TmsCacheService tmsCacheService;


    public DownloadResponse queryExportData(DownloadRequest request) {
        ShipmentDownloadRequest downloadRequest = JsonMapper.fromJson(request.getRequestContent(), ShipmentDownloadRequest.class);
        DownloadResponse response = new DownloadResponse();
        if (CollectionUtils.isEmpty(downloadRequest.getShipmentIds())) {
            shipmentAllShippedDownloadService.buildFbaShipmentId(downloadRequest, request, response);
        }
        if (CollectionUtils.isEmpty(downloadRequest.getShipmentIds())) {
            return response;
        }
        List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities = shipmentAmazonRelationService.listByShipmentId(downloadRequest.getShipmentIds());
        if (CollectionUtils.isEmpty(amazonRelationEntities)) {
            return response;
        }

        if (QuartzDownloadQueueTypeEnum.WMS_JIUFANG_SHIPMENT_DECLARE.name().equals(request.getType().name())) {
            response = buildExportOfJiuFang(downloadRequest.getShipmentIds(), amazonRelationEntities);
        } else if (QuartzDownloadQueueTypeEnum.WMS_KAIQI_SHIPMENT_DECLARE.name().equals(request.getType().name())) {
            response = buildExportOfKaiQi(downloadRequest.getShipmentIds(), amazonRelationEntities);
        } else if (QuartzDownloadQueueTypeEnum.WMS_LINCHUANG_SHIPMENT_DECLARE.name().equals(request.getType().name())) {
            response = buildExportOfLinChuang(downloadRequest.getShipmentIds(), amazonRelationEntities);
        } else if (QuartzDownloadQueueTypeEnum.WMS_HEBOSI_SHIPMENT_DECLARE.name().equals(request.getType().name())) {
            response = buildExportOfHeBoSi(downloadRequest.getShipmentIds(), amazonRelationEntities);
        } else if (QuartzDownloadQueueTypeEnum.WMS_DEPPON_SHIPMENT_DECLARE_AIR.name().equals(request.getType().name())
                || QuartzDownloadQueueTypeEnum.WMS_DEPPON_SHIPMENT_DECLARE_SEA.name().equals(request.getType().name())) {
            response = buildExportOfDeppon(downloadRequest.getShipmentIds(), amazonRelationEntities);
        } else if (QuartzDownloadQueueTypeEnum.WMS_HASHENG_SHIPMENT_DECLARE.name().equals(request.getType().name())) {
            response = buildExportOfHaSheng(downloadRequest.getShipmentIds(), amazonRelationEntities);
        } else if (QuartzDownloadQueueTypeEnum.WMS_YIYOU_SHIPMENT_DECLARE.name().equals(request.getType().name())) {
            response = buildExportOfYiYou(downloadRequest.getShipmentIds(), amazonRelationEntities);
        } else if (QuartzDownloadQueueTypeEnum.WMS_KAIQI_AIR_SHIPMENT_DECLARE.name().equals(request.getType().name())) {
            response = buildExportOfKaiQiAir(downloadRequest.getShipmentIds(), amazonRelationEntities);
        }


        return response;
    }

    /**
     * 哈盛
     *
     * @param shipmentIdList
     * @return
     */
    private DownloadResponse buildExportOfHaSheng(List<Integer> shipmentIdList, List<StockoutShipmentAmazonRelationEntity> amazonRelationList) {
        DownloadResponse response = new DownloadResponse();
        List<StockoutShipmentDeclareDownloadItemBo> downloadItemBoList = shipmentItemMapper.getShipmentDeclareDownloadItemBoList(shipmentIdList);
        if (CollectionUtils.isEmpty(downloadItemBoList)) {
            return response;
        }
        Map<Integer, List<StockoutShipmentDeclareDownloadItemBo>> downloadItemBoMap = downloadItemBoList.stream().collect(Collectors.groupingBy(StockoutShipmentDeclareDownloadItemBo::getShipmentId));

        ShipmentDeclareHaShengExport export = new ShipmentDeclareHaShengExport();
        export.setSpaceName(amazonRelationList.stream().map(StockoutShipmentAmazonRelationEntity::getDestinationFulfillmentCenterId).distinct().collect(Collectors.joining(",")));

        List<ShipmentDeclareHaShengExportItem> exportItemList = amazonRelationList.stream().collect(Collectors.groupingBy(StockoutShipmentAmazonRelationEntity::getFbaShipmentId))
                .values().stream().map(splitAmazonRelationList -> {
                    AtomicInteger boxIndex = new AtomicInteger();

                    return splitAmazonRelationList.stream().map(amazonRelation -> {
                        boxIndex.set(boxIndex.get() + 1);
                        return buildHaShengExportItem(downloadItemBoMap.get(amazonRelation.getShipmentId()), amazonRelation, boxIndex.get());
                    }).flatMap(Collection::stream).collect(Collectors.toList());
                }).flatMap(Collection::stream).collect(Collectors.toList());

        export.setItemList(exportItemList);
        response.setTotalCount(1L);
        response.setDataJsonStr(JsonMapper.toJson(export));
        LOGGER.info("赫伯斯导出装箱申报信息:{}", response.getDataJsonStr());
        return response;
    }

    public List<ShipmentDeclareHaShengExportItem> buildHaShengExportItem(List<StockoutShipmentDeclareDownloadItemBo> downloadItemBoList, StockoutShipmentAmazonRelationEntity amazonRelationEntity, Integer boxIndex) {
        if (CollectionUtils.isEmpty(downloadItemBoList))
            return new ArrayList<>();

        List<String> sellerSkuList = downloadItemBoList.stream().map(StockoutShipmentDeclareDownloadItemBo::getSellerSku).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        Map<String, String> asinMap = storeSkuMappingService.getSellerSkuList(sellerSkuList).stream().collect(Collectors.toMap(ProductStoreSkuMappingEntity::getStoreSku, ProductStoreSkuMappingEntity::getAsin, (v1, v2) -> v1));

        Map<Integer, List<StockoutShipmentDeclareDownloadItemBo>> downloadItemBoMap = downloadItemBoList.stream().collect(Collectors.groupingBy(StockoutShipmentDeclareDownloadItemBo::getShipmentId));


        return downloadItemBoMap.values().stream().map(tempDownloadItemBoList -> {
            AtomicInteger skuIndex = new AtomicInteger();
            return tempDownloadItemBoList.stream().map(itemBo -> {
                ShipmentDeclareHaShengExportItem exportItem = new ShipmentDeclareHaShengExportItem();
                exportItem.setHsCode(itemBo.getHsCode());
                if (skuIndex.get() == 0) {
                    exportItem.setFbaShipmentId(amazonRelationEntity.getFbaShipmentId());
                    exportItem.setBoxIndex(Integer.toString(boxIndex));
                    exportItem.setBoxWeight(itemBo.getWeight().toString());
                    BigDecimal realBoxWeight = itemBo.getWeight().subtract(BigDecimal.valueOf(0.2));
                    exportItem.setRealBoxWeight(realBoxWeight.toString());
                    String[] boxSizeArray = itemBo.getBoxSize().split("\\*");
                    if (boxSizeArray.length == 3) {
                        int length = Integer.parseInt(boxSizeArray[0]);
                        int width = Integer.parseInt(boxSizeArray[1]);
                        int height = Integer.parseInt(boxSizeArray[2]);
                        BigDecimal volume = BigDecimal.valueOf((long) length * width * height).divide(BigDecimal.valueOf(1000000), 2, BigDecimal.ROUND_HALF_UP);
                        exportItem.setVolume(volume.toString());
                    }
                }

                exportItem.setCustomsDeclareCn(itemBo.getCustomsDeclareCn());
                exportItem.setCustomsDeclareEn(itemBo.getCustomsDeclareEn());
                exportItem.setSku(itemBo.getSellerSku());
                exportItem.setQty(itemBo.getQty().toString());
                exportItem.setUnitPrice(itemBo.getPurchasePrice().toString());
                BigDecimal qtyPrice = itemBo.getPurchasePrice().multiply(BigDecimal.valueOf(itemBo.getQty()));
                exportItem.setQtyPrice(qtyPrice.toString());

                exportItem.setFabricTypeEn(itemBo.getFabricTypeEn());
                exportItem.setBrandName(productCustomsDeclareElementService.findBrand(itemBo.getSpu()));
                exportItem.setImageUrl(itemBo.getImageUrl());
                exportItem.setSellerSku(itemBo.getSellerSku());
                exportItem.setAsin(asinMap.get(itemBo.getSellerSku()));

                skuIndex.set(skuIndex.get() + 1);
                return exportItem;
            }).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 怡友
     *
     * @param shipmentIdList
     * @return
     */
    private DownloadResponse buildExportOfYiYou(List<Integer> shipmentIdList, List<StockoutShipmentAmazonRelationEntity> amazonRelationList) {
        DownloadResponse response = new DownloadResponse();
        List<StockoutShipmentDeclareDownloadItemBo> downloadItemBoList = shipmentItemMapper.getShipmentDeclareDownloadItemBoList(shipmentIdList);
        if (CollectionUtils.isEmpty(downloadItemBoList)) {
            return response;
        }

        ShipmentDeclareYiYouExport export = new ShipmentDeclareYiYouExport();
        export.setSpaceName(amazonRelationList.stream().map(StockoutShipmentAmazonRelationEntity::getDestinationFulfillmentCenterId).distinct().collect(Collectors.joining(",")));

        Map<Integer, List<StockoutShipmentDeclareDownloadItemBo>> itemBoMap = downloadItemBoList.stream().collect(Collectors.groupingBy(StockoutShipmentDeclareDownloadItemBo::getShipmentId));
        Map<String, List<StockoutShipmentAmazonRelationEntity>> fbaShipmentIdRelationMap = amazonRelationList.stream().collect(Collectors.groupingBy(StockoutShipmentAmazonRelationEntity::getFbaShipmentId));
        List<ShipmentDeclareYiYouExportItem> exportItemList = fbaShipmentIdRelationMap.values().stream().map(relationList -> {
            AtomicInteger boxIndex = new AtomicInteger();
            return relationList.stream().map(relation -> {
                boxIndex.set(boxIndex.get() + 1);
                return buildYiYouExportItem(itemBoMap.get(relation.getShipmentId()), relation, boxIndex.get());
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());

        export.setItemList(exportItemList);
        response.setTotalCount(1L);
        response.setDataJsonStr(JsonMapper.toJson(export));
        LOGGER.info("怡友导出装箱申报信息:{}", response.getDataJsonStr());
        return response;
    }


    public List<ShipmentDeclareYiYouExportItem> buildYiYouExportItem(List<StockoutShipmentDeclareDownloadItemBo> downloadItemBoList, StockoutShipmentAmazonRelationEntity amazonRelationEntity, Integer boxIndex) {
        if (CollectionUtils.isEmpty(downloadItemBoList))
            return new ArrayList<>();

        List<String> sellerSkuList = downloadItemBoList.stream().map(StockoutShipmentDeclareDownloadItemBo::getSellerSku).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        Map<String, ProductStoreSkuMappingEntity> asinMap = storeSkuMappingService.getSellerSkuList(sellerSkuList).stream().collect(Collectors.toMap(ProductStoreSkuMappingEntity::getStoreSku, Function.identity(), (v1, v2) -> v1));

        Map<Integer, List<StockoutShipmentDeclareDownloadItemBo>> downloadItemBoMap = downloadItemBoList.stream().collect(Collectors.groupingBy(StockoutShipmentDeclareDownloadItemBo::getShipmentId));
        String boxCode = String.format("%sU%06d", amazonRelationEntity.getFbaShipmentId(), boxIndex);

        return downloadItemBoMap.values().stream()
                .map(tempDownloadItemBoList -> tempDownloadItemBoList.stream().map(itemBo -> {
                    ShipmentDeclareYiYouExportItem exportItem = new ShipmentDeclareYiYouExportItem();
                    exportItem.setBoxCode(boxCode);
                    exportItem.setBoxWeight(itemBo.getWeight().toString());
                    String[] boxSizeArray = itemBo.getBoxSize().split("\\*");
                    if (boxSizeArray.length == 3) {
                        exportItem.setBoxLength(boxSizeArray[0]);
                        exportItem.setBoxWidth(boxSizeArray[1]);
                        exportItem.setBoxHeight(boxSizeArray[2]);
                    }
                    exportItem.setCustomsDeclareEn(itemBo.getCustomsDeclareEn());
                    exportItem.setCustomsDeclareCn(itemBo.getCustomsDeclareCn());
                    exportItem.setUnitPrice("6");
                    exportItem.setQty(itemBo.getQty().toString());
                    exportItem.setHsCode(itemBo.getHsCode());
                    exportItem.setReferenceId(amazonRelationEntity.getAmazonReferenceId());
                    exportItem.setBrandName(productCustomsDeclareElementService.findBrand(itemBo.getSpu()));
                    exportItem.setSku(itemBo.getSku());
                    exportItem.setFabricType(itemBo.getFabricType());
                    exportItem.setImageUrl(itemBo.getImageUrl());
                    ProductStoreSkuMappingEntity productStoreSkuMapping = asinMap.get(itemBo.getSellerSku());
                    exportItem.setAsin(Objects.isNull(productStoreSkuMapping) ? "" : productStoreSkuMapping.getAsin());
                    exportItem.setFnsku(Objects.isNull(productStoreSkuMapping) ? "" : productStoreSkuMapping.getStoreBarcode());
                    exportItem.setSellerBarcode(Objects.isNull(productStoreSkuMapping) ? "" : productStoreSkuMapping.getStoreBarcode());
                    exportItem.setSpinType(itemBo.getSpinType());
                    exportItem.setElementValue(itemBo.getElementValue());
                    return exportItem;
                }).collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private DownloadResponse buildExportOfDeppon(List<Integer> shipmentIds, List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities) {
        DownloadResponse response = new DownloadResponse();
        List<StockoutShipmentDeclareDownloadItemBo> downloadItemBoList = shipmentItemMapper.getDepponShipmentDeclareDownloadItemBoList(shipmentIds);
        if (CollectionUtils.isEmpty(downloadItemBoList)) {
            return response;
        }
        List<String> spaceNameList = amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getDestinationFulfillmentCenterId).distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        Map<String, List<StockoutShipmentDeclareDownloadItemBo>> downloadItemBoMap = downloadItemBoList.stream().collect(Collectors.groupingBy(StockoutShipmentDeclareDownloadItemBo::getFbaShipmentId));
        List<String> sellerSkuList = downloadItemBoList.stream().map(StockoutShipmentDeclareDownloadItemBo::getSellerSku).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        Map<String, String> asinMap = storeSkuMappingService.getSellerSkuList(sellerSkuList).stream().collect(Collectors.toMap(ProductStoreSkuMappingEntity::getStoreSku, ProductStoreSkuMappingEntity::getAsin, (v1, v2) -> v1));
        ShipmentDeclareDepponExport export = new ShipmentDeclareDepponExport();
        export.setSpaceName(StrUtil.join("、", spaceNameList));
        final BigDecimal[] totalCount = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        // 支持多票导出,且按箱序排列
        buildExportItem(downloadItemBoMap, asinMap, export, totalCount);
        export.setTotalBoxWeight(totalCount[0].toString());
        export.setTotalQty(totalCount[1].toString());
        export.setTotalQtyPrice(totalCount[2].toString());
        response.setTotalCount(1L);
        response.setDataJsonStr(JsonMapper.toJson(export));
        LOGGER.info("德邦导出装箱申报信息:{}", response.getDataJsonStr());
        return response;
    }

    private void buildExportItem(Map<String, List<StockoutShipmentDeclareDownloadItemBo>> downloadItemBoMap, Map<String, String> asinMap, ShipmentDeclareDepponExport export, BigDecimal[] totalCount) {
        downloadItemBoMap.forEach((key, value) -> {
            List<StockoutShipmentDeclareDownloadItemBo> sortBoxInfo = value.stream().sorted(Comparator.comparing(StockoutShipmentDeclareDownloadItemBo::getBoxIndex)).collect(Collectors.toList());
            Set<Integer> shipmentIdSet = new HashSet<>();
            // 每一票都从1开始
            AtomicInteger index = new AtomicInteger(1);
            sortBoxInfo.forEach(dto -> {
                // 每个箱子只展示第一个sku的箱子信息
                boolean newShipment = !shipmentIdSet.contains(dto.getShipmentId());
                // 每一票的明细sku信息
                ShipmentDeclareDepponExportItem exportItem = new ShipmentDeclareDepponExportItem();
                if (newShipment) {
                    exportItem.setFbaShipmentId(dto.getFbaShipmentId());
                    exportItem.setBoxNum(String.valueOf(index.getAndIncrement()));
                    exportItem.setBoxCount("1");
                    exportItem.setDestinationFulfillmentCenterId(dto.getDestinationFulfillmentCenterId());
                    exportItem.setAmazonReferenceId(dto.getAmazonReferenceId());
                    exportItem.setBoxWeight(String.valueOf(dto.getWeight()));
                    exportItem.setVolumeWeight(String.valueOf(dto.getWeight().subtract(BigDecimal.ONE)));
                    totalCount[0] = totalCount[0].add(dto.getWeight());
                }

                String[] boxSizeArray = dto.getBoxSize().split("\\*");
                if (boxSizeArray.length == 3) {
                    int length = Integer.parseInt(boxSizeArray[0]);
                    int width = Integer.parseInt(boxSizeArray[1]);
                    int height = Integer.parseInt(boxSizeArray[2]);
                    BigDecimal volume = BigDecimal.valueOf((long) length * width * height).divide(BigDecimal.valueOf(1000000), 2, BigDecimal.ROUND_HALF_UP);
                    exportItem.setBoxVolume(volume.toString());
                    if (newShipment) {
                        exportItem.setBoxLength(String.valueOf(length));
                        exportItem.setBoxWidth(String.valueOf(width));
                        exportItem.setBoxHeight(String.valueOf(height));
                    }
                }
                exportItem.setSku(dto.getSku());
                exportItem.setSellerSku(dto.getSellerSku());
                exportItem.setCustomsDeclareCn(dto.getCustomsDeclareCn());
                exportItem.setCustomsDeclareEn(dto.getCustomsDeclareEn());
                exportItem.setHsCode(dto.getHsCode());
                exportItem.setQty(dto.getQty().toString());
                exportItem.setUnitPrice(dto.getPurchasePrice().toString());
                exportItem.setQtyPrice(String.valueOf(dto.getPurchasePrice().multiply(BigDecimal.valueOf(dto.getQty()))));
                exportItem.setFabricTypeEn(dto.getFabricTypeEn());
                exportItem.setAsin(asinMap.get(dto.getSellerSku()));
                exportItem.setBrandName(productCustomsDeclareElementService.findBrand(dto.getSpu()));
                exportItem.setImageUrl(dto.getImageUrl());
                totalCount[1] = totalCount[1].add(BigDecimal.valueOf(dto.getQty()));
                totalCount[2] = totalCount[2].add(new BigDecimal(exportItem.getQtyPrice()));
                export.getItemList().add(exportItem);
                shipmentIdSet.add(dto.getShipmentId());
            });
        });
    }


    /**
     * 林创
     *
     * @param shipmentIdList
     * @return
     */
    private DownloadResponse buildExportOfHeBoSi(List<Integer> shipmentIdList, List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities) {
        DownloadResponse response = new DownloadResponse();
        List<StockoutShipmentDeclareDownloadItemBo> downloadItemBoList = shipmentItemMapper.getShipmentDeclareDownloadItemBoList(shipmentIdList);
        if (CollectionUtils.isEmpty(downloadItemBoList)) {
            return response;
        }
        Map<Integer, StockoutShipmentAmazonRelationEntity> amazonRelationMap = amazonRelationEntities.stream().collect(Collectors.toMap(StockoutShipmentAmazonRelationEntity::getShipmentId, Function.identity(), (v1, v2) -> v1));
        StockoutShipmentAmazonRelationEntity firstAmazonRelationEntity = amazonRelationEntities.get(0);
        Map<Integer, List<StockoutShipmentDeclareDownloadItemBo>> downloadItemBoMap = downloadItemBoList.stream().collect(Collectors.groupingBy(StockoutShipmentDeclareDownloadItemBo::getShipmentId));

        ShipmentDeclareHeBoSiExport export = new ShipmentDeclareHeBoSiExport();
        export.setSpaceName(firstAmazonRelationEntity.getDestinationFulfillmentCenterId());
        export.setToday(DateUtil.format(new Date(), "yyyy/M/dd"));
        final BigDecimal[] totalCount = {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
        List<ShipmentDeclareHeBoSiExportItem> exportItemList = downloadItemBoMap.entrySet()
                .stream().map(entry -> buildHeboSiExportItem(entry.getValue(), amazonRelationMap.get(entry.getKey()), totalCount))
                .flatMap(Collection::stream).collect(Collectors.toList());
        export.setItemList(exportItemList);
        export.setTotalQty(totalCount[0].toString());
        export.setTotalUnitPrice(totalCount[1].toString());
        export.setTotalQtyPrice(totalCount[2].toString());
        export.setTotalBoxNum(totalCount[3].toString());
        export.setTotalBoxWeight(totalCount[4].toString());
        export.setTotalRealBoxWeight(totalCount[5].toString());
        export.setTotalVolume(totalCount[6].toString());
        response.setTotalCount(1L);
        response.setDataJsonStr(JsonMapper.toJson(export));
        LOGGER.info("赫伯斯导出装箱申报信息:{}", response.getDataJsonStr());
        return response;
    }

    public List<ShipmentDeclareHeBoSiExportItem> buildHeboSiExportItem(List<StockoutShipmentDeclareDownloadItemBo> tempDownloadItemBoList, StockoutShipmentAmazonRelationEntity amazonRelationEntity, BigDecimal[] totalCount) {
        AtomicInteger index = new AtomicInteger();
        return tempDownloadItemBoList.stream().map(itemBo -> {
            ShipmentDeclareHeBoSiExportItem exportItem = new ShipmentDeclareHeBoSiExportItem();
            exportItem.setHsCode(itemBo.getHsCode());
            if (index.get() == 0) {
                exportItem.setFbaShipmentId(amazonRelationEntity.getFbaShipmentId());
                exportItem.setBoxIndex("1");
                exportItem.setBoxWeight(itemBo.getWeight().toString());
                BigDecimal realBoxWeight = itemBo.getWeight().subtract(BigDecimal.valueOf(0.5));
                exportItem.setRealBoxWeight(realBoxWeight.toString());
                String[] boxSizeArray = itemBo.getBoxSize().split("\\*");
                if (boxSizeArray.length == 3) {
                    int length = Integer.parseInt(boxSizeArray[0]);
                    int width = Integer.parseInt(boxSizeArray[1]);
                    int height = Integer.parseInt(boxSizeArray[2]);
                    BigDecimal volume = BigDecimal.valueOf((long) length * width * height).divide(BigDecimal.valueOf(1000000), 2, BigDecimal.ROUND_HALF_UP);
                    exportItem.setVolume(volume.toString());
                    totalCount[6] = totalCount[6].add(volume);
                }
                totalCount[3] = totalCount[3].add(BigDecimal.valueOf(1));
                totalCount[4] = totalCount[4].add(itemBo.getWeight());
                totalCount[5] = totalCount[5].add(realBoxWeight);

            }

            exportItem.setCustomsDeclareCn(itemBo.getCustomsDeclareCn());
            exportItem.setCustomsDeclareEn(itemBo.getCustomsDeclareEn());
            exportItem.setSku(itemBo.getSellerSku());
            exportItem.setQty(itemBo.getQty().toString());
            exportItem.setUnitPrice(itemBo.getPurchasePrice().toString());
            BigDecimal qtyPrice = itemBo.getPurchasePrice().multiply(BigDecimal.valueOf(itemBo.getQty()));
            exportItem.setQtyPrice(qtyPrice.toString());

            exportItem.setFabricTypeEn(itemBo.getFabricTypeEn());
            exportItem.setBrandName(productCustomsDeclareElementService.findBrand(itemBo.getSpu()));
            exportItem.setImageUrl(itemBo.getImageUrl());

            totalCount[0] = totalCount[0].add(BigDecimal.valueOf(itemBo.getQty()));
            totalCount[1] = totalCount[1].add(itemBo.getPurchasePrice());
            totalCount[2] = totalCount[2].add(qtyPrice);
            index.set(index.get() + 1);
            return exportItem;
        }).collect(Collectors.toList());
    }

    /**
     * 凯奇
     *
     * @param shipmentIdList
     * @return
     */
    private DownloadResponse buildExportOfKaiQi(List<Integer> shipmentIdList, List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities) {
        String fbaShipmentId = amazonRelationEntities.get(0).getFbaShipmentId();
        String amazonReferenceId = amazonRelationEntities.get(0).getAmazonReferenceId();
        if (StrUtil.isBlank(amazonReferenceId) || StrUtil.isBlank(fbaShipmentId)) {
            throw new BusinessServiceException("未找到ShipmentId/amazonReferenceId, 请重新选择");
        }
        if (amazonRelationEntities.stream().anyMatch(it -> !StrUtil.equals(it.getFbaShipmentId(), fbaShipmentId) || !StrUtil.equals(it.getAmazonReferenceId(), amazonReferenceId))) {
            throw new BusinessServiceException("找到多个ShipmentID，目前只支持单个ShipmentID导出");
        }

        DownloadResponse response = new DownloadResponse();
        List<ShipmentDeclareKaiQiExportItem> exportItemList = shipmentItemMapper.getDeclareInfoOfKaiQi(shipmentIdList);
        if (CollectionUtils.isEmpty(exportItemList)) {
            return response;
        }

        Map<String, Integer> sumSpuQtyMap = shipmentItemMapper.sumSpuQtyByShipmentIdList(shipmentIdList).stream()
                .collect(Collectors.groupingBy(temp -> temp.getShipmentId() + "_" + temp.getSpu(), Collectors.summingInt(StockoutShipmentSpuQtyBo::getQty)));

        Map<String, String> channelTypeMap = tmsCacheService.getAllLogisticsCompanyList().stream()
                .filter(tmsLogisticsCompany -> StrUtil.isNotEmpty(tmsLogisticsCompany.getLogisticsCompany())
                        && StrUtil.isNotEmpty(tmsLogisticsCompany.getChannelType()))
                .collect(Collectors.toMap(TmsLogisticsCompany::getLogisticsCompany, TmsLogisticsCompany::getChannelType));

        AtomicInteger index = new AtomicInteger(0);
        exportItemList.stream()
                .collect(Collectors.groupingBy(ShipmentDeclareKaiQiExportItem::getShipmentId))
                .values()
                .forEach(tempExportItemList -> {
                    index.set(index.get() + 1);
                    tempExportItemList.forEach(exportItem -> {
                        exportItem.setBoxIndex(index.get());
                        exportItem.setFbaShipmentId(fbaShipmentId);
                        exportItem.setReferenceId(amazonReferenceId);
                        String key = exportItem.getShipmentId() + "_" + exportItem.getSpu();
                        Integer qty = sumSpuQtyMap.get(key);
                        exportItem.setQty(qty);
                        exportItem.setTotalQty(qty);

                        String channelType = channelTypeMap.get(exportItem.getLogisticsCompany());
                        if ("AIR_FREIGHT_FORWARDER".equals(channelType)) {
                            exportItem.setPrice(BigDecimal.valueOf(6));
                        } else if ("SEA_FREIGHT_FORWARDER".equals(channelType)) {
                            exportItem.setPrice(BigDecimal.valueOf(5));
                        } else {
                            exportItem.setPrice(BigDecimal.ZERO);
                        }
                        exportItem.setTotalPrice(exportItem.getPrice().multiply(BigDecimal.valueOf(exportItem.getQty())));
                        if (!StrUtil.contains(exportItem.getBoxSize(), "*")) {
                            exportItem.setBoxSize("0*0*0");
                        }
                        String[] packageDimensions = exportItem.getBoxSize().split("\\*");
                        exportItem.setLength(packageDimensions[0]);
                        exportItem.setWidth(packageDimensions[1]);
                        exportItem.setHeight(packageDimensions[2]);
                        //净重 = 毛重 - 1
                        if (ObjectUtil.isNotNull(exportItem.getRoughWeight())) {
                            exportItem.setActualNetWeight(exportItem.getRoughWeight().subtract(BigDecimal.valueOf(1)));
                        }
                        exportItem.setBoxQty(1);
                        if (!LocationEnum.TAILI.name().equals(TenantContext.getTenant())) {
                            exportItem.setBrandName(brandCommonService.getBrandNameByAreaName(exportItem.getAreaName()));
                        }
                    });
                });

        ShipmentDeclareKaiQiExport export = new ShipmentDeclareKaiQiExport();
        List<StockoutShipmentAmazonRelationEntity> relationEntityList = amazonRelationService.listByShipmentId(shipmentIdList);
        if (!relationEntityList.isEmpty()) {
            export.setSpaceName(relationEntityList.get(0).getDestinationFulfillmentCenterId());
        }

        export.setItemList(exportItemList.stream().sorted(Comparator.comparing(temp -> temp.getBoxIndex() + "_" + temp.getSpu())).collect(Collectors.toList()));
        //设置物流单号
        export.setLogisticsNo(shipmentService.getById(exportItemList.get(0).getShipmentId()).getLogisticsNo());
        export.setTotalQty(exportItemList.stream().mapToInt(ShipmentDeclareKaiQiExportItem::getQty).sum());

        response.setTotalCount(1L);
        response.setDataJsonStr(JsonMapper.toJson(export));
        LOGGER.info("凯琦导出装箱申报信息:{}", response.getDataJsonStr());
        return response;
    }

    /**
     * 林创
     *
     * @param shipmentIdList
     * @return
     */
    private DownloadResponse buildExportOfLinChuang(List<Integer> shipmentIdList, List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities) {
        DownloadResponse response = new DownloadResponse();
        List<ShipmentDeclareLinChuangExportItem> exportItemList = shipmentItemMapper.getDeclareInfoOfLinChuang(shipmentIdList);
        if (CollectionUtils.isEmpty(exportItemList)) {
            return response;
        }

        Map<Integer, StockoutShipmentAmazonRelationEntity> amazonRelationMap = amazonRelationEntities.stream()
                .collect(Collectors.toMap(StockoutShipmentAmazonRelationEntity::getShipmentId, Function.identity(), (v1, v2) -> v1));
        for (ShipmentDeclareLinChuangExportItem exportItem : exportItemList) {
            if (!StrUtil.contains(exportItem.getBoxSize(), "*")) {
                exportItem.setBoxSize("0*0*0");
            }
            String[] packageDimensions = exportItem.getBoxSize().split("\\*");
            exportItem.setLength(packageDimensions[0]);
            exportItem.setWidth(packageDimensions[1]);
            exportItem.setHeight(packageDimensions[2]);
            exportItem.setPrice(BigDecimal.valueOf(6));
            exportItem.setTotalPrice(exportItem.getPrice().multiply(BigDecimal.valueOf(exportItem.getQty())));
            StockoutShipmentAmazonRelationEntity amazonRelationEntity = amazonRelationMap.get(exportItem.getShipmentId());
            if (ObjectUtil.isNotNull(amazonRelationEntity)) {
                exportItem.setFbaShipmentId(amazonRelationEntity.getFbaShipmentId());
            }
            exportItem.setBrandName(productCustomsDeclareElementService.findBrand(exportItem.getSpu()));
        }

        ShipmentDeclareLinChuangExport export = new ShipmentDeclareLinChuangExport();
        List<StockoutShipmentAmazonRelationEntity> relationEntityList = amazonRelationService.listByShipmentId(shipmentIdList);
        if (!relationEntityList.isEmpty()) {
            export.setSpaceName(relationEntityList.get(0).getDestinationFulfillmentCenterId());
        }
        exportItemList = exportItemList.stream().sorted(Comparator.comparing(ShipmentDeclareLinChuangExportItem::getFbaShipmentId)
                .thenComparing(ShipmentDeclareLinChuangExportItem::getBoxIndex)).collect(Collectors.toList());
        //记录旧的进行比对
        String recordFbaShipmentId = "";
        int recordShipmentId = 0;
        int index = 0;
        for (ShipmentDeclareLinChuangExportItem item : exportItemList) {
            if (recordFbaShipmentId.equals(item.getFbaShipmentId()) && recordShipmentId == item.getShipmentId()) {
                item.setBoxIndex(null);
                item.setBoxWeight(null);
                item.setLength(null);
                item.setWidth(null);
                item.setHeight(null);
                continue;
            }

            if (!recordFbaShipmentId.equals(item.getFbaShipmentId())) {
                recordFbaShipmentId = item.getFbaShipmentId();
                recordShipmentId = item.getShipmentId();
                index = 1;
            }

            if (recordShipmentId != item.getShipmentId()) {
                recordShipmentId = item.getShipmentId();
                index += 1;
            }

            item.setBoxIndex(index);
        }

        //一箱一个序号
        //一个箱只要一行重长宽高就可了
        export.setItemList(exportItemList);
        //设置物流单号
        export.setLogisticCompany(exportItemList.get(0).getLogisticsCompany());
        //计算重量
        Map<Integer, List<ShipmentDeclareLinChuangExportItem>> boxWeightMap = exportItemList.stream()
                .collect(Collectors.groupingBy(ShipmentDeclareLinChuangExportItem::getShipmentId));
        export.setTotalWeight(boxWeightMap.values().stream().map(itemList -> itemList.get(0).getBoxWeight()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        export.setTotalBoxNum((int) exportItemList.stream().map(ShipmentDeclareLinChuangExportItem::getShipmentId).distinct().count());
        export.setFbaShipmentIds(exportItemList.stream().map(ShipmentDeclareLinChuangExportItem::getFbaShipmentId).distinct().collect(Collectors.joining(",")));
        response.setTotalCount(1L);
        response.setDataJsonStr(JsonMapper.toJson(export));
        LOGGER.info("林创导出装箱申报信息:{}", response.getDataJsonStr());
        return response;
    }

    /**
     * 九方
     *
     * @param shipmentIdList
     * @return
     */
    private DownloadResponse buildExportOfJiuFang(List<Integer> shipmentIdList, List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities) {
        String fbaShipmentId = amazonRelationEntities.get(0).getFbaShipmentId();
        String amazonReferenceId = amazonRelationEntities.get(0).getAmazonReferenceId();
        if (StrUtil.isBlank(amazonReferenceId) || StrUtil.isBlank(fbaShipmentId)) {
            throw new BusinessServiceException("未找到ShipmentId/amazonReferenceId, 请重新选择");
        }
        if (amazonRelationEntities.stream().anyMatch(it -> !StrUtil.equals(it.getFbaShipmentId(), fbaShipmentId) || !StrUtil.equals(it.getAmazonReferenceId(), amazonReferenceId))) {
            throw new BusinessServiceException("找到多个ShipmentID，目前只支持单个ShipmentID导出");
        }

        DownloadResponse response = new DownloadResponse();

        ShipmentDeclareJiuFangExport export = new ShipmentDeclareJiuFangExport();
        List<DeclareItemInfo> declareInfoOfJiuFang = shipmentItemMapper.getDeclareInfoOfJiuFang(shipmentIdList);
        if (CollectionUtils.isEmpty(declareInfoOfJiuFang)) {
            return response;
        }

        List<StockoutShipmentEntity> shipmentEntities = shipmentService.list(new LambdaQueryWrapper<StockoutShipmentEntity>()
                .in(StockoutShipmentEntity::getShipmentId, shipmentIdList).orderByAsc(StockoutShipmentEntity::getBoxIndex));

        AtomicInteger boxIndex = new AtomicInteger(1);
        for (StockoutShipmentEntity shipmentEntity : shipmentEntities) {
            if (!StrUtil.contains(shipmentEntity.getBoxSize(), "*")) {
                shipmentEntity.setBoxSize("0*0*0");
            }
            String[] packageDimensions = shipmentEntity.getBoxSize().split("\\*");
            ShipmentBoxInfo boxInfo = new ShipmentBoxInfo();
            boxInfo.setWeight(shipmentEntity.getWeight());
            boxInfo.setLength(packageDimensions[0]);
            boxInfo.setWidth(packageDimensions[1]);
            boxInfo.setHeight(packageDimensions[2]);
            boxInfo.setBoxIndex(boxIndex.getAndIncrement());
            List<StockoutShipmentSpuQtyBo> items = shipmentItemMapper.sumSpuQtyByShipmentId(shipmentEntity.getShipmentId());
            boxInfo.setBoxItemSumMap(items.stream().collect(Collectors.groupingBy(StockoutShipmentSpuQtyBo::getSpu, Collectors.summingInt(StockoutShipmentSpuQtyBo::getQty))));
            export.getBoxList().add(boxInfo);
        }
        for (DeclareItemInfo declareItemInfo : declareInfoOfJiuFang) {
            declareItemInfo.setShipmentId(fbaShipmentId);
            declareItemInfo.setReferenceId(amazonReferenceId);
            if (declareItemInfo.getProductCustomsDeclareId() != null) {
                List<ProductCustomsDeclareCompanyEntity> declareCompanyEntities = productCustomsDeclareCompanyService.listByProductCustomsDeclareId(declareItemInfo.getProductCustomsDeclareId());
                if (!CollectionUtils.isEmpty(declareCompanyEntities)) {
                    declareItemInfo.setDeclarePrice(String.valueOf(declareCompanyEntities.get(0).getPrice()));
                }
            }
            if (StrUtil.containsAny(declareItemInfo.getDeclareUnit(), "个", "套", "包", "双", "支", "把", "条", "台", "件", "份", "副")) {
                String[] split = declareItemInfo.getDeclareUnit().split("/");
                if (StrUtil.containsAny(split[0], "个", "套", "包", "双", "支", "把", "条", "台", "件", "份", "副")) {
                    declareItemInfo.setDeclareUnit(split[0]);
                } else {
                    declareItemInfo.setDeclareUnit(split.length >= 2 ? split[0] : "件");
                }
            } else {
                declareItemInfo.setDeclareUnit("件");
            }
            declareItemInfo.setBoxQtyList(new ArrayList<>());
            for (ShipmentBoxInfo boxInfo : export.getBoxList()) {
                declareItemInfo.getBoxQtyList().add(boxInfo.getBoxItemSumMap().get(declareItemInfo.getSpu()));
            }
        }
        export.setDeclareItemList(declareInfoOfJiuFang);

        response.setTotalCount((long) export.getDeclareItemList().size());
        response.setDataJsonStr(JsonMapper.toJson(export));
        LOGGER.info("九方导出装箱申报信息:{}", response.getDataJsonStr());
        return response;
    }


    private DownloadResponse buildExportOfKaiQiAir(List<Integer> shipmentIdList, List<StockoutShipmentAmazonRelationEntity> amazonRelationEntities) {
        DownloadResponse response = new DownloadResponse();
        List<StockoutShipmentDeclareDownloadItemBo> downloadItemBoList = shipmentItemMapper.getShipmentDeclareDownloadItemBoList(shipmentIdList);
        if (CollectionUtils.isEmpty(downloadItemBoList)) {
            return response;
        }
        Map<String, List<StockoutShipmentAmazonRelationEntity>> amazonRelationMap = amazonRelationEntities.stream().collect(Collectors.groupingBy(StockoutShipmentAmazonRelationEntity::getFbaShipmentId));
        StockoutShipmentAmazonRelationEntity firstAmazonRelationEntity = amazonRelationEntities.get(0);
        Map<Integer, List<StockoutShipmentDeclareDownloadItemBo>> downloadItemBoMap = downloadItemBoList.stream().collect(Collectors.groupingBy(StockoutShipmentDeclareDownloadItemBo::getShipmentId));

        ShipmentDeclareKaiQiAirExport export = new ShipmentDeclareKaiQiAirExport();
        export.setSpaceName(firstAmazonRelationEntity.getDestinationFulfillmentCenterId());
        export.setTotalBoxNum(String.valueOf(shipmentIdList.stream().distinct().count()));
        List<String> fbaShipmentIdList = amazonRelationEntities.stream().map(StockoutShipmentAmazonRelationEntity::getFbaShipmentId).distinct().collect(Collectors.toList());
        export.setFbaShipmentIds(StrUtil.join(",", fbaShipmentIdList));

        int unitPrice = 6;

        List<ShipmentDeclareKaiQiAirExportItem> exportItemList = amazonRelationMap.entrySet().stream().map(entry -> {
            String fbaShipmentId = entry.getKey();
            List<StockoutShipmentAmazonRelationEntity> relationList = entry.getValue();
            AtomicInteger index = new AtomicInteger();
            return relationList.stream().map(relationEntity -> {
                List<StockoutShipmentDeclareDownloadItemBo> tempItemBoList = downloadItemBoMap.get(relationEntity.getShipmentId());
                index.addAndGet(1);
                final boolean[] hasFillIndex = {false};
                return tempItemBoList.stream().map(skuDownloadItemBo -> {
                    ShipmentDeclareKaiQiAirExportItem item = new ShipmentDeclareKaiQiAirExportItem();
                    if (!hasFillIndex[0]) {
                        item.setBoxIndex(String.valueOf(index.get()));
                        hasFillIndex[0] = true;
                        String[] boxSizeArray = skuDownloadItemBo.getBoxSize().split("\\*");
                        if (boxSizeArray.length == 3) {
                            item.setLength(boxSizeArray[0]);
                            item.setWidth(boxSizeArray[1]);
                            item.setHeight(boxSizeArray[2]);
                        }
                        item.setBoxWeight(skuDownloadItemBo.getWeight().toString());
                    }
                    String fbaShipmentCode = String.format("%sU%06d", fbaShipmentId, index.get());
                    item.setFbaShipmentCode(fbaShipmentCode);
                    item.setFbaShipmentId(relationEntity.getFbaShipmentId());
                    if (ObjectUtil.isNotNull(relationEntity))
                        item.setReferenceId(relationEntity.getAmazonReferenceId());
                    item.setCustomsDeclareCn(skuDownloadItemBo.getCustomsDeclareCn());
                    item.setCustomsDeclareEn(skuDownloadItemBo.getCustomsDeclareEn());
                    item.setHsCode(skuDownloadItemBo.getHsCode());
                    item.setUnitPrice(String.valueOf(unitPrice));
                    item.setQty(skuDownloadItemBo.getQty().toString());
                    item.setQtyPrice(String.valueOf(unitPrice * skuDownloadItemBo.getQty()));
                    item.setBrandName(productCustomsDeclareElementService.findBrand(skuDownloadItemBo.getSpu()));
                    item.setSku(skuDownloadItemBo.getSku());
                    item.setFabricType(skuDownloadItemBo.getFabricType());
                    item.setImageUrl(skuDownloadItemBo.getImageUrl());
                    return item;
                }).collect(Collectors.toList());
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());

        export.setItemList(exportItemList);
        response.setTotalCount(1L);
        response.setDataJsonStr(JsonMapper.toJson(export));
        LOGGER.info("凯奇空运导出装箱申报信息:{}", response.getDataJsonStr());
        return response;

    }


}
