package com.nsy.wms.business.service.qa;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.page.PageResponse;
import com.nsy.api.wms.enumeration.stockin.StockinQaTaskBatchStatusEnum;
import com.nsy.api.wms.enumeration.stockin.StockinQaTaskBatchTaskTypeEnum;
import com.nsy.api.wms.request.qa.StockinQaTaskBatchAssignmentRequest;
import com.nsy.api.wms.request.qa.StockinQaTaskBatchPageRequest;
import com.nsy.api.wms.response.qa.StockinQaTaskBatchPageResponse;
import com.nsy.wms.business.manage.user.UserApiService;
import com.nsy.wms.business.manage.user.response.SysUserInfo;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductCategoryService;
import com.nsy.wms.repository.entity.product.ProductCategoryEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskBatchEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskBatchItemEntity;
import com.nsy.wms.repository.entity.qa.StockinQaTaskEntity;
import com.nsy.wms.repository.jpa.mapper.qa.StockinQaTaskBatchMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: caishaohui
 * @version: v1.0
 * @description: 质检批次业务实现
 * @date: 2024-11-13 10:09
 */
@Service
public class StockinQaTaskBatchService extends ServiceImpl<StockinQaTaskBatchMapper, StockinQaTaskBatchEntity> {

    @Autowired
    private LoginInfoService loginInfoService;
    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private StockinQaTaskBatchItemService stockinQaTaskBatchItemService;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private StockinQaTaskService stockinQaTaskService;

    /**
     * 查询分页
     *
     * @param request
     * @return
     */
    public PageResponse<StockinQaTaskBatchPageResponse> pageList(StockinQaTaskBatchPageRequest request) {
        Page<StockinQaTaskBatchPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        this.buildGroupUserInfo(request);
        Page<StockinQaTaskBatchPageResponse> pageInfo = this.getBaseMapper().pageList(page, request);
        PageResponse<StockinQaTaskBatchPageResponse> pageResponse = new PageResponse<>();
        pageResponse.setTotalCount(pageInfo.getTotal());
        if (CollectionUtil.isEmpty(pageInfo.getRecords())) {
            pageResponse.setContent(Collections.emptyList());
            return pageResponse;
        }
        Map<Integer, Integer> boxQtyMap = stockinQaTaskService.getBoxQtyMap(pageInfo.getRecords().stream().map(StockinQaTaskBatchPageResponse::getBatchId).collect(Collectors.toList()));
        pageInfo.getRecords().forEach(detail -> {
            detail.setTaskTypeStr(StockinQaTaskBatchTaskTypeEnum.getDesc(detail.getTaskType()));
            detail.setBatchStatusStr(StockinQaTaskBatchStatusEnum.getDesc(detail.getBatchStatus()));
            if (CollectionUtils.isEmpty(boxQtyMap) || boxQtyMap.get(detail.getBatchId()) == null) {
                detail.setBoxQty(0);
            }
            detail.setBoxQty(boxQtyMap.get(detail.getBatchId()));
        });
        pageResponse.setContent(pageInfo.getRecords());
        return pageResponse;
    }


    /**
     * 分配批次给指定人员
     *
     * @param request
     */
    public void assignBatch(StockinQaTaskBatchAssignmentRequest request) {
        List<StockinQaTaskBatchEntity> entityList = this.listByIds(request.getBatchIdList());
        if (CollectionUtil.isEmpty(entityList)) {
            throw new BusinessServiceException("批次不存在");
        }
        entityList.forEach(entity -> {
            entity.setTaskOwner(request.getUserName());
            entity.setUserId(request.getUserId());
            entity.setIsDistribution(1);
            entity.setDistributionDate(new Date());
            entity.setBatchStatus(StockinQaTaskBatchStatusEnum.PENDING_QC.name());
            entity.setUpdateBy(loginInfoService.getName());
        });
        this.updateBatchById(entityList);
    }

    /**
     * 指派任务给指定用户
     *
     * @param entity
     * @param userId
     * @param userName
     */
    public void assignToUser(StockinQaTaskBatchEntity entity, Integer userId, String userName) {
        if (!StringUtils.hasText(userName)) {
            SysUserInfo userInfo = userApiService.getUserInfoByUserId(userId);
            if (Objects.nonNull(userInfo) && StringUtils.hasText(userInfo.getUserName())) {
                entity.setTaskOwner(userName);
            }
        } else {
            entity.setTaskOwner(userName);
        }

        entity.setUserId(userId);
        entity.setIsDistribution(1);
        entity.setBatchStatus(StockinQaTaskBatchStatusEnum.PENDING_QC.name());
        entity.setDistributionDate(new Date());
        entity.setUpdateBy(loginInfoService.getName());
        this.updateById(entity);
    }

    /**
     * 生成批次任务
     *
     * @param groupTasks
     * @param typeEnum
     */
    public void generateBatchTask(List<StockinQaTaskEntity> groupTasks, StockinQaTaskBatchTaskTypeEnum typeEnum) {
        StockinQaTaskBatchEntity entity = new StockinQaTaskBatchEntity();
        entity.setTaskType(typeEnum.name());
        entity.setSpaceId(groupTasks.get(0).getSpaceId());
        entity.setProductId(groupTasks.get(0).getProductId());
        entity.setSpu(groupTasks.get(0).getSpu());
        entity.setBatchStatus(StockinQaTaskBatchStatusEnum.WAIT_DISTRIBUTED.name());
        entity.setIsDistribution(0);
        entity.setCreateBy("系统");
        entity.setCreateDate(new Date());
        ProductCategoryEntity parentCategoryInfo = productCategoryService.findParentCategoryInfo(groupTasks.get(0).getCategoryId());
        if (Objects.nonNull(parentCategoryInfo)) {
            entity.setCategoryId(parentCategoryInfo.getCategoryId());
            entity.setCategoryName(parentCategoryInfo.getCategoryName());
        }
        this.save(entity);
        stockinQaTaskBatchItemService.saveInfo(groupTasks, entity.getBatchId());
    }

    /**
     * 获取仍未分配人员的质检批次
     *
     * @return
     */
    public List<StockinQaTaskBatchEntity> getNoAssignmentBatchList() {
        LambdaQueryWrapper<StockinQaTaskBatchEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaTaskBatchEntity::getBatchStatus, StockinQaTaskBatchStatusEnum.WAIT_DISTRIBUTED.name());
        queryWrapper.eq(StockinQaTaskBatchEntity::getIsDistribution, 0);
        return this.list(queryWrapper);
    }

    /**
     * 获取存在质检中任务的用户id
     *
     * @param userIdList
     * @return
     */
    public List<Integer> getExistQcPorcessUserIdList(List<Integer> userIdList) {
        LambdaQueryWrapper<StockinQaTaskBatchEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(StockinQaTaskBatchEntity::getUserId);
        queryWrapper.in(StockinQaTaskBatchEntity::getUserId, userIdList);
        queryWrapper.eq(StockinQaTaskBatchEntity::getBatchStatus, StockinQaTaskBatchStatusEnum.QC_PROCESSING);
        List<StockinQaTaskBatchEntity> taskBatchList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(taskBatchList)) {
            return Collections.emptyList();
        }
        return taskBatchList.stream().map(StockinQaTaskBatchEntity::getUserId).collect(Collectors.toList());
    }

    /**
     * 批量获取用户手上待质检、质检中任务个数
     *
     * @param userIdList 用户ID列表
     * @return 用户ID到任务数量的映射
     */
    public Map<Integer, Integer> getUserBatchTaskCountMap(List<Integer> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }
        //获取现在用户手上待质检、质检中任务
        LambdaQueryWrapper<StockinQaTaskBatchEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(StockinQaTaskBatchEntity::getUserId);
        queryWrapper.in(StockinQaTaskBatchEntity::getUserId, userIdList);
        queryWrapper.in(StockinQaTaskBatchEntity::getBatchStatus, Lists.newArrayList(StockinQaTaskBatchStatusEnum.PENDING_QC.name(), StockinQaTaskBatchStatusEnum.QC_PROCESSING.name()));

        List<StockinQaTaskBatchEntity> taskBatchList = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(taskBatchList)) {
            // 如果没有任何任务，返回所有用户的任务数量为0
            return userIdList.stream().collect(Collectors.toMap(userId -> userId, userId -> 0));
        }

        // 统计每个用户的任务数量
        Map<Integer, Long> userTaskCountMap = taskBatchList.stream().collect(Collectors.groupingBy(StockinQaTaskBatchEntity::getUserId, Collectors.counting()));

        // 确保所有用户都在结果中，没有任务的用户数量为0
        Map<Integer, Integer> result = new HashMap<>(32);
        for (Integer userId : userIdList) {
            result.put(userId, userTaskCountMap.getOrDefault(userId, 0L).intValue());
        }

        return result;
    }

    public void updateBatchStatus(Integer taskId, String batchStatus) {
        LambdaQueryWrapper<StockinQaTaskBatchItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaTaskBatchItemEntity::getTaskId, taskId);
        queryWrapper.last("limit 1");
        StockinQaTaskBatchItemEntity entity = stockinQaTaskBatchItemService.getOne(queryWrapper);
        //如果没有批次则不处理
        if (Objects.isNull(entity)) {
            return;
        }
        StockinQaTaskBatchEntity batchEntity = this.getById(entity.getBatchId());
        //如果是更新为质检中直接更新
        if (StockinQaTaskBatchStatusEnum.QC_PROCESSING.name().equalsIgnoreCase(batchStatus)) {
            batchEntity.setBatchStatus(batchStatus);
            this.updateById(batchEntity);
            return;
        }
        //如果是更新为质检完成则需要改批次下所有任务都完成
        if (!stockinQaTaskBatchItemService.validAllCompleteByBatchId(entity.getBatchId())) {
            return;
        }
        batchEntity.setBatchStatus(StockinQaTaskBatchStatusEnum.COMPLETED.name());
        this.updateById(batchEntity);
    }

    /**
     * 获取任务当前负责人
     *
     * @param taskId
     * @return
     */
    public String getOwnerNameByTaskId(Integer taskId) {
        LambdaQueryWrapper<StockinQaTaskBatchItemEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockinQaTaskBatchItemEntity::getTaskId, taskId);
        queryWrapper.last("limit 1");
        StockinQaTaskBatchItemEntity entity = stockinQaTaskBatchItemService.getOne(queryWrapper);
        //如果没有批次则不处理
        if (Objects.isNull(entity)) {
            return "";
        }
        StockinQaTaskBatchEntity batchEntity = this.getById(entity.getBatchId());
        return batchEntity.getTaskOwner();
    }

    private void buildGroupUserInfo(StockinQaTaskBatchPageRequest request) {
        if (Objects.isNull(request)) {
            return;
        }
        List<Integer> groupIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(request.getGroupIdList())) {
            groupIdList.addAll(request.getGroupIdList());
        }
        if (!Objects.isNull(request.getGroupId())) {
            groupIdList.add(request.getGroupId());
        }
        //没有分组则不处理
        if (CollectionUtils.isEmpty(groupIdList)) {
            return;
        }
        List<SysUserInfo> userInfoList = userApiService.getUserByDepartmentId(groupIdList);
        //分组下没有人员不处理
        if (CollectionUtils.isEmpty(userInfoList)) {
            return;
        }
        request.setGroupUserIdList(userInfoList.stream().map(SysUserInfo::getUserId).distinct().collect(Collectors.toList()));
    }
} 