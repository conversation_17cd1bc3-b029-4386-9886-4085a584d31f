package com.nsy.wms.business.manage.oms.request;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

public class FbtOrderShipShipmentInfoDto {

    private String fbtShipmentId;

    private List<String> forwarderChannelList;

    private List<String> logisticsNoList;

    private List<String> logisticsCompanyList;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryTime;

    public List<String> getLogisticsNoList() {
        return logisticsNoList;
    }

    public void setLogisticsNoList(List<String> logisticsNoList) {
        this.logisticsNoList = logisticsNoList;
    }

    public List<String> getLogisticsCompanyList() {
        return logisticsCompanyList;
    }

    public void setLogisticsCompanyList(List<String> logisticsCompanyList) {
        this.logisticsCompanyList = logisticsCompanyList;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getFbtShipmentId() {
        return fbtShipmentId;
    }

    public void setFbtShipmentId(String fbtShipmentId) {
        this.fbtShipmentId = fbtShipmentId;
    }

    public List<String> getForwarderChannelList() {
        return forwarderChannelList;
    }

    public void setForwarderChannelList(List<String> forwarderChannelList) {
        this.forwarderChannelList = forwarderChannelList;
    }
}
