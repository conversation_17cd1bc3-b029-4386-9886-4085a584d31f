package com.nsy.wms.business.service.stockout;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.constants.StockoutSpaceTrackConstant;
import com.nsy.api.wms.domain.stock.BoxDetails;
import com.nsy.api.wms.domain.stock.StockInOrderBox;
import com.nsy.api.wms.domain.stock.StockTrackingItem;
import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferTrackingStatusEnum;
import com.nsy.api.wms.enumeration.stock.StockTransferTrackingTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stock.WmsCreateStockInOrderRequest;
import com.nsy.api.wms.response.stockout.StockTransferTrackingResponse;
import com.nsy.wms.business.manage.erp.ErpTransferApiService;
import com.nsy.wms.business.manage.erp.request.ErpTransferStockInfo;
import com.nsy.wms.business.manage.erp.request.ErpTransferStockInfoRequest;
import com.nsy.wms.business.manage.tms.TmsApiService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.internal.common.DistributedModuleService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stock.StockTransferTrackingItemService;
import com.nsy.wms.business.service.stock.StockTransferTrackingService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stock.StockTransferTrackingEntity;
import com.nsy.wms.repository.entity.stock.StockTransferTrackingItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.utils.mp.TenantContext;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * HXD 惠州仓库
 * 2022/11/1
 **/
@Service
public class StockoutShangWeiSpaceTrackService implements IDBCKTrackService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutShangWeiSpaceTrackService.class);

    @Autowired
    StockTransferTrackingService trackingService;
    @Autowired
    StockService stockService;
    @Autowired
    StockTransferTrackingItemService trackingItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    ErpTransferApiService erpTransferApiService;
    @Autowired
    BdSpaceService spaceService;
    @Autowired
    BdPositionService positionService;
    @Autowired
    ApplicationContext context;
    @Autowired
    DistributedModuleService distributedModuleService;
    @Inject
    private StockoutShipmentItemService shipmentItemService;
    @Autowired
    private StockoutShipmentService shipmentService;
    @Autowired
    private TmsApiService tmsApiService;
    @Autowired
    private StockTransferTrackingItemService transferTrackingItemService;

    @Override
    public boolean isSupport(StockoutOrderEntity orderEntity) {
        Integer desSpaceId = orderEntity.getDesSpaceId();
        if (desSpaceId == null) {
            return false;
        }
        BdSpaceEntity space = spaceService.getById(desSpaceId);
        return space != null && StrUtil.contains(space.getDescription(), StockoutSpaceTrackConstant.SPACE_DESC_SHANG_WEI);
    }


    @Override
    public StockTransferTrackingEntity createTracking(StockoutOrderEntity orderEntity) {
        if (!StockoutOrderTypeEnum.SPACE_TRANSFER_DELIVERY.name().equals(orderEntity.getStockoutType()))
            return null;
        List<StockoutShipmentItemEntity> itemList = shipmentItemService.findItemByStockoutOrderNo(orderEntity.getStockoutOrderNo());
        StockTransferTrackingEntity tracking = trackingService.getByOrderNo(itemList.get(0).getOrderNo());
        if (Objects.isNull(tracking)) {
            throw new BusinessServiceException("海外仓订单未创建请先至装箱清单tab进行创建");
        }
        return tracking;
    }

    // 正常商品追踪

    @Override
    public void createStockInOrder(StockoutOrderEntity orderEntity, StockTransferTrackingEntity trackingEntity) {
        if (Objects.isNull(trackingEntity) || !StringUtils.hasText(trackingEntity.getPlatformReferenceNo())) {
            throw new BusinessServiceException("海外仓订单未创建请先至装箱清单tab进行创建");
        }
    }

    @Override
    public void doCreate(StockoutOrderEntity orderEntity, String remark) {
        if (!StockoutOrderTypeEnum.SPACE_TRANSFER_DELIVERY.name().equals(orderEntity.getStockoutType()))
            return;
        BdSpaceEntity spaceEntity = spaceService.getSpaceByIdValid(orderEntity.getDesSpaceId());
        StockTransferTrackingEntity trackingEntity = doCreateTracking(orderEntity, spaceEntity);
        if (trackingEntity == null) {
            throw new BusinessServiceException("创建在途跟踪失败！");
        }
        List<StockoutShipmentItemEntity> itemList = shipmentItemService.findItemByStockoutOrderNo(orderEntity.getStockoutOrderNo());
        Map<Integer, List<StockoutShipmentItemEntity>> shipmentItemMap = itemList.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getShipmentId));
        List<StockoutShipmentEntity> shipmentEntities = shipmentService.listByIds(shipmentItemMap.keySet());
        WmsCreateStockInOrderRequest req = buildNormalReq(shipmentItemMap, shipmentEntities);
        req.setLocation(TenantContext.getTenant());
        req.setSpace(spaceEntity.getSpaceName());
        String refNo = tmsApiService.createOverseaStockInOrder(req);
        trackingEntity.setPlatformReferenceNo(refNo);
        trackingService.updateById(trackingEntity);
    }

    private WmsCreateStockInOrderRequest buildNormalReq(Map<Integer, List<StockoutShipmentItemEntity>> shipmentItemMap, List<StockoutShipmentEntity> shipmentEntities) {
        WmsCreateStockInOrderRequest tmsReq = new WmsCreateStockInOrderRequest();
        List<StockInOrderBox> items = new ArrayList<>();
        List<StockoutShipmentEntity> shipmentSortList = shipmentEntities.stream().sorted(Comparator.comparing(StockoutShipmentEntity::getBoxIndex)).collect(Collectors.toList());
        AtomicInteger boxIndex = new AtomicInteger(1);
        shipmentSortList.forEach(shipmentEntity -> {
            List<StockoutShipmentItemEntity> shipmentItems = shipmentItemMap.get(shipmentEntity.getShipmentId());
            int boxNo = boxIndex.getAndIncrement();
            if (!Objects.equals(boxNo, shipmentEntity.getBoxIndex())) {
                shipmentEntity.setBoxIndex(boxNo);
                shipmentService.updateById(shipmentEntity);
            }
            if (StringUtils.hasText(shipmentEntity.getLogisticsCompany()) && tmsReq.getReceivingShippingType() == null) {
                if (shipmentEntity.getLogisticsCompany().contains("空运"))
                    tmsReq.setReceivingShippingType(0);
                if (shipmentEntity.getLogisticsCompany().contains("海运"))
                    tmsReq.setReceivingShippingType(1);
            }
            if (tmsReq.getEtaDate() == null)
                tmsReq.setEtaDate(DateUtils.addDays(new Date(), 20));
            if (tmsReq.getReferenceNo() == null)
                tmsReq.setReferenceNo(shipmentItems.get(0).getOrderNo());
            if (StringUtils.hasText(shipmentEntity.getLogisticsNo()) && !StringUtils.hasText(tmsReq.getTrackingNumber()))
                tmsReq.setTrackingNumber(shipmentEntity.getLogisticsNo());
            StockInOrderBox box = new StockInOrderBox();
            box.setBoxNo(boxNo);
            box.setBoxCode(shipmentEntity.getShipmentBoxCode());
            if (StrUtil.isBlank(shipmentEntity.getBoxSize())) {
                throw new BusinessServiceException(shipmentEntity.getShipmentBoxCode() + "此箱号未填写箱规！");
            }
            String[] packageDimensions = shipmentEntity.getBoxSize().split("\\*");
            if (packageDimensions.length < 3) {
                throw new BusinessServiceException(shipmentEntity.getShipmentBoxCode() + "此箱号规格不正确，需要长宽高！");
            }
            box.setLength(packageDimensions[0]);
            box.setWidth(packageDimensions[1]);
            box.setHeight(packageDimensions[2]);
            if (shipmentEntity.getWeight() == null) {
                throw new BusinessServiceException(shipmentEntity.getShipmentBoxCode() + "箱号重量必须填写");
            }
            box.setWeight(String.valueOf(shipmentEntity.getWeight()));
            Map<String, Integer> skuQtyMap = shipmentItems.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getSku, Collectors.summingInt(StockoutShipmentItemEntity::getQty)));
            skuQtyMap.forEach((sku, qty) -> {
                BoxDetails boxDetails = new BoxDetails();
                boxDetails.setProductSku(sku);
                boxDetails.setQuantity(qty);
                tmsReq.getSkuList().add(sku);
                box.getBoxDetails().add(boxDetails);
            });
            items.add(box);
        });
        tmsReq.setItems(items);
        return tmsReq;

    }

    @Transactional(rollbackFor = Exception.class)
    public void tracking(StockTransferTrackingEntity track) {
        if (!StringUtils.hasText(track.getPlatformReferenceNo())) {
            LOGGER.error("{}没有追踪单号,无法追踪", track.getPlatformReferenceNo());
            return;
        }
        LambdaQueryWrapper<StockTransferTrackingItemEntity> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(StockTransferTrackingItemEntity::getTrackingId, track.getId());
        List<StockTransferTrackingItemEntity> list = transferTrackingItemService.list(wrapper1);
        if (list.get(0).getSpaceId() == null) {
            LOGGER.error("{}没有目的仓库,无法追踪", list.get(0).getSpaceId());
            return;
        }
        BdSpaceEntity space = spaceService.getById(list.get(0).getSpaceId());
        if (space == null) {
            LOGGER.error("{}无法找到目的仓库,无法追踪", list.get(0).getSpaceId());
            return;
        }

        StockTransferTrackingResponse trackingInfo = tmsApiService.getOverseaStockInOrder(track.getPlatformReferenceNo(), space.getSpaceName());
        if (CollectionUtils.isEmpty(trackingInfo.getTrackingItemList())) {
            return;
        }
        // 如果是上架的要更新数量

        List<ErpTransferStockInfo> synErpInfoList = new LinkedList<>();

        LambdaQueryWrapper<StockTransferTrackingItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockTransferTrackingItemEntity::getTrackingId, track.getId());
        List<StockTransferTrackingItemEntity> list1 = transferTrackingItemService.list(wrapper);
        Map<String, List<StockTrackingItem>> packSkuMap = trackingInfo.getTrackingItemList().stream().collect(Collectors.groupingBy(StockTrackingItem::getSku));
        packSkuMap.forEach((packSku, packItems) -> {
            for (StockTransferTrackingItemEntity trackItem : list1) {
                if (!Objects.equals(trackItem.getSku(), packSku)) {
                    continue;
                }
                trackItem.setReceivedQty(packItems.stream().mapToInt(StockTrackingItem::getReceivedQty).sum());
                trackItem.setShelvedQty(packItems.stream().mapToInt(StockTrackingItem::getShelvedQty).sum());
                trackItem.setUpdateBy(loginInfoService.getName());
                transferTrackingItemService.updateById(trackItem);
                // 如果是已经上架的，更新库存
                // 调用erp更新库存
                synErpInfoList.add(new ErpTransferStockInfo(trackItem.getSpaceId(), trackItem.getShelvedPositionCode(), trackItem.getSku(), trackItem.getShelvedQty()));
                StockUpdateRequest request = new StockUpdateRequest();
                request.setQty(trackItem.getShelvedQty());
                request.setPositionCode(trackItem.getShelvedPositionCode());
                request.setSku(trackItem.getSku());
                request.setChangeLogType(StockChangeLogTypeEnum.POSITION_IN);
                request.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_INSIDE);
                stockService.updateStock(request);
            }
        });
        if (!synErpInfoList.isEmpty()) {
            ErpTransferStockInfoRequest syncErpRequest = new ErpTransferStockInfoRequest();
            syncErpRequest.setUserName(loginInfoService.getName());
            syncErpRequest.setLocation(TenantContext.getTenant());
            syncErpRequest.setErpTransferStockInfos(synErpInfoList);
            erpTransferApiService.syncSpaceTransferStock(syncErpRequest, track.getId());
        }
        track.setUpdateBy(loginInfoService.getName());
        track.setStatus(StockTransferTrackingStatusEnum.SHELVED.name());
        track.setEtlInDate(new Date());
        trackingService.updateById(track);
    }

    private StockTransferTrackingEntity doCreateTracking(StockoutOrderEntity orderEntity, BdSpaceEntity spaceEntity) {
        if (!StockoutOrderTypeEnum.SPACE_TRANSFER_DELIVERY.name().equals(orderEntity.getStockoutType()))
            return null;
        StockTransferTrackingEntity entity;
        if (orderEntity.getDesSpaceId() == null) {
            throw new BusinessServiceException("无目的仓库，请核对");
        }
        BdPositionEntity positionEntity = positionService.findFirstBySpaceIdAndPositionType(spaceEntity.getSpaceId(), BdPositionTypeEnum.STOCK_POSITION.name());
        if (positionEntity == null) {
            throw new BusinessServiceException("未配置对应海外仓库的存储库位！");
        }
        entity = trackingService.createNormalTracking(orderEntity, StockTransferTrackingTypeEnum.LING_XING.name(),
                positionEntity.getPositionCode(), spaceEntity.getSpaceId());
        return entity;
    }

    @Override
    public String printLabel(StockTransferTrackingEntity tracking, List<String> shipmentBoxCodeList) {
        List<StockoutShipmentEntity> entityByShipmentBoxCodeList = shipmentService.getEntityByShipmentBoxCodeList(shipmentBoxCodeList);
        List<String> boxList = entityByShipmentBoxCodeList.stream().sorted(Comparator.comparing(StockoutShipmentEntity::getBoxIndex))
                .map(StockoutShipmentEntity::getShipmentBoxCode).collect(Collectors.toList());
        LambdaQueryWrapper<StockTransferTrackingItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockTransferTrackingItemEntity::getTrackingId, tracking.getId());
        List<StockTransferTrackingItemEntity> list = transferTrackingItemService.list(wrapper);
        if (list.get(0).getSpaceId() == null) {
            LOGGER.error("{}没有目的仓库,无法追踪", list.get(0).getSpaceId());
            throw new BusinessServiceException("没有目的仓库无法打印");
        }
        BdSpaceEntity space = spaceService.getById(list.get(0).getSpaceId());
        if (space == null) {
            throw new BusinessServiceException("无法找到目的仓库,无法追踪无法打印");
        }
        return tmsApiService.printOverseaShipmentLabel(tracking.getPlatformReferenceNo(), space.getSpaceName(), boxList);
    }





}
