package com.nsy.wms.business.manage.pms;

import com.nsy.api.pms.dto.product.ProductBaseInfoDTO;
import com.nsy.api.pms.dto.product.ProductDesignInfoDTO;
import com.nsy.api.pms.dto.product.ProductImageInfoDTO;
import com.nsy.api.pms.dto.product.ProductLabelDTO;
import com.nsy.api.pms.dto.product.ProductSpecBaseDTO;
import com.nsy.api.pms.dto.product.ProductSpecImageUrlDTO;
import com.nsy.api.pms.dto.request.feign.TableIdRequest;
import com.nsy.api.pms.dto.response.BaseListResponse;
import com.nsy.api.pms.feign.ApiPmsProductFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
public class PmsApiService {

    @Autowired
    ApiPmsProductFeignClient apiPmsProductFeignClient;

    public ProductBaseInfoDTO getProductBaseInfoById(Integer productId) {
        TableIdRequest request = new TableIdRequest();
        request.setTableIds(Collections.singletonList(productId));
        BaseListResponse<ProductBaseInfoDTO> data = apiPmsProductFeignClient.fetchProductBaseInfos(request);
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getContent())) {
            return null;
        }
        return data.getContent().get(0);
    }

    public ProductDesignInfoDTO getProductDesignInfoById(Integer productId) {
        TableIdRequest request = new TableIdRequest();
        request.setTableIds(Collections.singletonList(productId));
        BaseListResponse<ProductDesignInfoDTO> data = apiPmsProductFeignClient.fetchProductDesignInfos(request);
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getContent())) {
            return null;
        }
        return data.getContent().get(0);
    }

    public ProductImageInfoDTO getProductImageInfoById(Integer productId) {
        TableIdRequest request = new TableIdRequest();
        request.setTableIds(Collections.singletonList(productId));
        BaseListResponse<ProductImageInfoDTO> data = apiPmsProductFeignClient.fetchProductImageUrls(request);
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getContent())) {
            return null;
        }
        return data.getContent().get(0);
    }

    public ProductSpecBaseDTO getProductSpecBaseBySpecId(Integer specId) {
        TableIdRequest request = new TableIdRequest();
        request.setTableIds(Collections.singletonList(specId));
        BaseListResponse<ProductSpecBaseDTO> data = apiPmsProductFeignClient.fetchProductSpecBaseInfos(request);
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getContent())) {
            return null;
        }
        return data.getContent().get(0);
    }

    public ProductSpecImageUrlDTO getProductSpecImageBySpecId(Integer specId) {
        TableIdRequest request = new TableIdRequest();
        request.setTableIds(Collections.singletonList(specId));
        BaseListResponse<ProductSpecImageUrlDTO> data = apiPmsProductFeignClient.fetchProductSpecImageInfos(request);
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getContent())) {
            return null;
        }
        return data.getContent().get(0);
    }


    public ProductLabelDTO getProductLabelById(Integer productId) {
        TableIdRequest request = new TableIdRequest();
        request.setTableIds(Collections.singletonList(productId));
        BaseListResponse<ProductLabelDTO> data = apiPmsProductFeignClient.fetchProductLabelTableInfos(request);
        if (Objects.isNull(data) || CollectionUtils.isEmpty(data.getContent())) {
            return null;
        }
        return data.getContent().get(0);
    }
}
