package com.nsy.wms.business.service.stockin;

import com.nsy.api.wms.request.stockin.StockinSupplierDeliveryListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockin.StockinSupplierDeliveryListResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StockinOrderTaskSearchService {

    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockinOrderService stockinOrderService;
    @Autowired
    StockinOrderTaskService stockinOrderTaskService;

    public PageResponse<StockinSupplierDeliveryListResponse> pageSupplierDeliveryNoList(StockinSupplierDeliveryListRequest request) {
        Page<StockinSupplierDeliveryListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        List<StockinSupplierDeliveryListResponse> pageList = stockinOrderTaskService.getBaseMapper().pageSupplierDeliveryNoList(page, request);

        PageResponse<StockinSupplierDeliveryListResponse> response = new PageResponse<>();
        response.setContent(pageList);
        response.setTotalCount(page.getTotal());
        return response;
    }
}
