package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.wms.domain.product.ProductSpecInfo;
import com.nsy.api.wms.enumeration.bd.BdSystemParameterEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWeightCheckOriginTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutWeightCheckResultTypeEnum;
import com.nsy.api.wms.request.stockout.StockoutWeightCheckPageRequest;
import com.nsy.api.wms.request.stockout.StockoutWeightCheckRequest;
import com.nsy.api.wms.request.stockout.StockoutWeightCheckSaveRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutWeightCheckPageResponse;
import com.nsy.wms.business.service.bd.BdSystemParameterService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.bd.BdSystemParameterEntity;
import com.nsy.wms.repository.entity.stockout.StockoutWeightCheckEntity;
import com.nsy.wms.repository.entity.stockout.StockoutWeightCheckItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutWeightCheckMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/18 16:30
 */
@Service
public class StockoutWeightCheckService extends ServiceImpl<StockoutWeightCheckMapper, StockoutWeightCheckEntity> {

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutWeightCheckService.class);

    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    private BdSystemParameterService bdSystemParameterService;
    @Autowired
    private StockoutWeightCheckItemService stockoutWeightCheckItemService;
    @Autowired
    private LoginInfoService loginInfoService;

    public PageResponse<StockoutWeightCheckPageResponse> pageList(StockoutWeightCheckPageRequest request) {
        PageResponse<StockoutWeightCheckPageResponse> pageResponse = new PageResponse<>();
        Page<StockoutWeightCheckPageResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        IPage<StockoutWeightCheckPageResponse> pageResult = this.baseMapper.pageList(page, request);
        pageResponse.setTotalCount(page.getTotal());
        pageResponse.setContent(pageResult.getRecords());
        return pageResponse;
    }

    public void checkWeight(StockoutWeightCheckRequest request) {
        List<StockoutWeightCheckEntity> weightCheckEntityList = this.listByIds(request.getIdList());
        if (CollectionUtils.isEmpty(weightCheckEntityList)) {
            throw new BusinessServiceException("查询不到对应的核对记录，请确认!");
        }
        if (weightCheckEntityList.stream().filter(detail -> 1 == detail.getStatus()).findAny().isPresent()) {
            throw new BusinessServiceException("选择的记录存在已核对记录，请勿重复处理!");
        }
        if (StockoutWeightCheckResultTypeEnum.CONSISTENT_WITH_PRODUCT_WEIGHT.name().equals(request.getResult())) {
            weightCheckEntityList.forEach(detail -> {
                detail.setResult(request.getResult());
                detail.setStatus(1);
                detail.setOperate(loginInfoService.getUserName());
                detail.setOperateDate(new Date());
            });
        } else if (StockoutWeightCheckResultTypeEnum.CONSISTENT_WITH_AVG_WEIGHT.name().equals(request.getResult())) {
            weightCheckEntityList.forEach(detail -> {
                detail.setResult(request.getResult());
                detail.setStatus(1);
                detail.setOperate(loginInfoService.getUserName());
                detail.setOperateDate(new Date());
                detail.setRemark(String.format("系统重量更新成：%s", detail.getStockoutAvgWeight().setScale(0, RoundingMode.HALF_UP)));
                productSpecInfoService.editWeight(productSpecInfoService.getProductSpecIdBySku(detail.getSku()), detail.getStockoutAvgWeight().setScale(0, RoundingMode.HALF_UP));
            });

        } else if (StockoutWeightCheckResultTypeEnum.REWEIGH.name().equals(request.getResult())) {
            if (ObjectUtils.isEmpty(request.getWeight())) {
                throw new BusinessServiceException("重新称重重量不能为空!");
            }
            if (request.getIdList().size() > 1) {
                throw new BusinessServiceException("重新称重重量不允许批量操作!");
            }
            weightCheckEntityList.forEach(detail -> {
                detail.setResult(request.getResult());
                detail.setStatus(1);
                detail.setOperate(loginInfoService.getUserName());
                detail.setOperateDate(new Date());
                detail.setRemark(String.format("系统重量更新成：%s", request.getWeight()));
                productSpecInfoService.editWeight(productSpecInfoService.getProductSpecIdBySku(detail.getSku()), request.getWeight());
            });
        }
        this.updateBatchById(weightCheckEntityList);
    }

    /**
     * 新增重量核对数据
     *
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    @JLock(keyConstant = "saveStockoutWeightInfo", lockKey = "#request.sku")
    public void saveStockoutWeightInfo(StockoutWeightCheckSaveRequest request) {
        ProductSpecInfo productSpecInfo = productSpecInfoService.getBySku(request.getSku());
        LambdaQueryWrapper<StockoutWeightCheckEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StockoutWeightCheckEntity::getSku, request.getSku());
        queryWrapper.eq(StockoutWeightCheckEntity::getStatus, Boolean.FALSE);
        StockoutWeightCheckEntity checkEntity = this.getOne(queryWrapper);
        BigDecimal productWeight = !ObjectUtils.isEmpty(productSpecInfo.getActualWeight()) ? productSpecInfo.getActualWeight() : BigDecimal.ZERO;
        //当同个sku存在待核对的数据时，直接保存明细 并更新单据头更新平均值
        if (!ObjectUtils.isEmpty(checkEntity)) {
            this.buildWeightCheckItemInfo(checkEntity, request);
            checkEntity.setProductWeight(productWeight);
            checkEntity.setStockoutAvgWeight(stockoutWeightCheckItemService.getAvgWeight(checkEntity.getId()));
            this.updateById(checkEntity);
        }
        BdSystemParameterEntity weightOfPackingBagEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_STOCKOUT_WEIGHT_OF_PACKAGING_BAG.getKey());
        if (weightOfPackingBagEntity == null || !StringUtils.hasText(weightOfPackingBagEntity.getConfigValue())) {
            LOGGER.error("未配置包装袋重量！");
            return;
        }
        BdSystemParameterEntity allowableRangeEntity = bdSystemParameterService.getByKey(BdSystemParameterEnum.WMS_STOCKOUT_WEIGHT_ALLOWABLE_RANGE.getKey());
        if (allowableRangeEntity == null || !StringUtils.hasText(allowableRangeEntity.getConfigValue())) {
            LOGGER.error("未配置误差范围！");
            return;
        }
        BigDecimal differentValue = request.getWeight().subtract(new BigDecimal(weightOfPackingBagEntity.getConfigValue())).subtract(productWeight).abs();
        //在误差允许范围内不做处理
        if (differentValue.compareTo(new BigDecimal(allowableRangeEntity.getConfigValue())) < 0) {
            LOGGER.info("误差在合理范围内无需处理,sku为：{},误差为：{}！", request.getSku(), differentValue);
            return;
        }
        if (ObjectUtils.isEmpty(checkEntity)) {
            checkEntity = new StockoutWeightCheckEntity();
            checkEntity.setStockoutAvgWeight(request.getWeight());
            checkEntity.setProductWeight(productWeight);
            checkEntity.setStatus(0);
            checkEntity.setCreateBy(request.getCreateBy());
            checkEntity.setSku(productSpecInfo.getSku());
            checkEntity.setBarcode(productSpecInfo.getBarcode());
            checkEntity.setProductId(productSpecInfo.getProductId());
            checkEntity.setSpecId(productSpecInfo.getSpecId());
            checkEntity.setOriginType(StockoutWeightCheckOriginTypeEnum.SMALL_PACK_STOCKOUT.name());
            checkEntity.setLocation(TenantContext.getTenant());
            this.save(checkEntity);
            this.buildWeightCheckItemInfo(checkEntity, request);
        }
    }

    private void buildWeightCheckItemInfo(StockoutWeightCheckEntity checkEntity, StockoutWeightCheckSaveRequest request) {
        StockoutWeightCheckItemEntity stockoutWeightCheckItemEntity = new StockoutWeightCheckItemEntity();
        BeanUtils.copyProperties(checkEntity, stockoutWeightCheckItemEntity, "id", "createBy", "updateBy", "createDate", "updateDate", "version");
        stockoutWeightCheckItemEntity.setCreateBy(request.getCreateBy());
        stockoutWeightCheckItemEntity.setCreateDate(request.getCrateDate());
        stockoutWeightCheckItemEntity.setStockoutWeight(request.getWeight());
        stockoutWeightCheckItemEntity.setCheckId(checkEntity.getId());
        stockoutWeightCheckItemService.save(stockoutWeightCheckItemEntity);
    }

}
