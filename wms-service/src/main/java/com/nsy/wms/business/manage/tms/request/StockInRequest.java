package com.nsy.wms.business.manage.tms.request;
import java.util.List;

public class StockInRequest {

    private String sellerOrderNo;
    private String inspectionWarehouseCode;
    private String destinationWarehouseCode;
    private String winitProductCode;
    private String importerCode;
    private String exporterCode;
    private String importDeclarationRuleCode;
    private String exportDeclarationType;
    private String logisticsPlanNo;
    private String orderType;
    private StockInDispatchInfo dispatchInfo;
    private List<StockInPackageList> packageList;

    private String logisticsCompany;

    private String location;

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSellerOrderNo() {
        return sellerOrderNo;
    }

    public void setSellerOrderNo(String sellerOrderNo) {
        this.sellerOrderNo = sellerOrderNo;
    }

    public String getInspectionWarehouseCode() {
        return inspectionWarehouseCode;
    }

    public void setInspectionWarehouseCode(String inspectionWarehouseCode) {
        this.inspectionWarehouseCode = inspectionWarehouseCode;
    }

    public String getDestinationWarehouseCode() {
        return destinationWarehouseCode;
    }

    public void setDestinationWarehouseCode(String destinationWarehouseCode) {
        this.destinationWarehouseCode = destinationWarehouseCode;
    }

    public String getWinitProductCode() {
        return winitProductCode;
    }

    public void setWinitProductCode(String winitProductCode) {
        this.winitProductCode = winitProductCode;
    }

    public String getImporterCode() {
        return importerCode;
    }

    public void setImporterCode(String importerCode) {
        this.importerCode = importerCode;
    }

    public String getExporterCode() {
        return exporterCode;
    }

    public void setExporterCode(String exporterCode) {
        this.exporterCode = exporterCode;
    }

    public String getImportDeclarationRuleCode() {
        return importDeclarationRuleCode;
    }

    public void setImportDeclarationRuleCode(String importDeclarationRuleCode) {
        this.importDeclarationRuleCode = importDeclarationRuleCode;
    }

    public String getExportDeclarationType() {
        return exportDeclarationType;
    }

    public void setExportDeclarationType(String exportDeclarationType) {
        this.exportDeclarationType = exportDeclarationType;
    }

    public String getLogisticsPlanNo() {
        return logisticsPlanNo;
    }

    public void setLogisticsPlanNo(String logisticsPlanNo) {
        this.logisticsPlanNo = logisticsPlanNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public StockInDispatchInfo getDispatchInfo() {
        return dispatchInfo;
    }

    public void setDispatchInfo(StockInDispatchInfo dispatchInfo) {
        this.dispatchInfo = dispatchInfo;
    }

    public List<StockInPackageList> getPackageList() {
        return packageList;
    }

    public void setPackageList(List<StockInPackageList> packageList) {
        this.packageList = packageList;
    }
}
