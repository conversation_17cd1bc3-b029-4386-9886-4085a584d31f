package com.nsy.wms.business.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.pms.dto.response.BaseBooleanResponse;
import com.nsy.api.pms.dto.response.BaseStringResponse;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.constants.StringConstant;
import com.nsy.api.wms.domain.stockout.PackSellerPrintDTO;
import com.nsy.api.wms.domain.stockout.PackShipmentTransferSku;
import com.nsy.api.wms.domain.stockout.ShipmentBoxSkuExport;
import com.nsy.api.wms.domain.stockout.StockoutOrderPackMappingInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentPackItemInfo;
import com.nsy.api.wms.domain.stockout.StockoutShipmentPackModel;
import com.nsy.api.wms.domain.stockout.StockoutShipmentPackSearchResult;
import com.nsy.api.wms.domain.stockout.StockoutShipmentScanResponse;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchCount;
import com.nsy.api.wms.enumeration.PrintTemplateNameEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutOrderWorkSpaceEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentPackStatusEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutShipmentStatusEnum;
import com.nsy.api.wms.request.stockout.ShipmentDownloadRequest;
import com.nsy.api.wms.request.stockout.StockoutAmazonPackPrintRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentPackBoxRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentPackSkuRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentSearchRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.config.PrintListResponse;
import com.nsy.api.wms.response.stockout.StockoutPackShipmentItemSkuInfoResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentPackItemSkuInfoRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentPackResponse;
import com.nsy.wms.business.service.bd.BdBoxSpecificationsService;
import com.nsy.wms.business.service.bd.ProductStoreSkuMappingService;
import com.nsy.wms.business.service.common.GenerateCodeService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.config.PrintTemplateService;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stockout.building.StockoutBuilding;
import com.nsy.wms.business.service.stockout.valid.StockoutShipmentValid;
import com.nsy.wms.common.lock.annotation.JLock;
import com.nsy.wms.repository.entity.config.PrintTemplateEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderPackMappingInfoEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentPackEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentPackItemEntity;
import com.nsy.wms.repository.entity.stockout.StockoutTransparencyCodeEntity;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentPackMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.PrintTransferUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 装箱清单
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class StockoutShipmentPackService extends ServiceImpl<StockoutShipmentPackMapper, StockoutShipmentPackEntity> {

    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockoutShipmentPackItemService packItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockoutOrderService stockoutOrderService;
    @Autowired
    GenerateCodeService gcService;
    @Autowired
    ProductSpecInfoService specInfoService;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    StockoutOrderPackMappingInfoService packMappingInfoService;
    @Autowired
    StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutShipmentItemService shipmentItemService;
    @Autowired
    StockoutShipmentService shipmentService;
    @Autowired
    StockoutShipmentErpPickingBoxService pickingBoxService;
    @Autowired
    ApplicationContext context;
    @Autowired
    StockoutShipmentValid shipmentValid;
    @Autowired
    private ProductStoreSkuMappingService productStoreSkuMappingService;
    @Autowired
    PrintTemplateService printService;
    @Autowired
    StockoutOrderPrintService stockoutOrderPrintService;
    @Autowired
    StockoutTransparencyCodeService transparencyCodeService;
    @Autowired
    BdBoxSpecificationsService specificationsService;

    private static final Logger LOGGER = LoggerFactory.getLogger(StockoutShipmentPackService.class);

    public StockoutShipmentPackResponse getPackShipmentById(Integer id) {
        StockoutShipmentPackResponse stockoutShipmentResponse = new StockoutShipmentPackResponse();
        StockoutShipmentPackModel stockoutShipment = new StockoutShipmentPackModel();
        StockoutShipmentPackEntity packEntity = getById(id);
        if (packEntity == null)
            throw new BusinessServiceException("装箱清单-找不到" + id + "id记录");
        BeanUtils.copyProperties(packEntity, stockoutShipment);
        stockoutShipment.setStatus(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_SHIPMENT_PACKING_LIST_STATUS.getName(), stockoutShipment.getStatus()));
        stockoutShipment.setBoxSizeText(specificationsService.getValueByInfo(stockoutShipment.getBoxSize()));
        stockoutShipment.setPackTypeLabel(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKAGING_TYPE.getName(), stockoutShipment.getPackType()));
        stockoutShipmentResponse.setStockoutShipment(stockoutShipment);
        return stockoutShipmentResponse;
    }

    public StockoutShipmentPackResponse getByShipmentBoxCode(String shipmentBoxCode) {
        StockoutShipmentPackResponse stockoutShipmentResponse = new StockoutShipmentPackResponse();
        StockoutShipmentPackModel stockoutShipment = new StockoutShipmentPackModel();
        StockoutShipmentPackEntity packEntity = findTopByShipmentBoxCode(shipmentBoxCode);
        BeanUtils.copyProperties(packEntity, stockoutShipment);
        List<String> orderNoList = packItemService.getOrderNosByShipmentId(packEntity.getShipmentPackId());
        stockoutShipment.setPackTypeLabel(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKAGING_TYPE.getName(), stockoutShipment.getPackType()));
        //        stockoutShipment.setPackTypeLabel(enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_PACKAGING_TYPE.getName(), stockoutShipment.getPackType()));
        StringBuilder orderNoStr = new StringBuilder();
        for (String item : orderNoList) {
            if (item != null)
                orderNoStr.append(',').append(item);
        }
        if (StringUtils.hasText(orderNoStr.toString())) {
            stockoutShipment.setOrderNoStr(orderNoStr.toString().replaceFirst(",", ""));
        }
        stockoutShipmentResponse.setStockoutShipment(stockoutShipment);
        return stockoutShipmentResponse;
    }

    public StockoutShipmentPackEntity findTopByShipmentBoxCode(String shipmentBoxCode) {
        StockoutShipmentPackEntity one = this.getOne(new QueryWrapper<StockoutShipmentPackEntity>().lambda().eq(StockoutShipmentPackEntity::getShipmentBoxCode, shipmentBoxCode)
                .last("limit 1"));
        if (Objects.isNull(one))
            throw new BusinessServiceException("未找到" + shipmentBoxCode + "的装箱记录");
        return one;
    }

    public void updateStockoutShipmentPack(StockoutShipmentUpdateRequest request) {
        StockoutShipmentPackEntity shipment = findTopByShipmentBoxCode(request.getShipmentBoxCode());
        // 校验相同boxIndex
        validSameBoxIndex(shipment, request.getBoxIndex());
        BeanUtils.copyProperties(request, shipment);
        shipment.setUpdateBy(loginInfoService.getName());
        this.updateById(shipment);
    }

    private void validSameBoxIndex(StockoutShipmentPackEntity shipment, Integer boxIndex) {
        if (boxIndex == null) {
            return;
        }
        if (!Objects.equals(shipment.getBoxIndex(), boxIndex)) {
            List<String> stockoutOrderNo = packItemService.getStockoutOrderNoByShipmentId(shipment.getShipmentPackId());
            if (CollectionUtils.isEmpty(stockoutOrderNo)) {
                return;
            }
            Integer sameBoxIndex = packItemService.getBaseMapper().validSameBoxIndex(stockoutOrderNo, shipment.getShipmentPackId(), boxIndex);
            if (sameBoxIndex > 0) {
                throw new BusinessServiceException("存在相同的箱子序号，请重新填写");
            }
        }
    }

    public PageResponse<StockoutShipmentPackSearchResult> searchPackList(StockoutShipmentSearchRequest request) {
        StockoutBuilding.buildRequest(request);
        PageResponse<StockoutShipmentPackSearchResult> pageResponse = new PageResponse<>();
        pageResponse.setContent(new ArrayList<>());
        Page<StockoutShipmentPackSearchResult> page = new Page<>(request.getPageIndex(), request.getPageSize());
        page.setSearchCount(false);
        IPage<Integer> pageResult = this.getBaseMapper().pageSearchShipmentIds(page, request);
        pageResponse.setTotalCount(this.getBaseMapper().pageSearchShipmentIdsCount(request));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return pageResponse;
        }
        List<StockoutShipmentPackItemEntity> shipmentItems = packItemService.findByShipmentIdList(pageResult.getRecords());
        // 每个装箱对应的明细
        Map<Integer, List<StockoutShipmentPackItemEntity>> itemMap = shipmentItems.stream().collect(Collectors.groupingBy(StockoutShipmentPackItemEntity::getShipmentPackId));
        Map<String, String> pickingListStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_SHIPMENT_PACKING_LIST_STATUS.getName());
        Map<String, String> boxStandardsEnumMap = specificationsService.getBdBoxSpecificationsMap();
        pageResult.getRecords().forEach(shipmentId -> {
            StockoutShipmentPackEntity shipment = getById(shipmentId);
            StockoutShipmentPackSearchResult item = new StockoutShipmentPackSearchResult();
            BeanUtilsEx.copyProperties(shipment, item);
            item.setStatus(pickingListStatusEnumMap.get(item.getStatus()));
            item.setBoxSize(boxStandardsEnumMap.get(item.getBoxSize()));
            List<StockoutShipmentPackItemEntity> itemEntityList = itemMap.get(shipmentId);
            if (!CollectionUtils.isEmpty(itemEntityList)) {
                // 不是空箱子
                List<String> stockoutOrderNos = itemEntityList.stream().map(StockoutShipmentPackItemEntity::getStockoutOrderNo).distinct().collect(Collectors.toList());
                List<StockoutOrderEntity> stockoutOrderList = stockoutOrderService.getByStockoutOrderNoList(stockoutOrderNos);
                // 组装出库单信息
                item.setStockoutOrderNoList(stockoutOrderNos);
                item.setOrderNoList(StockoutBuilding.shipmentPackOrderList(itemEntityList));
                item.setWorkspace(stockoutOrderList.stream().map(it -> StockoutOrderWorkSpaceEnum.getNameBy(it.getWorkspace())).distinct().filter(StringUtils::hasText).collect(Collectors.joining(StringConstant.COMMA)));
            }
            item.setBoxSkuAmount(itemEntityList.stream().mapToInt(StockoutShipmentPackItemEntity::getQty).sum());
            pageResponse.getContent().add(item);
        });
        return pageResponse;
    }

    public StockoutShipmentScanResponse startScan(String stockoutOrderNo, String shipmentBoxCode) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        if (StockoutOrderStatusEnum.DELIVERED.name().equalsIgnoreCase(stockoutOrder.getStatus()))
            throw new BusinessServiceException("此出库单已发货，无法进行pack装箱");
        if (!stockoutOrder.getHasPack())
            throw new BusinessServiceException(stockoutOrder.getStockoutOrderNo() + "此出库单不含pack商品，请核对");
        StockoutShipmentScanResponse stockoutShipmentScanResponse = new StockoutShipmentScanResponse();
        List<StockoutOrderItemEntity> stockoutOrderItemEntities = stockoutOrderItemService.listByStockoutOrderId(stockoutOrder.getStockoutOrderId());
        stockoutShipmentScanResponse.setOrderNo(stockoutOrderItemEntities.stream().map(StockoutOrderItemEntity::getOrderNo).filter(StringUtils::hasText).distinct().collect(Collectors.joining(",")));
        if (StringUtils.hasText(shipmentBoxCode)) {
            StockoutShipmentPackEntity packShipment = findTopByShipmentBoxCode(shipmentBoxCode);
            if (StockoutShipmentPackStatusEnum.SYNCED.name().equals(packShipment.getStatus()))
                throw new BusinessServiceException("此箱子已经同步，无法继续操作");
            stockoutShipmentScanResponse.setPackShipmentId(packShipment.getShipmentPackId());
        }
        stockoutShipmentScanResponse.setBoxCode(shipmentBoxCode);
        Map<String, List<StockoutOrderPackMappingInfoEntity>> packSkuInfo = packMappingInfoService.getPackSkuInfo(stockoutOrder.getStockoutOrderId());
        List<StockoutShipmentPackItemInfo> showItems = new ArrayList<>();
        packSkuInfo.forEach((k, v) -> {
            StockoutShipmentPackItemInfo itemInfo = new StockoutShipmentPackItemInfo();
            ProductSpecInfoEntity specInfo = specInfoService.findTopBySku(k);
            ProductInfoEntity product = productInfoService.findTopByProductId(specInfo.getProductId());
            itemInfo.setBarcode(specInfo.getBarcode());
            itemInfo.setSku(k);
            itemInfo.setOrderNo(v.get(0).getOrderNo());
            StockoutShipmentPackItemEntity packItemEntity = packItemService.findByIdAndSku(stockoutShipmentScanResponse.getPackShipmentId(), k);
            itemInfo.setQty(packItemEntity == null ? Integer.valueOf("0") : packItemEntity.getQty());
            itemInfo.setSellerBarcode(v.get(0).getPackSellerBarcode());
            itemInfo.setProductName(product.getProductName());
            itemInfo.setStockoutOrderNo(stockoutOrderNo);
            itemInfo.setPreviewImageUrl(product.getPreviewImageUrl());
            itemInfo.setThumbnailImageUrl(product.getThumbnailImageUrl());
            itemInfo.setImageUrl(product.getImageUrl());
            itemInfo.setPackSkuQty(v.get(0).getOriginQty());
            itemInfo.setPackChildrenQty(v.stream().mapToInt(StockoutOrderPackMappingInfoEntity::getMappingQty).sum());
            itemInfo.setPackSkuScanQty(packItemService.countSkuScanQty(stockoutOrderNo, itemInfo.getSku()));
            showItems.add(itemInfo);
        });
        stockoutShipmentScanResponse.setSkuInfoList(showItems);
        return stockoutShipmentScanResponse;
    }

    private StockoutShipmentPackItemInfo buildScanItemInfo(StockoutShipmentPackItemEntity item, Map<String, List<StockoutOrderPackMappingInfoEntity>> packSkuInfo, String stockoutOrderNo) {
        StockoutShipmentPackItemInfo itemInfo = new StockoutShipmentPackItemInfo();
        BeanUtilsEx.copyProperties(item, itemInfo);
        if (item.getSpecId() == null) {
            return itemInfo;
        }
        ProductSpecInfoEntity specInfo = specInfoService.findTopBySpecId(item.getSpecId());
        ProductInfoEntity product = productInfoService.findTopByProductId(specInfo.getProductId());
        itemInfo.setBarcode(specInfo.getBarcode());
        itemInfo.setProductName(product.getProductName());
        itemInfo.setPreviewImageUrl(product.getPreviewImageUrl());
        itemInfo.setThumbnailImageUrl(product.getThumbnailImageUrl());
        itemInfo.setImageUrl(product.getImageUrl());
        List<StockoutOrderPackMappingInfoEntity> mappingInfoEntityList = packSkuInfo.get(itemInfo.getSku());
        itemInfo.setPackSkuQty(CollectionUtils.isEmpty(mappingInfoEntityList) ? Integer.valueOf(0) : mappingInfoEntityList.get(0).getOriginQty());
        itemInfo.setPackChildrenQty(mappingInfoEntityList.stream().mapToInt(StockoutOrderPackMappingInfoEntity::getMappingQty).sum());
        itemInfo.setPackSkuScanQty(packItemService.countSkuScanQty(stockoutOrderNo, itemInfo.getSku()));
        if (itemInfo.getPackSkuQty() < itemInfo.getPackSkuScanQty()) {
            throw new BusinessServiceException(itemInfo.getSku() + "扫描数大于出库数，请核对");
        }
        return itemInfo;
    }

    public StockoutShipmentPackModel createShipmentBoxByUser(String stockoutOrderNo) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        StockoutShipmentPackModel model = new StockoutShipmentPackModel();
        model.setShipmentBoxCode(generatePackBoxCode());
        StockoutShipmentPackEntity entity = StockoutBuilding.buildSortShipmentPack(stockoutOrder, loginInfoService.getName(), model.getShipmentBoxCode());
        LambdaQueryWrapper<StockoutShipmentPackItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentPackItemEntity::getStockoutOrderNo, stockoutOrderNo)
                .eq(StockoutShipmentPackItemEntity::getIsDeleted, 0);
        List<StockoutShipmentPackItemEntity> list = packItemService.list(wrapper);
        if (!CollectionUtils.isEmpty(list)) {
            List<Integer> collect = list.stream().map(StockoutShipmentPackItemEntity::getShipmentPackId).distinct().collect(Collectors.toList());
            StockoutShipmentPackEntity shipment = getOne(new LambdaQueryWrapper<StockoutShipmentPackEntity>()
                    .in(StockoutShipmentPackEntity::getShipmentPackId, collect).orderByDesc(StockoutShipmentPackEntity::getBoxIndex).last("limit 1"));
            entity.setBoxIndex(shipment == null || shipment.getBoxIndex() == null ? 1 : shipment.getBoxIndex() + 1);
        }
        save(entity);
        BeanUtils.copyProperties(entity, model);
        return model;
    }

    @Transactional
    public StockoutShipmentPackItemInfo confirmSku(StockoutShipmentPackSkuRequest request) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(request.getStockoutOrderNo());
        if (!StockoutOrderStatusEnum.READY_DELIVERY.name().equalsIgnoreCase(stockoutOrder.getStatus()))
            throw new BusinessServiceException("此出库单不是待发货，无法进行pack装箱");
        if (request.getScanQty() < 0)
            throw new BusinessServiceException("扫描数必须大于等于0");
        StockoutShipmentPackEntity packShipment = findTopByShipmentBoxCode(request.getShipmentBoxCode());
        if (StockoutShipmentPackStatusEnum.SYNCED.name().equals(packShipment.getStatus()))
            throw new BusinessServiceException("此箱子已经同步，无法继续操作");
        ProductSpecInfoEntity spec = getPackSkuInfo(request, stockoutOrder);
        List<StockoutShipmentPackItemEntity> packItems = packItemService.findByShipmentIdList(packShipment.getShipmentPackId());
        Map<String, List<StockoutOrderPackMappingInfoEntity>> packSkuInfo = packMappingInfoService.getPackSkuInfo(request.getStockoutOrderNo());
        if (!packSkuInfo.containsKey(spec.getSku())) {
            throw new BusinessServiceException("出库单中无此sku：" + spec.getSku());
        }
        List<StockoutShipmentPackItemEntity> itemEntities = packItems.stream().filter(item -> item.getSku().equalsIgnoreCase(spec.getSku())).collect(Collectors.toList());
        StockoutShipmentPackItemEntity updateItem;
        if (CollectionUtils.isEmpty(itemEntities)) {
            // 还未装箱
            StockoutShipmentPackItemEntity packItemEntity = new StockoutShipmentPackItemEntity();
            packItemEntity.setShipmentPackId(packShipment.getShipmentPackId());
            packItemEntity.setStockoutOrderNo(request.getStockoutOrderNo());
            packItemEntity.setOrderNo(packSkuInfo.get(spec.getSku()).get(0).getOrderNo());
            packItemEntity.setOrderItemId(packSkuInfo.get(spec.getSku()).get(0).getOrderItemNo());
            packItemEntity.setSpecId(spec.getSpecId());
            packItemEntity.setSku(spec.getSku());
            packItemEntity.setQty(request.getScanQty());
            packItemEntity.setIsDeleted(0);
            if (packItemEntity.getQty() == 0) {
                packItemEntity.setIsDeleted(1);
            }
            packItemService.save(packItemEntity);
            updateItem = packItemEntity;
        } else {
            StockoutShipmentPackItemEntity packItemEntity = itemEntities.get(0);
            packItemEntity.setQty(request.getScanQty());
            if (packItemEntity.getQty() == 0) {
                packItemEntity.setIsDeleted(1);
            }
            packItemService.updateById(packItemEntity);
            updateItem = packItemEntity;
        }
        return buildScanItemInfo(updateItem, packSkuInfo, stockoutOrder.getStockoutOrderNo());
    }

    /**
     * 获取Pack sku
     */
    public ProductSpecInfoEntity getPackSkuInfo(StockoutShipmentPackSkuRequest request, StockoutOrderEntity stockoutOrder) {
        // 根据业务条码获取
        if (StockoutOrderTypeEnum.FIRST_LEG_DELIVERY.name().equals(stockoutOrder.getStockoutType()) && stockoutOrder.getHasPack()) {
            List<StockoutOrderPackMappingInfoEntity> packMappingInfoEntities = packMappingInfoService.list(new QueryWrapper<StockoutOrderPackMappingInfoEntity>().lambda()
                    .eq(StockoutOrderPackMappingInfoEntity::getStockoutOrderId, stockoutOrder.getStockoutOrderId()));
            StockoutOrderPackMappingInfoEntity mappingInfoEntity = packMappingInfoEntities.stream()
                    .filter(o -> StringUtils.equals(o.getPackSellerSku(), request.getSku()) || StringUtils.equals(o.getPackSellerBarcode(), request.getSku())).findFirst().orElse(null);
            if (mappingInfoEntity == null) {
                throw new BusinessServiceException(request.getSku() + "该sku在系统中不存在");
            } else {
                request.setSku(mappingInfoEntity.getOriginSku());
            }
        }
        ProductSpecInfoEntity spec = specInfoService.findTopBySku(request.getSku());
        if (spec == null) {
            spec = specInfoService.findTopByBarcode(request.getSku());
            if (spec != null) {
                request.setSku(spec.getSku());
            }
        }
        if (spec == null) {
            throw new BusinessServiceException(request.getSku() + "该sku或条码 在系统中不存在");
        }

        return spec;
    }

    @Transactional
    public void clearBox(StockoutShipmentPackBoxRequest request) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(request.getStockoutOrderNo());
        if (StockoutOrderStatusEnum.DELIVERED.name().equalsIgnoreCase(stockoutOrder.getStatus())) {
            throw new BusinessServiceException("此出库单已发货，无法进行清除操作");
        }
        StockoutShipmentPackEntity packShipment = findTopByShipmentBoxCode(request.getShipmentBoxCode());
        if (StockoutShipmentPackStatusEnum.SYNCED.name().equals(packShipment.getStatus())) {
            throw new BusinessServiceException("此箱子已经同步，无法继续操作");
        }
        List<StockoutShipmentPackItemEntity> packItemEntities = packItemService.findByShipmentIdList(packShipment.getShipmentPackId());
        packItemEntities.forEach(item -> {
            item.setQty(0);
            item.setIsDeleted(1);
            packItemService.updateById(item);
        });

    }

    @Transactional
    public BaseBooleanResponse confirmBox(StockoutShipmentPackBoxRequest request) {
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(request.getStockoutOrderNo());
        if (!StockoutOrderStatusEnum.READY_DELIVERY.name().equalsIgnoreCase(stockoutOrder.getStatus())) {
            throw new BusinessServiceException("此出库单不是待发货，无法操作");
        }
        StockoutShipmentPackEntity packShipment = findTopByShipmentBoxCode(request.getShipmentBoxCode());
        if (StockoutShipmentPackStatusEnum.SYNCED.name().equals(packShipment.getStatus())) {
            throw new BusinessServiceException("此箱子已经同步，无法继续操作");
        }
        request.setShipmentPackId(packShipment.getShipmentPackId());
        BeanUtilsEx.copyProperties(request, packShipment);
        packShipment.setStatus(StockoutShipmentPackStatusEnum.PACKING_END.name());
        packShipment.setShipmentDate(new Date());
        updateById(packShipment);
        // 检查是不是全部完成了扫描  找出全部的箱子，统计所有的pack数量
        List<StockoutShipmentPackItemEntity> itemAll = packItemService.findByStockoutOrderNo(stockoutOrder.getStockoutOrderNo());
        int sum = itemAll.stream().mapToInt(StockoutShipmentPackItemEntity::getQty).sum();
        Integer integer = packMappingInfoService.countPackQty(stockoutOrder.getStockoutOrderId());
        BaseBooleanResponse baseBooleanResponse = new BaseBooleanResponse();
        if (integer == sum) {
            completeScan(stockoutOrder.getStockoutOrderNo());
            baseBooleanResponse.setFlag(true);
        } else {
            baseBooleanResponse.setFlag(false);
        }
        return baseBooleanResponse;
    }

    @Transactional
    public BaseStringResponse completeScan(String stockoutOrderNo) {
        BaseStringResponse response = new BaseStringResponse();
        StockoutOrderEntity stockoutOrder = stockoutOrderService.getByStockoutOrderNo(stockoutOrderNo);
        if (!StockoutOrderStatusEnum.READY_DELIVERY.name().equalsIgnoreCase(stockoutOrder.getStatus()))
            throw new BusinessServiceException("此出库单不是待发货状态，无法进行同步操作");
        LambdaQueryWrapper<StockoutShipmentItemEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockoutShipmentItemEntity::getStockoutOrderNo, stockoutOrderNo).eq(StockoutShipmentItemEntity::getIsDeleted, 0);
        // 旧箱子明细
        List<StockoutShipmentItemEntity> oldItemlist = shipmentItemService.list(wrapper);
        Map<String, List<StockoutShipmentItemEntity>> shipmentItemMap = oldItemlist.stream().collect(Collectors.groupingBy(StockoutShipmentItemEntity::getSku));
        Set<Integer> ids = new HashSet<>();
        // 删除旧箱明细
        oldItemlist.forEach(item -> {
            item.setIsDeleted(1);
            item.setUpdateBy(loginInfoService.getName());
            shipmentItemService.updateById(item);
            ids.add(item.getShipmentId());
        });
        // 删除旧箱
        if (!CollectionUtils.isEmpty(ids)) {
            List<StockoutShipmentEntity> oldShipmentEntities = shipmentService.listByIds(ids);
            oldShipmentEntities.forEach(shipmentEntity -> {
                shipmentEntity.setIsDeleted(1);
                if (shipmentEntity.getStatus().equals(StockoutShipmentStatusEnum.SHIPPED.name()))
                    throw new BusinessServiceException("海外仓部分箱子已经发货，请核对");
                // 重置箱子编码
                if (shipmentEntity.getShipmentBoxCode().contains("Pack-"))
                    shipmentEntity.setShipmentBoxCode(shipmentEntity.getShipmentBoxCode() + "-d-" + new Date().getTime());
                shipmentEntity.setUpdateBy(loginInfoService.getName());
                shipmentService.updateById(shipmentEntity);
            });
            oldShipmentEntities.forEach(shipmentEntity -> pickingBoxService.sendErpPickingBoxSyncRequest(shipmentEntity));
        }
        List<StockoutShipmentPackItemEntity> allPackItems = packItemService.findByStockoutOrderNo(stockoutOrderNo);
        Map<Integer, List<StockoutShipmentPackItemEntity>> packShipments = allPackItems.stream().collect(Collectors.groupingBy(StockoutShipmentPackItemEntity::getShipmentPackId));
        Map<String, List<StockoutOrderPackMappingInfoEntity>> packSkuInfo = packMappingInfoService.getPackSkuInfo(stockoutOrderNo);
        // 生成新箱子
        List<Integer> orderItemId = new ArrayList<>();
        List<StockoutShipmentEntity> newShipment = generateShipment(packShipments, packSkuInfo, stockoutOrder, shipmentItemMap, orderItemId);
        List<StockoutShipmentItemEntity> itemEntities = oldItemlist.stream().filter(it -> !orderItemId.contains(it.getStockoutOrderItemId())).collect(Collectors.toList());
        itemEntities.forEach(itemEntity -> {
            // 把其余的sku放在第一箱
            itemEntity.setShipmentId(newShipment.get(0).getShipmentId());
            itemEntity.setIsDeleted(0);
            shipmentItemService.updateById(itemEntity);
        });
        newShipment.forEach(shipmentEntity -> pickingBoxService.sendErpPickingBoxSyncRequest(shipmentEntity));
        return response;
    }

    private List<StockoutShipmentEntity> generateShipment(Map<Integer, List<StockoutShipmentPackItemEntity>> packShipments, Map<String, List<StockoutOrderPackMappingInfoEntity>> packSkuInfo, StockoutOrderEntity stockoutOrder, Map<String, List<StockoutShipmentItemEntity>> shipmentItemMap, List<Integer> orderItemId) {
        List<StockoutShipmentEntity> newShipment = new ArrayList<>();
        packShipments.forEach((shipmentPackId, packItems) -> {
            StockoutShipmentPackEntity shipmentPackEntity = getById(shipmentPackId);
            if (!StockoutShipmentPackStatusEnum.PACKING_END.name().equals(shipmentPackEntity.getStatus())) {
                throw new BusinessServiceException(shipmentPackEntity.getShipmentBoxCode() + "请核对此箱子的装箱状态！");
            }
            StockoutShipmentEntity entity = new StockoutShipmentEntity();
            BeanUtilsEx.copyProperties(shipmentPackEntity, entity);
            entity.setShipmentBoxCode("Pack-" + shipmentPackEntity.getShipmentBoxCode());
            entity.setStockoutType(stockoutOrder.getStockoutType());
            entity.setPlatformName(stockoutOrder.getPlatformName());
            shipmentService.save(entity);
            newShipment.add(entity);
            packItems.forEach(packItem -> {
                List<StockoutOrderPackMappingInfoEntity> stockoutOrderPackMappingInfoEntities = packSkuInfo.get(packItem.getSku());
                if (CollectionUtils.isEmpty(stockoutOrderPackMappingInfoEntities)) {
                    throw new BusinessServiceException(packItem.getSku() + "该sku不属于此出库单");
                }
                stockoutOrderPackMappingInfoEntities.forEach(mappingInfo -> {
                    // 创建装箱明细
                    StockoutShipmentItemEntity itemEntity = new StockoutShipmentItemEntity();
                    List<StockoutShipmentItemEntity> stockoutShipmentItemEntities1 = shipmentItemMap.get(mappingInfo.getMappingSku());
                    StockoutShipmentItemEntity stockoutShipmentItemEntities = CollectionUtils.isEmpty(stockoutShipmentItemEntities1) ? new StockoutShipmentItemEntity() : stockoutShipmentItemEntities1.get(0);
                    StockoutOrderItemEntity stockoutOrderItemEntity = stockoutOrderItemService.listByStockoutOrderIdAndSkuAndOrderItemId(mappingInfo.getStockoutOrderId(), mappingInfo.getMappingSku(), mappingInfo.getOrderItemNo());
                    if (stockoutOrderItemEntity == null)
                        throw new BusinessServiceException(mappingInfo.getStockoutOrderId() + "该出库单中不存在" + mappingInfo.getMappingSku() + "[订单明细为：" + mappingInfo.getOrderItemNo() + "]的sku,请核对");
                    itemEntity.setStockoutOrderItemId(stockoutOrderItemEntity.getStockoutOrderItemId());
                    itemEntity.setSellerBarcode(stockoutOrderItemEntity.getSellerBarcode());
                    itemEntity.setSellerSku(stockoutOrderItemEntity.getSellerSku());
                    BeanUtilsEx.copyProperties(stockoutOrderItemEntity, itemEntity);
                    itemEntity.setStockoutOrderNo(stockoutOrder.getStockoutOrderNo());
                    itemEntity.setIsDeleted(0);
                    itemEntity.setBatchId(stockoutShipmentItemEntities.getBatchId());
                    itemEntity.setShipmentItemId(null);
                    itemEntity.setOrderItemId(mappingInfo.getOrderItemNo());
                    itemEntity.setQty(mappingInfo.getMappingQty() * packItem.getQty());
                    itemEntity.setShipmentId(entity.getShipmentId());
                    shipmentItemService.save(itemEntity);
                    orderItemId.add(stockoutOrderItemEntity.getStockoutOrderItemId());
                });
            });
        });
        return newShipment;
    }

    private String generatePackBoxCode() {
        LambdaQueryWrapper<StockoutShipmentPackEntity> wrapper = new LambdaQueryWrapper<>();
        for (int i = 1; i < 5; i++) {
            String boxCode = gcService.randomTimeCode("");
            wrapper.eq(StockoutShipmentPackEntity::getShipmentBoxCode, boxCode).last("limit 1");
            StockoutShipmentPackEntity one = getOne(wrapper);
            if (one == null) {
                return boxCode;
            }
            wrapper.clear();
        }
        throw new BusinessServiceException("系统无法生成箱号");
    }

    public List<StockoutOrderPackMappingInfo> packSkuMapping(String stockoutOrderNo, String packSku) {
        Map<String, List<StockoutOrderPackMappingInfoEntity>> packSkuInfo = packMappingInfoService.getPackSkuInfo(stockoutOrderNo);
        List<StockoutOrderPackMappingInfoEntity> stockoutOrderPackMappingInfoEntities = packSkuInfo.get(packSku);
        return stockoutOrderPackMappingInfoEntities.stream().map(item -> {
            StockoutOrderPackMappingInfo info = new StockoutOrderPackMappingInfo();
            BeanUtilsEx.copyProperties(item, info);
            return info;
        }).collect(Collectors.toList());

    }

    public Map<Integer, List<StockoutShipmentPackItemEntity>> getByStockoutOrderNo(String stockoutOrderNo) {
        List<StockoutShipmentPackItemEntity> packItemServiceByStockoutOrderNo = packItemService.findByStockoutOrderNo(stockoutOrderNo);
        return packItemServiceByStockoutOrderNo.stream().collect(Collectors.groupingBy(StockoutShipmentPackItemEntity::getShipmentPackId));
    }

    @JLock(keyConstant = "packConfirmSkuLock", lockKey = "#request.shipmentBoxCode")
    public StockoutShipmentPackItemInfo confirmSkuLock(StockoutShipmentPackSkuRequest request) {
        return context.getBean(this.getClass()).confirmSku(request);
    }

    @JLock(keyConstant = "packCompleteScanLock", lockKey = "#stockoutOrderNo")
    public BaseStringResponse completeScanLock(String stockoutOrderNo) {
        return context.getBean(this.getClass()).completeScan(stockoutOrderNo);
    }

    public StockoutShipmentSearchCount pageSearchCount(StockoutShipmentSearchRequest request) {
        StockoutBuilding.buildRequest(request);

        return this.getBaseMapper().pageSearchCount(request);
    }

    // 箱子发货前的校验
    public void validDeliveryBox(StockoutOrderEntity orderEntity) {
        if (!orderEntity.getHasPack()) {
            return;
        }
        List<StockoutShipmentPackItemEntity> byStockoutOrderNo = packItemService.findByStockoutOrderNo(orderEntity.getStockoutOrderNo());
        List<Integer> packIds = byStockoutOrderNo.stream().map(StockoutShipmentPackItemEntity::getShipmentPackId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packIds)) {
            throw new BusinessServiceException("暂无Pack装箱，无法发货");
        }
        List<StockoutShipmentPackEntity> stockoutShipmentPackEntities = listByIds(packIds);
        List<String> packCode = stockoutShipmentPackEntities.stream().map(it -> "Pack-" + it.getShipmentBoxCode()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(packCode)) {
            return;
        }
        List<StockoutShipmentEntity> entityByShipmentBoxCodeList = shipmentService.getEntityByShipmentBoxCodeList(packCode);
        if (stockoutShipmentPackEntities.size() != entityByShipmentBoxCodeList.size()) {
            throw new BusinessServiceException("复核Pack装箱数 和 装箱清单数不符，请核对后重新点击完成扫描");
        }
    }

    public List<ShipmentBoxSkuExport> packShipmentBoxSkuList(ShipmentDownloadRequest request) {
        List<ShipmentBoxSkuExport> shipmentBoxSkuExports = this.baseMapper.packShipmentBoxSkuList(request.getShipmentIds(), request.getDeclareDocumentNo());

        shipmentBoxSkuExports.forEach(export -> {
            StockoutOrderEntity stockoutOrder = stockoutOrderService.findByStockoutOrderNo(export.getStockoutOrderNo());
            if (Objects.isNull(stockoutOrder)) {
                LOGGER.error("出库单 {} 不存在", export.getStockoutOrderNo());
                return;
            }
            StockoutOrderItemEntity stockoutOrderItem = stockoutOrderItemService.getByStockoutOrderIdAndOrderItemId(stockoutOrder.getStockoutOrderId(), export.getOrderItemId());
            if (Objects.isNull(stockoutOrderItem)) {
                LOGGER.error("出库单明细 {} {} {} 不存在", export.getStockoutOrderNo(), export.getSku(), export.getOrderItemId());
                return;
            }
            export.setStoreSku(stockoutOrderItem.getSellerSku());
            export.setStoreBarcode(stockoutOrderItem.getSellerBarcode());
            export.setInvoicePrice(stockoutOrderItem.getInvoicePrice().toString());
        });

        return shipmentBoxSkuExports;
    }

    public StockoutPackShipmentItemSkuInfoResponse getSkuListByShipmentBoxCode(String shipmentBoxCode, Integer isOriginBox) {
        StockoutShipmentPackEntity shipmentEntity = this.getOne(new QueryWrapper<StockoutShipmentPackEntity>().lambda().eq(StockoutShipmentPackEntity::getShipmentBoxCode, shipmentBoxCode).last("limit 1"));
        if (shipmentEntity == null)
            throw new BusinessServiceException(isOriginBox == 1 ? "源箱号不存在" : "目的箱号不存在");
        StockoutPackShipmentItemSkuInfoResponse itemSkuInfo = new StockoutPackShipmentItemSkuInfoResponse();
        itemSkuInfo.setBoxIndex(shipmentEntity.getBoxIndex());
        if (shipmentEntity.getStatus().equalsIgnoreCase(StockoutShipmentPackStatusEnum.SYNCED.name()))
            throw new BusinessServiceException("已同步的箱子不能调拨");
        if (isOriginBox == null || isOriginBox != 1) {
            return itemSkuInfo;
        }
        List<StockoutShipmentPackItemEntity> shipmentItemEntities = packItemService.findByShipmentIdList(shipmentEntity.getShipmentPackId());
        if (CollectionUtils.isEmpty(shipmentItemEntities)) throw new BusinessServiceException("请选择不是空箱的箱子");
        List<String> skuList = shipmentItemEntities.stream().map(StockoutShipmentPackItemEntity::getSku).collect(Collectors.toList());
        List<ProductSpecInfoEntity> specInfoEntities = specInfoService.findAllBySkuIn(skuList);
        Map<String, ProductSpecInfoEntity> specMap = specInfoEntities.stream().collect(Collectors.toMap(ProductSpecInfoEntity::getSku, t -> t));
        List<PackShipmentTransferSku> skuInfoList = shipmentItemEntities.stream().map(item -> {
            PackShipmentTransferSku transferSku = new PackShipmentTransferSku();
            ProductSpecInfoEntity spec = specMap.get(item.getSku());
            if (Objects.isNull(spec))
                throw new BusinessServiceException("未找到箱子中sku[" + item.getSku() + "]对应的商品记录");
            BeanUtils.copyProperties(item, transferSku);
            transferSku.setBarcode(spec.getBarcode());
            ProductInfoEntity productInfo = productInfoService.findTopByProductId(spec.getProductId());
            transferSku.setProductName(productInfo.getProductName());
            transferSku.setSpu(productInfo.getSpu());
            transferSku.setSize(spec.getSize());
            transferSku.setColor(spec.getColor());
            StockoutOrderEntity one = stockoutOrderService.getOne(new LambdaQueryWrapper<StockoutOrderEntity>().eq(StockoutOrderEntity::getStockoutOrderNo, item.getStockoutOrderNo()));
            if (Objects.isNull(one) || Objects.isNull(one.getStoreId())) {
                return transferSku;
            }
            transferSku.setStoreBarcode(productStoreSkuMappingService.getStoreBarcode(one.getStoreId(), spec.getSku()));
            return transferSku;
        }).collect(Collectors.toList());
        itemSkuInfo.setSkuList(skuInfoList);
        return itemSkuInfo;
    }

    @Transactional
    @JLock(keyConstant = "transferPackShipmentBox", lockKey = "#request.originShipmentBoxCode")
    public void transferShipmentBox(StockoutShipmentPackItemSkuInfoRequest request) {
        StockoutShipmentPackEntity originBox = findTopByShipmentBoxCode(request.getOriginShipmentBoxCode());
        StockoutShipmentPackEntity targetBox = findTopByShipmentBoxCode(request.getTargetShipmentBoxCode());
        List<StockoutShipmentPackItemEntity> shipmentItemEntities = packItemService.findByShipmentIdList(originBox.getShipmentPackId());
        shipmentValid.validTransfiferPackBox(originBox, targetBox, shipmentItemEntities, request);
        // 同步.net装箱清单
        List<StockoutShipmentPackItemEntity> originItemList = new LinkedList<>();
        List<StockoutShipmentPackItemEntity> targetItemList = new LinkedList<>();
        for (PackShipmentTransferSku transferSku : request.getSkuList()) {
            if (transferSku.getAllocateNum() == 0) continue; // 没有要调拨的数量
            StockoutShipmentPackItemEntity topForUpdateTransfer = packItemService.findTopForUpdateTransfer(originBox.getShipmentPackId(), transferSku.getSku(), transferSku.getOrderNo(), transferSku.getStockoutOrderNo());
            if (topForUpdateTransfer == null)
                throw new BusinessServiceException(transferSku.getSku() + "在原箱子找不到");
            topForUpdateTransfer.setQty(transferSku.getQty());
            originItemList.add(topForUpdateTransfer);
            StockoutShipmentPackItemEntity shipmentItem = packItemService.getById(transferSku.getShipmentPackItemId());
            if (shipmentItem.getQty() != transferSku.getQty() + transferSku.getAllocateNum())
                throw new BusinessServiceException(shipmentItem.getSku() + "此sku调拨数量不正确，请到箱子确认！");
            // 整批货被调拨
            if (transferSku.getQty() == 0) {
                shipmentItem.setUpdateBy(loginInfoService.getName());
                shipmentItem.setQty(0);
                shipmentItem.setIsDeleted(IsDeletedConstant.DELETED);
                packItemService.updateById(shipmentItem);
            } else {
                // 部分调拨
                StockoutShipmentPackItemEntity entity = new StockoutShipmentPackItemEntity();
                entity.setShipmentPackItemId(transferSku.getShipmentPackItemId());
                entity.setQty(transferSku.getQty());
                packItemService.updateById(entity);
            }
            targetItemList.add(transferTonNewBox(transferSku, targetBox));
        }
        if (!originItemList.isEmpty() && !targetItemList.isEmpty()) {
            originBox.setUpdateBy(loginInfoService.getName());
            updateById(originBox);
            targetBox.setUpdateBy(loginInfoService.getName());
            updateById(targetBox);
        }
    }

    // 查询目的箱号是否有同批号的，如果没有就新增，如果有就叠加
    private StockoutShipmentPackItemEntity transferTonNewBox(PackShipmentTransferSku transferSku, StockoutShipmentPackEntity targetBox) {
        StockoutShipmentPackItemEntity updateEntity = packItemService.findTopForUpdateTransfer(targetBox.getShipmentPackId(), transferSku.getSku(), transferSku.getOrderNo(), transferSku.getStockoutOrderNo());
        if (updateEntity == null) {
            updateEntity = new StockoutShipmentPackItemEntity();
            BeanUtils.copyProperties(transferSku, updateEntity, "shipmentPackItemId");
            updateEntity.setShipmentPackId(targetBox.getShipmentPackId());
            updateEntity.setQty(transferSku.getAllocateNum());
            updateEntity.setCreateBy(loginInfoService.getName());
        } else {
            updateEntity.setQty(updateEntity.getQty() + transferSku.getAllocateNum());
        }
        updateEntity.setUpdateBy(loginInfoService.getName());
        packItemService.saveOrUpdate(updateEntity);
        return updateEntity;
    }

    public PrintListResponse printPackSellerSkuAll(StockoutAmazonPackPrintRequest orderNos) {
        List<StockoutOrderEntity> stockoutOrderEntityList = stockoutOrderService.getByStockoutOrderNoList(orderNos.getStringList());
        List<StockoutOrderPackMappingInfoEntity> allPackSellerList = packMappingInfoService.findListByStockoutOrderIds(stockoutOrderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()))
                .stream().filter(o -> StringUtils.hasText(o.getPackSellerSku())).collect(Collectors.toList());
        if (allPackSellerList.isEmpty())
            throw new BusinessServiceException("无Pack 业务条码信息，无法打印");
        // 打印t code
        List<StockoutTransparencyCodeEntity> byStockouOrderNo = transparencyCodeService.findByStockouOrderNos(orderNos.getStringList());
        if (!CollectionUtils.isEmpty(byStockouOrderNo)) {
            return stockoutOrderPrintService.printAllTcode(stockoutOrderEntityList, false);
        }

        List<StockoutOrderItemEntity> stockoutOrderItemEntityList = stockoutOrderItemService.listByStockoutOrderIds(stockoutOrderEntityList.stream().map(StockoutOrderEntity::getStockoutOrderId).collect(Collectors.toList()));

        PrintListResponse response = new PrintListResponse();
        response.setHtmlList(new ArrayList<>());

        PrintTemplateEntity template = printService.getByName(PrintTemplateNameEnum.PACK_SELLER_BARCODE.getTemplateName());

        Map<String, List<StockoutOrderPackMappingInfoEntity>> packInfoMap = allPackSellerList.stream().collect(Collectors.groupingBy(item -> item.getOriginSku() + "_" + item.getStockoutOrderId()));
        List<String> htmlList = new ArrayList<>();
        packInfoMap.forEach((key, value) -> {
            PackSellerPrintDTO dto = new PackSellerPrintDTO();
            dto.setSellerSku(value.get(0).getPackSellerSku());
            dto.setSellerBarcode(value.get(0).getPackSellerBarcode());
            StockoutOrderItemEntity curItem = stockoutOrderItemEntityList.stream().filter(o -> o.getOrderItemId().equals(value.get(0).getOrderItemNo())).findFirst().orElse(null);
            if (curItem != null) {
                dto.setSellerText(curItem.getSellerText());
                if (StringUtils.hasText(curItem.getSellerTitle()) && curItem.getSellerTitle().length() > 30) {
                    dto.setSellerTitle(curItem.getSellerTitle().substring(0, 15) + "..." + curItem.getSellerTitle().substring(curItem.getSellerTitle().length() - 15));
                } else {
                    dto.setSellerTitle(curItem.getSellerTitle());
                }
            }
            String transfer = PrintTransferUtils.transfer(template.getContent(), dto);
            for (int i = 0; i < value.get(0).getOriginQty(); i++) {
                htmlList.add(transfer);
            }
        });

        List<String> list = PrintTransferUtils.doubleTransfer(orderNos.getSinglePrint(), htmlList, template);
        response.setHtmlList(list);
        response.setSpec(template.getSpec());
        response.setTemplateName(template.getName());
        return response;
    }

    public List<StockoutShipmentPackEntity> getStockShipmentByPackShipmentId(List<Integer> packShipmentIdList) {
        LambdaQueryWrapper<StockoutShipmentPackEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentPackEntity::getShipmentPackId, packShipmentIdList);
        queryWrapper.eq(StockoutShipmentPackEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

    public void cancelPackShipment(StockoutOrderEntity stockoutOrderEntity) {
        List<StockoutShipmentPackItemEntity> packItemEntities = packItemService.findByStockoutOrderNo(stockoutOrderEntity.getStockoutOrderNo());
        if (CollectionUtils.isEmpty(packItemEntities)) {
            return;
        }
        List<Integer> list = new ArrayList<>();
        packItemEntities.forEach(packItem -> {
            packItem.setIsDeleted(IsDeletedConstant.DELETED);
            list.add(packItem.getShipmentPackId());
        });
        packItemService.updateBatchById(packItemEntities);
        update(new LambdaUpdateWrapper<StockoutShipmentPackEntity>().in(StockoutShipmentPackEntity::getShipmentPackId, list.stream().distinct().collect(Collectors.toList()))
                .set(StockoutShipmentPackEntity::getIsDeleted, IsDeletedConstant.DELETED));
    }

    public List<StockoutShipmentPackEntity> findByShipmentPackIdsList(List<Integer> shipmentPackIdList) {
        LambdaQueryWrapper<StockoutShipmentPackEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(StockoutShipmentPackEntity::getShipmentPackId, shipmentPackIdList);
        queryWrapper.eq(StockoutShipmentPackEntity::getIsDeleted, 0);
        return list(queryWrapper);
    }

}
