package com.nsy.wms.business.service.stock;

import com.alibaba.fastjson.JSONObject;
import com.nsy.api.wms.domain.stock.StockDifferenceCheckOrderExport;
import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.DictionaryNameEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeEnum;
import com.nsy.api.wms.enumeration.stock.StockChangeLogTypeModuleEnum;
import com.nsy.api.wms.enumeration.stock.StockDifferenceCheckOrderStatusEnum;
import com.nsy.api.wms.enumeration.stock.TakeStockPlanTypeEnum;
import com.nsy.api.wms.enumeration.stock.TakeStockTaskModeEnum;
import com.nsy.api.wms.enumeration.stockout.StockTakeStockTaskStatusEnum;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stock.ConfirmDifferenceRequest;
import com.nsy.api.wms.request.stock.StockDifferenceCheckOrderListRequest;
import com.nsy.api.wms.request.stock.StockUpdateRequest;
import com.nsy.api.wms.request.stockin.TabCountRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.ExceptionPositionListResponse;
import com.nsy.api.wms.response.stock.StockDifferenceCheckOrderItemResponse;
import com.nsy.api.wms.response.stock.StockDifferenceCheckOrderListResponse;
import com.nsy.api.wms.response.stockin.TaskListCountResponse;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.wms.business.service.download.IDownloadService;
import com.nsy.wms.repository.jpa.mapper.bd.BdPositionMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockDifferenceCheckOrderMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockMapper;
import com.nsy.wms.repository.entity.base.BaseMpEntity;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.stock.StockDifferenceCheckOrderEntity;
import com.nsy.wms.repository.entity.stock.StockDifferenceCheckOrderItemEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockPlanEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskItemEntity;
import com.nsy.wms.business.manage.erp.ErpApiService;
import com.nsy.wms.business.manage.erp.request.ErpInventoryRequest;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import com.nsy.wms.utils.JsonMapper;
import com.nsy.wms.utils.mp.TenantContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StockDifferenceCheckOrderService extends ServiceImpl<StockDifferenceCheckOrderMapper, StockDifferenceCheckOrderEntity> implements IDownloadService {

    @Autowired
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockDifferenceCheckOrderItemService stockDifferenceCheckOrderItemService;
    @Autowired
    LoginInfoService loginInfoService;
    @Autowired
    StockService stockService;
    @Autowired
    BdPositionMapper bdPositionMapper;
    @Autowired
    StockTakeStockTaskService taskService;
    @Autowired
    StockTakeStockTaskItemService taskItemService;
    @Autowired
    StockTakeStockLogService takeStockLogService;
    @Autowired
    StockDifferenceCheckOrderService stockDifferenceCheckOrderService;
    @Autowired
    StockTakeStockTaskItemService stockTakeStockTaskItemService;
    @Autowired
    StockTakeStockLogService stockTakeStockLogService;
    @Autowired
    ErpApiService erpApiService;
    @Autowired
    StockMapper stockMapper;

    public PageResponse<StockDifferenceCheckOrderListResponse> pageList(StockDifferenceCheckOrderListRequest request) {
        PageResponse<StockDifferenceCheckOrderListResponse> pageResponse = new PageResponse<>();
        Page<StockDifferenceCheckOrderListResponse> page = new Page<>(request.getPageIndex(), request.getPageSize());
        List<StockDifferenceCheckOrderListResponse> list = this.baseMapper.pageList(page, request);
        Map<String, String> varianceChecklistStatusEnumMap = enumConversionChineseUtils.baseConversion(DictionaryNameEnum.WMS_VARIANCE_CHECKLIST_STATUS.getName());
        list.forEach(stockDifferenceCheckOrderListResponse -> {
            stockDifferenceCheckOrderListResponse.setStatusStr(varianceChecklistStatusEnumMap.get(stockDifferenceCheckOrderListResponse.getStatus()));
            stockDifferenceCheckOrderListResponse.setDifferenceType(stockDifferenceCheckOrderListResponse.getDifferenceQty() > 0 ? "盘亏" : "盘盈");
        });
        pageResponse.setContent(list);
        pageResponse.setTotalCount(page.getTotal());
        return pageResponse;
    }

    public List<TaskListCountResponse> getTaskListCount(TabCountRequest request) {
        Map<String, List<TaskListCountResponse>> collect = this.getBaseMapper().countByStatus(request).stream().collect(Collectors.groupingBy(TaskListCountResponse::getStatus));
        List<TaskListCountResponse> list = Arrays.stream(StockDifferenceCheckOrderStatusEnum.values()).map(statusEnum -> {
            TaskListCountResponse response = new TaskListCountResponse();
            List<TaskListCountResponse> responses = collect.get(statusEnum.name());
            response.setQty(CollectionUtils.isEmpty(responses) ? Integer.valueOf(0) : responses.get(0).getQty());
            response.setStatus(statusEnum.name());
            response.setValue(statusEnum.name());
            response.setLabel(statusEnum.getStatus());
            return response;
        }).collect(Collectors.toList());
        TaskListCountResponse response = new TaskListCountResponse();
        response.setQty(list.stream().mapToInt(TaskListCountResponse::getQty).sum());
        response.setStatus("ALL");
        response.setValue("ALL");
        response.setLabel("所有");
        list.add(response);
        return list;
    }

    public List<StockDifferenceCheckOrderItemResponse> itemList(Integer checkOrderId) {
        return this.baseMapper.itemList(checkOrderId);
    }

    @Transactional
    public void confirmDifference(ConfirmDifferenceRequest request) {
        if (CollectionUtils.isEmpty(request.getList())) {
            throw new BusinessServiceException("参数错误");
        }
        // 更新确认数量
        List<StockDifferenceCheckOrderItemEntity> stockDifferenceCheckOrderItemEntityList = stockDifferenceCheckOrderItemService.getBaseMapper()
                .selectBatchIds(request.getList().stream().map(ConfirmDifferenceRequest.ConfirmDifferenceParam::getCheckOrderItemId).collect(Collectors.toList()));
        StockDifferenceCheckOrderEntity checkOrderEntity = this.baseMapper.selectById(stockDifferenceCheckOrderItemEntityList.get(0).getCheckOrderId());
        if (!checkOrderEntity.getStatus().equals(StockDifferenceCheckOrderStatusEnum.TO_BE_CONFIRMED.name())) {
            throw new BusinessServiceException("该差异核对单状态不是待确认");
        }
        checkOrderEntity.setStatus(StockDifferenceCheckOrderStatusEnum.CONFIRMED.name());
        checkOrderEntity.setOperator(loginInfoService.getName());
        checkOrderEntity.setOperateDate(new Date());
        this.baseMapper.updateById(checkOrderEntity);

        Map<Integer, StockDifferenceCheckOrderItemEntity> map = stockDifferenceCheckOrderItemEntityList.stream()
                .collect(Collectors.toMap(StockDifferenceCheckOrderItemEntity::getCheckOrderItemId, itemEntity -> itemEntity));
        request.getList().forEach(orderItemEntity -> {
            map.get(orderItemEntity.getCheckOrderItemId()).setConfirmQty(orderItemEntity.getConfirmQty());
            map.get(orderItemEntity.getCheckOrderItemId()).setUpdateBy(loginInfoService.getName());
        });
        stockDifferenceCheckOrderItemService.saveOrUpdateBatch(stockDifferenceCheckOrderItemEntityList);
        // 更新异常库位sku库存
        List<BdPositionEntity> positionEntityList = bdPositionMapper.selectBatchIds(stockDifferenceCheckOrderItemEntityList.stream()
                .map(StockDifferenceCheckOrderItemEntity::getPositionId).collect(Collectors.toList()));
        Map<Integer, BdPositionEntity> positionEntityMap = positionEntityList.stream().collect(Collectors.toMap(BdPositionEntity::getPositionId, positionEntity -> positionEntity));
        List<StockUpdateRequest> updateRequests = new LinkedList<>();
        stockDifferenceCheckOrderItemEntityList.forEach(itemEntity -> {
            StockUpdateRequest stockUpdateRequest = new StockUpdateRequest();
            stockUpdateRequest.setSku(itemEntity.getSku());
            stockUpdateRequest.setPositionCode(positionEntityMap.get(itemEntity.getPositionId()).getPositionCode());
            stockUpdateRequest.setChangeLogType(StockChangeLogTypeEnum.DIFFERENCE_CHECK);
            stockUpdateRequest.setTypeModule(StockChangeLogTypeModuleEnum.STOCK_INSIDE);
            stockUpdateRequest.setQty(-itemEntity.getConfirmQty());
            stockUpdateRequest.setTakeTaskId(checkOrderEntity.getTaskId());
            updateRequests.add(stockUpdateRequest);
        });
        stockService.updateStockBatch(updateRequests);
        // ERP生成盘点单，差异为0则不生成
        generateErpInventory(stockDifferenceCheckOrderItemEntityList);
    }

    /**
     * 生成erp盘点单
     */
    public void generateErpInventory(List<StockDifferenceCheckOrderItemEntity> stockDifferenceCheckOrderItemEntityList) {
        stockDifferenceCheckOrderItemEntityList.stream().filter(t -> t.getConfirmQty() != 0)
                .collect(Collectors.groupingBy(StockDifferenceCheckOrderItemEntity::getPositionCode))
                .forEach((positionCode, differenceOrderItemList) -> {
                    List<ErpInventoryRequest.ErpInventoryItem> inventoryItemList = differenceOrderItemList.stream().map(diffItem -> {
                        ErpInventoryRequest.ErpInventoryItem inventoryItem = new ErpInventoryRequest.ErpInventoryItem();
                        inventoryItem.setQty(diffItem.getConfirmQty());
                        inventoryItem.setSku(diffItem.getSku());
                        return inventoryItem;
                    }).collect(Collectors.toList());
                    ErpInventoryRequest inventoryRequest = new ErpInventoryRequest();
                    inventoryRequest.setInventoryItemList(inventoryItemList);
                    inventoryRequest.setLocation(TenantContext.getTenant());
                    inventoryRequest.setOperator(loginInfoService.getUserName());
                    inventoryRequest.setPositionCode(positionCode);
                    erpApiService.inventory(inventoryRequest);
                });
    }
    public void createTakeStockTask(IdListRequest request) {
        if (CollectionUtils.isEmpty(request.getIdList())) {
            throw new BusinessServiceException("参数错误");
        }
        List<StockDifferenceCheckOrderEntity> checkOrderList = this.baseMapper.selectBatchIds(request.getIdList());
        if (CollectionUtils.isEmpty(checkOrderList)) {
            throw new BusinessServiceException("查询不到差异核对单！");
        }
        checkOrderList.forEach(order -> {
            if (StockDifferenceCheckOrderStatusEnum.TO_BE_INVENTORY.name().equals(order.getStatus())
                    || StockDifferenceCheckOrderStatusEnum.INVENTORYING.name().equals(order.getStatus())) {
                throw new BusinessServiceException("不能选择待盘点，盘点中的差异核对单！");
            }
        });

        List<StockDifferenceCheckOrderItemEntity> checkOrderItemList = stockDifferenceCheckOrderItemService.list(Wrappers.<StockDifferenceCheckOrderItemEntity>lambdaQuery()
                .in(StockDifferenceCheckOrderItemEntity::getCheckOrderId, request.getIdList())
        );
        createTask(null, checkOrderItemList);
    }

    /**
     * 生成差异盘点任务
     */
    @Transactional
    public void createTask(StockTakeStockPlanEntity plan, List<StockDifferenceCheckOrderItemEntity> checkOrderItemList) {
        if (!CollectionUtils.isEmpty(checkOrderItemList)) {
            List<Integer> checkOrderIds = checkOrderItemList.stream().map(StockDifferenceCheckOrderItemEntity::getCheckOrderId).distinct().collect(Collectors.toList());
            List<Integer> specIds = checkOrderItemList.stream().map(StockDifferenceCheckOrderItemEntity::getSpecId).distinct().collect(Collectors.toList());
            List<StockEntity> stockList = stockMapper.queryStockBySpecIds(specIds);
            if (CollectionUtils.isEmpty(stockList)) {
                return;
            }
            int spaceAreaQty = (int) stockList.stream().mapToInt(StockEntity::getSpaceAreaId).distinct().count();
            int positionQty = (int) stockList.stream().mapToInt(StockEntity::getPositionId).distinct().count();
            StockTakeStockTaskEntity taskEntity = new StockTakeStockTaskEntity();
            taskEntity.setPlanId(ObjectUtils.isEmpty(plan) ? null : plan.getPlanId());
            taskEntity.setSupervisor(ObjectUtils.isEmpty(plan) ? null : plan.getSupervisor());
            taskEntity.setLocation(TenantContext.getTenant());
            taskEntity.setPlanType(TakeStockPlanTypeEnum.VARIANCE_INVENTORY.getCode());
            taskEntity.setTaskGenerateMode(TakeStockTaskModeEnum.BY_SKU.getCode());
            taskEntity.setSpaceAreaQty(spaceAreaQty);
            taskEntity.setPositionQty(positionQty);
            taskEntity.setStatus(StockTakeStockTaskStatusEnum.TO_BE_INVENTORY.name());
            taskEntity.setCreateBy(loginInfoService.getName());
            taskService.save(taskEntity);
            List<StockTakeStockTaskItemEntity> taskItemEntityList = stockList.stream().map(s -> {
                StockTakeStockTaskItemEntity taskItemEntity = new StockTakeStockTaskItemEntity();
                BeanUtils.copyProperties(s, taskItemEntity);
                taskItemEntity.setTaskId(taskEntity.getTaskId());
                taskItemEntity.setBeforeQty(s.getStock());
                taskItemEntity.setStatus(StockTakeStockTaskStatusEnum.TO_BE_INVENTORY.name());
                taskItemEntity.setCreateBy(loginInfoService.getName());
                return taskItemEntity;
            }).collect(Collectors.toList());
            taskItemService.saveBatch(taskItemEntityList);
            // 更新差异单状态：待盘点
            update(Wrappers.<StockDifferenceCheckOrderEntity>lambdaUpdate()
                    .in(StockDifferenceCheckOrderEntity::getCheckOrderId, checkOrderIds)
                    .set(StockDifferenceCheckOrderEntity::getStatus, StockDifferenceCheckOrderStatusEnum.TO_BE_INVENTORY.name())
                    .set(BaseMpEntity::getUpdateBy, loginInfoService.getName())
                    .set(StockDifferenceCheckOrderEntity::getTaskId, taskEntity.getTaskId())
            );
            takeStockLogService.addLog(taskEntity.getTaskId(), "盘点任务生成", "盘点任务生成");
        }
    }

    @Override
    public QuartzDownloadQueueTypeEnum type() {
        return QuartzDownloadQueueTypeEnum.WMS_DIFFERENCE_CHECK_ORDER;
    }

    @Override
    public DownloadResponse queryExportData(DownloadRequest request) {
        DownloadResponse response = new DownloadResponse();
        if (request.getPageIndex() * request.getPageSize() <= 1000) {
            StockDifferenceCheckOrderListRequest stockDifferenceCheckOrderListRequest = JSONObject.parseObject(request.getRequestContent(), StockDifferenceCheckOrderListRequest.class);
            stockDifferenceCheckOrderListRequest.setPageIndex(request.getPageIndex());
            stockDifferenceCheckOrderListRequest.setPageSize(request.getPageSize());
            PageResponse<StockDifferenceCheckOrderListResponse> pageResponse = this.pageList(stockDifferenceCheckOrderListRequest);
            if (CollectionUtils.isEmpty(pageResponse.getContent())) {
                response.setTotalCount(pageResponse.getTotalCount());
                response.setDataJsonStr(JsonMapper.toJson(Lists.newArrayList()));
                return response;
            }
            Map<Integer, StockDifferenceCheckOrderListResponse> map = pageResponse.getContent().stream()
                    .collect(Collectors.toMap(StockDifferenceCheckOrderListResponse::getCheckOrderId, listResponse -> listResponse));
            List<Integer> checkOrderIds = pageResponse.getContent().stream().map(StockDifferenceCheckOrderListResponse::getCheckOrderId).collect(Collectors.toList());
            List<StockDifferenceCheckOrderExport> exportList = this.baseMapper.queryExportData(checkOrderIds);
            exportList.forEach(export -> {
                export.setDifferenceType(map.get(export.getCheckOrderId()).getDifferenceType());
                export.setStatus(map.get(export.getCheckOrderId()).getStatusStr());
            });
            response.setTotalCount(pageResponse.getTotalCount());
            response.setDataJsonStr(JsonMapper.toJson(exportList));
        } else {
            response.setTotalCount(0L);
            response.setDataJsonStr(JsonMapper.toJson(new ArrayList<ExceptionPositionListResponse>()));
        }
        return response;
    }
}
