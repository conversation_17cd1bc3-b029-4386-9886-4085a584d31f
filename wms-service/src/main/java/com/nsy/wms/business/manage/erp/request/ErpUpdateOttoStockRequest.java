package com.nsy.wms.business.manage.erp.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class ErpUpdateOttoStockRequest {

    @JsonProperty("OttoSkuStockInfoList")
    private List<Item> ottoSkuStockInfoList;

    public List<Item> getOttoSkuStockInfoList() {
        return ottoSkuStockInfoList;
    }

    public void setOttoSkuStockInfoList(List<Item> ottoSkuStockInfoList) {
        this.ottoSkuStockInfoList = ottoSkuStockInfoList;
    }

    public static class Item {
        @JsonProperty("StoreSku")
        private String storeSku;

        @JsonProperty("Stock")
        private Integer stock;

        public String getStoreSku() {
            return storeSku;
        }

        public void setStoreSku(String storeSku) {
            this.storeSku = storeSku;
        }

        public Integer getStock() {
            return stock;
        }

        public void setStock(Integer stock) {
            this.stock = stock;
        }
    }
}
