package com.nsy.wms.business.manage.erp.response;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ErpReturnApplyInfo {

    @JsonProperty("ReturnApplyId")
    private Integer returnApplyId;

    @JsonProperty("OrderNo")
    private String orderNo;

    @JsonProperty("ConfirmType")
    private String confirmType;

    public Integer getReturnApplyId() {
        return returnApplyId;
    }

    public void setReturnApplyId(Integer returnApplyId) {
        this.returnApplyId = returnApplyId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getConfirmType() {
        return confirmType;
    }

    public void setConfirmType(String confirmType) {
        this.confirmType = confirmType;
    }
}
