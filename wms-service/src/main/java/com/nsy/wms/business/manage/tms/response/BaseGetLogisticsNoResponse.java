package com.nsy.wms.business.manage.tms.response;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class BaseGetLogisticsNoResponse {
    @ApiModelProperty(value = "物流单号", name = "logisticsNo")
    private String logisticsNo;
    @ApiModelProperty(value = "面单路径", name = "labelUrlList")
    private List<String> labelUrlList;
    @ApiModelProperty(value = "（UPS、Fedex使用）发件国家发票是否支持无纸化，支持为true，不支持为false", name = "supportPaperless")
    private Boolean supportPaperless;
    @ApiModelProperty(value = "UPS特有物流单号集合(包括主从单号)", name = "logisticsNos")
    private String logisticsNos;
    @ApiModelProperty(value = "FedEx物流单号集合(包括主从单号)", name = "logisticsNoList", hidden = true)
    private List<String> logisticsNoList;
    /**
     * 计费重
     */
    private String chargeWeight;
    private String responseJson;
    private int status;
    /**
     * 菜鸟物流独有
     */
    private String lpNumber;

    private String logisticsTid;

    private Boolean isBaoguanOf9610;

    private String receiveCountryCode;

    private String secondaryNumber;

    private Boolean fedexV2;

    //amazonBuyShippingFlag（购买配置标识：0：不设置、1，设置）
    private Integer amazonBuyShippingFlag = 0;

    private boolean success = true;

    public String getLogisticsTid() {
        return logisticsTid;
    }

    public void setLogisticsTid(String logisticsTid) {
        this.logisticsTid = logisticsTid;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Boolean getFedexV2() {
        return fedexV2;
    }

    public void setFedexV2(Boolean fedexV2) {
        this.fedexV2 = fedexV2;
    }

    public String getSecondaryNumber() {
        return secondaryNumber;
    }

    public void setSecondaryNumber(String secondaryNumber) {
        this.secondaryNumber = secondaryNumber;
    }

    public List<String> getLabelUrlList() {
        return labelUrlList;
    }

    public void setLabelUrlList(List<String> labelUrlList) {
        this.labelUrlList = labelUrlList;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getChargeWeight() {
        return chargeWeight;
    }

    public void setChargeWeight(String chargeWeight) {
        this.chargeWeight = chargeWeight;
    }

    public String getResponseJson() {
        return responseJson;
    }

    public void setResponseJson(String responseJson) {
        this.responseJson = responseJson;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getLpNumber() {
        return lpNumber;
    }

    public List<String> getLogisticsNoList() {
        return logisticsNoList;
    }

    public void setLogisticsNoList(List<String> logisticsNoList) {
        this.logisticsNoList = logisticsNoList;
    }

    public void setLpNumber(String lpNumber) {
        this.lpNumber = lpNumber;
    }

    public Boolean getBaoguanOf9610() {
        return isBaoguanOf9610;
    }

    public void setBaoguanOf9610(Boolean baoguanOf9610) {
        isBaoguanOf9610 = baoguanOf9610;
    }

    public String getReceiveCountryCode() {
        return receiveCountryCode;
    }

    public void setReceiveCountryCode(String receiveCountryCode) {
        this.receiveCountryCode = receiveCountryCode;
    }

    public String getLogisticsNos() {
        return logisticsNos;
    }

    public void setLogisticsNos(String logisticsNos) {
        this.logisticsNos = logisticsNos;
    }

    public Boolean getSupportPaperless() {
        return supportPaperless;
    }

    public void setSupportPaperless(Boolean supportPaperless) {
        this.supportPaperless = supportPaperless;
    }

    public Integer getAmazonBuyShippingFlag() {
        return amazonBuyShippingFlag;
    }

    public void setAmazonBuyShippingFlag(Integer amazonBuyShippingFlag) {
        this.amazonBuyShippingFlag = amazonBuyShippingFlag;
    }
}
