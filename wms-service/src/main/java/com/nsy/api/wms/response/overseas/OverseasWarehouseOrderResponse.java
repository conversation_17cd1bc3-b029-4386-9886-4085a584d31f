package com.nsy.api.wms.response.overseas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 海外仓订单响应
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel("海外仓订单响应")
public class OverseasWarehouseOrderResponse {

    @ApiModelProperty("主键ID")
    private Integer id;

    @ApiModelProperty("地区")
    private String location;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("出库单ID")
    private Integer stockoutOrderId;

    @ApiModelProperty("出库单号")
    private String stockoutOrderNo;

    @ApiModelProperty("海外仓订单号")
    private String overseasOrderNo;

    @ApiModelProperty("店铺ID")
    private Integer storeId;

    @ApiModelProperty("店铺")
    private String storeName;

    @ApiModelProperty("平台")
    private String platformName;

    @ApiModelProperty("平台Cn")
    private String platformNameCn;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("状态中文")
    private String statusCn;

    @ApiModelProperty("海外仓ID")
    private Integer overseasSpaceId;

    @ApiModelProperty("海外仓")
    private String overseasSpaceName;

    @ApiModelProperty("物流公司")
    private String logisticsCompany;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("下单时间")
    private Date orderTime;

    @ApiModelProperty("出库单创建时间")
    private Date stockoutCreateTime;

    @ApiModelProperty("推送海外仓时间")
    private Date pushOverseasTime;

    @ApiModelProperty("海外仓发货时间")
    private Date overseasShipTime;

    @ApiModelProperty("包裹提取时间")
    private Date packagePickupTime;

    @ApiModelProperty("包裹签收时间")
    private Date packageSignedTime;

    @ApiModelProperty("推送海外仓时长（h）")
    private BigDecimal pushOverseasDuration;

    @ApiModelProperty("海外仓处理时长（h）")
    private BigDecimal overseasProcessDuration;

    @ApiModelProperty("包裹提取时长（h）")
    private BigDecimal packagePickupDuration;

    @ApiModelProperty("包裹派送时长（天）")
    private BigDecimal packageDeliveryDuration;

    @ApiModelProperty("发货总时长（天）")
    private BigDecimal totalShipDuration;

    @ApiModelProperty("创建时间")
    private Date createDate;

    @ApiModelProperty("更新时间")
    private Date updateDate;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("更新人")
    private String updateBy;


    // Getters and Setters

    public String getPlatformNameCn() {
        return platformNameCn;
    }

    public void setPlatformNameCn(String platformNameCn) {
        this.platformNameCn = platformNameCn;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getStockoutOrderId() {
        return stockoutOrderId;
    }

    public void setStockoutOrderId(Integer stockoutOrderId) {
        this.stockoutOrderId = stockoutOrderId;
    }

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOverseasOrderNo() {
        return overseasOrderNo;
    }

    public void setOverseasOrderNo(String overseasOrderNo) {
        this.overseasOrderNo = overseasOrderNo;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusCn() {
        return statusCn;
    }

    public void setStatusCn(String statusCn) {
        this.statusCn = statusCn;
    }

    public Integer getOverseasSpaceId() {
        return overseasSpaceId;
    }

    public void setOverseasSpaceId(Integer overseasSpaceId) {
        this.overseasSpaceId = overseasSpaceId;
    }

    public String getOverseasSpaceName() {
        return overseasSpaceName;
    }

    public void setOverseasSpaceName(String overseasSpaceName) {
        this.overseasSpaceName = overseasSpaceName;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getStockoutCreateTime() {
        return stockoutCreateTime;
    }

    public void setStockoutCreateTime(Date stockoutCreateTime) {
        this.stockoutCreateTime = stockoutCreateTime;
    }

    public Date getPushOverseasTime() {
        return pushOverseasTime;
    }

    public void setPushOverseasTime(Date pushOverseasTime) {
        this.pushOverseasTime = pushOverseasTime;
    }

    public Date getOverseasShipTime() {
        return overseasShipTime;
    }

    public void setOverseasShipTime(Date overseasShipTime) {
        this.overseasShipTime = overseasShipTime;
    }

    public Date getPackagePickupTime() {
        return packagePickupTime;
    }

    public void setPackagePickupTime(Date packagePickupTime) {
        this.packagePickupTime = packagePickupTime;
    }

    public Date getPackageSignedTime() {
        return packageSignedTime;
    }

    public void setPackageSignedTime(Date packageSignedTime) {
        this.packageSignedTime = packageSignedTime;
    }

    public BigDecimal getPushOverseasDuration() {
        return pushOverseasDuration;
    }

    public void setPushOverseasDuration(BigDecimal pushOverseasDuration) {
        this.pushOverseasDuration = pushOverseasDuration;
    }

    public BigDecimal getOverseasProcessDuration() {
        return overseasProcessDuration;
    }

    public void setOverseasProcessDuration(BigDecimal overseasProcessDuration) {
        this.overseasProcessDuration = overseasProcessDuration;
    }

    public BigDecimal getPackagePickupDuration() {
        return packagePickupDuration;
    }

    public void setPackagePickupDuration(BigDecimal packagePickupDuration) {
        this.packagePickupDuration = packagePickupDuration;
    }

    public BigDecimal getPackageDeliveryDuration() {
        return packageDeliveryDuration;
    }

    public void setPackageDeliveryDuration(BigDecimal packageDeliveryDuration) {
        this.packageDeliveryDuration = packageDeliveryDuration;
    }

    public BigDecimal getTotalShipDuration() {
        return totalShipDuration;
    }

    public void setTotalShipDuration(BigDecimal totalShipDuration) {
        this.totalShipDuration = totalShipDuration;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
