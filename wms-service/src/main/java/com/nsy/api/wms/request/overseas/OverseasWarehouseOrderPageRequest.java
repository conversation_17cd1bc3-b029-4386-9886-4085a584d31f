package com.nsy.api.wms.request.overseas;

import com.nsy.api.wms.request.base.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 海外仓订单分页查询请求
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel("海外仓订单分页查询请求")
public class OverseasWarehouseOrderPageRequest extends PageRequest {

    @ApiModelProperty("出库单号")
    private String stockoutOrderNo;

    @ApiModelProperty("海外仓订单号")
    private String overseasOrderNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("海外仓名称")
    private String overseasSpaceName;

    @ApiModelProperty("平台")
    private String platformName;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("创建时间开始")
    private String createTimeStart;

    @ApiModelProperty("创建时间结束")
    private String createTimeEnd;

    @ApiModelProperty("发货时间开始")
    private String shipTimeStart;

    @ApiModelProperty("发货时间结束")
    private String shipTimeEnd;

    @ApiModelProperty("地区")
    private String location;

    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public String getOverseasOrderNo() {
        return overseasOrderNo;
    }

    public void setOverseasOrderNo(String overseasOrderNo) {
        this.overseasOrderNo = overseasOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOverseasSpaceName() {
        return overseasSpaceName;
    }

    public void setOverseasSpaceName(String overseasSpaceName) {
        this.overseasSpaceName = overseasSpaceName;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public String getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public String getShipTimeStart() {
        return shipTimeStart;
    }

    public void setShipTimeStart(String shipTimeStart) {
        this.shipTimeStart = shipTimeStart;
    }

    public String getShipTimeEnd() {
        return shipTimeEnd;
    }

    public void setShipTimeEnd(String shipTimeEnd) {
        this.shipTimeEnd = shipTimeEnd;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}