/*
* =============================================================================
* Designer: Linhui
* Description: nsy_359277
* Created: 2021/06/22 10:17:34
* ============================================================================= 
*/

CREATE TABLE IF NOT EXISTS bd_material (
  `material_id` INT PRIMARY KEY AUTO_INCREMENT,
  `location` VARCHAR (50) NOT NULL COMMENT '地区',
  `material_type`  varchar(50) NOT NULL COMMENT '类型' ,
  `material_name`  varchar(50) NOT NULL COMMENT '物料名称' ,
  `material_size`  varchar(50) NOT NULL COMMENT '物料规格' ,
  `total_qty`  INT UNSIGNED NOT NULL COMMENT '总数量' ,
  `available_qty`  INT UNSIGNED NOT NULL COMMENT '可用数量' ,
  `property`  varchar(50) NOT NULL COMMENT '性质' ,
  `unit`  varchar(50) NOT NULL COMMENT '计量单位' ,
  `description`  varchar(200) COMMENT '描述' ,
  `create_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` VARCHAR (50) COMMENT '创建者',
  `update_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` VARCHAR (50) COMMENT '更新者',
  `version` BIGINT DEFAULT 0 NOT NULL COMMENT '版本号'
) ENGINE = INNODB COMMENT '物料' ;
ALTER TABLE bd_material ADD INDEX idx_material_type(material_type);
ALTER TABLE bd_material ADD INDEX idx_material_name(material_name);
ALTER TABLE bd_material ADD INDEX idx_material_size(material_size);
ALTER TABLE bd_material ADD INDEX idx_location(location);

CREATE TABLE IF NOT EXISTS stock_material_apply (
  `material_apply_id` INT PRIMARY KEY AUTO_INCREMENT,
  `location` VARCHAR (50) NOT NULL COMMENT '地区',
  `material_id` INT UNSIGNED NOT NULL COMMENT '物料Id' ,
  `material_name`  varchar(50) NOT NULL COMMENT '物料名称' ,
  `applier`  varchar(50) NOT NULL COMMENT '登记人' ,
  `apply_type`  varchar(50) NOT NULL COMMENT '登记类型' ,
  `apply_date`  TIMESTAMP NULL DEFAULT NULL COMMENT '登记时间' ,
  `apply_qty`  INT UNSIGNED NOT NULL COMMENT '登记数量' ,
  `audit_date`  TIMESTAMP NULL DEFAULT NULL COMMENT '审批时间' ,
  `audit_qty`  INT UNSIGNED COMMENT '审批数量' ,
  `description`  varchar(200) COMMENT '描述' ,
  `create_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` VARCHAR (50) COMMENT '创建者',
  `update_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` VARCHAR (50) COMMENT '更新者',
  `version` BIGINT DEFAULT 0 NOT NULL COMMENT '版本号'
) ENGINE = INNODB COMMENT '物料使用记录' ;
ALTER TABLE stock_material_apply ADD INDEX idx_applier(applier);
ALTER TABLE stock_material_apply ADD INDEX idx_apply_type(apply_type);
ALTER TABLE stock_material_apply ADD INDEX idx_material_name(material_name);
ALTER TABLE stock_material_apply ADD INDEX idx_location(location);

ALTER TABLE stock_internal_box_item CHANGE COLUMN box_id internal_box_id INT UNSIGNED NOT NULL COMMENT '内部箱Id';
ALTER TABLE stock_internal_box_item CHANGE COLUMN box_code internal_box_code varchar(50) COMMENT '内部箱编码';