/*
* =============================================================================
* Designer: Administrator@ZHUANGSHIYONG-P
* Description: nsy_383031
* Created: 2021/09/06 17:36:45
* ============================================================================= 
*/
create table if not exists stock_take_stock_plan (
     plan_id int ( 11 ) not null auto_increment comment '唯一标识',
     location varchar ( 50 ) not null comment '地区',
     plan_name varchar ( 50 ) not null comment '计划规则名称',
     plan_type varchar ( 50 ) not null comment '盘点类型：正常盘点：NORMAL_INVENTORY，差异盘点：VARIANCE_INVENTORY，临时盘点：TEMPORARY_INVENTORY',
     plan_scope varchar ( 50 ) not null comment '盘点范围  全盘： OVERALL，循环盘点：CYCLE_INVENTORY，指定范围：SPECIFIED_RANGE',
     status tinyint ( 2 ) not null comment '计划状态：启用：1，禁用：0',
     cycle_time varchar ( 50 ) not null comment '循环周期 按季度：QUARTERLY, 按月：MONTHLY, 按周：BY_WEEK, 按天：BY_DAY',
     is_auto_task tinyint ( 2 ) not null comment '是否自动生成任务 1：是 0：否',
     task_generate_mode varchar ( 50 ) not null comment '盘点任务生成方式 按区域：BY_AREA, 按库区：BY_SPACE_AREA, 按库位：BY_POSITION, 按计划：BY_PLAN, 按sku：BY_SKU',
     area_id int ( 11 ) comment '区域id',
     area_name varchar ( 50 ) comment '区域名称',
     space_area_id int ( 11 ) comment '库区id',
     space_area_name varchar ( 50 ) comment '库区名称',
     supervisor varchar ( 50 ) comment '监盘人',
     position_type varchar ( 50 ) comment '库位类型',
     position_code_start varchar ( 50 ) comment '库位从',
     position_code_end varchar ( 50 ) comment '库位至',
     remark varchar ( 150 )  comment '备注',
     create_date     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
     create_by       VARCHAR (50) COMMENT '创建者',
     update_date     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
     update_by       VARCHAR (50) COMMENT '更新者',
     version BIGINT NOT NULL default 0 comment '版本号',
     primary key ( plan_id )
)  engine = innodb comment '盘点计划';

CREATE TABLE stock_take_stock_task (
    task_id int ( 11 ) NOT NULL AUTO_INCREMENT comment '唯一标识',
    location varchar ( 50 ) NOT NULL comment '地区',
    plan_id int ( 11 ) NOT NULL comment '计划ID',
    plan_type varchar ( 50 ) NOT NULL comment '盘点类型：正常盘点：NORMAL_INVENTORY，差异盘点：VARIANCE_INVENTORY，临时盘点：TEMPORARY_INVENTORY',
    task_generate_mode varchar ( 50 ) NOT NULL comment '盘点任务生成方式
    按区域：BY_AREA
    按库区：BY_SPACE_AREA
    按库位：BY_POSITION
    按计划：BY_PLAN
    按SKU：BY_SKU',
    space_area_qty int ( 11 ) comment '盘点库区数',
    position_qty int ( 11 ) comment '盘点库位数',
    supervisor varchar ( 50 ) comment '监盘人',
    operator varchar ( 50 ) comment '盘点人',
    operate_date timestamp NULL comment '盘点时间',
    status tinyint ( 2 ) NOT NULL comment '盘点任务状态
    待盘点：TO_BE_INVENTORY
    盘点中：INVENTORYING
    盘点完成：INVENTORY_COMPLETED
    已取消：INVENTORY_CANCELL',
    create_date     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by       VARCHAR (50) COMMENT '创建者',
    update_date     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by       VARCHAR (50) COMMENT '更新者',
    version BIGINT NOT NULL default 0 comment '版本号',
    PRIMARY KEY ( task_id )
) engine = innodb comment '盘点任务';

CREATE TABLE stock_take_stock_task_item (
    id int ( 11 ) NOT NULL AUTO_INCREMENT,
    task_id int ( 11 ) NOT NULL comment '唯一标识',
    location varchar ( 50 ) NOT NULL comment '地区',
    product_id int ( 11 ) NOT NULL comment '商品ID',
    spec_id int ( 11 ) NOT NULL comment '商品SKU主键',
    sku varchar ( 50 ) NOT NULL comment '商品编码',
    area_id int ( 11 ) comment '区域ID',
    area_name varchar ( 50 ) comment '区域名称',
    space_area_id int ( 11 ) comment '库区ID',
    space_area_name varchar ( 50 ) comment '库区名称',
    position_id int ( 11 ) comment '库位ID',
    position_code varchar ( 50 ) comment '库位编码',
    status tinyint ( 2 ) NOT NULL comment '盘点任务状态
    待盘点：TO_BE_INVENTORY
    盘点中：INVENTORYING
    盘点完成：INVENTORY_COMPLETED
    已取消：INVENTORY_CANCELL',
    create_date     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by       VARCHAR (50) COMMENT '创建者',
    update_date     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by       VARCHAR (50) COMMENT '更新者',
    version BIGINT NOT NULL default 0 comment '版本号',
    PRIMARY KEY ( id )
) engine = innodb comment '盘点任务详情';

CREATE TABLE stock_take_stock_log (
    id int ( 11 ) NOT NULL AUTO_INCREMENT,
    location varchar ( 50 ) NOT NULL comment '地区',
    task_id int ( 11 ) NOT NULL comment '盘点任务ID',
    type varchar ( 50 ) NOT NULL comment '日志类型',
    content varchar ( 255 ) comment '内容',
    ip_address varchar ( 50 ) comment 'IP地址',
    create_date     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by       VARCHAR (50) COMMENT '创建者',
    update_date     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by       VARCHAR (50) COMMENT '更新者',
    version BIGINT NOT NULL default 0 comment '版本号',
    PRIMARY KEY ( id )
) engine = innodb comment '盘点任务日志';

ALTER TABLE `nsy_wms`.`bd_position`
    ADD COLUMN `plan_id` int(10) NULL COMMENT '盘点计划ID' AFTER `maximum_weight`;
