/*
* =============================================================================
* Designer: YongHuo
* Description: nsy_365628
* Created: 2021/07/14 15:39:40
* 拣货任务规则, 拣货任务规则明细
* ============================================================================= 
*/

CREATE TABLE IF NOT EXISTS bd_picking_task_rule
(
    picking_task_rule_id        INT PRIMARY KEY AUTO_INCREMENT,
    location                    VARCHAR(50) NOT NULL COMMENT '地区',
    picking_task_rule_code      VARCHAR(50) COMMENT '编码',
    picking_task_rule_name      VARCHAR(50) COMMENT '名称',
    space_id                    INT UNSIGNED COMMENT '仓库id',
    stockout_type               VARCHAR(50) COMMENT '出库类型',
    workspace                   VARCHAR(50) COMMENT '工作区域',
    is_auto_generate            tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否生成任务  1-是  0-否',
    picking_type                VARCHAR(50) COMMENT '拣货模式',
    description                 VARCHAR(200) COMMENT '备注',
    is_deleted                  tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否弃用  0-启用  1-禁用',
    create_date                 TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    create_by                   VARCHAR(50) COMMENT '创建者',
    update_date                 TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by                   VARCHAR(50) COMMENT '更新者',
    version                     BIGINT DEFAULT 0 NOT NULL COMMENT '版本号'
    ) ENGINE = INNODB COMMENT '拣货任务规则';

ALTER TABLE bd_picking_task_rule ADD INDEX idx_picking_task_rule_code(picking_task_rule_code);
ALTER TABLE bd_picking_task_rule ADD INDEX idx_space_id(space_id);


CREATE TABLE IF NOT EXISTS bd_picking_task_rule_item
(
    picking_task_rule_item_id   INT PRIMARY KEY AUTO_INCREMENT,
    location                    VARCHAR(50) NOT NULL COMMENT '地区',
    picking_task_rule_id        INT UNSIGNED COMMENT '拣货任务规则id',
    rule_item_type              VARCHAR(50) COMMENT '规则明细类型',
    rule_item_operator          VARCHAR(50) COMMENT '限制运算符',
    rule_item_value             INT UNSIGNED COMMENT '限制值',
    description                 VARCHAR(200) COMMENT '描述',
    sort                        INT UNSIGNED NOT NULL DEFAULT 0  COMMENT '排序',
    is_deleted                  tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否弃用  0-启用  1-禁用',
    create_date                 TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    create_by                   VARCHAR(50) COMMENT '创建者',
    update_date                 TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by                   VARCHAR(50) COMMENT '更新者',
    version                     BIGINT DEFAULT 0 NOT NULL COMMENT '版本号'
    ) ENGINE = INNODB COMMENT '拣货任务规则明细';

ALTER TABLE bd_picking_task_rule_item ADD INDEX idx_picking_task_rule_id(picking_task_rule_id);
ALTER TABLE bd_picking_task_rule_item ADD INDEX idx_rule_item_type(rule_item_type);
ALTER TABLE bd_picking_task_rule_item ADD INDEX idx_sort(sort);
