/*
* =============================================================================
* Designer: huoya@DESKTOP-L7FA5ML
* Description: nsy_358752
* Created: 2021/06/09 15:16:16
* =============================================================================
*/
CREATE TABLE bd_area (
	area_id     INT AUTO_INCREMENT PRIMARY KEY,
	location    VARCHAR(50)                          NOT NULL COMMENT '地区',
	space_id    INT UNSIGNED                         NOT NULL COMMENT '仓库id',
	area_name   VARCHAR(50)                          NOT NULL COMMENT '区域名称',
	floor       VARCHAR(100)                         NULL COMMENT '楼层',
	description VARCHAR(200)                         NULL COMMENT '备注',
	is_deleted  TINYINT(1) DEFAULT 0                 NOT NULL COMMENT '是否已删除 1-是，0-否',
	create_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP  NOT NULL COMMENT '创建时间',
	create_by   VARCHAR(50)                          NULL COMMENT '创建者',
	update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP  NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	update_by   VARCHAR(50)                          NULL COMMENT '更新者',
	version     BIGINT DEFAULT 0                     NOT NULL COMMENT '版本号') COMMENT '区域表' ENGINE = InnoDB;

alter table bd_area add index idx_area_space_id(space_id);
alter table bd_area add index idx_area_location(location);