
CREATE TABLE IF NOT EXISTS bd_position
(
    position_id          int unsigned primary key auto_increment,
    location             varchar(50) NOT NULL comment '地区',
    position_code        varchar(50) comment '库位编码',
    position_name        varchar(50) comment '库位名',
    space_id             int unsigned comment '仓库id',
    space_name           varchar(50) comment '仓库名称',
    area_id              int unsigned comment '区域id',
    area_name            varchar(50) comment '区域名',
    space_area_id        int unsigned comment '库区id',
    space_area_code      varchar(50) comment '库区码',
    position_type                 varchar(50) comment '类型',
    sort                 int(10) unsigned NOT NULL default 0 comment '排序',
    description          varchar(200) comment '描述',
    length               decimal(18,3) comment '长',
    width                decimal(18,3) comment '宽',
    height               decimal(18,3) comment '高',
    maximum_volume        decimal(18,3) comment '库容限制',
    maximum_weight        decimal(18,3) comment '库位限重',
    is_deleted           tinyint(1) NOT NULL default 0 comment '是否已删除 0启用,1停用',
    create_date          timestamp default CURRENT_TIMESTAMP NOT NULL comment '创建时间',
    create_by            varchar(50) comment '创建者',
    update_date          timestamp default CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP comment '更新时间',
    update_by            varchar(50) comment '更新者',
    version              bigint default 0 NOT NULL comment '版本号'
) ENGINE = INNODB COMMENT '库位';

alter table bd_position add index idx_position_code(position_code);
alter table bd_position add index idx_space_id(space_id);
alter table bd_position add index idx_space_area_id(space_area_id);
alter table bd_position add index idx_space_area_code(space_area_code);
alter table bd_position add index idx_position_type(position_type);

