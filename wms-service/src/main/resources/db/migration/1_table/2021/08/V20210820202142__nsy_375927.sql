/*
* =============================================================================
* Designer: Administrator@ZHUANGSHIYONG-P
* Description: nsy_375927
* Created: 2021/08/20 20:21:42
* ============================================================================= 
*/
-- 退货单
create table if not exists stockin_return_product_order
(
    return_order_id         int(11)     not null auto_increment comment '唯一标识',
    location               varchar(50) not null comment '地区',
    order_no               varchar(50) comment '订单号',
    return_order_no        varchar(50) not null comment '退货单号',
    store_id               int(11) comment '店铺id',
    store_name             varchar(50) comment '店铺名称',
    user_name              varchar(50) comment '客户',
    status                 varchar(50) not null comment '登记状态：WAIT_REGISTER: 待登记,REGISTERING：登记中,ALREADY_REGISTER: 已登记',
    type                   varchar(50) not null comment '退货类型：USER_APPLY: 客户申请,ORIGIN_BACK: 原件退回',
    logistics_company_id   int(11) comment '物流公司ID',
    logistics_company_name varchar(50) comment '物流公司名称',
    logistics_no           varchar(50) comment '物流单号',
    register_by            varchar(50) comment '登记人',
    register_date          timestamp null comment '登记时间',
    sender                 varchar(50) comment '寄件人',
    tel                    varchar(20) comment '手机电话',
    address                varchar(150) comment '地址',
    customs_clearance_fee  decimal(10, 2) comment '清关费',
    back_fee               decimal(10, 2) comment '退回费用',
    is_collected           tinyint(2) comment '是否到付: 1：是,0：否',
    remark                 varchar(100) comment '备注',
    create_date            timestamp   not null default current_timestamp comment '创建时间',
    create_by              varchar(50) comment '创建人',
    update_date            timestamp   not null default current_timestamp on update current_timestamp comment '更新时间',
    update_by              varchar(50) comment '更新人',
    version                bigint               default 0 comment '版本号',
    primary key (return_order_id),
    index idx_query (order_no, logistics_no, sender, tel)
) comment '退货单' engine = innodb;

-- 退货单明细表
create table if not exists stockin_return_product_order_item
(
    id             int(11)     not null auto_increment comment '唯一标识',
    location       varchar(50) not null comment '地区',
    return_order_id int(11)     not null comment '退货单主键ID',
    product_id     int(11)     not null comment '商品id',
    spec_id        int(11)     not null comment '商品规格id',
    sku            varchar(50) not null comment '商品编码',
    origin_qty     int(11) UNSIGNED     default 0 comment '原来数量',
    return_qty     int(11) UNSIGNED     default 0 comment '退货数量',
    register_qty   int(11) UNSIGNED     default 0 comment '登记数量',
    inferior_qty   int(11) UNSIGNED     default 0 comment '次品数量',
    internal_box_code varchar(50) comment '退货箱编码',
    remark         varchar(100) comment '备注',
    create_date    timestamp   not null default current_timestamp comment '创建时间',
    create_by      varchar(50) comment '创建人',
    update_date    timestamp   not null default current_timestamp on update current_timestamp comment '更新时间',
    update_by      varchar(50) comment '更新人',
    version        bigint               default 0 comment '版本号',
    primary key (id)
) comment '退货单明细表' engine = innodb;
