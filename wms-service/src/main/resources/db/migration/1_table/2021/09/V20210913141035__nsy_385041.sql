/*
* =============================================================================
* Designer: Administrator@ZHENGMEIYUE-PC
* Description: nsy_385041
* Created: 2021/09/13 14:10:35
* ============================================================================= 
*/

CREATE TABLE IF NOT EXISTS `stock_lend` (
    `lend_id` int primary key auto_increment,
    `location` varchar(50) NOT NULL COMMENT '地区',
    `stock_lend_code` varchar(50) NOT NULL  COMMENT '借用单号',
    `space_id` int COMMENT '仓库id',
    `space_name` varchar(50) COMMENT '仓库名称',
    `store_id` int COMMENT '店铺id',
    `store_name` varchar(50) COMMENT '店铺名称',
    `business_type` varchar(50) COMMENT '部门',
    `status` varchar(10) NOT NULL COMMENT '状态 待处理(WAIT_DEAL)待归还(WAIT_RETURN)已取消(CANCELLED)已归还(RETURNED)部分归还(PARTIAL_RETURN)无法归还(CANT_RETURN)',
    `remark` varchar(200) COMMENT '备注',
    `create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(50)  DEFAULT NULL COMMENT '创建者',
    `update_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(50)  DEFAULT NULL COMMENT '更新者',
    `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本号'
) ENGINE=InnoDB COMMENT='内部存货借用';

ALTER TABLE stock_lend ADD INDEX idx_space_id(space_id);
ALTER TABLE stock_lend ADD INDEX idx_stock_lend_code(stock_lend_code);
ALTER TABLE stock_lend ADD INDEX idx_business_type(business_type);
ALTER TABLE stock_lend ADD INDEX idx_status(status);

CREATE TABLE IF NOT EXISTS `stock_lend_item` (
    `lend_item_id` int primary key auto_increment,
    `location` varchar(50) NOT NULL COMMENT '地区',
    `lend_id` int COMMENT '内部存货借用id',
    `spec_id` int COMMENT '规格id',
    `product_id` int COMMENT '商品id',
    `sku` varchar(50) NOT NULL COMMENT 'sku',
    `barcode` varchar(50) COMMENT '商品条形码',
    `position_id` int COMMENT '库位Id',
    `position_code` VARCHAR (50)  COMMENT '库位编码',
    `apply_lend_qty` int COMMENT '申请借用数量',
    `lend_qty` int COMMENT '借出数量',
    `return_qty` int COMMENT '归还数量',
    `expect_return_date` timestamp NULL COMMENT '预计归还时间',
    `return_date` timestamp NULL COMMENT '归还时间',
    `remark` varchar(200) COMMENT '备注',
    `create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_by` varchar(50)  DEFAULT NULL COMMENT '创建者',
    `update_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `update_by` varchar(50)  DEFAULT NULL COMMENT '更新者',
    `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本号'
) ENGINE=InnoDB COMMENT='内部存货借用明细';

ALTER TABLE stock_lend_item ADD INDEX idx_lend_id(lend_id);
ALTER TABLE stock_lend_item ADD INDEX idx_barcode(barcode);
ALTER TABLE stock_lend_item ADD INDEX idx_sku(sku);

CREATE TABLE IF NOT EXISTS stock_lend_log (
    lend_log_id    INT PRIMARY KEY AUTO_INCREMENT,
    location VARCHAR(50) NOT NULL COMMENT '地区',
    lend_id  INT UNSIGNED NOT NULL COMMENT '内部存货借用id',
    event_type VARCHAR(50) NOT NULL COMMENT '事件类型',
    content VARCHAR(500) NOT NULL COMMENT '日志消息',
    ip_address VARCHAR(50) NOT NULL COMMENT 'ip',
    create_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR (50) COMMENT '创建者',
    update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR (50) COMMENT '更新者',
    version BIGINT NOT NULL DEFAULT 0 COMMENT '版本号'
) ENGINE = INNODB COMMENT = '内部存货借用日志';

ALTER TABLE stock_lend_log ADD INDEX idx_lend_id(lend_id);
ALTER TABLE stock_lend_log ADD INDEX idx_event_type(event_type);