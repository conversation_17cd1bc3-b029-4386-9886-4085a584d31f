/*
* =============================================================================
* Designer: Z@LAPTOP-Q2AEFPCJ
* Description: nsy_398
* Created: 2022/04/29 18:22:48
* ============================================================================= 
*/

ALTER TABLE `nsy_wms`.`stockout_customs_declare_document`
    ADD COLUMN `country_code` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '目的国家代码' AFTER `destination`,
    ADD COLUMN `type_of_shipping` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '运输方式' AFTER `country_code`;