/*
* =============================================================================
* Description: nsy_431517
* Created: 2022/01/21 15:20:10
* ============================================================================= 
*/

CREATE TABLE IF NOT EXISTS bd_lack_delivery_rule (
    lack_delivery_rule_id int primary key auto_increment,
    store_id INT UNSIGNED COMMENT '店铺Id',
    store_name varchar(50) COMMENT '店铺',
    workspace varchar(50) NOT NULL COMMENT '工作区域',
    business_type varchar(50) COMMENT '部门',
    rule_value INT UNSIGNED DEFAULT 0 COMMENT '限制值',
    location varchar(50) NOT NULL COMMENT '地区',
    is_deleted tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否弃用  0-启用  1-禁用',
    create_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR (50) COMMENT '创建者',
    update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR (50) COMMENT '更新者',
    version BIGINT NOT NULL default 0 comment '版本号'
) ENGINE = INNODB COMMENT = '缺货发货规则';

alter table bd_lack_delivery_rule add index idx_store_id_workspace(store_id, workspace);