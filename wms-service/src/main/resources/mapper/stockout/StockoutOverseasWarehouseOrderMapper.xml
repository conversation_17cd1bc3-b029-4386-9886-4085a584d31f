<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.wms.repository.jpa.mapper.overseas.OverseasWarehouseOrderMapper">


    <!-- 分页查询海外仓订单 -->
    <select id="pageSearchOverseasWarehouseOrder" resultType="com.nsy.api.wms.response.overseas.OverseasWarehouseOrderResponse">
        SELECT
            o.id,
            o.location,
            o.stockout_order_id,
            o.stockout_order_no,
            o.overseas_order_no,
            o.store_id,
            o.store_name,
            o.platform_name,
            o.status,
            o.space_id,
            o.space_name,
            o.logistics_company,
            o.logistics_no,
            o.order_time,
            o.stockout_create_time,
            o.push_overseas_time,
            o.overseas_ship_time,
            o.package_pickup_time,
            o.package_signed_time,
            o.push_overseas_duration,
            o.overseas_process_duration,
            o.package_pickup_duration,
            o.package_delivery_duration,
            o.total_ship_duration,
            o.create_date,
            o.update_date,
            o.create_by,
            o.update_by,
            -- 从关联的出库单明细中获取去重后的订单号（多个订单号用逗号分隔）
            (SELECT GROUP_CONCAT(DISTINCT i2.order_no SEPARATOR ',')
             FROM stockout_order_item i2
             WHERE i2.stockout_order_id = o.stockout_order_id) as orderNo
        FROM stockout_overseas_warehouse_order o
        <where>
            <!-- 出库单号：精确匹配 -->
            <if test="param.stockoutOrderNo != null and param.stockoutOrderNo != ''">
                AND o.stockout_order_no = #{param.stockoutOrderNo}
            </if>
            <!-- 订单号：精确匹配，通过子查询在出库单明细表中查找 -->
            <if test="param.orderNo != null and param.orderNo != ''">
                AND EXISTS (
                    SELECT 1 FROM stockout_order_item i
                    WHERE i.stockout_order_id = o.stockout_order_id
                    AND i.order_no = #{param.orderNo}
                )
            </if>
            <!-- 海外仓订单号：精确匹配 -->
            <if test="param.overseasOrderNo != null and param.overseasOrderNo != ''">
                AND o.overseas_order_no = #{param.overseasOrderNo}
            </if>
            <!-- 状态：下拉框选择 -->
            <if test="param.status != null and param.status != ''">
                AND o.status = #{param.status}
            </if>
            <!-- 海外仓：下拉框选择 -->
            <if test="param.overseasSpaceName != null and param.overseasSpaceName != ''">
                AND o.overseas_space_name = #{param.overseasSpaceName}
            </if>
            <!-- 销售平台：下拉框选择 -->
            <if test="param.platformName != null and param.platformName != ''">
                AND o.platform_name = #{param.platformName}
            </if>
            <!-- 物流单号：模糊匹配 -->
            <if test="param.logisticsNo != null and param.logisticsNo != ''">
                AND o.logistics_no = #{param.logisticsNo}
            </if>
            <!-- 创建时间区间 -->
            <if test="param.createTimeStart != null">
                AND o.create_date &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null">
                AND o.create_date &lt;= #{param.createTimeEnd}
            </if>
            <!-- 发货时间区间 -->
            <if test="param.shipTimeStart != null">
                AND o.overseas_ship_time &gt;= #{param.shipTimeStart}
            </if>
            <if test="param.shipTimeEnd != null">
                AND o.overseas_ship_time &lt;= #{param.shipTimeEnd}
            </if>
        </where>
        ORDER BY o.create_date DESC
    </select>

    <!-- 根据状态统计订单数量 -->
    <select id="countByStatus" resultType="com.nsy.api.wms.response.stockout.StatusCountResponse">
        SELECT
            status as status,
            COUNT(1) as qty
        FROM overseas_warehouse_order
        GROUP BY status
    </select>
</mapper>
