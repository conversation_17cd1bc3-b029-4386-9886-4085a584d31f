<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutOrderLogMapper">
    <select id="pageSearchOutorderLog" resultType="com.nsy.wms.repository.entity.stockout.StockoutOrderLogEntity">
        SELECT l.* FROM stockout_order_log l
        <where>
            <if test="query!=null and query.stockoutOrderNo != null and query.stockoutOrderNo !=''">
                and l.stockout_order_no = #{query.stockoutOrderNo}
            </if>
            <if test="query!=null and query.orderLogType != null and query.orderLogType !=''">
                and l.order_log_type = #{query.orderLogType}
            </if>
            <if test="query!=null and query.createBy != null and query.createBy !=''">
                and l.create_by = #{query.createBy}
            </if>
            <if test="query!=null and query.content != null and query.content !=''">
                and l.content like concat('%',#{query.content},'%')
            </if>
        </where>
    </select>
</mapper>
