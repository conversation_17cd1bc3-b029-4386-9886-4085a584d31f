<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutCustomsFilesKeyCacheMapper">

    <resultMap id="BaseResultMap" type="com.nsy.wms.repository.entity.stockout.StockoutCustomsFilesKeyCacheEntity">
            <id property="stockoutCustomsFilesKeyCacheId" column="stockout_customs_files_key_cache_id" jdbcType="INTEGER"/>
            <result property="aliyunOssObjectName" column="aliyun_oss_object_name" jdbcType="VARCHAR"/>
            <result property="informationKey" column="information_key" jdbcType="VARCHAR"/>
            <result property="eTag" column="e_tag" jdbcType="VARCHAR"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        stockout_customs_files_key_cache_id,aliyun_oss_object_name,information_key,
        e_tag,location,create_by,
        create_date,update_by,update_date,
        version
    </sql>
</mapper>
