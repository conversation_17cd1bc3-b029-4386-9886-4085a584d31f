<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockout.StockoutEasyScanTaskLogMapper">

    <select id="searchPage" resultType="com.nsy.api.wms.response.stockout.StockoutEasyScanTaskLogListResponse">
        SELECT
        sestl.*
        FROM
        stockout_easy_scan_task_log sestl
        <where>
            <if test="query.taskId != null">
                and sestl.task_id = #{query.taskId}
            </if>

            <if test="query.operator != null and query.operator !=''">
                and sestl.operator like concat('%',#{query.operator},'%')
            </if>

            <if test="query.logType != null and query.logType !=''">
                and sestl.log_type like concat('%',#{query.logType},'%')
            </if>

            <if test="query.content != null and query.content !=''">
                and sestl.content like concat('%',#{query.content},'%')
            </if>
        </where>
        order by sestl.scan_task_log_id desc
    </select>


</mapper>
