<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.demo.DemoMapper">

    <select id="searchDemo" resultType="com.nsy.wms.repository.entity.demo.DemoEntity">
        select * from demo where is_deleted = 0
        <if test="query!=null and query.name!=''">
            and name = #{query.name}
        </if>

        <if test="query!=null and query.location !=null">
            and location = #{query.location}
        </if>
    </select>


    <select id="pageSearchDemo" resultType="com.nsy.wms.repository.entity.demo.DemoEntity">
        select * from demo where is_deleted = 0
        <if test="query!=null and query.name!=''">
            and name = #{query.name}
        </if>

        <if test="query!=null and query.location !=null">
            and location = #{query.location}
        </if>
    </select>

</mapper>
