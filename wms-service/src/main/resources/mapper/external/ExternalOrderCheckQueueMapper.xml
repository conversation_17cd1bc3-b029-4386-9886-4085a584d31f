<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.external.ExternalOrderCheckQueueMapper">

    <select id="pageSearch" resultType="com.nsy.api.wms.domain.external.ExternalOrderCheckQueue">
        SELECT
            check_queue_id AS checkQueueId,
            module AS module,
            external_order_no AS externalOrderNo,
            external_system AS externalSystem,
            `status` AS `status`
        FROM
            external_order_check_queue
        <where>
            <if test="query!=null">
                <if test="query.status!= null and query.status!=''">
                    and external_order_no = #{query.externalOrderNo}
                </if>
                <if test="query.status!= null and query.status!=''">
                    and status = #{query.status}
                </if>
            </if>
        </where>
        ORDER BY create_date DESC
    </select>
</mapper>
