<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.product.ProductStoreSkuMappingMapper">

    <select id="pageSearchList" resultType="com.nsy.api.wms.domain.product.ProductStoreSkuMapping">
        SELECT
            m.mapping_id,
            m.spec_id,
            m.store_id,
            m.store_name,
            m.sku,
            m.barcode,
            m.store_sku,
            m.store_barcode,
            m.sku_rule,
            m.is_push_wcs,
            m.create_by,
            m.create_date,
            m.update_by,
            m.update_date,
            m.title,
            m.asin,
            pi.spu,
            psi.image_url,
            psi.thumbnail_image_url,
            psi.preview_image_url,
            m.is_push_cross_belt
        FROM product_store_sku_mapping m
        inner join product_spec_info psi on psi.spec_id = m.spec_id
        inner join product_info pi on pi.product_id = psi.product_id
        inner join (
            select s.mapping_id from product_store_sku_mapping s
            <where>
                <if test="query.storeBarcode != null ">
                    and s.store_barcode like #{query.storeBarcode}
                </if>
                <if test="query.storeSku != null ">
                    and s.store_sku like #{query.storeSku}
                </if>
                <if test="query.asin != null ">
                    and s.asin like #{query.asin}
                </if>
                <if test="query.skuRule != null ">
                    and s.sku_rule = #{query.skuRule}
                </if>
                <if test="query.isPushWcs != null ">
                    and s.is_push_wcs = #{query.isPushWcs}
                </if>
                <if test="query.sku != null and query.sku.size() > 0">
                    and s.sku in
                    <foreach collection="query.sku" separator="," index="index" item="item" open="("
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.storeId != null ">
                    and s.store_id = #{query.storeId}
                </if>
                <if test="query.isPushCrossBelt != null ">
                    and s.is_push_cross_belt = #{query.isPushCrossBelt}
                </if>
            </where>
            order by s.mapping_id desc
            limit #{pageIndex},#{pageSize}
        ) b on m.mapping_id = b.mapping_id
    </select>

    <select id="mappingCount" resultType="java.lang.Integer">
        SELECT
            COUNT( s.mapping_id )
        FROM
            product_store_sku_mapping s
        <where>
            <if test="query.storeBarcode != null ">
                and s.store_barcode = #{query.storeBarcode}
            </if>
            <if test="query.storeSku != null ">
                and s.store_sku = #{query.storeSku}
            </if>
            <if test="query.skuRule != null ">
                and s.sku_rule = #{query.skuRule}
            </if>
            <if test="query.isPushWcs != null ">
                and s.is_push_wcs = #{query.isPushWcs}
            </if>
            <if test="query.sku != null and query.sku.size() > 0">
                and s.sku in
                <foreach collection="query.sku" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.storeId != null ">
                and s.store_id = #{query.storeId}
            </if>
        </where>
    </select>
    <select id="findMappingLocationBySku" resultType="com.nsy.api.wms.domain.product.ProductLocationInfo">
        select s.location, si.sku , si.barcode as barcode
        from product_spec_info si
        INNER JOIN product_info p
        on si.product_id = p.product_id
        LEFT JOIN sys_company s
        on s.company_code = p.company_code

        where si.sku in
        <foreach collection="skuList" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>;
    </select>

    <update id="updateByQuery">
        update product_store_sku_mapping set is_push_wcs = #{isPushWcs}
        <where>
            <choose>
                <when test="request.mappingIdList != null and request.mappingIdList.size() > 0 ">
                    and mapping_id in
                    <foreach collection="request.mappingIdList" separator="," index="index" item="mappingId" open="(" close=")">
                        #{mappingId}
                    </foreach>
                </when>
                <otherwise>
                    <if test="request.storeBarcode != null and request.storeBarcode != ''">
                        and store_barcode = #{request.storeBarcode}
                    </if>
                    <if test="request.storeSku != null and request.storeSku != ''">
                        and store_sku = #{request.storeSku}
                    </if>
                    <if test="request.skuRule != null and request.skuRule != ''">
                        and sku_rule = #{request.skuRule}
                    </if>
                    <if test="request.isPushWcs != null">
                        and is_push_wcs = #{request.isPushWcs}
                    </if>
                    <if test="request.sku != null and request.sku != ''">
                        and sku = #{request.sku}
                    </if>
                    <if test="request.storeId != null">
                        and store_id = #{request.storeId}
                    </if>
                    <if test="request.storeName != null and request.storeName != ''">
                        and store_name = #{request.storeName}
                    </if>
                </otherwise>
            </choose>
        </where>
    </update>
</mapper>
