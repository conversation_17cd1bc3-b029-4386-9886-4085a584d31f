<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaTaskItemMapper">


    <select id="getItemList" resultType="com.nsy.api.wms.response.qa.StockinQaTaskItemPageResponse">
        select t.sku,
               ti.supplier_delivery_no,
               ti.qty as qty,
               ti.package_name,
               ti.brand_name,
               ti.first_label
        from stockin_qa_task t
                 inner join stockin_qa_task_item ti
                            on t.task_id = ti.task_id
        where t.task_id = #{taskId}
    </select>
    <select id="sumShelveQtyByTaskId" resultType="java.lang.Integer">
        select ifnull(sum(oi.shelved_qty),0) from stockin_qa_task_item ti
        inner join stockin_order_item oi
        on ti.stockin_order_item_id = oi.stockin_order_item_id
        where ti.task_id = #{taskId}
    </select>
</mapper>