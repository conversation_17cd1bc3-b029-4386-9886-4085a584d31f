<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaRoutingInspectMapper">

    <select id="findCheckStatusByInfo" resultType="com.nsy.wms.repository.entity.qa.StockinQaRoutingInspectEntity">
        select t.* from
        stockin_qa_routing_inspect t
        inner join stockin_qa_routing_inspect_item ti on t.id = ti.stockin_qc_routing_inspect_id
        <where>
            <if test="boxBarcode!= null and boxBarcode!= ''">
                and t.internal_box_code = #{boxBarcode}
            </if>
            <if test="purchaseNumber != null and purchaseNumber != ''">
                and ti.purchase_plan_no = #{purchaseNumber}
            </if>
            <if test="receiveOrderList!=null and receiveOrderList.size() > 0">
                and ti.supplier_delivery_box_code in
                <foreach collection="receiveOrderList" item="receiveOrder" open="(" close=")" separator=",">
                    #{receiveOrder}
                </foreach>
            </if>
            <if test="sku != null and sku != ''">
                and t.sku = #{sku}
            </if>
        </where>
        order by t.create_date desc
        limit 1
    </select>

    <select id="pageList" resultType="com.nsy.api.wms.response.qa.StockinQaRoutingInspectPageResponse">
        select
        t.id,t.location as location,
        t.internal_box_code as internalBoxCode,
        t.product_id as productId,t.sku as sku,t.skc as skc,t.spu as spu,
        t.supplier_id as supplierId,t.supplier_name as supplierName,
        GROUP_CONCAT(DISTINCT ti.supplier_delivery_no) as supplierDeliveryNo,
        t.arrival_count as arrivalCount,
        sum(ti.qty) as stockinQty,
        t.workmanship_version as workmanshipVersion,
        t.item_part_result as  itemPartResult,
        t.item_craft_result as itemCraftResult,
        t.unqualified_category as unqualifiedCategory,
        t.unqualified_reason as unqualifiedReason,
        t.unqualified_count as unqualifiedCount,
        t.check_status as checkStatus,
        t.item_remark as itemRemark,
        t.had_push as hadPush,
        t.inbounds_date as inboundsDate,
        t.create_date as createDate,
        t.qc_user_name as qcUserName,
        t.qc_user_real_name as qcUserRealName,
        s.contact_qc_emp_name as lastQcUserRealName,
        t.qc_count as qcCount,
        t.cancel_count as cancelCount
        from stockin_qa_routing_inspect t
        inner join stockin_qa_routing_inspect_item ti on t.id = ti.stockin_qc_routing_inspect_id
        left join supplier s on s.supplier_id = t.supplier_id
        <where>
            <if test="request.internalBoxCode != null and request.internalBoxCode !=''">
                AND t.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.sku != null and request.sku !=''">
                AND t.sku = #{request.sku}
            </if>
            <if test="request.spu != null and request.spu !=''">
                AND t.spu = #{request.spu}
            </if>
            <if test="request.skc != null and request.skc !=''">
                AND t.skc = #{request.skc}
            </if>
            <if test="request.skuList != null and request.skuList.size() > 0 ">
                and t.sku in
                <foreach collection="request.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.skcList != null and request.skcList.size() > 0 ">
                and t.skc in
                <foreach collection="request.skcList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spuList != null and request.spuList.size() > 0 ">
                and t.spu in
                <foreach collection="request.spuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.supplierId != null">
                AND t.supplier_id = #{request.supplierId}
            </if>
            <if test="request.checkStatus != null and request.checkStatus !=''">
                AND t.check_status = #{request.checkStatus}
            </if>
            <if test="request.qcUserRealName != null and request.qcUserRealName !=''">
                AND t.qc_user_real_name = #{request.qcUserRealName}
            </if>
            <if test="request.createDateStart != null">
                and t.create_date &gt;= #{request.createDateStart}
            </if>
            <if test="request.createDateEnd != null ">
                and t.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.inboundsDateStart != null">
                and t.inbounds_date &gt;= #{request.inboundsDateStart}
            </if>
            <if test="request.inboundsDateEnd != null">
                and t.inbounds_date &lt;= #{request.inboundsDateEnd}
            </if>
        </where>
        group by t.id
        ORDER BY t.create_date desc
    </select>

    <select id="findDetail" resultType="com.nsy.api.wms.response.qa.StockinQaRoutingInspectDetailResponse">
        select t.id,t.location as location,
        t.internal_box_code as internalBoxCode,
        t.product_id as productId,t.sku as sku,t.skc as skc,t.spu as spu,
        t.supplier_id as supplierId,t.supplier_name as supplierName,
        GROUP_CONCAT(DISTINCT ti.supplier_delivery_no) as supplierDeliveryNos,
        GROUP_CONCAT(DISTINCT ti.supplier_delivery_box_code) as supplierDeliveryBoxCodes,
        GROUP_CONCAT(DISTINCT ti.purchase_plan_no) as purchasePlanNos,
        t.arrival_count as arrivalCount,
        sum(ti.qty) as stockinQty,
        t.workmanship_version as workmanshipVersion,
        t.item_part_result as  itemPartResult,
        t.item_craft_result as itemCraftResult,
        t.unqualified_category as unqualifiedCategory,
        t.unqualified_reason as unqualifiedReason,
        t.check_status as checkStatus,
        t.item_remark as itemRemark,
        t.had_push as hadPush,
        t.inbounds_date as inboundsDate,
        t.create_date as createDate,
        t.qc_user_name as qcUserName,
        t.qc_user_real_name as qcUserRealName,
        t.qc_count as qcCount,
        t.cancel_count as cancelCount,
        sps.delivery_date,
        p.image_url as imageUrl,
        p.thumbnail_image_url as thumbnailImageUrl,
        p.preview_image_url as previewImageUrl,
        s.buyer_name as purchaseName
        from stockin_qa_routing_inspect t
        inner join stockin_qa_routing_inspect_item ti on t.id = ti.stockin_qc_routing_inspect_id
        inner join product_spec_info p on p.sku = t.sku
        left join supplier s on s.supplier_id = t.supplier_id
        left  join stock_platform_schedule sps on sps.supplier_delivery_no = ti.supplier_delivery_no
        where
        t.id = #{id}
        group by t.id
    </select>
</mapper>