<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaOrderSkuTypeInfoMapper">
    <select id="listSkuType" resultType="com.nsy.api.wms.response.qa.StockinQaOrderPageExport">
        select sti.stockin_qa_order_id,
        group_concat(distinct sti.sku_type) as skuType
        from stockin_qa_order_sku_type_info sti
        where sti.stockin_qa_order_id in
        <foreach collection="stockinQaOrderIds" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        group by sti.stockin_qa_order_id
    </select>
    
    <select id="pageList" resultType="com.nsy.wms.repository.entity.qa.StockinQaOrderSkuTypeInfoEntity">
        select t.id as id, 
               t.stockin_qa_order_id as stockinQaOrderId,
               o.task_id as taskId
        from stockin_qa_order_sku_type_info t
        inner join stockin_qa_order o on t.stockin_qa_order_id = o.stockin_qa_order_id
        where t.task_id = 0
    </select>
</mapper>