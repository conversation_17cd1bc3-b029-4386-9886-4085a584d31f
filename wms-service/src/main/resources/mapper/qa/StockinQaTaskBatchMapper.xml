<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qa.StockinQaTaskBatchMapper">

    <select id="pageList" resultType="com.nsy.api.wms.response.qa.StockinQaTaskBatchPageResponse">
        select
        t.batch_id as batchId,
        t.location as location,
        t.task_type as taskType,
        t.space_id as spaceId,
        s.space_name as spaceName,
        t.product_id as productId,
        t.spu as spu,
        p.image_url as imageUrl,
        p.thumbnail_image_url as thumbnailImageUrl,
        p.preview_image_url as previewImageUrl,
        t.category_id as categoryId,
        t.category_name as categoryName,
        t.batch_status as batchStatus,
        t.is_distribution as isDistribution,
        t.task_owner as taskOwner,
        t.user_id as userId,
        t.distribution_date as distributionDate,
        t.remark as remark,
        sum(task.qa_qty) as qaQty,
        sum(task.arrival_count) as arrivalQty,
        t.create_date as createDate,
        t.update_date as updateDate,
        t.create_by as createBy,
        t.update_by as updateBy
        from stockin_qa_task_batch t
        inner join product_info p on t.product_id = p.product_id
        inner join stockin_qa_task_batch_item ti on ti.batch_id = t.batch_id
        inner join stockin_qa_task task on task.task_id = ti.task_id
        left join bd_space s on t.space_id = s.space_id
        <where>
            <if test="request.batchId != null and request.batchId != ''">
                AND t.batch_id = #{request.batchId}
            </if>
            <if test="request.taskType != null and request.taskType != ''">
                AND t.task_type = #{request.taskType}
            </if>
            <if test="request.spaceId != null">
                AND t.space_id = #{request.spaceId}
            </if>
            <if test="request.productId != null">
                AND t.product_id = #{request.productId}
            </if>
            <if test="request.skuList != null and request.skuList.size() > 0 ">
                and task.sku in
                <foreach collection="request.skuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.spuList != null and request.spuList.size() > 0 ">
                and t.spu in
                <foreach collection="request.spuList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.categoryId != null">
                AND t.category_id = #{request.categoryId}
            </if>
            <if test="request.batchStatus != null and request.batchStatus != ''">
                AND t.batch_status = #{request.batchStatus}
            </if>
            <if test="request.isDistribution != null">
                AND t.is_distribution = #{request.isDistribution}
            </if>
            <if test="request.taskOwner != null and request.taskOwner != ''">
                AND t.task_owner = #{request.taskOwner}
            </if>
            <if test="request.userId != null">
                AND t.user_id = #{request.userId}
            </if>
            <if test="request.groupUserIdList != null and request.groupUserIdList.size() > 0 ">
                and t.user_id in
                <foreach collection="request.groupUserIdList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.createDateStart != null">
                and t.create_date &gt;= #{request.createDateStart}
            </if>
            <if test="request.createDateEnd != null">
                and t.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.distributionDateStart != null">
                and t.distribution_date &gt;= #{request.distributionDateStart}
            </if>
            <if test="request.distributionDateDateEnd != null">
                and t.distribution_date &lt;= #{request.distributionDateDateEnd}
            </if>
        </where>
        group by t.batch_id
        ORDER BY updateDate desc
    </select>

</mapper> 