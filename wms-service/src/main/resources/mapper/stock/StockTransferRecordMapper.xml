<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.stock.StockTransferRecordMapper">
    <select id="pageSearchList" resultType="com.nsy.api.wms.domain.stock.StockTransferRecordList">
        SELECT
            t.id,
            IFNULL( bp.area_id, sa.area_id ) AS areaId,
            IFNULL(bp.area_name,a.area_name) AS areaName,
            t.transfer_out_code AS transferOutCode,
            t.transfer_in_code AS transferInCode,
            t.transfer_sku_qty AS qty,
            t.sku AS sku,
            t.sync_erp_status AS `status`,
            t.create_by AS createBy,
            t.create_date AS transferDate
        FROM
        stock_transfer_record t
        LEFT JOIN bd_position bp ON bp.position_code = t.transfer_out_code
        LEFT JOIN stock_internal_box b ON b.internal_box_code = t.transfer_out_code
        LEFT JOIN bd_space_area sa ON sa.space_area_id = b.space_area_id
        LEFT JOIN bd_area a ON a.area_id = sa.area_id
        <where>
            <if test="query!=null and query.id != null ">
                and t.id = #{query.id}
            </if>
            <if test="query!=null and query.areaId != null ">
                and (bp.area_id = #{query.areaId} or sa.area_id = #{query.areaId})
            </if>
            <if test="query!=null and query.transferOutCode != null and query.transferOutCode !=''">
                and t.transfer_out_code like concat(#{query.transferOutCode},'%')
            </if>
            <if test="query!=null and query.transferInCode != null and query.transferInCode !=''">
                and t.transfer_in_code like concat(#{query.transferInCode},'%')
            </if>
            <if test="query!=null and query.sku != null and query.sku !=''">
                and t.sku like concat(#{query.sku},'%')
            </if>
            <if test="query!=null and query.createBy != null and query.createBy !=''">
                and t.create_by = #{query.createBy}
            </if>
            <if test="query!=null and query.transferStartDate!=null">
                and t.create_date &gt;= #{query.transferStartDate}
            </if>
            <if test="query!=null and query.transferEndDate!=null">
                and t.create_date &lt; #{query.transferEndDate}
            </if>
        </where>
        order by t.id desc
    </select>

</mapper>
