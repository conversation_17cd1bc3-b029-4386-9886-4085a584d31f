<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stock.StockLendReturnMapper">

    <select id="pageList" resultType="com.nsy.api.wms.response.stockin.StockLendReturnListResponse">
        SELECT
        slr.lend_return_id,
        slr.stock_lend_return_code,
        slr.business_type,
        slr.stock_lend_code,
        slr.space_id,
        slr.space_name,
        slr.`status`,
        slr.scan_qty,
        slr.inferior_qty,
        (slr.scan_qty - slr.wait_distributed_qty) as return_qty,
        (slr.inferior_qty - slr.wait_inferior_qty) as return_inferior_qty,
        slr.wait_distributed_qty,
        slr.wait_inferior_qty,
        slr.create_by,
        slr.create_date,
        slr.regist_type,
        slr.stock_lend_code,
        slr.store_id,
        slr.store_name,
        slr.internal_box_code
        FROM
        stock_lend_return slr
        left join stock_lend_return_item slri on slr.lend_return_id = slri.return_id
        <include refid="page_where"></include>
        group by slr.lend_return_id
        order by slr.create_date desc
    </select>

    <select id="countPageList" resultType="java.lang.Integer">
        SELECT
        count(distinct slr.lend_return_id)
        FROM
        stock_lend_return slr
        left join stock_lend_return_item slri on slr.lend_return_id = slri.return_id
        <include refid="page_where"></include>
    </select>

    <select id="export" resultType="com.nsy.api.wms.response.stockin.StockLendReturnExportResponse">
        SELECT
        slr.stock_lend_return_code,
        slr.stock_lend_code,
        slr.business_type,
        slr.store_name,
        slri.sku,
        slri.scan_qty,
        slri.inferior_qty,
        ( slri.scan_qty - slri.wait_distributed_qty ) AS return_qty,
        ( slri.inferior_qty - slri.wait_inferior_qty ) AS return_inferior_qty,
        slr.registed_date
        FROM
        stock_lend_return slr
        LEFT JOIN stock_lend_return_item slri ON slr.lend_return_id = slri.return_id
        <include refid="page_where"></include>
        order by slr.stock_lend_return_code
    </select>

    <sql id="page_where">
        <where>
            <if test="query.idList != null and query.idList.size() > 0">
                and slr.lend_return_id in
                <foreach collection="query.idList" separator="," index="index" item="id" open="("
                         close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.stockLendReturnCode!= null and query.stockLendReturnCode!=''">
                AND slr.stock_lend_return_code = #{query.stockLendReturnCode}
            </if>
            <if test="query.statusList != null and query.statusList.size() > 0">
                and slr.status in
                <foreach collection="query.statusList" separator="," index="index" item="status" open="("
                         close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query.spaceId!= null">
                AND slr.space_id = #{query.spaceId}
            </if>
            <if test="query.businessType!= null and query.businessType!=''">
                AND slr.business_type = #{query.businessType}
            </if>
            <if test="query.sku!= null and query.sku!=''">
                AND slri.sku = #{query.sku}
            </if>
            <if test="query.createDateBegin != null">
                and slr.create_date &gt;= #{query.createDateBegin}
            </if>
            <if test="query.createDateEnd != null">
                and slr.create_date &lt;= #{query.createDateEnd}
            </if>
            <if test="query.stockLendCode!= null and query.stockLendCode!=''">
                AND slr.stock_lend_code = #{query.stockLendCode}
            </if>
            <if test="query.internalBoxCode!= null and query.internalBoxCode!=''">
                AND slr.internal_box_code = #{query.internalBoxCode}
            </if>
        </where>
    </sql>

    <select id="findNoScanBoList" resultType="com.nsy.wms.business.domain.bo.stock.StockLendReturnNoScanBo">
        SELECT
        slr.lend_return_id,
        IFNULL(sum(slri.scan_qty),0) scan_qty
        FROM
        stock_lend_return slr
        LEFT JOIN stock_lend_return_item slri ON slr.lend_return_id = slri.return_id
        GROUP BY
        slr.lend_return_id
        HAVING
        scan_qty &lt;= 0
    </select>
</mapper>
