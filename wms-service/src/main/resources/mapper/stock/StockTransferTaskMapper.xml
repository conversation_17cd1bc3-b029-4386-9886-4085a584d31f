<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stock.StockTransferTaskMapper">

    <select id="pageList" resultType="com.nsy.api.wms.response.stockout.StockTransferTaskResponse">
        SELECT
        stt.*
        FROM
        stock_transfer_task stt
        LEFT JOIN stock_transfer_task_item stti ON stt.id = stti.transfer_task_id
        <where>
            <if test="request.erpTransferIdList != null and request.erpTransferIdList.size() > 0">
                and stt.erp_transfer_id in
                <foreach collection="request.erpTransferIdList" separator="," index="index" item="item" open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.erpTransferId!= null">
                AND stt.erp_transfer_id = #{request.erpTransferId}
            </if>

            <if test="request.transferType!= null and request.transferType!=''">
                AND stt.transfer_type = #{request.transferType}
            </if>
            <if test="request.spaceAreaId!= null">
                AND stt.space_area_id = #{request.spaceAreaId}
            </if>
            <if test="request.status!= null and request.status!=''">
                AND stt.status = #{request.status}
            </if>
            <if test="request.transferNo!= null and request.transferNo!=''">
                AND stt.transfer_no = #{request.transferNo}
            </if>

            <if test="request.createBy!= null and request.createBy!=''">
                AND stt.create_by = #{request.createBy}
            </if>

            <if test="request.createDateBegin!=null">
                and stt.create_date &gt;= #{request.createDateBegin}
            </if>
            <if test="request.createDateEnd!=null">
                and stt.create_date &lt; #{request.createDateEnd}
            </if>

            <if test="request.operator!= null and request.operator!=''">
                AND stti.operator = #{request.operator}
            </if>

            <if test="request.operateStart!=null">
                and stti.operate_date &gt;= #{request.operateStart}
            </if>
            <if test="request.operateEnd!=null">
                and stti.operate_date &lt; #{request.operateEnd}
            </if>
            <if test="request.spaceId!=null">
                and stt.space_id = #{request.spaceId}
            </if>
        </where>
        group by
            stt.id
    </select>
</mapper>
