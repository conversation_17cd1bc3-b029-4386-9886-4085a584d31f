<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stock.StockLendLogMapper">

    <select id="pageSearch" resultType="com.nsy.api.wms.domain.stock.StockLendLog">
        SELECT lend_log_id AS lendLogId,
               lend_id AS lendId,
               event_type AS eventType,
               content AS content,
               create_by AS createBy,
               create_date AS createDate,
               ip_address AS ipAddress
        FROM stock_lend_log
        <where>
            <if test="query!=null">
                <if test="query.lendId!= null and query.lendId!=''">
                    and lend_id = #{query.lendId}
                </if>
                <if test="query.eventType!= null and query.eventType!=''">
                    and event_type = #{query.eventType}
                </if>
                <if test="query.content!= null and query.content!=''">
                    and content like concat('%',#{query.content},'%')
                </if>
                <if test="query.createBy!= null and query.createBy!=''">
                    and create_by like concat('%',#{query.createBy},'%')
                </if>
            </if>
        </where>
        ORDER BY lend_log_id desc
    </select>
</mapper>
