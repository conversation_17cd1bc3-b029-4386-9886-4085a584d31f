<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.bd.BdPlatformMapper">
    <select id="pageSearchPlatform" resultType="com.nsy.api.wms.domain.bd.BdPlatform">
        SELECT p.platform_id as platformId, MAX(p.platform_name) as platformName, MAX(p.platform_code)as platformCode,
               MAX(p.location) as location, MAX(p.work_area) as workArea, MAX(p.create_by) as createBy, MAX(p.update_by) as updateBy, MAX(p.create_date) as createDate,
               MAX(p.update_date) as updateDate, MAX(p.start_time1) as startTime1, MA<PERSON>(p.end_time1) as endTime1, MAX(p.start_time2) as startTime2, <PERSON><PERSON>(p.end_time2) as endTime2,
               <PERSON><PERSON>(p.start_time3) as startTime3, MAX(p.end_time3) as endTime3, MAX(p.is_deleted) as isDeleted
        FROM bd_platform p
            LEFT JOIN bd_platform_config pc_car ON pc_car.platform_id = p.platform_id and pc_car.config_type=#{query.CAR_TYPE_ENUM} and pc_car.is_deleted=0
            LEFT JOIN bd_platform_config pc_use ON pc_use.platform_id = p.platform_id and pc_use.config_type=#{query.USE_TYPE_ENUM} and pc_use.is_deleted=0
        <where>

            <if test="query.platformName!=null and query.platformName!=''">
                and p.platform_name like '${query.platformName}%'
            </if>
            <if test="query.carType!=null and query.carType!=''">
                and pc_car.config_value = #{query.carType}
            </if>
            <if test="query.useType!=null and query.useType.size() > 0">
                and pc_use.config_value in
                <foreach collection="query.useType" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <!--            <if test="query.spaceIds!=null and query.spaceIds.size() > 0">-->
            <!--                and p.space_id in-->
            <!--                <foreach collection="query.spaceIds" separator="," index="index" item="spaceId" open="(" close=")">-->
            <!--                    #{spaceId}-->
            <!--                </foreach>-->
            <!--            </if>-->
        </where>
        GROUP BY p.platform_id
        order by p.platform_id desc
    </select>

</mapper>
