<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.overseas.OverseasWarehouseOrderMapper">

    <!-- 分页查询海外仓订单 -->
    <select id="pageSearchOverseasWarehouseOrder" resultType="com.nsy.wms.repository.entity.overseas.OverseasWarehouseOrderEntity">
        SELECT 
            *
        FROM wms_overseas_warehouse_order
        <where>
            <if test="param.stockoutOrderNo != null and param.stockoutOrderNo != ''">
                AND stockout_order_no LIKE CONCAT('%', #{param.stockoutOrderNo}, '%')
            </if>
            <if test="param.overseasOrderNo != null and param.overseasOrderNo != ''">
                AND overseas_order_no LIKE CONCAT('%', #{param.overseasOrderNo}, '%')
            </if>
            <if test="param.status != null and param.status != ''">
                AND status = #{param.status}
            </if>
            <if test="param.overseasSpaceName != null and param.overseasSpaceName != ''">
                AND overseas_space_name LIKE CONCAT('%', #{param.overseasSpaceName}, '%')
            </if>
            <if test="param.platformName != null and param.platformName != ''">
                AND platform_name LIKE CONCAT('%', #{param.platformName}, '%')
            </if>
            <if test="param.logisticsNo != null and param.logisticsNo != ''">
                AND logistics_no LIKE CONCAT('%', #{param.logisticsNo}, '%')
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                AND create_date &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                AND create_date &lt;= #{param.createTimeEnd}
            </if>
            <if test="param.shipTimeStart != null and param.shipTimeStart != ''">
                AND overseas_ship_time &gt;= #{param.shipTimeStart}
            </if>
            <if test="param.shipTimeEnd != null and param.shipTimeEnd != ''">
                AND overseas_ship_time &lt;= #{param.shipTimeEnd}
            </if>
            <if test="param.location != null and param.location != ''">
                AND location = #{param.location}
            </if>
        </where>
        ORDER BY create_date DESC
    </select>

    <!-- 根据状态统计订单数量 -->
    <select id="countByStatus" resultType="com.nsy.api.wms.response.stockout.StatusCountResponse">
        SELECT 
            status as status,
            COUNT(1) as count
        FROM wms_overseas_warehouse_order
        WHERE location = #{location}
        GROUP BY status
    </select>

</mapper> 