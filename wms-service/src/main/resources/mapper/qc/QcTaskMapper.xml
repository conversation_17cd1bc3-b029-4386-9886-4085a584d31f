<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.nsy.wms.repository.jpa.mapper.qc.QcTaskMapper">
    <select id="pageList" resultType="com.nsy.api.wms.response.qc.QcTaskListResponse">
        SELECT
        ANY_VALUE(ps.image_url) as imageUrl,
        ANY_VALUE(ps.thumbnail_image_url) as thumbnailImageUrl,
        ANY_VALUE(ps.preview_image_url) as previewImageUrl,
        ps.barcode as barcode,
        qt.internal_box_code as internal_box_code,
        qt.internal_box_type as internal_box_type,
        qt.qc_task_id as qcTaskId,
        qt.sku as sku,
        qt.qty as qty,
        qt.unqualified_count as unqualifiedCount,
        qt.unqualified_category as unqualifiedCategory,
        qt.unqualified_reason as unqualifiedReason,
        qt.unqualified_reason_info as unqualifiedReasonInfo,
        qt.qualified_count as qualifiedCount,
        qt.create_by as createBy,
        qt.create_date as createDate,
        qrl.content as content,
        slr.business_type as businessType,
        slr.store_name as storeName,
        slri.create_date as lendReturnDate,
        qt.return_order_no as returnOrderNo,
        qt.stock_lend_return_code as stockLendReturnCode,
        qt.stock_lend_code as stockLendCode,
        qt.packing_method as packingMethod,
        qt.version_no as versionNo
        from qc_record qt
        left join product_spec_info ps on qt.spec_id=ps.spec_id
        left join qc_record_log qrl on qrl.qc_task_id=qt.qc_task_id
        left join stock_lend_return slr on slr.lend_return_id = qt.lend_return_id
        left join stock_lend_return_item slri on slr.lend_return_id=slri.return_id and slri.sku = qt.sku
        <include refid="whereCondition"></include>
        ORDER BY qt.create_date DESC
    </select>
    <select id="pageListForDownload" resultType="com.nsy.api.wms.response.qc.QcTaskListResponse">
        SELECT
        qt.sku as sku,
        qt.internal_box_code as internal_box_code,
        qt.internal_box_type as internal_box_type,
        qt.qty as qty,
        qt.unqualified_count as unqualifiedCount,
        qt.unqualified_category as unqualifiedCategory,
        qt.unqualified_reason as unqualifiedReason,
        qt.unqualified_reason_info as unqualifiedReasonInfo,
        qt.qualified_count as qualifiedCount,
        qt.create_by as createBy,
        qt.create_date as createDate,
        slr.business_type as businessType,
        slr.store_name as storeName,
        slri.create_date as lendReturnDate,
        qt.return_order_no as returnOrderNo,
        qt.stock_lend_return_code as stockLendReturnCode,
        qt.stock_lend_code as stockLendCode
        from qc_record qt
        left join stock_lend_return slr on slr.lend_return_id = qt.lend_return_id
        left join stock_lend_return_item slri on slr.lend_return_id=slri.return_id and slri.sku = qt.sku
        <include refid="whereCondition"></include>
        ORDER BY qt.create_date DESC
    </select>
    <sql id="whereCondition">
        <where>
            <if test="request.idList!=null and request.idList.size() > 0">
                and qt.qc_task_id in
                <foreach collection="request.idList" separator="," index="index" item="item" open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.internalBoxCode != null and request.internalBoxCode != ''">
                and qt.internal_box_code = #{request.internalBoxCode}
            </if>
            <if test="request.internalBoxType != null and request.internalBoxType != ''">
                and qt.internal_box_type = #{request.internalBoxType}
            </if>
            <if test="request.sku != null and request.sku != ''">
                and qt.sku like concat(#{request.sku}, '%')
            </if>
            <if test="request.createDateStart != null and request.createDateStart != ''">
                and qt.create_date &gt;= #{request.createDateStart}
            </if>
            <if test="request.createDateEnd != null and request.createDateEnd != ''">
                and qt.create_date &lt;= #{request.createDateEnd}
            </if>
            <if test="request.createBy != null and request.createBy != ''">
                and qt.create_by like concat('%',#{request.createBy},'%')
            </if>
            <if test="request.businessType != null and request.businessType != ''">
                and slr.business_type = #{request.businessType}
            </if>
            <if test="request.storeName != null and request.storeName != ''">
                and slr.store_name = #{request.storeName}
            </if>
            <if test="request.lendReturnDateStart != null and request.lendReturnDateStart != ''">
                and slri.create_date &gt;= #{request.lendReturnDateStart}
            </if>
            <if test="request.lendReturnDateEnd != null and request.lendReturnDateEnd != ''">
                and slri.create_date &lt;= #{request.lendReturnDateEnd}
            </if>
            <if test="request.unqualifiedCount != null and request.unqualifiedCount >= 0">
                and qt.unqualified_count &gt; #{request.unqualifiedCount}
            </if>
            <if test="request.location != null and request.location != ''">
                and qt.location = #{request.location}
            </if>
            <if test="request.returnOrderNo != null and request.returnOrderNo != ''">
                and qt.return_order_no = #{request.returnOrderNo}
            </if>
            <if test="request.stockLendCode != null and request.stockLendCode != ''">
                and qt.stock_lend_code = #{request.stockLendCode}
            </if>
            <if test="request.stockLendReturnCode != null and request.stockLendReturnCode != ''">
                and qt.stock_lend_return_code = #{request.stockLendReturnCode}
            </if>
        </where>
    </sql>
    <select id="qcTask" resultType="com.nsy.api.wms.response.qc.QcTaskListResponse">
        SELECT
        ANY_VALUE(ps.image_url) as imageUrl,
        ANY_VALUE(ps.thumbnail_image_url) as thumbnailImageUrl,
        ANY_VALUE(ps.preview_image_url) as previewImageUrl,
        ps.barcode as barcode,
        sibi.internal_box_code as internal_box_code,
        sib.internal_box_type as internal_box_type,
        sibi.internal_box_item_id as internal_box_item_id,
        sibi.create_date as createDate,
        sibi.sku as sku,
        sibi.qty as qty,
        lendreturn.lend_return_id as lendReturnId,
        sibi.return_order_no as returnOrderNo,
        lendreturn.stock_lend_return_code as stockLendReturnCode,
        sibi.stockout_order_no as stockLendCode
        from stock_internal_box_item sibi
        left join product_spec_info ps on sibi.spec_id=ps.spec_id
        left join stock_internal_box sib on sibi.internal_box_id=sib.internal_box_id
        left join stock_lend_return lendreturn on lendreturn.stock_lend_return_code=sibi.return_order_no
        <where>
            sib.status in ('WAIT_QC','PACKING','QC_PROCESSING')
            and sib.internal_box_type in ('RETURN_BOX','BORROW_RETURN_BOX')
            and sibi.internal_box_code = #{internalBoxCode} and sibi.status = 'WAIT_QC'
            and (lendreturn.status != 'REGISTING' or sibi.return_order_no='' or ISNULL(sibi.return_order_no)=1 or sib.internal_box_type='RETURN_BOX')
        </where>
    </select>
    <select id="qcTaskItem" resultType="com.nsy.api.wms.response.qc.QcTaskListResponse">
        SELECT ps.color as color,
               ps.size as size,
            sibi.internal_box_code as internal_box_code,
            sib.internal_box_type as internal_box_type,
            sibi.internal_box_item_id as internal_box_item_id,
            sibi.space_id as space_id,
            sibi.spec_id as spec_id,
            sibi.product_id as product_id,
            sibi.sku as sku,
            sibi.qty as qty
        from stock_internal_box_item sibi
            left join product_spec_info ps
        on sibi.spec_id=ps.spec_id
            left join stock_internal_box sib on sibi.internal_box_id=sib.internal_box_id
        where sibi.internal_box_item_id = #{internalBoxItemId} and sibi.status = 'WAIT_QC'
    </select>
</mapper>
