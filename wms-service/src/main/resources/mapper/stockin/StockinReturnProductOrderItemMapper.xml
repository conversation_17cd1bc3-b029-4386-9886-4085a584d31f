<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductOrderItemMapper">

    <resultMap id="BaseResultMap" type="com.nsy.wms.repository.entity.stockin.StockinReturnProductOrderItemEntity">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="location" column="location" jdbcType="VARCHAR"/>
            <result property="returnOrderId" column="return_order_id" jdbcType="INTEGER"/>
            <result property="productId" column="product_id" jdbcType="INTEGER"/>
            <result property="specId" column="spec_id" jdbcType="INTEGER"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="originQty" column="origin_qty" jdbcType="INTEGER"/>
            <result property="returnQty" column="return_qty" jdbcType="INTEGER"/>
            <result property="registerQty" column="register_qty" jdbcType="INTEGER"/>
            <result property="inferiorQty" column="inferior_qty" jdbcType="INTEGER"/>
            <result property="internalBoxCode" column="internal_box_code" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,location,return_order_id,
        product_id,spec_id,sku,
        origin_qty,return_qty,register_qty,
        inferior_qty,internal_box_code,remark,create_date,
        create_by,update_date,update_by,
        version
    </sql>

    <select id="orderItemPage"
            resultType="com.nsy.api.wms.response.stockin.StockinReturnProductOrderItemListResponse">
    select
        p.image_url,
        p.preview_image_url,
        p.thumbnail_image_url,
        p.product_id,
        p.spec_id,
        p.sku,
        p.barcode,
        p.color,
        p.size,
        p.skc,
        item.id,
        item.return_order_id,
        item.order_item_id,
        item.origin_qty,
        item.return_qty,
        item.register_qty,
        item.inferior_qty,
        item.internal_box_code,
        item.remark
    from
        stockin_return_product_order_item item
    left join product_spec_info p on item.spec_id = p.spec_id
    <where>
        <if test="request.returnOrderId != null">
            AND item.return_order_id = #{request.returnOrderId}
        </if>
        <if test="request.sku != null and request.sku != ''">
            AND item.sku = #{request.sku}
        </if>
    </where>
    </select>

    <select id="orderItemList"
            resultType="com.nsy.api.wms.response.stockin.StockinReturnProductOrderItemListResponse">
        select
        p.image_url,
        p.preview_image_url,
        p.thumbnail_image_url,
        p.product_id,
        p.spec_id,
        p.sku,
        p.barcode,
        p.color,
        p.size,
        p.skc,
        item.id,
        item.return_order_id,
        item.order_item_id,
        item.origin_qty,
        item.return_qty,
        item.register_qty,
        item.inferior_qty,
        item.internal_box_code,
        item.remark
        from
        stockin_return_product_order_item item
        left join product_spec_info p on item.spec_id = p.spec_id
        where
            item.return_order_id in
            <foreach collection="returnOrderIdList" separator="," index="index" item="returnOrderId" open="(" close=")">
                #{returnOrderId}
            </foreach>
        order by item.update_date desc
    </select>
</mapper>
