<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>打印发货单</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            font-size: 12px;
        }

        .outBox {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .codeImg{
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .spaceBetween {
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            width: 100%;
        }
        .spaceBetween div{
            line-height: 30px;
        }
        .flexEnd {
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            width: 100%;
        }

        table {
            border-collapse: collapse;
        }

        /*table, table tr th, table tr td {*/
        /*    border: 1px solid #000000;*/
        /*}*/
    </style>
</head>
<body>
<div class="outBox">
    <!--    放置条形码-->
    <div class="codeImg">
        ${data.orderNo!}
    </div>
    <br>
    <!--    入库任务信息-->
    <div class="spaceBetween">
        <div style="width: 33%;">
            流水号：${data.tradeNo!}
        </div>
        <div style="width: 66%;">
            店铺名称：${data.storeName!}
        </div>
        <div style="width: 80%;">
            发货日期：${data.shipDate!}
        </div>
        <div style="width: 99%;">
            收件人ID：${data.receiveId!}
        </div>
        <div style="width: 49%;">
            收件人：${data.receiver!}
        </div>
        <div style="width: 49%;text-align:right;">
            电话号码：${data.phoneNumber!}
        </div>
        <div style="width: 100%;">
            卖家备注：
        </div>
        <div style="width: 100%;">
            仓库备注：${data.spaceMemo!}
        </div>
    </div>
    <br>
    <!--    入库任务明细列表-->
    <table align="center" width="100%" style="width: 100%;table-layout: fixed; word-break: break-all;">
        <tr>
            <th align="center" valign="middle">序号</th>
            <th align="center" valign="middle">规格编码</th>
            <th align="center" valign="middle">数量</th>
            <th align="center" valign="middle">颜色尺码</th>
        </tr>
        <#list data.skuInfoList as detail>
            <tr>
                <td align="center" valign="middle">${detail.index!}</td>
                <td align="center" valign="middle">${detail.sku!}</td>
                <td align="center" valign="middle">${detail.qty!}</td>
                <td align="center" valign="middle">${detail.color!}/${detail.size!}</td>
            </tr>
        </#list>
    </table>
    <br>
    <div class="flexEnd">
        <div style="width: 100%;">
            数量合计：${data.totalCount!}
        </div>
        <div style="width: 100%;">
            买家留言：
        </div>
        <br>
        <div style="width: 100%;">
            请保存好此发货单，如需退货请把此单、同衣服一起寄回，这样方便我们仓库签收验货
        </div>
        <br>
    </div>
</div>
</body>
</html>
