<!-- 装箱清单 -->
<!DOCTYPE html>
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <style>
        @page{
            size:a4;
            margin:8mm 0 0 0;
        }
        tr
        {mso-height-source:auto;
            mso-ruby-visibility:none;
        }
        td { overflow: hidden;}
        col
        {mso-width-source:auto;
            mso-ruby-visibility:none;}
        br
        {mso-data-placement:same-cell;}
        .font5
        {color:windowtext;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:"Times New Roman";
            mso-generic-font-family:roman;
            mso-font-charset:0;}
        .font8
        {color:windowtext;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:"宋体";
            mso-generic-font-family:auto;
            mso-font-charset:134;}
        .style0
        {mso-number-format:"General";
            text-align:general;
            vertical-align:middle;
            white-space:nowrap;
            mso-rotate:0;
            mso-pattern:auto;
            mso-background-source:auto;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:134;
            border:none;
            mso-protection:locked visible;
            mso-style-name:"常规";
            mso-style-id:0;}
        .style16
        {mso-number-format:"_ \0022\00A5\0022* \#\,\#\#0_ \;_ \0022\00A5\0022* \\-\#\,\#\#0_ \;_ \0022\00A5\0022* \0022-\0022_ \;_ \@_ ";
            mso-style-name:"货币[0]";
            mso-style-id:7;}
        .style17
        {mso-pattern:auto none;
            background:#EBF1DE;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"20% - 强调文字颜色 3";}
        .style18
        {mso-pattern:auto none;
            background:#FFCC99;
            color:#3F3F76;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            border:.5pt solid #7F7F7F;
            mso-style-name:"输入";}
        .style19
        {mso-number-format:"_ \0022\00A5\0022* \#\,\#\#0\.00_ \;_ \0022\00A5\0022* \\-\#\,\#\#0\.00_ \;_ \0022\00A5\0022* \0022-\0022??_ \;_ \@_ ";
            mso-style-name:"货币";
            mso-style-id:4;}
        .style20
        {mso-number-format:"_ * \#\,\#\#0_ \;_ * \\-\#\,\#\#0_ \;_ * \0022-\0022_ \;_ \@_ ";
            mso-style-name:"千位分隔[0]";
            mso-style-id:6;}
        .style21
        {mso-pattern:auto none;
            background:#D8E4BC;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"40% - 强调文字颜色 3";}
        .style22
        {mso-pattern:auto none;
            background:#FFC7CE;
            color:#9C0006;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"差";}
        .style23
        {mso-number-format:"_ * \#\,\#\#0\.00_ \;_ * \\-\#\,\#\#0\.00_ \;_ * \0022-\0022??_ \;_ \@_ ";
            mso-style-name:"千位分隔";
            mso-style-id:3;}
        .style24
        {mso-pattern:auto none;
            background:#C4D79B;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"60% - 强调文字颜色 3";}
        .style25
        {color:#0000FF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:underline;
            text-underline-style:single;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"超链接";
            mso-style-id:8;}
        .style26
        {mso-number-format:"0%";
            mso-style-name:"百分比";
            mso-style-id:5;}
        .style27
        {color:#800080;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:underline;
            text-underline-style:single;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"已访问的超链接";
            mso-style-id:9;}
        .style28
        {mso-pattern:auto none;
            background:#FFFFCC;
            border:.5pt solid #B2B2B2;
            mso-style-name:"注释";}
        .style29
        {mso-pattern:auto none;
            background:#DA9694;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"60% - 强调文字颜色 2";}
        .style30
        {color:#1F497D;
            font-size:11.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:134;
            mso-style-name:"标题 4";}
        .style31
        {color:#FF0000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"警告文本";}
        .style32
        {color:#1F497D;
            font-size:18.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:134;
            mso-style-name:"标题";}
        .style33
        {color:#7F7F7F;
            font-size:11.0pt;
            font-weight:400;
            font-style:italic;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"解释性文本";}
        .style34
        {color:#1F497D;
            font-size:15.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:134;
            border-bottom:1.0pt solid #4F81BD;
            mso-style-name:"标题 1";}
        .style35
        {color:#1F497D;
            font-size:13.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:134;
            border-bottom:1.0pt solid #4F81BD;
            mso-style-name:"标题 2";}
        .style36
        {mso-pattern:auto none;
            background:#95B3D7;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"60% - 强调文字颜色 1";}
        .style37
        {color:#1F497D;
            font-size:11.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:134;
            border-bottom:1.0pt solid #A7BFDE;
            mso-style-name:"标题 3";}
        .style38
        {mso-pattern:auto none;
            background:#B1A0C7;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"60% - 强调文字颜色 4";}
        .style39
        {mso-pattern:auto none;
            background:#F2F2F2;
            color:#3F3F3F;
            font-size:11.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            border:.5pt solid #3F3F3F;
            mso-style-name:"输出";}
        .style40
        {mso-pattern:auto none;
            background:#F2F2F2;
            color:#FA7D00;
            font-size:11.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            border:.5pt solid #7F7F7F;
            mso-style-name:"计算";}
        .style41
        {mso-pattern:auto none;
            background:#A5A5A5;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            border:2.0pt double #3F3F3F;
            mso-style-name:"检查单元格";}
        .style42
        {mso-pattern:auto none;
            background:#FDE9D9;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"20% - 强调文字颜色 6";}
        .style43
        {mso-pattern:auto none;
            background:#C0504D;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"强调文字颜色 2";}
        .style44
        {color:#FA7D00;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            border-bottom:2.0pt double #FF8001;
            mso-style-name:"链接单元格";}
        .style45
        {color:#000000;
            font-size:11.0pt;
            font-weight:700;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            border-top:.5pt solid #4F81BD;
            border-bottom:2.0pt double #4F81BD;
            mso-style-name:"汇总";}
        .style46
        {mso-pattern:auto none;
            background:#C6EFCE;
            color:#006100;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"好";}
        .style47
        {mso-pattern:auto none;
            background:#FFEB9C;
            color:#9C6500;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"适中";}
        .style48
        {mso-pattern:auto none;
            background:#DAEEF3;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"20% - 强调文字颜色 5";}
        .style49
        {mso-pattern:auto none;
            background:#4F81BD;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"强调文字颜色 1";}
        .style50
        {mso-pattern:auto none;
            background:#DCE6F1;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"20% - 强调文字颜色 1";}
        .style51
        {mso-pattern:auto none;
            background:#B8CCE4;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"40% - 强调文字颜色 1";}
        .style52
        {mso-pattern:auto none;
            background:#F2DCDB;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"20% - 强调文字颜色 2";}
        .style53
        {mso-pattern:auto none;
            background:#E6B8B7;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"40% - 强调文字颜色 2";}
        .style54
        {mso-pattern:auto none;
            background:#9BBB59;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"强调文字颜色 3";}
        .style55
        {mso-pattern:auto none;
            background:#8064A2;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"强调文字颜色 4";}
        .style56
        {mso-pattern:auto none;
            background:#E4DFEC;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"20% - 强调文字颜色 4";}
        .style57
        {mso-pattern:auto none;
            background:#CCC0DA;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"40% - 强调文字颜色 4";}
        .style58
        {mso-pattern:auto none;
            background:#4BACC6;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"强调文字颜色 5";}
        .style59
        {mso-pattern:auto none;
            background:#B7DEE8;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"40% - 强调文字颜色 5";}
        .style60
        {mso-pattern:auto none;
            background:#92CDDC;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"60% - 强调文字颜色 5";}
        .style61
        {mso-pattern:auto none;
            background:#F79646;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"强调文字颜色 6";}
        .style62
        {mso-pattern:auto none;
            background:#FCD5B4;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"40% - 强调文字颜色 6";}
        .style63
        {mso-pattern:auto none;
            background:#FABF8F;
            color:#FFFFFF;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:0;
            mso-style-name:"60% - 强调文字颜色 6";}
        .style64
        {mso-number-format:"General";
            text-align:general;
            vertical-align:middle;
            mso-rotate:0;
            mso-pattern:auto;
            mso-background-source:auto;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:134;
            border:none;
            mso-protection:locked visible;
            mso-style-name:"常规 5";}
        td
        {mso-style-parent:style0;
            padding-top:1px;
            padding-right:1px;
            padding-left:1px;
            mso-ignore:padding;
            mso-number-format:"General";
            text-align:general;
            vertical-align:middle;
            mso-rotate:0;
            mso-pattern:auto;
            mso-background-source:auto;
            color:#000000;
            font-size:11.0pt;
            font-weight:400;
            font-style:normal;
            text-decoration:none;
            font-family:宋体;
            mso-generic-font-family:auto;
            mso-font-charset:134;
            border:none;
            mso-protection:locked visible;}
        .xl66 {
            mso-style-parent: style0;
            mso-font-charset: 134;
            margin-left: 10px;}
        .xl67
        {mso-style-parent:style0;
            vertical-align:bottom;
            color:windowtext;
            font-size:12.0pt;
            mso-font-charset:134;}
        .xl68
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            color:windowtext;
            font-size:18.0pt;
            font-weight:700;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl69
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            color:windowtext;
            font-size:14.0pt;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl70
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            color:windowtext;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl71
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            white-space:normal;
            color:windowtext;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl72
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            font-size:15.0pt;
            font-weight:700;
            mso-font-charset:134;
            border:.5pt solid windowtext;}
        .xl73
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            font-size:14.0pt;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl74
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            color:windowtext;
            mso-font-charset:134;
            border:.5pt solid windowtext;}
        .xl75
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            color:#FF0000;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl76
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl77
        {mso-style-parent:style0;
            vertical-align:bottom;
            font-weight:700;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl78
        {mso-style-parent:style0;
            vertical-align:bottom;
            color:#FF0000;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl79
        {mso-style-parent:style0;
            vertical-align:bottom;
            color:windowtext;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl80
        {mso-style-parent:style0;
            vertical-align:bottom;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl81
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            color:windowtext;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl82
        {mso-style-parent:style0;
            vertical-align:bottom;
            color:#FF0000;
            font-size:10.0pt;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl83
        {mso-style-parent:style0;
            mso-number-format:"\[$-409\]mmmm\\ d\\\,\\ yyyy\;\@";
            text-align:left;
            vertical-align:bottom;
            color:#FF0000;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl84
        {mso-style-parent:style0;
            vertical-align:bottom;
            color:windowtext;
            font-weight:700;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl85
        {mso-style-parent:style0;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl86
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            font-weight:700;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;
            border:.5pt solid windowtext;}
        .xl87
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            color:windowtext;
            font-size:12.0pt;
            font-weight:700;
            mso-font-charset:134;
            border:.5pt solid windowtext;}
        .xl88
        {mso-style-parent:style0;
            vertical-align:bottom;
            color:windowtext;
            font-size:12.0pt;
            mso-font-charset:134;
            border:.5pt solid windowtext;}
        .xl89
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            color:windowtext;
            font-size:12.0pt;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;}
        .xl90
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            color:#FF0000;
            font-size:12.0pt;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;}
        .xl91
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            font-size:12.0pt;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;}
        .xl92
        {mso-style-parent:style0;
            text-align:center;
            vertical-align:bottom;
            color:windowtext;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;}
        .xl93
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            color:#FF0000;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;}
        .xl94
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            font-family:Times New Roman;
            mso-generic-font-family:roman;
            mso-font-charset:0;}
        .xl95
        {mso-style-parent:style0;
            text-align:left;
            vertical-align:bottom;
            color:windowtext;
            mso-font-charset:134;}
        .xl96
        {mso-style-parent:style64;
            mso-font-charset:134;
            border:.5pt solid windowtext;}
        .xl97
        {mso-style-parent:style64;
            mso-font-charset:134;
            border:.5pt solid windowtext;}

        <!-- @page
        {margin:0.75in 0.70in 0.75in 0.70in;
            mso-header-margin:0.30in;
            mso-footer-margin:0.30in;}
        -->  </style>

</head>
<body>
<div style="margin: auto 0;margin-left: 10px">

    <table id="table" width="90%" border="2" cellpadding="0" cellspacing="0" style='width:100%;border-collapse:collapse;table-layout:fixed;align:center;z-index: 1;'>

    <tr height="30" class="xl66" style='height:22.50pt;'>
        <td class="xl68" height="30" width="100%" colspan="10" style='height:22.50pt;width:100%;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'>${company.companyName!}</td>

    </tr>
    <tr height="25" class="xl66" style='height:18.75pt;'>
        <td class="xl69" height="25" colspan="10" style='height:18.75pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'>${company.companyEnglishName!}</td>

    </tr>
    <tr height="21" class="xl66" style='height:15.75pt;'>
        <td class="xl70" height="21" colspan="10" style='height:15.75pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'>地址：${company.address!}</td>

    </tr>
    <tr height="44" class="xl66" style='height:33.00pt;mso-height-source:userset;mso-height-alt:660;'>
        <td class="xl71" height="44" colspan="10" style='height:33.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'>ADD：${company.englishAddress!}</td>
    </tr>
    <tr height="26" class="xl66" style='height:19.50pt;'>
        <td class="xl72" height="26" colspan="10" style='height:19.50pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'>装&#160; 箱&#160; 单</td>
    </tr>
    <tr height="25" class="xl66" style='height:18.75pt;'>
        <td class="xl73" height="25" colspan="10" style='height:18.75pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'>PACKING&#160; LIST</td>
    </tr>
    <tr height="20" class="xl66" style='height:15.00pt;'>
        <td class="xl74" height="20" style='height:15.00pt;'><font class="font8">客户</font><font class="font5">:</font></td>
        <td class="xl75"></td>
        <td class="xl76"></td>
        <td class="xl76"></td>
        <td class="xl76"></td>
        <td class="xl76"></td>
        <td class="xl74"><font class="font8">发票号</font><font class="font5">:</font></td>
        <td class="xl75" colspan="3">${documentInfo.declareDocumentNo!}</td>
    </tr>
    <tr height="20" class="xl66" style='height:15.00pt;'>
        <td class="xl77" height="20" style='height:15.00pt;'>MESSRS.:</td>
        <td class="xl78" colspan="2">${documentInfo.customer!}</td>
        <td class="xl80"></td>
        <td class="xl80"></td>
        <td class="xl79"></td>
        <td class="xl81">INV. NO.:</td>
        <td class="xl78"></td>
        <td class="xl79"></td>
        <td class="xl79"></td>
    </tr>
    <tr height="20" class="xl66" style='height:15.00pt;'>
        <td class="xl74" height="20" style='height:15.00pt;'></td>
        <td class="xl82"></td>
        <td class="xl79"></td>
        <td class="xl80"></td>
        <td class="xl80"></td>
        <td class="xl79"></td>
        <td class="xl74"><font class="font8">日期</font><font class="font5">:</font></td>
        <td class="xl83" colspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;'>${date!}</td>
        <td class="xl79"></td>
    </tr>
    <tr height="20" class="xl66" style='height:15.00pt;'>
        <td class="xl84" height="20" style='height:15.00pt;'></td>
        <td class="xl82"></td>
        <td class="xl85"></td>
        <td class="xl85"></td>
        <td class="xl79"></td>
        <td class="xl79"></td>
        <td class="xl86">DATE:</td>
        <td class="xl79"></td>
        <td class="xl80"></td>
        <td class="xl80"></td>
    </tr>
    <#assign no=1/>
    <#list itemMap?keys as key>
        <tr height="19" class="xl67" style='height:14.25pt;mso-height-source:userset;mso-height-alt:285;'>
            <td class="xl87" height="19" style='height:14.25pt;'>${key}</td>
            <td class="xl87">报关名称</td>
            <td class="xl87">报关英文</td>
            <td class="xl87">织造方式</td>
            <td class="xl87">成分</td>
            <td class="xl87">客户Sku</td>
            <td class="xl87">数量</td>
            <td class="xl87">要素备注</td>
            <td class="xl87">重量</td>
            <td class="xl87">净重</td>
            <td class="xl67" colspan="245" style='mso-ignore:colspan;'></td>
        </tr>
        <#list itemMap[key] as subList>
            <#list subList as item>
                <tr height="19" class="xl67" style='height:14.25pt;'>
                    <td class="xl88" height="19" style='height:14.25pt;'
                    ><#if item_index == 0>NO.${no} <#assign no=no+1/></#if></td>
                    <td class="xl88">${item.customsDeclareCn!}</td>
                    <td class="xl88">${item.customsDeclareEn!}</td>
                    <td class="xl88">${item.spinType!}</td>
                    <td class="xl88">${item.fabricType!}</td>
                    <td class="xl88">${item.sellerSku!}</td>
                    <td class="xl88" align="right">${item.qty!}</td>
                    <td class="xl88">${item.declareElement!}</td>
                    <td class="xl88" align="right">
                        <#if item_index == 0>${item.boxWeight!}</#if>
                    </td>
                    <td class="xl88" align="right">
                        <#if item_index == 0>${item.boxWeight - 1.6!}</#if>
                    </td>
                </tr>
            </#list>
            <tr height="19" class="xl67" style='height:14.25pt;'>
                <td class="xl88" height="19" style='height:14.25pt;'></td>
                <td class="xl88"></td>
                <td class="xl88"></td>
                <td class="xl88"></td>
                <td class="xl88"></td>
                <td class="xl88"></td>
                <td class="xl88" align="right"></td>
                <td class="xl88"></td>
                <td class="xl88" align="right">
                </td>
                <td class="xl88" align="right">
                </td>

            </tr>
            <tr height="19" class="xl67" style='height:14.25pt;'>
                <td class="xl88" height="19" style='height:14.25pt;'></td>
                <td class="xl88"></td>
                <td class="xl88"></td>
                <td class="xl88"></td>
                <td class="xl88"></td>
                <td class="xl88"></td>
                <td class="xl88" align="right"></td>
                <td class="xl88"></td>
                <td class="xl88" align="right">
                </td>
                <td class="xl88" align="right">
                </td>
            </tr>
        </#list>
    </#list>
    <tr height="19" style='height:14.25pt;'></tr>
    <tr height="19" style='height:14.25pt;border: hidden'></tr>
    <tr height="19" style='height:14.25pt;border: hidden'></tr>

</table>
<div style="font-size: 15px">补充电子盖章：</div>
</div>
</body>
</html>
<script>
    function getImgae1(){
        let table=document.getElementById('table')
        let num=parseInt(table.clientHeight/1100)
        let tops=930
        for(let i=0;i<num+1;i++){
            var bigImg = document.createElement("img"); //创建一个img元素
            bigImg.src = "${company.electronicSeal!}"; //给img元素的src属性赋值
            //bigImg.width="320"; //320个像素 不用加px
            bigImg.setAttribute('transcolor',"#FFFFFF")
            document.body.appendChild(bigImg); //为dom添加子元素img
            bigImg.style.position='absolute'
            bigImg.style.width = '145px'
            bigImg.style.height = '145px'
            if(i!==0){
                bigImg.style.top=tops+1100*i+'px'
                bigImg.style.left='250px'
            }else{
                bigImg.style.top=tops+'px'
                bigImg.style.left='250px'
            }
        }
    }
    function getImgae2(){
        let table=document.getElementById('table')
        let num=parseInt(table.clientHeight/1100)
        let tops=925
        for(let i=0;i<num+1;i++){
            var bigImg = document.createElement("img"); //创建一个img元素
            bigImg.src = "${fixedElectronicSeal1!}"; //给img元素的src属性赋值
            //bigImg.width="320"; //320个像素 不用加px
            bigImg.setAttribute('transcolor',"#FFFFFF")
            document.body.appendChild(bigImg); //为dom添加子元素img
            bigImg.style.position='absolute'
            bigImg.style.width = '155px'
            bigImg.style.height= '155px'
            if(i!==0){
                bigImg.style.top=tops+1100*i+'px'
                bigImg.style.left='385px'
            }else{
                bigImg.style.top=tops+'px'
                bigImg.style.left='385px'
            }
        }
    }
    function getImgae3(){
        let table=document.getElementById('table')
        let num=parseInt(table.clientHeight/1100)
        let tops=925
        for(let i=0;i<num+1;i++){
            var bigImg = document.createElement("img"); //创建一个img元素
            bigImg.src = "${fixedElectronicSeal2!}"; //给img元素的src属性赋值
            //bigImg.width="320"; //320个像素 不用加px
            bigImg.setAttribute('transcolor',"#FFFFFF")
            document.body.appendChild(bigImg); //为dom添加子元素img
            bigImg.style.position='absolute'
            bigImg.style.width = '155px'
            bigImg.style.height = '155px'
            if(i!==0){
                bigImg.style.top=tops+1100*i+'px'
                bigImg.style.left='520px'
            }else{
                bigImg.style.top=tops+'px'
                bigImg.style.left='520px'
            }
        }
    }
    getImgae1()
    getImgae2()
    getImgae3()
</script>
