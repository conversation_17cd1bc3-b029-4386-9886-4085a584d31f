<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>打印拣货任务</title>
    <style>
        @page{
            size:a4;
            margin:8mm 0 0 0;
        }
        body {
            font-size: 13px;
            width: 198mm;
            margin-top: 2mm;
            font-family: "宋体";
        }

        .outBox {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 98%;
            margin: 0 auto;
        }

        .codeImg {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .spaceBetween {
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            width: 100%;
        }

        .spaceBetween div {
            line-height: 20px;
        }

        .flexEnd {
            width: 100%;
            text-align: right;
        }

        table {
            border-collapse: collapse;
        }
    </style>
</head>
<body>
<div class="outBox">
    <div align="center" width="100%">
        <h1>退货明细清单</h1>
    </div>
    <br>
    <!--    放置条形码-->
    <#list taskList as task>
    <div class="spaceBetween" style="width: 100%">
        <div style="width: 33%">
            <div class="codeImg">
                <img src="data:image/png;base64,${task.taskId}" width="190px" height="50px"/>
                退货ID：${task.returnProductTaskIdStr!}
            </div>
        </div>
        <div style="width: 33%;">
            <h3>工厂名称：${task.supplierName!}</h3>
        </div>
        <div style="width: 33%;">
            <h3>退货性质：${task.returnNature!}</h3>
        </div>
    </div>
    <br>
    <!--    入库任务信息-->
    <div class="spaceBetween">
        <div style="width: 33%;">
            打印时间：${task.nowDate!}
        </div>
        <div style="width: 33%;">
            物流单号：${task.logisticsNo!}
        </div>
        <div style="width: 33%;">
            物流公司：${task.logisticsCompany!}
        </div>
        <div style="width: 33%;">
            退货时间：${task.createDate!}
        </div>
        <div style="width: 33%;">
            打印人：${task.printBy!}
        </div>
        <div style="width: 33%;">
            发货时间：${task.deliveryDateStr!}
        </div>
        <div style="width: 66%;">
            发货地址：${task.shippingAddress!}
        </div>
    </div>
    <br>
    <!--    任务明细列表-->
    <div class="spaceBetween">
        <table align="left" width="98%" style="width: 98%;table-layout: fixed; word-break: break-all;">
            <tr>
                <th align="left" width="25%">规格编码</th>
                <th align="left" width="10%">退货件数</th>
                <th align="left" width="10%">区域</th>
                <th align="left" width="10%">包装方式</th>
                <th align="left" width="10%">版本号</th>
                <th align="left" width="15%">采购单号</th>
                <th align="left" width="20%">退货原因</th>
            </tr>
            <#list task.itemList as item>
                <tr>
                    <td valign="middle" width="25%">${item.sku!}</td>
                    <td valign="middle" width="10%">${item.actualReturnQty!}</td>
                    <td valign="middle" width="10%">${item.sourceAreaName!}</td>
                    <td valign="middle" width="10%">${item.packingMethod!}</td>
                    <td valign="middle" width="10%">${item.versionNo!}</td>
                    <td valign="middle" width="15%">${item.purchasePlanNo!}</td>
                    <td valign="middle" width="20%">${item.unqualifiedReason!}</td>
                </tr>
            </#list>
            <tr>
                <td valign="middle" width="25%"></td>
                <td valign="middle" width="10%">共${task.totalReturnQty!}件</td>
                <td valign="middle" width="10%"></td>
                <td valign="middle" width="10%"></td>
                <td valign="middle" width="10%"></td>
                <td valign="middle" width="15%"></td>
                <td valign="middle" width="20%"></td>
            </tr>
        </table>

    </div>
    <br>
    </#list>
</div>
</body>
</html>
