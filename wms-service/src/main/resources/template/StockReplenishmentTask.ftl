<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>打印拣货任务</title>
    <style>
        @page{
            size:a4;
            margin:8mm 0 0 0;
        }
        body {
            font-size: 13px;
            width: 198mm;
            margin-top: 2mm;
            font-family: "宋体";
        }

        .outBox {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 98%;
            margin: 0 auto;
        }
        .spaceBetween {
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            width: 100%;
        }
        .spaceBetween div{
            white-space: nowrap;
            line-height: 20px;
        }
        table {
            border-collapse: collapse;
        }
    </style>
</head>
<body>
<div class="outBox">
    <div align="center" width="100%">
        <h1>补货任务</h1>
    </div>
    <br>
    <!--    入库任务明细列表-->
    <div class="spaceBetween">
        <table style="table-layout: fixed; word-break: break-all; width: 100%; text-align: center;">
            <tr>
                <th>任务ID</th>
                <th>规格编码</th>
                <th>需补货库位</th>
                <th>建议补货数</th>
                <th>补货库位</th>
                <th>状态</th>
            </tr>
            <#if taskList??>
                <#list taskList as item>
                    <tr>
                        <td valign="middle">${item.id}</td>
                        <td valign="middle">${item.sku}</td>
                        <td valign="middle">${item.toPositionCode}</td>
                        <td valign="middle">${item.qty}</td>
                        <td valign="middle">${item.fromPositionCode}</td>
                        <td valign="middle">${item.statusCn}</td>
                    </tr>
                </#list>
            </#if>
        </table>
    </div>
    <br>
</div>
</div>
</body>
</html>
