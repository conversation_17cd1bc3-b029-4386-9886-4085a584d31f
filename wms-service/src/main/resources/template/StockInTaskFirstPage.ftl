<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>打印出库任务</title>
    <style>
        @page {
            size: a4;
            margin: 8mm 0 0 0;
        }

        body {
            font-size: 13px;
            width: 198mm;
            margin-top: 2mm;
            font-family: "宋体";
        }

        .outBox {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .codeImg {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .spaceBetween {
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            width: 100%;
        }

        .spaceBetween div {
            line-height: 30px;
        }

        .flexEnd {
            width: 100%;
            text-align: right;
        }

        table {
            border-collapse: collapse;
        }

        table, table tr th, table tr td {
            border: 1px solid #000000;
        }
    </style>
</head>
<body>
<div class="outBox">
    <!--    放置条形码-->
    <div class="codeImg">
        <img src="data:image/png;base64,${barCodeImage}"/>
        出库箱码：${taskInfo.supplierDeliveryBoxCode!}
    </div>
    <br>
    <!--    入库任务信息-->
    <div class="spaceBetween">
        <div style="width: 33%;">
            入库ID：${taskInfo.taskId!}
        </div>
        <div style="width: 33%;">
            入库类型：${taskInfo.stockinTypeLabel!}
        </div>
        <div style="width: 33%;text-align:right;">
            供应商：${taskInfo.supplierName!}
        </div>
        <div style="width: 33%;">
            预收总数：${taskInfo.expectedQty!}
        </div>
        <div style="width: 33%;">
            工厂出库单号：${taskInfo.supplierDeliveryNo!}
        </div>
    </div>
    <br>
    <!--    入库任务明细列表-->
    <!--    入库任务明细列表-->
    <table align="center" width="100%" style="width: 100%;table-layout: fixed; word-break: break-all;">
        <tr>
            <th align="center" valign="middle">序号</th>
            <th align="center" width="200px" valign="middle">规格编号</th>
            <th align="center" width="80px" valign="middle">是否质检</th>
            <th align="center" valign="middle">上架库区</th>
            <th align="center" valign="middle">品牌名称</th>
            <th align="center" width="80px" valign="middle">预收货数</th>
            <th align="center" width="80px" valign="middle">需质检数</th>
            <th align="center" width="80px" valign="middle">实际收货数</th>
        </tr>
        <#list detailList as detail>
            <tr>
                <td align="center" valign="middle">${detail.index!}</td>
                <td align="center" width="200px" valign="middle">${detail.sku!}</td>
                <td align="center" width="80px" valign="middle">${detail.isNeedQaLabel!}</td>
                <td align="center" valign="middle">${detail.shelveSpaceAreaName!}</td>
                <td align="center" valign="middle">${detail.brandName!}</td>
                <td align="center" width="80px" valign="middle">${detail.expectedQty!}</td>
                <td align="center" width="80px" valign="middle">${detail.qaQty!}</td>
                <td align="center" width="80px" valign="middle">${detail.stockinQty!}</td>
            </tr>
        </#list>
    </table>
    <br>
    <div class="flexEnd">
        打印时间：${nowDate!}
    </div>
</div>
</body>
</html>
