<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>打印拣货任务</title>
    <style>
        @page{
            size:a4;
            margin:8mm 0 0 0;
        }
        body {
            font-size: 13px;
            width: 198mm;
            margin-top: 2mm;
            font-family: "宋体";
        }

        .outBox {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 98%;
            margin: 0 auto;
        }

        .codeImg {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .spaceBetween {
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            width: 100%;
        }

        .spaceBetween div {
            line-height: 20px;
        }

        .flexEnd {
            width: 100%;
            text-align: right;
        }

        table {
            border-collapse: collapse;
        }
    </style>
</head>
<body>
<div class="outBox">
    <div align="center" width="100%">
        <h1>${date!}${pickingType!}----<#if workspace??>${workspace!}拣货单</#if>${pack!}${processType!}</h1><br>
        <h2><#if titleInfo??>${titleInfo!}</#if></h2>
    </div>
    <#if isPdd?exists && isPdd>
        <div style="color: red; position: absolute; right: 5mm">
            <h1>抓</h1>
        </div>
    </#if>
    <br>
    <!--    放置条形码-->
    <div class="spaceBetween" style="width: 100%; justify-content:center;">
            <div class="codeImg" style="width: 30%">
                <img src="data:image/png;base64,${taskIdImg!}" width="190px" height="50px"/>
                ${taskId!}${taskProcessType!}
            </div>
        <div style="width: 5%">
        </div>
            <div class="codeImg" style="width: 30%">
                <img src="data:image/png;base64,${internalBoxCodeImg!}" width="190px" height="50px"/>
                拣货箱：${internalBoxCode!}
            </div>
    </div>
    <br>
    <!--    入库任务信息-->
    <div class="spaceBetween">
        <div style="width: 33%;">
            波次号：${batchId!}${processType!}
        </div>
        <div style="width: 33%;">
            扫描台：${scanTypeMsg!}
        </div>
        <div style="width: 33%;">
            拣货任务打印状态：${firstPrint!}
        </div>
        <div style="width: 33%;">
            打印时间：${nowDate!}
        </div>
        <div style="width: 33%;">
            订单编号：<#if orderNo??>${orderNo!}</#if>
        </div>
        <div style="width: 33%;">
            <#if storeName??>${storeName!}</#if>
        </div>

        <div style="width: 33%;">
            TO：<#if receiverName??>${receiverName!}</#if>
        </div>
        <div style="width: 33%;">
            Tel：<#if receiverMobile??>${receiverMobile!}</#if> <#if receiverPhone??>${receiverPhone!}</#if>
        </div>
        <div style="width: 33%;">
            ${receiverCountry!}
        </div>
        <div style="width: 99%;">
            Delivery Address：<#if receiverAddress??>${receiverAddress!}</#if>
        </div>
        <div style="width: 33%;">
            Zip：<#if receiverZip??>${receiverZip!}</#if>
        </div>
        <div style="width: 33%;">
            <#if logisticsCompany??>${logisticsCompany!}</#if>
        </div>
        <div style="width: 33%;">
            ${qty!} PCS
        </div>
        <div style="width: 33%;">
            付款重量：${payWeight!}
        </div>
        <div style="width: 66%;">
            商品重量：${weight!} (KG)
        </div>
        <div style="width: 33%;font-weight: bold;font-size: 14px">
            急单状态：<#if urgent>是<#else>否</#if>
        </div>
        <#if platformName?has_content>
        <div style="width: 33%;">
            平台名称：${platformName!}
        </div>
        </#if>
        <div style="width: 99%;">
            备注：<#if remark??>${remark!}</#if>
        </div>
    </div>
    <br>
    <!--    入库任务明细列表-->
    <div class="spaceBetween">
        <table align="left" width="47%" style="width: 47%;table-layout: fixed; word-break: break-all;">
            <tr>
                <th align="left" width="28%">库位</th>
                <th align="left" width="62%">规格编码</th>
                <th align="left" width="10%">数量</th>
            </tr>
            <#list itemList1 as item>
                <tr>
                    <td valign="middle" width="28%">${item.positionCode!}</td>
                    <td valign="middle" width="62%">${item.sku!}${item.productVersion!}${item.transparency!}${item.productTag!}${item.storeOrderFirst!}</td>
                    <td valign="middle" width="10%">${item.qty}</td>
                </tr>
            </#list>
        </table>
        <div style="width: 2%"></div>
        <table align="right" width="47%" style="width: 47%;table-layout: fixed; word-break: break-all;">
            <tr>
                <th align="left" width="28%">库位</th>
                <th align="left" width="62%">规格编码</th>
                <th align="left" width="10%">数量</th>
            </tr>
            <#list itemList2 as item>
                <tr>
                    <td valign="middle" width="28%">${item.positionCode!}</td>
                    <td valign="middle" width="62%">${item.sku!}${item.productVersion!}${item.transparency!}${item.productTag!}${item.storeOrderFirst!}${item.vasType!}${item.packageVacuum!}</td>
                    <td valign="middle" width="10">${item.qty}</td>
                </tr>
            </#list>
        </table>
    </div>
    <br>
    <!--    放置条形码-->
    <div class="spaceBetween" style="justify-content:center;">
        <div class="codeImg" style="width: 50%;">
            <img src="data:image/png;base64,${stockoutOrderNoImg!}" height="50px"/>
            出库单号：${stockoutOrderNo!}
        </div>
        <#list orderNoImgList as item>
        <div class="codeImg" style="width: 50%;">
            <img src="data:image/png;base64,${item.orderNoImg!}" height="50px"/>
            订单号：${item.orderNo!}
        </div>
        </#list>
    </div>
    <br>
    <br>
    <div class="spaceBetween">
        <div style="width: 33%;">
            打印时间：${nowDate!}
        </div>
        <div style="width: 66%;">
            订单编号：<#if orderNo??>${orderNo!}</#if>
        </div>

        <div style="width: 33%;">
            TO：<#if receiverName??>${receiverName!}</#if>
        </div>
        <div style="width: 66%;">
            Tel：<#if receiverMobile??>${receiverMobile!}</#if> <#if receiverPhone??>${receiverPhone!}</#if>
        </div>
        <div style="width: 99%;">
            Delivery Address：<#if receiverAddress??>${receiverAddress!}</#if>
        </div>
        <div style="width: 33%;">
            Zip：<#if receiverZip??>${receiverZip!}</#if>
        </div>
        <div style="width: 33%;">
            <#if logisticsCompany??>${logisticsCompany!}</#if>
        </div>
        <div style="width: 33%;">
            ${qty!} PCS
        </div>
        <div style="width: 33%;">
            付款重量：${payWeight!}
        </div>
        <div style="width: 66%;">
            商品重量：${weight!} （KG）
        </div>
        <div style="width: 99%;">
            备注：<#if remark??>${remark!}</#if>
        </div>
    </div>
</div>
</body>
</html>
