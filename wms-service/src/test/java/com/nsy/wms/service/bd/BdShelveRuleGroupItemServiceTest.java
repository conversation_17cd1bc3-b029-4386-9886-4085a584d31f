package com.nsy.wms.service.bd;

import com.nsy.wms.SpringServiceTest;
import com.nsy.api.wms.domain.bd.BdShelveRuleGroupItem;
import com.nsy.wms.repository.entity.bd.BdShelveRuleGroupEntity;
import com.nsy.wms.repository.entity.bd.BdShelveRuleGroupItemEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdShelveRuleGroupItemMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdShelveRuleGroupMapper;
import com.nsy.api.wms.request.bd.BdShelveRuleGroupItemRequest;
import com.nsy.wms.business.service.bd.BdShelveRuleGroupItemService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @since 1.0
 * 上架规则组明细service层单元测试
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BdShelveRuleGroupItemServiceTest extends SpringServiceTest {
    @Autowired
    private BdShelveRuleGroupItemService bdShelveRuleGroupItemService;

    @Autowired
    private BdShelveRuleGroupItemMapper bdShelveRuleGroupItemMapper;
    @Autowired
    private BdShelveRuleGroupMapper bdShelveRuleGroupMapper;
    BdShelveRuleGroupEntity bdShelveRuleGroupEntity;
    BdShelveRuleGroupItemEntity bdShelveRuleGroupItemEntity;

    @Test
    public void testNotNull() {
        Assertions.assertThat(bdShelveRuleGroupItemService).isNotNull();
    }

    @BeforeAll
    public void init() {
        bdShelveRuleGroupEntity = new BdShelveRuleGroupEntity();
        bdShelveRuleGroupEntity.setIsDeleted(0);
        bdShelveRuleGroupMapper.insert(bdShelveRuleGroupEntity);
        bdShelveRuleGroupItemEntity = new BdShelveRuleGroupItemEntity();
        bdShelveRuleGroupItemEntity.setIsDeleted(0);
        bdShelveRuleGroupItemMapper.insert(bdShelveRuleGroupItemEntity);
    }

    @AfterAll
    public void deleteTestData() {
        bdShelveRuleGroupMapper.deleteById(bdShelveRuleGroupEntity.getShelveRuleGroupId());
        bdShelveRuleGroupItemMapper.deleteById(bdShelveRuleGroupItemEntity.getShelveRuleGroupItemId());
    }

    @Test
    public void updateStatusTest() {
        BdShelveRuleGroupItemEntity entity = bdShelveRuleGroupItemMapper.selectById(bdShelveRuleGroupItemEntity.getShelveRuleGroupItemId());
        bdShelveRuleGroupItemService.changeBdShelveRuleGroupItemStatus(entity.getShelveRuleGroupItemId(), 1);
    }

    @Test
    public void updateTest() {
        BdShelveRuleGroupItemEntity entity = bdShelveRuleGroupItemMapper.selectById(bdShelveRuleGroupItemEntity.getShelveRuleGroupItemId());
        Integer itemId = entity.getShelveRuleGroupItemId();
        BdShelveRuleGroupItemRequest request = new BdShelveRuleGroupItemRequest();
        request.setShelveRuleGroupItemId(itemId);
        request.setIsDeleted(0);
        request.setShelveRuleId(1);

        BdShelveRuleGroupEntity entity2 = bdShelveRuleGroupMapper.selectById(bdShelveRuleGroupEntity.getShelveRuleGroupId());
        Integer id = entity2.getShelveRuleGroupId();
        request.setShelveRuleGroupId(id);
        bdShelveRuleGroupItemService.updateBdShelveRuleGroupItem(request);
        BdShelveRuleGroupItemEntity entity3 = bdShelveRuleGroupItemMapper.selectById(bdShelveRuleGroupItemEntity.getShelveRuleGroupItemId());
        Assertions.assertThat(entity3).isNotNull();
        Assertions.assertThat(entity3.getShelveRuleId()).isEqualTo(1);
    }

    @Test
    public void listByPageTest() {
        List<BdShelveRuleGroupItem> list = bdShelveRuleGroupItemService.bdShelveRuleGroupItemList();
        Assertions.assertThat(list).isNotNull();
    }
}
