package com.nsy.wms.service.external;

import com.nsy.wms.SpringServiceTest;
import com.nsy.wms.repository.entity.external.ExternalApiLogEntity;
import com.nsy.wms.repository.entity.external.ExternalApiLogItemEntity;
import com.nsy.api.wms.enumeration.external.ExternalApiLogApiModuleEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogApiTypeEnum;
import com.nsy.api.wms.enumeration.external.ExternalApiLogStatusEnum;
import com.nsy.api.wms.request.external.ExternalApiLogAddRequest;
import com.nsy.wms.business.service.external.ExternalApiLogItemService;
import com.nsy.wms.business.service.external.ExternalApiLogService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class ExternalApiLogServiceTest extends SpringServiceTest {
    @Autowired
    ExternalApiLogService externalApiLogService;

    @Autowired
    ExternalApiLogItemService externalApiLogItemService;


    @Test
    public void addExternalApiLog() {
        ExternalApiLogAddRequest addRequest = new ExternalApiLogAddRequest();
        addRequest.setApiName("推送质检任务");
        addRequest.setApiModule(ExternalApiLogApiModuleEnum.API_PMS);
        addRequest.setApiType(ExternalApiLogApiTypeEnum.QC_TASK_PUSH);
        addRequest.setApiUrl("http://localhost:8080");
        addRequest.setRequestMethod("POST");
        addRequest.setRequestContent("{\"lendId\": 1}");
        addRequest.setResponseContent("{\"code\": 200}");
        addRequest.setDescription("z001推送质检任务");
        addRequest.setStatus(ExternalApiLogStatusEnum.SUCCESS);
        addRequest.setDocumentNo("zj0001");
        externalApiLogService.addExternalApiLog(addRequest);
        List<ExternalApiLogEntity> apiLogEntityList = externalApiLogService.list();
        List<ExternalApiLogItemEntity> apiLogItemEntityList = externalApiLogItemService.list();

        Assertions.assertEquals(1, apiLogEntityList.size());
        Assertions.assertEquals(1, apiLogItemEntityList.size());
    }

}
