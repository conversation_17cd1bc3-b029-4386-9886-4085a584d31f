package com.nsy.wms.service.bd;

import com.nsy.wms.SpringServiceTest;
import com.nsy.api.wms.domain.bd.BdStockinRule;
import com.nsy.wms.repository.entity.bd.BdStockinRuleEntity;
import com.nsy.api.wms.enumeration.stockin.StockinMethodEnum;
import com.nsy.wms.repository.jpa.mapper.bd.BdStockinRuleMapper;
import com.nsy.api.wms.request.bd.BdStockinRuleAddRequest;
import com.nsy.api.wms.request.bd.BdStockinRuleListRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.bd.BdStockinRuleResponse;
import com.nsy.wms.business.service.bd.BdStockinRuleService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class BdStockinRuleServiceTest extends SpringServiceTest {
    @Autowired
    BdStockinRuleService bdStockinRuleService;
    @Autowired
    BdStockinRuleMapper bdStockinRuleMapper;
    @BeforeAll
    public void init() {
        BdStockinRuleEntity entity = new BdStockinRuleEntity();
        entity.setLocation("QUANZHOU");
        entity.setStockinRuleName("规则名称");
        entity.setStockinRuleCode("QuanZhoucode0001");
        entity.setStockinType("入库单类型1");
        entity.setQcReceive("入库质检优先级1");
        entity.setStockinMethod(StockinMethodEnum.STOCKIN_SHELVE_SPLIT.name());
        entity.setQcShelve("质检后上架方式1");
        entity.setWeightProcess("称重顺序1");
        entity.setIsCrossDock(1);
        entity.setIsDeleted(0);

        bdStockinRuleService.save(entity);

    }

    @AfterAll
    public void deleteAll() {
        bdStockinRuleMapper.delete(null);
    }

    @Test
    @Order(1)
    public void getListByRequest() {
        BdStockinRuleListRequest request = new BdStockinRuleListRequest();
        PageResponse<BdStockinRule> platformList = bdStockinRuleService.getStockinRuleList(request);
        Assertions.assertNotNull(platformList);
    }

    @Test
    @Order(2)
    public void saveBdStockRule() {
        BdStockinRuleAddRequest request = new BdStockinRuleAddRequest();
        request.setStockinRuleName("规则名称2");
        request.setStockinRuleCode("QuanZhoucode0005");
        request.setStockinType("入库单类型2");
        request.setQcReceive("入库质检优先级1");
        request.setStockinMethod("入库方式1");
        request.setQcShelve("质检后上架方式1");
        request.setWeightProcess("称重顺序1");
        request.setIsCrossDock(0);
        request.setIsDeleted(0);
        bdStockinRuleService.addBdStockRule(request);
    }
    @Test
    @Order(3)
    public void updateBdStockRule() {
        List<BdStockinRuleEntity> ruleEntityList = bdStockinRuleMapper.selectList(null);
        BdStockinRuleAddRequest request = new BdStockinRuleAddRequest();
        Integer ruleId = ruleEntityList.get(0).getStockinRuleId();
        bdStockinRuleService.updateStockinRuleStatus(ruleId, 1);
        request.setStockinRuleName("规则名称修改");
        request.setStockinRuleCode("QuanZhoucode0905");
        request.setStockinType("入库单类型3");
        request.setQcReceive("入库质检优先级1");
        request.setStockinMethod("入库方式1");
        request.setQcShelve("质检后上架方式1");
        request.setWeightProcess("称重顺序1");
        request.setIsCrossDock(0);
        bdStockinRuleService.updateBdStockRule(ruleId, request);
        BdStockinRuleResponse response = bdStockinRuleService.getRuleInfo(ruleId);
        Assertions.assertNotNull(response);
        Assertions.assertEquals(response.getStockinRuleName(), "规则名称修改");
    }


}
