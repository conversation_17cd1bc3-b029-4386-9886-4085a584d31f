package com.nsy.wms.service.stockout;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.executor.ShardingContexts;
import com.nsy.wms.SpringServiceTest;
import com.nsy.api.wms.domain.bd.BdBatchAutoGenerateRule;
import com.nsy.wms.elasticjob.stockout.BatchAutoGenerateRuleJob;
import com.nsy.wms.repository.entity.stockout.StockoutOrderEntity;
import com.nsy.wms.repository.entity.stockout.StockoutOrderItemEntity;
import com.nsy.wms.business.service.bd.BdBatchGenerateRuleService;
import com.nsy.wms.business.service.common.LoginInfoService;
import com.nsy.wms.business.service.stockout.StockoutBatchLogService;
import com.nsy.wms.business.service.stockout.StockoutBatchOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutBatchOrderService;
import com.nsy.wms.business.service.stockout.StockoutBatchService;
import com.nsy.wms.business.service.stockout.StockoutOrderItemService;
import com.nsy.wms.business.service.stockout.StockoutOrderService;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public class BatchAutoGenerateRuleJobTest extends SpringServiceTest {

    @Autowired
    private BatchAutoGenerateRuleJob batchAutoGenerateRuleJob;
    @Autowired
    private StockoutOrderService stockoutOrderService;
    @Autowired
    private StockoutOrderItemService stockoutOrderItemService;
    @Autowired
    StockoutBatchService stockoutBatchService;
    @Autowired
    StockoutBatchOrderService stockoutBatchOrderService;
    @Autowired
    StockoutBatchOrderItemService stockoutBatchOrderItemService;
    @MockBean
    BdBatchGenerateRuleService bdBatchGenerateRuleService;
    @Autowired
    StockoutBatchLogService stockoutBatchLogService;
    @Autowired
    LoginInfoService loginInfoService;

    @Test
    public void test() {

        StockoutOrderEntity stockoutOrderEntity = getStockoutOrderEntity();
        BdBatchAutoGenerateRule rule = new BdBatchAutoGenerateRule();
        rule.setWorkspace(stockoutOrderEntity.getWorkspace());
        rule.setPickingType(stockoutOrderEntity.getPickingType());
        rule.setSpaceId(stockoutOrderEntity.getSpaceId());
        rule.setStockoutType(stockoutOrderEntity.getStockoutType());
        List<BdBatchAutoGenerateRule> ruleList = new ArrayList<>();
        ruleList.add(rule);
        Mockito.doReturn(ruleList)
                .when(bdBatchGenerateRuleService).getAutoGenerateRule();

        getStockoutOrderItemEntity(stockoutOrderEntity);
        ShardingContexts s = new ShardingContexts("test", "test", 1, "test", new HashMap<Integer, String>());
        ShardingContext shardingContext = new ShardingContext(s, 1);
        batchAutoGenerateRuleJob.execute(shardingContext);
//        List<StockoutBatchEntity> all = stockoutBatchService.list();
//        Assertions.assertEquals(1, all.size());
//        Optional<StockoutOrderEntity> byId = stockoutOrderRepository.findById(stockoutOrderEntity.getStockoutOrderId());
//        Assertions.assertEquals(StockoutOrderStatusEnum.READY_PICK.name(), byId.get().getStatus());
//
//        List<StockoutBatchLogEntity> allByBatchId = stockoutBatchLogService.list(new QueryWrapper<StockoutBatchLogEntity>().lambda().eq(StockoutBatchLogEntity::getBatchId, all.get(0).getBatchId()));
//        Assertions.assertEquals(StockoutBatchLogTypeEnum.NEW.getStockoutBatchLogType(), allByBatchId.get(0).getBatchLogType());
    }


    private StockoutOrderEntity getStockoutOrderEntity() {
        StockoutOrderEntity stockoutOrderEntity = new StockoutOrderEntity();
        stockoutOrderEntity.setLocation("QuanZhou");
        stockoutOrderEntity.setSpaceId(1);
        stockoutOrderEntity.setStockoutOrderNo("CK20210709001");
        stockoutOrderEntity.setLogisticsCompany("顺丰");
        stockoutOrderEntity.setLogisticsNo("SF000001");
        stockoutOrderEntity.setStatus("READY_WAVE_GENERATED");
        stockoutOrderEntity.setLatestDeliveryDate(new Date());
        stockoutOrderEntity.setStockoutType("SALES_DELIVERY");
        stockoutOrderEntity.setNotifyShipStatus("WAIT_NOTIC_DELIVERY");
        stockoutOrderEntity.setPickingType("WHOLE_PICK");
        stockoutOrderEntity.setWorkspace("FBA_AREA");
        stockoutOrderEntity.setMergeState("CHILEDRE");
        stockoutOrderEntity.setBusinessType("B2B");
        stockoutOrderEntity.setStoreName("亚马逊-1");
        stockoutOrderEntity.setSaler("测试股");
        stockoutOrderEntity.setLack(Boolean.FALSE);
        stockoutOrderService.save(stockoutOrderEntity);
        return stockoutOrderEntity;
    }

    private StockoutOrderItemEntity getStockoutOrderItemEntity(StockoutOrderEntity orderEntity) {
        StockoutOrderItemEntity stockoutOrderItemEntity = new StockoutOrderItemEntity();
        stockoutOrderItemEntity.setBarcode("712594051343");
        stockoutOrderItemEntity.setLack(orderEntity.getLack());
        stockoutOrderItemEntity.setLocation(orderEntity.getLocation());
        stockoutOrderItemEntity.setOrderItemId("123");
        stockoutOrderItemEntity.setOrderNo("111");
        stockoutOrderItemEntity.setProductId(63);
        stockoutOrderItemEntity.setQty(50);
        stockoutOrderItemEntity.setScanQty(50);
        stockoutOrderItemEntity.setShipmentQty(50);
        stockoutOrderItemEntity.setSpecId(1);
        stockoutOrderItemEntity.setStockoutOrderId(orderEntity.getStockoutOrderId());
        stockoutOrderItemService.save(stockoutOrderItemEntity);
        return stockoutOrderItemEntity;
    }

}
