package com.nsy.wms.service.stockin;

import com.nsy.api.wms.enumeration.bd.BdSupplierPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnProductOrderTypeEnum;
import com.nsy.api.wms.enumeration.stockin.ReturnProductStatusEnum;
import com.nsy.api.wms.response.stockin.StockinReturnProductPDATaskListRequest;
import com.nsy.api.wms.response.stockin.StockinReturnProductPDATaskListResponse;
import com.nsy.api.wms.response.stockin.StockinReturnProductPDAUpdateFinishRequest;
import com.nsy.api.wms.response.stockin.StockinReturnProductPDAUpdateReturningRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.wms.SpringServiceTest;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceAreaEntity;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.bd.BdSupplierPositionMappingEntity;
import com.nsy.wms.repository.entity.product.ProductInfoEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinReturnProductTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.bd.BdPositionMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdSpaceAreaMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdSpaceMapper;
import com.nsy.wms.repository.jpa.mapper.bd.BdSupplierPositionMappingMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductMapper;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinReturnProductTaskItemMapper;
import com.nsy.wms.mq.producer.MessageProducer;
import com.nsy.wms.business.service.product.ProductInfoService;
import com.nsy.wms.business.service.product.ProductSpecInfoService;
import com.nsy.wms.business.service.stock.StockService;
import com.nsy.wms.business.service.stockin.StockinReturnProductLogService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskPdaService;
import com.nsy.wms.business.service.stockin.StockinReturnProductTaskService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.util.ObjectUtils;

import java.util.Collections;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class StockinReturnProductTaskPDATest extends SpringServiceTest {

    @Autowired
    private StockinReturnProductTaskService taskService;
    @Autowired
    private StockinReturnProductTaskItemMapper taskItemMapper;
    @Autowired
    private StockinReturnProductLogService logService;
    @Autowired
    private ProductSpecInfoService productSpecInfoService;
    @Autowired
    BdSupplierPositionMappingMapper mappingMapper;
    @Autowired
    private BdPositionMapper positionMapper;
    @Autowired
    private BdSpaceAreaMapper spaceAreaMapper;
    @Autowired
    private BdSpaceMapper spaceMapper;
    @Autowired
    private ProductInfoService productInfoService;
    @Autowired
    private StockinReturnProductMapper returnProductMapper;
    @Autowired
    private StockService stockService;
    @MockBean
    MessageProducer messageProducer;
    @Autowired
    StockinReturnProductTaskPdaService taskPdaService;

    BdSupplierPositionMappingEntity mappingEntity;
    BdPositionEntity positionEntity;
    StockinReturnProductTaskEntity taskEntity;
    StockinReturnProductTaskItemEntity itemEntity;

    @BeforeAll
    public void init() {
        Mockito.doNothing().when(messageProducer).sendMessage(Mockito.anyString(), Mockito.any());
        ProductInfoEntity productInfoEntity = buildProductInfoEntity();
        ProductSpecInfoEntity specInfoEntity = buildSpecInfoEntity();
        BdSpaceEntity bdSpaceEntity = buildSpaceEntity();
        BdSpaceAreaEntity spaceAreaEntity = buildSpaceAreaEntity(bdSpaceEntity.getSpaceId());
        positionEntity = buildPositionEntity(spaceAreaEntity.getSpaceAreaId());
        mappingEntity = buildMappingEntity(positionEntity.getPositionId(), positionEntity.getPositionCode());
        StockinReturnProductEntity returnProductEntity = new StockinReturnProductEntity();
        returnProductEntity.setLocation(LocationEnum.QUANZHOU.name());
        returnProductEntity.setProductId(productInfoEntity.getProductId());
        returnProductEntity.setSpecId(specInfoEntity.getSpecId());
        returnProductEntity.setSku(specInfoEntity.getSku());
        returnProductEntity.setSpaceId(bdSpaceEntity.getSpaceId());
        returnProductEntity.setSpaceName(bdSpaceEntity.getSpaceName());
        returnProductEntity.setSupplierId(1);
        returnProductEntity.setSupplierName("供应商测试");
        returnProductEntity.setSupplierDeliveryNo("TH20210624001");
        returnProductEntity.setReturnQty(100);
        returnProductEntity.setStatus("PENDING");
        returnProductEntity.setDescription("*****");
        returnProductEntity.setInternalBoxCode("NBX0000001");
        returnProductMapper.insert(returnProductEntity);
        taskEntity = buildTask(bdSpaceEntity, spaceAreaEntity, mappingEntity, positionEntity);
        itemEntity = buildTaskItem(taskEntity, specInfoEntity);
        buildStock(positionEntity, specInfoEntity);
    }

    private StockinReturnProductTaskEntity buildTask(BdSpaceEntity bdSpaceEntity, BdSpaceAreaEntity spaceAreaEntity,
                                                     BdSupplierPositionMappingEntity mappingEntity,
                                                     BdPositionEntity positionEntity) {
        StockinReturnProductTaskEntity taskEntity = new StockinReturnProductTaskEntity();
        taskEntity.setStatus(ReturnProductStatusEnum.ALREADY_GENERATE.getCode());
        taskEntity.setLocation(LocationEnum.QUANZHOU.name());
        taskEntity.setPositionId(positionEntity.getPositionId());
        taskEntity.setPositionCode(positionEntity.getPositionCode());
        taskEntity.setReturnMethod(ReturnProductOrderTypeEnum.ORIGIN_BACK.getCode());
        taskEntity.setSpaceId(bdSpaceEntity.getSpaceId());
        taskEntity.setSpaceName(bdSpaceEntity.getSpaceName());
        taskEntity.setSpaceAreaId(spaceAreaEntity.getSpaceAreaId());
        taskEntity.setSpaceAreaName(spaceAreaEntity.getSpaceAreaName());
        taskEntity.setSupplierId(mappingEntity.getSupplierId());
        taskEntity.setSupplierName(mappingEntity.getSupplierName());
        taskService.save(taskEntity);
        return taskEntity;
    }

    private StockinReturnProductTaskItemEntity buildTaskItem(StockinReturnProductTaskEntity taskEntity, ProductSpecInfoEntity specInfoEntity) {
        StockinReturnProductTaskItemEntity taskItemEntity = new StockinReturnProductTaskItemEntity();
        taskItemEntity.setStatus(ReturnProductStatusEnum.ALREADY_GENERATE.getCode());
        taskItemEntity.setLocation(LocationEnum.QUANZHOU.name());
        taskItemEntity.setPositionId(taskEntity.getPositionId());
        taskItemEntity.setPositionCode(taskEntity.getPositionCode());
        taskItemEntity.setReturnProductTaskId(taskEntity.getReturnProductTaskId());
        taskItemEntity.setProductId(specInfoEntity.getProductId());
        taskItemEntity.setSpecId(specInfoEntity.getSpecId());
        taskItemEntity.setSku(specInfoEntity.getSku());
        taskItemEntity.setWaitReturnQty(10);
        taskItemEntity.setActualReturnQty(0);
        taskItemEntity.setLackProductQty(0);
        taskItemMapper.insert(taskItemEntity);
        return taskItemEntity;
    }

    private void buildStock(BdPositionEntity positionEntity, ProductSpecInfoEntity specInfoEntity) {
        StockEntity stockEntity = new StockEntity();
        stockEntity.setLocation(LocationEnum.QUANZHOU.name());
        stockEntity.setSpaceId(positionEntity.getSpaceId());
        stockEntity.setSpaceName(positionEntity.getSpaceName());
        stockEntity.setAreaId(positionEntity.getAreaId());
        stockEntity.setAreaName(positionEntity.getAreaName());
        stockEntity.setSpaceAreaId(positionEntity.getSpaceAreaId());
        stockEntity.setSpaceAreaName(positionEntity.getSpaceAreaName());
        stockEntity.setProductId(specInfoEntity.getProductId());
        stockEntity.setSpecId(specInfoEntity.getSpecId());
        stockEntity.setSku(specInfoEntity.getSku());
        stockEntity.setStock(100);
        stockEntity.setPositionCode(positionEntity.getPositionCode());
        stockEntity.setPositionId(positionEntity.getPositionId());
        stockService.save(stockEntity);
    }

    /**
     * 测试生成退货任务数据异常
     */
    @Test
    @Order(2)
    public void pdaTaskItemListTest() {
        StockinReturnProductPDATaskListRequest request = new StockinReturnProductPDATaskListRequest();
        request.setPositionCode(positionEntity.getPositionCode());
        StockinReturnProductPDATaskListResponse stockinReturnProductPDATaskListResponse = taskPdaService.pdaTaskItemList(request);
        Assertions.assertTrue(!ObjectUtils.isEmpty(stockinReturnProductPDATaskListResponse));
    }

    /**
     * 通过退货详情任务ID，更新sku状态为退货中
     */
    @Test
    @Order(3)
    public void pdaUpdateSkuStatusReturningTest() {
        StockinReturnProductPDAUpdateReturningRequest request = new StockinReturnProductPDAUpdateReturningRequest();
        request.setTaskItemIdList(Collections.singletonList(itemEntity.getId()));
        request.setSku(itemEntity.getSku());
        taskService.pdaUpdateSkuStatusReturning(request);
    }

    /**
     * 通过退货详情任务ID，更新sku状态为退货完成
     */
    @Test
    @Order(4)
    public void pdaUpdateSkuStatusFinishTest() {
        StockinReturnProductPDAUpdateFinishRequest request = new StockinReturnProductPDAUpdateFinishRequest();
        request.setTaskItemIdList(Collections.singletonList(itemEntity.getId()));
        request.setActualReturnQty(itemEntity.getWaitReturnQty());
        taskPdaService.pdaUpdateSkuStatusFinish(request);
    }

    private ProductSpecInfoEntity buildSpecInfoEntity() {
        ProductSpecInfoEntity specInfoEntity = new ProductSpecInfoEntity();
        specInfoEntity.setProductId(1);
        specInfoEntity.setSpecId(1);
        specInfoEntity.setSku("LC-11-202020");
        specInfoEntity.setImageUrl("https://nsy-products.oss-cn-hangzhou.aliyuncs.com/development/zlg/btz/1623919874472.png");
        specInfoEntity.setThumbnailImageUrl("https://nsy-products.oss-cn-hangzhou.aliyuncs.com/development/zlg/btz/1623919874472.png");
        specInfoEntity.setPreviewImageUrl("https://nsy-products.oss-cn-hangzhou.aliyuncs.com/development/zlg/btz/1623919874472.png");
        productSpecInfoService.save(specInfoEntity);
        return specInfoEntity;
    }

    @AfterAll
    public void deleteTestData() {
        taskService.remove(new QueryWrapper<>());
        logService.remove(new QueryWrapper<>());
        mappingMapper.delete(new QueryWrapper<>());
        productInfoService.remove(new QueryWrapper<>());
        productSpecInfoService.remove(new QueryWrapper<>());
        spaceAreaMapper.delete(new QueryWrapper<>());
        positionMapper.delete(new QueryWrapper<>());
        returnProductMapper.delete(new QueryWrapper<>());
        spaceMapper.delete(new QueryWrapper<>());
        taskItemMapper.delete(new QueryWrapper<>());
        stockService.remove(new QueryWrapper<>());
    }

    private BdSupplierPositionMappingEntity buildMappingEntity(Integer positionId, String positionCode) {
        BdSupplierPositionMappingEntity mappingEntity = new BdSupplierPositionMappingEntity();
        mappingEntity.setLocation(LocationEnum.QUANZHOU.name());
        mappingEntity.setSupplierId(1);
        mappingEntity.setSupplierName("供应商测试");
        mappingEntity.setSpaceId(1);
        mappingEntity.setPositionId(positionId);
        mappingEntity.setPositionCode(positionCode);
        mappingEntity.setPositionType(BdSupplierPositionTypeEnum.RETURN.getType());
        mappingEntity.setIsDeleted(Boolean.FALSE);
        mappingMapper.insert(mappingEntity);
        return mappingEntity;
    }

    private BdPositionEntity buildPositionEntity(Integer spaceAreaId) {
        BdPositionEntity positionEntity = new BdPositionEntity();
        positionEntity.setLocation(LocationEnum.QUANZHOU.name());
        positionEntity.setPositionCode("A-1-1-1");
        positionEntity.setPositionName("A-1-1-1");
        positionEntity.setSpaceId(1);
        positionEntity.setSpaceName("时颖主仓库");
        positionEntity.setAreaId(1);
        positionEntity.setIsDeleted(0);
        positionEntity.setAreaName("时颖");
        positionEntity.setSpaceAreaId(spaceAreaId);
        positionEntity.setSpaceAreaName("退货区");
        positionEntity.setPositionType("退货库位");
        positionMapper.insert(positionEntity);
        return positionEntity;
    }

    private BdSpaceEntity buildSpaceEntity() {
        BdSpaceEntity space = new BdSpaceEntity();
        space.setLocation(LocationEnum.QUANZHOU.name());
        space.setSpaceName("时颖主仓库");
        space.setIsDeleted(0);
        spaceMapper.insert(space);
        return space;
    }


    private BdSpaceAreaEntity buildSpaceAreaEntity(Integer spaceId) {
        BdSpaceAreaEntity spaceAreaEntity = new BdSpaceAreaEntity();
        spaceAreaEntity.setLocation(LocationEnum.QUANZHOU.name());
        spaceAreaEntity.setSpaceAreaName("R区");
        spaceAreaEntity.setSpaceId(spaceId);
        spaceAreaEntity.setAreaId(1);
        spaceAreaEntity.setSpaceAreaCode("R");
        spaceAreaEntity.setSpaceAreaType("退货区");
        spaceAreaEntity.setIsDeleted(Boolean.FALSE);
        spaceAreaMapper.insert(spaceAreaEntity);
        return spaceAreaEntity;
    }

    private ProductInfoEntity buildProductInfoEntity() {
        ProductInfoEntity productInfoEntity = new ProductInfoEntity();
        productInfoEntity.setProductId(1);
        productInfoEntity.setSpu("LC");
        productInfoEntity.setWmsCategoryId(1);
        productInfoEntity.setProductName("商品A");
        productInfoService.save(productInfoEntity);
        return productInfoEntity;
    }
}
