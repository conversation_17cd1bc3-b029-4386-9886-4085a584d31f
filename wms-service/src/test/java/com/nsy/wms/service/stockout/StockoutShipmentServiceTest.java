package com.nsy.wms.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.core.apicore.util.BeanUtilsEx;
import com.nsy.wms.SpringServiceTest;
import com.nsy.api.wms.domain.stockout.ShipmentTransferSku;
import com.nsy.api.wms.domain.stockout.StockoutShipmentSearchResult;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentEntity;
import com.nsy.wms.repository.entity.stockout.StockoutShipmentItemEntity;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.wms.repository.jpa.mapper.stockout.StockoutShipmentMapper;
import com.nsy.api.wms.request.stockout.StockoutShipmentSearchRequest;
import com.nsy.api.wms.request.stockout.StockoutShipmentUpdateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentItemSkuInfoRequest;
import com.nsy.api.wms.response.stockout.StockoutShipmentItemSkuInfoResponse;
import com.nsy.api.wms.response.stockout.StockoutShipmentResponse;
import com.nsy.wms.business.service.stockout.StockoutShipmentItemService;
import com.nsy.wms.business.service.stockout.StockoutShipmentService;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 装箱清单-测试
 *
 * <AUTHOR>
 * @since 1.0
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class StockoutShipmentServiceTest extends SpringServiceTest {
    @Autowired
    private StockoutShipmentService stockoutShipmentService;
    @Autowired
    private StockoutShipmentMapper stockoutShipmentMapper;
    @Autowired
    private StockoutShipmentItemService stockoutShipmentItemService;
    @MockBean
    EnumConversionChineseUtils enumConversionChineseUtils;
    private Integer initId = 1;

    @BeforeAll
    public void init() {
        Mockito.doReturn("").when(enumConversionChineseUtils).baseConversion(Mockito.anyString(), Mockito.anyString());
        StockoutShipmentEntity entity = generateEntity();
        stockoutShipmentService.save(entity);
        initId = entity.getShipmentId();
        StockoutShipmentItemEntity itemEntity = generateItemEntity();
        stockoutShipmentItemService.saveOrUpdate(itemEntity);
    }

    @AfterAll
    public void deleteTestData() {
        stockoutShipmentMapper.delete(new QueryWrapper<>());

    }

    // 装箱清单实体
    private StockoutShipmentEntity generateEntity() {
        StockoutShipmentEntity entity = new StockoutShipmentEntity();
        entity.setLocation(LocationEnum.QUANZHOU.name());
        entity.setShipmentBoxCode("shipment001");
        entity.setBoxIndex(initId);
        entity.setBoxSize("String");
        entity.setLogisticsCompany("String");
        entity.setLogisticsNo("String");
        entity.setLogisticsLabelSize("String");
        entity.setForwarderChannel("String");
        entity.setTransferLogisticsCompany("String");
        entity.setTransferLogisticsNo("String");
        entity.setStatus("String");
        entity.setDeliveryDate(new Date());
        entity.setCustomsDeclareType("String");
        entity.setWeight(BigDecimal.TEN);
        entity.setVolumeWeight(BigDecimal.TEN);
        entity.setCreateBy("String");
        entity.setUpdateBy("String");
        return entity;
    }

    // 装箱清单明细实体
    private StockoutShipmentItemEntity generateItemEntity() {
        StockoutShipmentItemEntity entity = new StockoutShipmentItemEntity();
        entity.setLocation(LocationEnum.QUANZHOU.name());
        entity.setShipmentId(initId);
        entity.setStockoutOrderNo("String");
        entity.setStockoutOrderItemId(1);
        entity.setOrderNo("String");
        entity.setOrderItemId("1");
        entity.setSpecId(1);
        entity.setSku("String");
        entity.setQty(1);
        return entity;
    }

    @Test
    @Order(1)
    public void getOne() {
        StockoutShipmentResponse response = stockoutShipmentService.get(initId);
        Assertions.assertThat(response.getStockoutShipment().getShipmentBoxCode()).isEqualTo("shipment001");
    }

    @Test
    @Order(2)
    public void searchList() {
        StockoutShipmentSearchRequest request = new StockoutShipmentSearchRequest();
        // 默认查所有
        PageResponse<StockoutShipmentSearchResult> list = stockoutShipmentService.searchList(request);
        Assertions.assertThat(list.getTotalCount()).isGreaterThan(0);
    }

    @Test
    @Order(3)
    public void getByShipmentBoxCode() {
        StockoutShipmentResponse response = stockoutShipmentService.getByShipmentBoxCode("shipment001");
        Integer shipmentId = response.getStockoutShipment().getShipmentId();
        Assertions.assertThat(shipmentId).isEqualTo(initId);
    }

    @Test
    @Order(4)
    public void updateStockoutShipment() {
        StockoutShipmentUpdateRequest request = new StockoutShipmentUpdateRequest();
        request.setBoxIndex(12);
        stockoutShipmentService.updateStockoutShipment(request);
        StockoutShipmentEntity entity = stockoutShipmentService.getById(initId);
        Assertions.assertThat(entity.getBoxIndex()).isEqualTo(12);
    }

    @Test
    @Order(5)
    public void findTopByShipmentBoxCode() {
        StockoutShipmentEntity entity = stockoutShipmentService.findTopByShipmentBoxCode("shipment001");
        Assertions.assertThat(entity.getShipmentId()).isEqualTo(initId);
    }

    @Test
    @Order(6)
    public void getSkuListByShipmentBoxCode() {
        StockoutShipmentItemSkuInfoResponse shipmentList = stockoutShipmentService.getSkuListByShipmentBoxCode("shipment001", 1);
        Assertions.assertThat(shipmentList.getSkuList().size()).isGreaterThan(0);
    }
    @Test
    @Order(7)
    public void shipmentAllocate() {
        StockoutShipmentEntity entity = generateEntity();
        entity.setShipmentBoxCode("shipment002");
        stockoutShipmentService.save(entity);
        StockoutShipmentItemSkuInfoRequest request = new StockoutShipmentItemSkuInfoRequest();
        List<ShipmentTransferSku> skuList = new ArrayList<>();
        ShipmentTransferSku allocateSku = new ShipmentTransferSku();
        request.setSkuList(skuList);
        BeanUtilsEx.copyProperties(entity, allocateSku);
        skuList.add(allocateSku);
        request.setOriginShipmentBoxCode("shipment001");
        request.setTargetShipmentBoxCode("shipment002");
        stockoutShipmentService.transferShipmentBox(request);
    }


}
