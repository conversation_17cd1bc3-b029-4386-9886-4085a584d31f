package com.nsy.wms.service.stockout;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.api.wms.domain.stockout.StockoutBatchSplitTaskItemList;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.StockoutBatchSplitTaskStatus;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitLogListRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitTaskItemListRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitTaskListRequest;
import com.nsy.api.wms.request.stockout.StockoutBatchSplitTaskOperateRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitLogListResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskListResponse;
import com.nsy.api.wms.response.stockout.StockoutBatchSplitTaskResponse;
import com.nsy.wms.SpringServiceTest;
import com.nsy.wms.business.manage.user.response.BdDictionaryItem;
import com.nsy.wms.business.service.bd.BdSpaceService;
import com.nsy.wms.business.service.stockout.StockoutBatchService;
import com.nsy.wms.business.service.stockout.StockoutBatchSplitLogService;
import com.nsy.wms.business.service.stockout.StockoutBatchSplitTaskItemService;
import com.nsy.wms.business.service.stockout.StockoutBatchSplitTaskService;
import com.nsy.wms.business.service.stockout.AutoMachineSortService;
import com.nsy.wms.repository.entity.bd.BdSpaceEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitLogEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskEntity;
import com.nsy.wms.repository.entity.stockout.StockoutBatchSplitTaskItemEntity;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedList;
import java.util.List;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class StockoutBatchSplitTaskServiceTest extends SpringServiceTest {

    @Autowired
    StockoutBatchSplitTaskService splitTaskService;

    @Autowired
    StockoutBatchSplitTaskItemService splitTaskItemService;

    @Autowired
    StockoutBatchSplitLogService splitLogService;

    @Autowired
    StockoutBatchService batchService;

    @Autowired
    BdSpaceService spaceService;

    @Autowired
    AutoMachineSortService autoMachineSortService;

    @MockBean
    private RestTemplate restTemplate;

    private StockoutBatchSplitTaskEntity splitTaskEntity;
    private StockoutBatchEntity batchEntity;
    private BdSpaceEntity spaceEntity;

    @BeforeAll
    public void init() {
        mockGetDictionaryItemLikeDictionaryName();
        buildSpaceEntity();
        buildBatchEntity();
        buildSplitTaskEntity();
        buildSplitTaskItems();
        buileSplitLog();
    }

    private void mockGetDictionaryItemLikeDictionaryName() {
        BdDictionaryItem bdDictionaryItem = new BdDictionaryItem();
        bdDictionaryItem.setDictionaryId(1);
        bdDictionaryItem.setDictionaryName("wms_stockout_work_location");
        bdDictionaryItem.setValue("FBA_AREA");
        bdDictionaryItem.setLabel("FBA区域");
        bdDictionaryItem.setIsDeleted(0);
        BdDictionaryItem[] bdDictionaryItems = {bdDictionaryItem};
        Mockito.doReturn(bdDictionaryItems)
                .when(restTemplate).getForObject(Mockito.anyString(), ArgumentMatchers.eq(BdDictionaryItem[].class));
    }

    @Test
    @Order(1)
    public void getSplitTaskListTest() {
        StockoutBatchSplitTaskListRequest request = new StockoutBatchSplitTaskListRequest();
        PageResponse<StockoutBatchSplitTaskListResponse> response = splitTaskService.getTaskListByRequest(request);
        Assertions.assertNotNull(response);
    }

    @Test
    @Order(2)
    public void getSplitTaskDetailTest() {
        StockoutBatchSplitTaskResponse task = splitTaskService.getTaskById(splitTaskEntity.getTaskId());
        Assertions.assertNotNull(task);
    }

    @Test
    @Order(3)
    public void getSplitTaskItemListTest() {
        StockoutBatchSplitTaskItemListRequest request = new StockoutBatchSplitTaskItemListRequest();
        request.setTaskId(splitTaskEntity.getTaskId());
        PageResponse<StockoutBatchSplitTaskItemList> response = splitTaskService.getItemListByRequest(request);
        Assertions.assertNotNull(response);
    }

    @Test
    @Order(4)
    public void getSplitLogListTest() {
        StockoutBatchSplitLogListRequest request = new StockoutBatchSplitLogListRequest();
        request.setTaskId(splitTaskEntity.getTaskId());
        PageResponse<StockoutBatchSplitLogListResponse> response = splitTaskService.getLogListByRequest(request);
        Assertions.assertNotNull(response);
    }

    @Test
    @Order(5)
    public void forceFinishTaskTest() {
        StockoutBatchSplitTaskOperateRequest request = new StockoutBatchSplitTaskOperateRequest();
        List<Integer> splitTaskIdList = new LinkedList<>();
        splitTaskIdList.add(splitTaskEntity.getTaskId());
        request.setTaskIdList(splitTaskIdList);
        splitTaskService.forceFinishSplitTask(request);
        StockoutBatchSplitTaskEntity task = splitTaskService.getById(splitTaskEntity.getTaskId());
        Assertions.assertEquals(task.getStatus(), StockoutBatchSplitTaskStatus.SORTED.name());
    }

    @Test
    @Order(6)
    public void createSplitTask() {
        splitTaskEntity.setStatus(StockoutBatchSplitTaskStatus.SUSPEND_SORT.name());
        splitTaskService.updateById(splitTaskEntity);
        autoMachineSortService.createSplitTaskByExceptionSort(batchEntity.getBatchId());
        List<StockoutBatchSplitTaskEntity> taskEntityList = splitTaskService.getBaseMapper().selectList(new LambdaQueryWrapper<StockoutBatchSplitTaskEntity>()
                .eq(StockoutBatchSplitTaskEntity::getBatchId, batchEntity.getBatchId()));
        Assertions.assertFalse(taskEntityList.isEmpty());
    }

    @AfterAll
    public void deleteTestData() {
        splitTaskService.remove(new QueryWrapper<>());
        splitTaskItemService.remove(new QueryWrapper<>());
        spaceService.remove(new QueryWrapper<>());
        batchService.remove(new QueryWrapper<>());
    }

    private void buildSplitTaskEntity() {
        splitTaskEntity = new StockoutBatchSplitTaskEntity();
        splitTaskEntity.setLocation(LocationEnum.QUANZHOU.name());
        splitTaskEntity.setBatchId(batchEntity.getBatchId());
        splitTaskEntity.setTaskIndex(1);
        splitTaskEntity.setBatchSplitType(batchEntity.getBatchSplitType());
        splitTaskEntity.setPickingBoxQty(1);
        splitTaskEntity.setOutletQty(20);
        splitTaskEntity.setStatus("WAIT_SORT");
        splitTaskEntity.setOperator("lin");
        splitTaskService.save(splitTaskEntity);
    }

    private void buildBatchEntity() {
        batchEntity = new StockoutBatchEntity();
        batchEntity.setLocation(LocationEnum.QUANZHOU.name());
        batchEntity.setSpaceId(spaceEntity.getSpaceId());
        batchEntity.setPickingType("出库类型");
        batchEntity.setBatchSplitType("分拣类型");
        batchEntity.setWorkspace("FBA_AREA");
        batchEntity.setScanType("扫描台");
        batchEntity.setLogisticsCompany("物流公司");
        batchService.save(batchEntity);
    }

    private void buildSpaceEntity() {
        spaceEntity = new BdSpaceEntity();
        spaceEntity.setLocation(LocationEnum.QUANZHOU.name());
        spaceEntity.setSpaceName("时颖主仓库");
        spaceEntity.setSpaceCode("ZC");
        spaceEntity.setIsDeleted(0);
        spaceEntity.setIsShipping(0);
        spaceService.save(spaceEntity);
    }

    private void buildSplitTaskItems() {
        StockoutBatchSplitTaskItemEntity item1 = buildSplitTaskItemEntity();
        item1.setTaskId(splitTaskEntity.getTaskId());
        item1.setStockoutOrderNo("CK20210731001");
        item1.setProductId(1);
        item1.setSpecId(1);
        item1.setSku("LC610978-3-3X");
        item1.setBarcode("BARCODE110");
        item1.setBatchQty(10);
        item1.setExpectedQty(10);
        item1.setPickedQty(0);
        splitTaskItemService.save(item1);
        StockoutBatchSplitTaskItemEntity item2 = buildSplitTaskItemEntity();
        item2.setTaskId(splitTaskEntity.getTaskId());
        item2.setStockoutOrderNo("CK20210731001");
        item2.setProductId(1);
        item2.setSpecId(2);
        item2.setSku("LC-11-606060");
        item2.setBarcode("BARCODE111");
        item2.setBatchQty(5);
        item2.setExpectedQty(5);
        item2.setPickedQty(0);
        splitTaskItemService.save(item2);
        StockoutBatchSplitTaskItemEntity item3 = buildSplitTaskItemEntity();
        item3.setTaskId(splitTaskEntity.getTaskId());
        item3.setStockoutOrderNo("CK20210731002");
        item3.setProductId(1);
        item3.setSpecId(1);
        item3.setSku("LC610978-3-3X");
        item3.setBarcode("BARCODE110");
        item3.setBatchQty(20);
        item3.setExpectedQty(20);
        item3.setPickedQty(0);
        splitTaskItemService.save(item3);
    }

    private StockoutBatchSplitTaskItemEntity buildSplitTaskItemEntity() {
        StockoutBatchSplitTaskItemEntity result = new StockoutBatchSplitTaskItemEntity();
        result.setLocation(LocationEnum.QUANZHOU.name());
        result.setScanQty(0);
        result.setLackQty(0);
        result.setIsLack(0);
        return result;
    }

    private void buileSplitLog() {
        StockoutBatchSplitLogEntity log1 = new StockoutBatchSplitLogEntity();
        log1.setLocation(LocationEnum.QUANZHOU.name());
        log1.setTaskId(splitTaskEntity.getTaskId());
        log1.setBatchId(batchEntity.getBatchId());
        log1.setBatchSplitType("二次分拣");
        log1.setLogType("分拣任务生成");
        log1.setContent("分拣任务生成，生成窄带机器分拣任务，分拣总波次/出库单数30，分拣总件数3000件，共10箱");
        splitLogService.save(log1);
        StockoutBatchSplitLogEntity log2 = new StockoutBatchSplitLogEntity();
        log2.setLocation(LocationEnum.QUANZHOU.name());
        log2.setTaskId(splitTaskEntity.getTaskId());
        log2.setBatchId(batchEntity.getBatchId());
        log2.setBatchSplitType("二次分拣");
        log2.setLogType("设置分拣口");
        log2.setContent("生成机器分拣口，共设置19个口");
        splitLogService.save(log2);
        StockoutBatchSplitLogEntity log3 = new StockoutBatchSplitLogEntity();
        log3.setLocation(LocationEnum.QUANZHOU.name());
        log3.setTaskId(splitTaskEntity.getTaskId());
        log3.setBatchId(batchEntity.getBatchId());
        log3.setBatchSplitType("二次分拣");
        log3.setLogType("分拣开始");
        log3.setContent("窄带分拣机开始执行分拣任务，当前分拣口19，异常口4个");
        splitLogService.save(log3);
    }
}
