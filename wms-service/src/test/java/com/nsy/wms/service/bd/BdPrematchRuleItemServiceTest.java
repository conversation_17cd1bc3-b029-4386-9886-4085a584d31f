package com.nsy.wms.service.bd;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.wms.SpringServiceTest;
import com.nsy.api.wms.constants.IsDeletedConstant;
import com.nsy.api.wms.domain.bd.PrematchRuleItemModel;
import com.nsy.wms.repository.entity.bd.BdPrematchRuleItemEntity;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.wms.repository.jpa.mapper.bd.BdPrematchRuleItemMapper;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.bd.PrematchRuleItemAddRequest;
import com.nsy.wms.business.service.bd.BdPrematchRuleItemService;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Collections;
import java.util.List;

/**
 * HXD
 * 2021/7/16
 **/

public class BdPrematchRuleItemServiceTest extends SpringServiceTest {
    @Autowired
    private BdPrematchRuleItemService ruleItemService;
    @Autowired
    private BdPrematchRuleItemMapper bdPrematchRuleItemMapper;
    @MockBean
    EnumConversionChineseUtils enumConversionChineseUtils;

    @Test
    public void testNotNull() {
        Assertions.assertThat(ruleItemService).isNotNull();
    }

    @BeforeEach
    public void init() {
        BdPrematchRuleItemEntity entity = generateEntity();
        bdPrematchRuleItemMapper.insert(entity);
        Mockito.doReturn("")
                .when(enumConversionChineseUtils).baseConversion(Mockito.anyString(), Mockito.anyString());
    }

    private Integer getSaveId() {
        List<BdPrematchRuleItemEntity> all = bdPrematchRuleItemMapper.selectList(new QueryWrapper<>());
        return all.get(0).getPrematchRuleItemId();
    }

    @AfterEach
    public void deleteTestData() {
        bdPrematchRuleItemMapper.delete(new QueryWrapper<>());
    }


    public static BdPrematchRuleItemEntity generateEntity() {
        BdPrematchRuleItemEntity entity = new BdPrematchRuleItemEntity();
        entity.setPrematchRuleId(Integer.MAX_VALUE);
        entity.setPositionType("PositionTypeEnName");
        entity.setRuleItemType("规则名称6847be4169f14b2ea1edea05e4c60e2c");
        entity.setRuleItemValue("规则描述6847be4169f14b2ea1edea05e4c60e2c");
        entity.setIsLessThanSkuLimit(0);
        entity.setIsDeleted(IsDeletedConstant.NOT_DELETED);
        entity.setSort(1);
        entity.setDescription("备注");
        entity.setLocation(LocationEnum.QUANZHOU.name());
        entity.setVersion(1);
        entity.setCreateBy("Test");
        entity.setUpdateBy("Test");
        return entity;
    }

    @Test
    public void getList() {
        List<PrematchRuleItemModel> prematchRuleItemList = ruleItemService.getPrematchRuleItemList(Integer.MAX_VALUE);
        Assertions.assertThat(prematchRuleItemList.size()).isGreaterThan(0);
    }

    @Test
    public void insert() {
        BdPrematchRuleItemEntity entity = generateEntity();
        entity.setPrematchRuleId(Integer.MAX_VALUE - 1);
        PrematchRuleItemAddRequest request = new PrematchRuleItemAddRequest();
        BeanUtils.copyProperties(entity, request);
        ruleItemService.addPrematchRuleItem(request, Integer.MAX_VALUE);
    }

    @Test
    public void update() {
        BdPrematchRuleItemEntity itemEntity = bdPrematchRuleItemMapper.selectById(getSaveId());
        PrematchRuleItemAddRequest request = new PrematchRuleItemAddRequest();
        BeanUtils.copyProperties(itemEntity, request);
        request.setSort(10);
        ruleItemService.updatePrematchRuleItem(itemEntity.getPrematchRuleItemId(), request);
        BdPrematchRuleItemEntity entity = bdPrematchRuleItemMapper.selectById(itemEntity.getPrematchRuleItemId());
        Assertions.assertThat(entity.getSort()).isEqualTo(request.getSort());
    }

    @Test
    public void updatePrematchRuleItemIsDeleted() {
        Integer id = getSaveId();
        IdListRequest request = new IdListRequest();
        request.setIdList(Collections.singletonList(id));
        ruleItemService.updatePrematchRuleItemIsDeleted(request);
        BdPrematchRuleItemEntity one = bdPrematchRuleItemMapper.selectById(id);
        Assertions.assertThat(one.getIsDeleted()).isEqualTo(IsDeletedConstant.DELETED);
    }
}
