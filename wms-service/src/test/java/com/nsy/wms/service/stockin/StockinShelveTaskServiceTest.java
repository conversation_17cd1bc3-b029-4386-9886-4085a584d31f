package com.nsy.wms.service.stockin;

import com.nsy.api.wms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.request.download.DownloadRequest;
import com.nsy.api.wms.request.stockin.StockinShelveTaskListRequest;
import com.nsy.api.wms.response.base.DownloadResponse;
import com.nsy.api.wms.response.stockin.ShelveTaskListResponse;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.nsy.wms.SpringServiceTest;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskEntity;
import com.nsy.wms.repository.entity.stockin.StockinShelveTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.stockin.StockinShelveTaskMapper;
import com.nsy.wms.business.service.stockin.StockinShelveTaskItemService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskListService;
import com.nsy.wms.business.service.stockin.StockinShelveTaskService;
import com.nsy.wms.utils.JsonMapper;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class StockinShelveTaskServiceTest extends SpringServiceTest {

    @Autowired
    StockinShelveTaskService stockinShelveTaskService;
    @Autowired
    StockinShelveTaskItemService stockinShelveTaskItemService;
    @Autowired
    StockinShelveTaskMapper stockinShelveTaskMapper;
    StockinShelveTaskEntity stockinShelveTaskEntity;
    @Autowired
    StockinShelveTaskListService stockinShelveTaskListService;
    @BeforeAll
    public void init() {
        stockinShelveTaskEntity = getStockinShelveTaskInfo();
        stockinShelveTaskEntity.setLocation(LocationEnum.QUANZHOU.name());
        stockinShelveTaskEntity.setStatus("PENDING");
        stockinShelveTaskEntity.setInternalBoxCode("001");
        stockinShelveTaskMapper.insert(stockinShelveTaskEntity);
        initStockinShelveTaskItemInfo();
    }

    @AfterAll
    public void deleteTestData() {
        stockinShelveTaskMapper.deleteById(stockinShelveTaskEntity.getShelveTaskId());
        stockinShelveTaskItemService.remove(new QueryWrapper<>());
    }

    @Test
    @Order(2)
    public void getStockinShelveTaskListTest() {
        StockinShelveTaskListRequest request = new StockinShelveTaskListRequest();
        request.setInternalBoxCode("001");
        List<ShelveTaskListResponse> responseList = stockinShelveTaskListService.getStockinShelveTaskList(request).getContent();
        Assertions.assertNotNull(responseList);
    }

    @Test
    @Order(1)
    public void changeStockinShelveTaskStatusTest() {
        List<StockinShelveTaskEntity> list = stockinShelveTaskMapper.selectList(new QueryWrapper<>());
        stockinShelveTaskService.changeStockinShelveTaskStatus(list.get(0), "待上架");
    }

    @Test
    @Order(2)
    public void typeTest() {
        QuartzDownloadQueueTypeEnum type = stockinShelveTaskService.type();
        Assertions.assertEquals(type, QuartzDownloadQueueTypeEnum.WMS_STOCKIN_SHELVE_TASK);
    }

    @Test
    @Order(3)
    public void queryExportData() {
        StockinShelveTaskListRequest request = new StockinShelveTaskListRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        DownloadRequest downloadRequest = new DownloadRequest();
        downloadRequest.setPageSize(10);
        downloadRequest.setPageIndex(1);
        downloadRequest.setType(QuartzDownloadQueueTypeEnum.WMS_STOCKIN_SHELVE_TASK);
        downloadRequest.setLocation(LocationEnum.QUANZHOU.name());
        downloadRequest.setRequestContent(JsonMapper.toJson(request));
        DownloadResponse response = stockinShelveTaskService.queryExportData(downloadRequest);
        Assertions.assertNotEquals(response.getTotalCount(), 0);
    }


    private StockinShelveTaskEntity getStockinShelveTaskInfo() {
        StockinShelveTaskEntity entity = new StockinShelveTaskEntity();
        entity.setStatus("待上架");
        entity.setLocation(LocationEnum.QUANZHOU.name());
        entity.setInternalBoxCode("001");
        return entity;
    }

    private void initStockinShelveTaskItemInfo() {
        StockinShelveTaskItemEntity entity = new StockinShelveTaskItemEntity();
        List<StockinShelveTaskEntity> shelveTaskList = stockinShelveTaskService.list(new QueryWrapper<>());
        entity.setLocation(LocationEnum.QUANZHOU.name());
        entity.setBarcode("001");
        entity.setStockinQty(30);
        entity.setReturnedQty(0);
        entity.setShelvedQty(0);
        entity.setSourceId(1);
        entity.setSku("sku001");
        entity.setShelveTaskId(shelveTaskList.get(0).getShelveTaskId());
        entity.setSpecId(1);
        stockinShelveTaskItemService.save(entity);
    }
}
