package com.nsy.wms.service.stock;

import com.nsy.api.wms.enumeration.bd.BdPositionTypeEnum;
import com.nsy.api.wms.enumeration.bd.LocationEnum;
import com.nsy.api.wms.enumeration.stockout.StockTakeStockTaskStatusEnum;
import com.nsy.api.wms.request.base.PageRequest;
import com.nsy.api.wms.request.bd.IdListRequest;
import com.nsy.api.wms.request.stock.StockTakeStockLogListRequest;
import com.nsy.api.wms.request.stock.StockTakeStockTaskRequest;
import com.nsy.api.wms.response.base.PageResponse;
import com.nsy.api.wms.response.stock.StockTakeStockTaskItemResponse;
import com.nsy.api.wms.response.stock.StockTakeStockTaskLogResponse;
import com.nsy.api.wms.response.stock.StockTakeStockTaskResponse;
import com.nsy.wms.SpringServiceTest;
import com.nsy.wms.business.manage.erp.ErpTransferApiService;
import com.nsy.wms.business.service.bd.BdPositionService;
import com.nsy.wms.business.service.stock.StockTakeStockTaskService;
import com.nsy.wms.repository.entity.bd.BdPositionEntity;
import com.nsy.wms.repository.entity.product.ProductSpecInfoEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskEntity;
import com.nsy.wms.repository.entity.stock.StockTakeStockTaskItemEntity;
import com.nsy.wms.repository.jpa.mapper.product.ProductSpecInfoMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockTakeStockTaskItemMapper;
import com.nsy.wms.repository.jpa.mapper.stock.StockTakeStockTaskMapper;
import com.nsy.wms.utils.EnumConversionChineseUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class StockTakeStockTaskServiceTest extends SpringServiceTest {

    @Autowired
    StockTakeStockTaskService taskService;
    @MockBean
    EnumConversionChineseUtils enumConversionChineseUtils;
    @Autowired
    StockTakeStockTaskMapper taskMapper;
    @Autowired
    StockTakeStockTaskItemMapper taskItemMapper;
    @Autowired
    ProductSpecInfoMapper specInfoMapper;
    @Autowired
    BdPositionService positionService;
    StockTakeStockTaskEntity taskEntity;
    StockTakeStockTaskItemEntity taskItemEntity;
    ProductSpecInfoEntity specInfoEntity;
    @MockBean
    ErpTransferApiService erpTransferApiService;

    @BeforeAll
    public void init() {
        BdPositionEntity positionEntity = new BdPositionEntity();
        positionEntity.setPositionCode("101");
        positionEntity.setSpaceId(1);
        positionEntity.setSpaceName("时颖主仓");
        positionEntity.setAreaId(22);
        positionEntity.setAreaName("20楼");
        positionEntity.setSpaceAreaId(30);
        positionEntity.setSpaceAreaName("000041");
        positionEntity.setPositionName("P6666665");
        positionEntity.setIsDeleted(0);
        positionEntity.setPositionType(BdPositionTypeEnum.EXCEPTION_POSITION.name());
        positionService.save(positionEntity);

        taskEntity = new StockTakeStockTaskEntity();
        taskEntity.setLocation(LocationEnum.QUANZHOU.name());
        taskEntity.setPlanId(1);
        taskEntity.setPlanType("test");
        taskEntity.setTaskGenerateMode("test");
        taskEntity.setOperator("admin");
        taskEntity.setSupervisor("admin");
        taskEntity.setStatus(StockTakeStockTaskStatusEnum.TO_BE_INVENTORY.name());
        taskMapper.insert(taskEntity);

        taskItemEntity = new StockTakeStockTaskItemEntity();
        taskItemEntity.setLocation(LocationEnum.QUANZHOU.name());
        taskItemEntity.setTaskId(taskEntity.getTaskId());
        taskItemEntity.setSpecId(1);
        taskItemEntity.setAreaId(1);
        taskItemEntity.setAreaName("test");
        taskItemEntity.setSpaceAreaId(1);
        taskItemEntity.setSpaceAreaName("test");
        taskItemEntity.setPositionId(positionEntity.getPositionId());
        taskItemEntity.setPositionCode(positionEntity.getPositionCode());
        taskItemMapper.insert(taskItemEntity);

        specInfoEntity = new ProductSpecInfoEntity();
        specInfoEntity.setSpecId(1);
        specInfoMapper.insert(specInfoEntity);

    }

    @AfterAll
    public void destroy() {
        taskMapper.delete(null);
        taskItemMapper.delete(null);
        specInfoMapper.delete(null);
    }

    @Test
    @Order(1)
    public void pageList() {
        PageResponse<StockTakeStockTaskResponse> response = taskService.pageList(new StockTakeStockTaskRequest());
        Assertions.assertTrue(response.getTotalCount() > 0);
    }

    @Test
    @Order(2)
    public void cancel() {
        IdListRequest request = new IdListRequest();
        request.setIdList(Lists.newArrayList(taskEntity.getTaskId()));
        taskService.cancel(request);
        Assertions.assertEquals(taskMapper.selectById(taskEntity.getTaskId()).getStatus(), StockTakeStockTaskStatusEnum.INVENTORY_CANCEL.name());
        Assertions.assertEquals(taskItemMapper.selectById(taskItemEntity.getId()).getStatus(), StockTakeStockTaskStatusEnum.INVENTORY_CANCEL.name());
    }

    @Test
    @Order(4)
    public void itemList() {
        PageResponse<StockTakeStockTaskItemResponse> response = taskService.itemList(new PageRequest(), taskEntity.getTaskId());
        Assertions.assertTrue(response.getTotalCount() > 0);
    }

    @Test
    @Order(5)
    public void logList() {
        StockTakeStockLogListRequest request = new StockTakeStockLogListRequest();
        request.setTaskId(taskEntity.getTaskId());
        PageResponse<StockTakeStockTaskLogResponse> response = taskService.logList(request);
        Assertions.assertTrue(response.getTotalCount() > 0);
    }
}
