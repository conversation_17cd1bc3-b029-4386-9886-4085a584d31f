FROM registry.cn-hangzhou.aliyuncs.com/xinshiying/openjdk:8-jdk-alpine-font-sw
COPY --from=registry.cn-hangzhou.aliyuncs.com/xinshiying/arthas /opt/arthas /opt/arthas
VOLUME /tmp
EXPOSE 8080
ADD ./build/libs/wms-service-0.0.1.jar /app/app.jar
ENV APP_NAME=api-wms
COPY entrypoint.sh /app/
COPY pre_stop.sh /app/
RUN chmod +x /app/entrypoint.sh
RUN chmod +x /app/pre_stop.sh
ENTRYPOINT [ "tini","--","/app/entrypoint.sh" ]
